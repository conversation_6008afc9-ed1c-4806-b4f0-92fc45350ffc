package dh

import (
	"encoding/json"
	"net/http"
	"net/url"
	"strings"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/game/common/config"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
)

const (
	DH_GAME_CD    = "3013"
	APP_BUNDLE_ID = "twgame.global.acers"
	DH_MD5_SALT   = "fbiubi9gewaga=niu1n3091mlnoahgawng"

	DH_TEST_URL    = "https://ulog-test.dhgames.cn:8181/external/push_svr_log"
	DH_PRODUCT_URL = "https://ulog.dhgames.com:8180/external/push_svr_log"
)

// dh事件名
const (
	DH_EVENT_NAME_ONLINE   = "online"       // 在线统计
	DH_EVENT_NAME_REGISTER = "register"     // 注册
	DH_EVENT_NAME_LOGIN    = "login"        // 登录
	DH_EVENT_NAME_LOGOUT   = "logout"       // 登出
	DH_EVENT_NAME_PAY      = "pay"          // 付费
	DH_EVENT_NAME_REFUND   = "refund_order" // 退款
)

// dh事件
var DH_EVENT_MAP = map[string]map[string]string{
	DH_EVENT_NAME_ONLINE: {
		"event_type": "server",
		"event_code": "1100510002",
	},
	DH_EVENT_NAME_REGISTER: {
		"event_type": "base",
		"event_code": "1100110001",
	},
	DH_EVENT_NAME_LOGIN: {
		"event_type": "base",
		"event_code": "1100110002",
	},
	DH_EVENT_NAME_LOGOUT: {
		"event_type": "base",
		"event_code": "1100110003",
	},
	DH_EVENT_NAME_PAY: {
		"event_type": "base",
		"event_code": "1100110004",
	},
	DH_EVENT_NAME_REFUND: {
		"event_type": "game",
		"event_code": "1100410004",
	},
}

var (
	client *http.Client
	dhUrl  string

	DhLogServerId int // dh上报使用的serverId 亚服1 美服2
)

// 初始化dh日志
func InitDhLog() {
	client = &http.Client{Timeout: 10 * time.Second}
	switch slg.SERVER_AREA {
	case slg.SERVER_AREA_HK:
		DhLogServerId = 1
		dhUrl = DH_PRODUCT_URL
	case slg.SERVER_AREA_US:
		DhLogServerId = 2
		dhUrl = DH_PRODUCT_URL
	case "singapore", "local": // TEST
		DhLogServerId = 0
		dhUrl = DH_TEST_URL
	}
}

func Track(eventName string, eventValue map[string]interface{}) {
	if slg.DEBUG {
		log.Info("dh track eventName: %v, eventValue: %v", eventName, eventValue)
	}
	if client == nil || dhUrl == "" || !slg.HD_LOG_OPEN {
		return
	}
	eventInfo := DH_EVENT_MAP[eventName]
	if eventInfo == nil {
		log.Warning("dh track eventInfo nil, eventName: %v", eventName)
		return
	}
	now := time.Now()
	params := map[string]interface{}{}
	// 补充字段
	params["event_name"] = eventName
	params["event_type"] = eventInfo["event_type"]
	params["event_code"] = eventInfo["event_code"]
	params["game_cd"] = DH_GAME_CD
	params["create_ts"] = ut.String(now.UnixNano())
	// event_value的值需要序列化成字符串
	jsonbytes, _ := json.Marshal(eventValue)
	eventValStr := string(jsonbytes)
	params["event_value"] = eventValStr
	go _track(eventName, params, now)
}

func _track(eventName string, params map[string]interface{}, now time.Time) {
	// data := map[string]interface{}{"data": params}
	jsonBytes, _ := json.Marshal(params)
	jsonStr := string(jsonBytes)

	// 对 JSON 字符串进行 URL 编码
	encodedData := url.QueryEscape(jsonStr)
	// 构造表单数据，key 为 "data"，value 为 URL 编码后的 JSON 字符串
	formData := "data=" + encodedData

	// formData := url.Values{}
	// formData.Add("data", jsonStr) // 将值转换为字符串
	// formStr := formData.Encode()
	req, err := http.NewRequest("POST", dhUrl, strings.NewReader(formData))
	if err != nil {
		log.Error("dh track http req err: %v", err)
		return
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("X-Ulog-Date", ut.String(now.Unix()))
	token := GetDhToken(now)
	req.Header.Add("Authorization", token)
	resp, err := client.Do(req)
	defer func() {
		if resp != nil && resp.Body != nil {
			resp.Body.Close()
		}
	}()
	if err != nil {
		log.Error("dh track err: %v, eventName: %v", err, eventName)
		return
	}
	if resp.StatusCode != 200 {
		log.Warning("dh track err: %v, eventName: %v", err, eventName)
	}
	// bodyBytes, err := io.ReadAll(resp.Body)
	// if err != nil {
	// 	return
	// }
	// bodyString := string(bodyBytes)
	// log.Info("bodyString: %v", bodyString)
}

// 获取token
func GetDhToken(now time.Time) string {
	// token是指定字符串和秒级时间戳拼接的md5
	return ut.MD5(DH_MD5_SALT + ut.String(now.Unix()))
}

// 平台转换
func DhPlatformSwitch(platform string) string {
	switch platform {
	case "ios_global":
		return "iOS"
	case "ios_inland ":
		return "iOS"
	case "google":
		return "android"
	case "taptap":
		return "android"
	case "web":
		return "pc"
	}
	return platform
}

// 付费dh上报
func DhPayLog(userId, sessionId, platform, orderId, payPlatform, productId string, isSub, isfirst bool, quantity int32) {
	isSandBox := 0
	if slg.IS_SANDBOX {
		isSandBox = 1
	}
	// 支付渠道其他支付渠道默认为999 微信：1 支付宝：2 IOS：3 GP：4 Xsolla web支付：5 Xsolla 游戏内支付：6
	var payChannel int
	switch payPlatform {
	case slg.PAY_PLATFORM_WX:
		payChannel = 1
	case slg.PAY_PLATFORM_APPLE:
		payChannel = 3
	default:
		payChannel = 999
	}
	var priceUs float64
	var priceRmb float64
	var storeId int
	if isSub {
		info := slg.SUB_PRODUCT_INFO_MAP[productId]
		if info == nil {
			return
		}
		// 获取订阅金额
		storeId = ut.Int(info["storeId"])
		if isfirst {
			priceRmb = info["rmbFirst"]
			priceUs = info["usFirst"]
		} else {
			priceRmb = info["rmb"]
			priceUs = info["us"]
		}
	} else if productId == slg.BATTLE_PASS_PRODUCT_ID {
		storeId = ut.Int(slg.BATTLE_PASS_PRODUCT_INFO["storeId"])
		priceRmb = slg.BATTLE_PASS_PRODUCT_INFO["rmb"]
		priceUs = slg.BATTLE_PASS_PRODUCT_INFO["us"]

	} else {
		datas := config.GetJson("recharge").Datas
		if datas == nil {
			return
		}
		for _, v := range datas {
			if ut.String(v["product_id"]) == productId {
				storeId = ut.Int(v["id"])
				priceRmbStr := ut.String(v["money_china"])
				priceRmbStrArr := strings.Split(priceRmbStr, "¥")
				if len(priceRmbStrArr) >= 2 {
					priceRmb = ut.Float64(priceRmbStrArr[1])
				}
				priceUsStr := ut.String(v["money_en"])
				priceUsStrArr := strings.Split(priceUsStr, "$")
				if len(priceUsStrArr) >= 2 {
					priceUs = ut.Float64(priceUsStrArr[1])
				}
			}
		}
	}
	priceUs *= float64(quantity)
	priceRmb *= float64(quantity)
	Track(DH_EVENT_NAME_PAY, map[string]interface{}{
		"user_info": map[string]interface{}{
			"bundle_id":   APP_BUNDLE_ID, // 游戏包名
			"sub_package": "",            // 头条分包，没有的传空值
			"server_id":   DhLogServerId, // 逻辑服ID， 大于等于10000的server_id会被当作测试环境数据处理
			"user_id":     userId,        // 用户游戏角色ID，为8位数字id，全服唯一
			"session_id":  sessionId,     // 由客户端初始化时生成（ 建议使用时间戳+设备信息or uuid  md5）一个唯一值，并传给后端
			"account":     userId,
			"platform":    DhPlatformSwitch(platform), // 平台，iOS android pc
			"lv":          0,
			"vip":         0,
		},
		"event_info": map[string]interface{}{
			"order_no":      orderId,    // 第三方订单号【自研项目接入中台的third_id】
			"pay_price":     priceUs,    // 美元计费点
			"pay_price_rmb": priceRmb,   // 人民币计费点 市场需求
			"pay_channel":   payChannel, // 支付渠道
			"store_id":      storeId,    // 礼包ID（数字）
			"is_sandbox":    isSandBox,  // 是否为沙盒付费 1沙盒付费 0非沙盒付费,2024.02.07新增
		},
	})
}
