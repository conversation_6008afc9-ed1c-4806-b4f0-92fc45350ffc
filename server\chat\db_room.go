package chat

import (
	"context"
	"time"

	slg "slgsrv/server/common"
	r "slgsrv/server/game/room"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type RoomMongodb struct {
	table string
}

var (
	mgo_database string
	mgo_client   *mongo.Client
	db_room      = &RoomMongodb{slg.DB_COLLECTION_NAME_ROOM}
)

// 初始化游戏服db
func InitRoomDB(url, dbname string) {
	mgo_database = dbname
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	opt := options.Client().ApplyURI(url)
	opt.SetLocalThreshold(3 * time.Second)  // 只使用与mongo操作耗时小于3秒的
	opt.SetMaxConnIdleTime(5 * time.Second) // 指定连接可以保持空闲的最大毫秒数
	opt.SetMaxPoolSize(20)                  // 使用最大的连接数
	var err error
	if mgo_client, err = mongo.Connect(ctx, opt); err == nil {
		log.Info("room_mongodb init success! " + url + "[" + mgo_database + "]")
	} else if err == mongo.ErrNoDocuments {
		log.Error("room_mongodb init error! ErrNoDocuments")
	} else {
		log.Error(err.Error())
	}
}

func (this *RoomMongodb) getCollection() *mongo.Collection {
	return mgo_client.Database(mgo_database).Collection(this.table)
}

// 获取已结束房间数据
func (this *RoomMongodb) FindGameOverAll(roomType int) (list []r.RoomTableData, err string) {
	cur, e := this.getCollection().Find(context.TODO(), bson.M{"type": roomType, "game_over_info": bson.M{"$ne": nil}})
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	if e != nil {
		return []r.RoomTableData{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []r.RoomTableData{}, e.Error()
	} else if e = cur.All(context.TODO(), &list); e != nil {
		err = e.Error()
	}
	return
}
