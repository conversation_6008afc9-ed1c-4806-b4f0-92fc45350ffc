package world

import (
	"sort"
	"strings"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/sasha-s/go-deadlock"
)

const (
	CHAT_MAX_COUNT        = 50  // 聊天信息最多存储数量（内存）
	CHAT_MAX_DB_COUNT_PER = 200 // 聊天信息DB每次存储频道数量
)

type ChatMap struct {
	deadlock.RWMutex
	Map        map[string][]*ChatInfo
	AddTimeMap map[string]int64 // 添加时间
	DbMap      map[string]int
}

type ChatInfo struct {
	UID            string `bson:"uid"`
	Channel        string `bson:"channel"`         // 频道 0.世界("0") 1.联盟("1_alliUid") 2.私聊("2_uid_uid") 3.系统("3")
	Sender         string `bson:"sender"`          // 发送者
	Content        string `bson:"content"`         // 内容
	SenderNickname string `bson:"sender_nickname"` // 发送者昵称
	SenderHeadicon string `bson:"sender_headicon"` // 发送者头像

	PortrayalInfo *pb.PortrayalInfo   `bson:"portrayal_info"` // 画像
	EquipInfo     *pb.EquipInfo       `bson:"equip_info"`     // 装备
	BattleInfo    *pb.ShareBattleInfo `bson:"battle_info"`    // 战报信息
	ReplyInfo     *pb.ReplyChatInfo   `bson:"reply_info"`     // 回复信息

	Time              int64 `bson:"time"`                // 发送时间
	BannedSurplusTime int64 `bson:"banned_surplus_time"` // 禁言剩余时间\

	SID         int32 `bson:"sid"`
	Emoji       int32 `bson:"emoji"`        // 表情
	SenderTitle int32 `bson:"sender_title"` // 发送者称号
	BanDisplay  bool  `bson:"ban_display"`  // 该条消息禁止显示
}

func (this *ChatInfo) ToWeb(wld *Model) map[string]interface{} {
	senderName, senderHead := "", ""
	if plr := wld.GetTempPlayer(this.Sender); plr != nil {
		senderName = plr.Nickname
		senderHead = plr.HeadIcon
	}
	var portrayalInfo map[string]interface{} = nil
	if this.PortrayalInfo != nil {
		portrayalInfo = g.NewPortrayalByPb(this.PortrayalInfo).ToJson()
	}
	var equipInfo map[string]interface{} = nil
	if this.EquipInfo != nil {
		equipInfo = g.NewEquipByPb(this.EquipInfo).ToJson()
	}
	var replyInfo map[string]interface{} = nil
	if this.ReplyInfo != nil {
		replyInfo = map[string]interface{}{"uid": this.ReplyInfo.Uid, "text": this.ReplyInfo.Text}
	}
	return map[string]interface{}{
		"uid":               this.UID,
		"channel":           this.Channel,
		"sender":            this.Sender,
		"senderName":        senderName,
		"senderHead":        senderHead,
		"content":           this.Content,
		"emoji":             this.Emoji,
		"time":              this.Time,
		"portrayalInfo":     portrayalInfo,
		"equipInfo":         equipInfo,
		"replyInfo":         replyInfo,
		"bannedSurplusTime": this.BannedSurplusTime,
		"banDisplay":        this.BanDisplay,
	}
}

func (this *ChatInfo) ToPb() *pb.ChatInfo {
	return &pb.ChatInfo{
		Uid:               this.UID,
		Channel:           this.Channel,
		Sender:            this.Sender,
		Content:           this.Content,
		Emoji:             this.Emoji,
		PortrayalInfo:     this.PortrayalInfo,
		EquipInfo:         this.EquipInfo,
		ReplyInfo:         this.ReplyInfo,
		Time:              int64(this.Time),
		BannedSurplusTime: int64(this.BannedSurplusTime),
		SenderNickname:    this.SenderNickname,
		SenderHeadicon:    this.SenderHeadicon,
		SenderTitle:       this.SenderTitle,
		BattleInfo:        this.BattleInfo,
	}
}

func (this *Model) ToChatDB() map[string][]*ChatInfo {
	this.Chats.RLock()
	defer this.Chats.RUnlock()
	obj := map[string][]*ChatInfo{}
	for k, v := range this.Chats.Map {
		obj[k] = v
	}
	return obj
}

func (this *Model) FromChatDB(data []ChatTableData) {
	for _, m := range data {
		var list []*ChatInfo = nil
		if len(m.List) > CHAT_MAX_COUNT {
			startIndex := len(m.List) - CHAT_MAX_COUNT
			list = m.List[startIndex:]
		} else {
			list = m.List
		}
		// 兼容私聊
		if strings.Index(m.Channel, "2_") == 0 {
			if arr := strings.Split(m.Channel, "_"); len(arr) < 4 {
				m.Channel = m.Channel + "_" + arr[1]
				for _, chat := range list {
					chat.Channel = m.Channel
				}
			}
		}
		// 兼容观战者聊天
		if m.Channel == "0" {
			for i := len(list) - 1; i >= 0; i-- {
				this.allTempPlayers.RLock()
				_, exist := this.allTempPlayers.Map[list[i].Sender]
				this.allTempPlayers.RUnlock()
				if !exist {
					// 发送者是观战者则初始化时删除该聊天
					list = append(list[:i], list[i+1:]...)
				}
			}
		}
		this.Chats.Map[m.Channel] = list
	}
}

func (this *Model) GetChatsByChannel(channel string) []*ChatInfo {
	return this.Chats.Map[channel]
}

// 删除聊天
func (this *Model) DoDelGameChat(channel, uid string) {
	needDelDb := false
	// 先从内存中删除
	this.Chats.Lock()
	chats := this.GetChatsByChannel(channel)
	if chats != nil {
		delIndex := -1
		for i, v := range chats {
			if v.UID == uid {
				delIndex = i
				break
			}
		}
		if delIndex >= 0 {
			// 在内存中存在 判断是否已保存
			dbIndex, ok := this.Chats.DbMap[channel]
			if !ok {
				needDelDb = true
			} else if delIndex < dbIndex {
				needDelDb = true
			}
			chats = append(chats[:delIndex], chats[delIndex+1:]...)
			this.Chats.Map[channel] = chats
		} else {
			needDelDb = true
		}
	}
	this.Chats.Unlock()
	// 从数据库删除
	if needDelDb {
		this.db.DelChatByUid(channel, uid)
	}
}

// 添加聊天频道
func (this *Model) AddChatChannel(channel string) {
	this.Chats.Lock()
	defer this.Chats.Unlock()
	if _, ok := this.Chats.Map[channel]; ok {
		// 频道已存在
		return
	}
	this.Chats.Map[channel] = []*ChatInfo{}
}

// 删除聊天频道
func (this *Model) DelChatChannel(channel string) {
	this.Chats.Lock()
	delete(this.Chats.Map, channel)
	delete(this.Chats.DbMap, channel)
	this.Chats.Unlock()
	go this.db.DelChatByChannel(channel)
}

// 获取聊天信息 根据联盟id
func (this *Model) GetChatsByAlliUID(uid string, alliUid string, start, size int, startTime, endTime int64, filterUid string) []map[string]interface{} {
	this.Chats.RLock()
	defer this.Chats.RUnlock()
	chats := []map[string]interface{}{}
	channel := ""
	if uid != "" {
		// 私聊
		channel = "2_" + uid
	} else if alliUid != "" {
		// 联盟
		channel = "1_" + alliUid
	} else {
		// 世界
		channel = "0"
	}
	data, e := this.db.GetChat(channel)
	// 私聊兼容
	if uid != "" {
		uids := strings.Split(uid, "_")
		channelNew := channel + "_" + uids[0]
		dataNew, err := this.db.GetChat(channelNew)
		if err != "" {
			channelNew = channel + "_" + uids[1]
			dataNew, err = this.db.GetChat(channelNew)
			if err == "" {
				data.List = append(data.List, dataNew.List...)
			}
		} else {
			data.List = append(data.List, dataNew.List...)
		}
		e = err
	}
	dataList := []*ChatInfo{}
	for i := len(data.List) - 1; i >= 0; i-- {
		if filterUid != "" && filterUid != data.List[i].Sender {
			continue
		}
		if startTime > 0 && data.List[i].Time < startTime {
			continue
		}
		if endTime > 0 && data.List[i].Time > endTime {
			break
		}
		dataList = append(dataList, data.List[i])
	}
	tail := ut.Min(len(dataList), start+size)
	list := dataList[start:tail]
	if e == "" {
		chats = append(chats, array.Map(list, func(m *ChatInfo, _ int) map[string]interface{} { return m.ToWeb(this) })...)
	}
	return chats
}

// 获取私聊列表
func (this *Model) GetPlayerChatList(filterUid string) []map[string]interface{} {
	ret := []map[string]interface{}{}
	this.Chats.RLock()
	defer this.Chats.RUnlock()
	for channel, chats := range this.Chats.Map {
		arr := strings.Split(channel, "_")
		if len(arr) < 3 {
			continue
		}
		uid1 := arr[1]
		uid2 := arr[2]
		if arr[0] == "2" {
			if filterUid != "" && uid1 != filterUid && uid2 != filterUid {
				// uid筛选
				continue
			}
			name1 := uid1
			user1 := this.GetTempPlayer(uid1)
			if user1 != nil {
				name1 = user1.Nickname
			}
			name2 := uid2
			user2 := this.GetTempPlayer(uid2)
			if user2 != nil {
				name2 = user2.Nickname
			}
			info := map[string]interface{}{
				"uid1":  uid1,
				"name1": name1,
				"uid2":  uid2,
				"name2": name2,
			}
			if len(chats) > 0 {
				lastChat := chats[len(chats)-1]
				sendName := name1
				if lastChat.Sender == uid2 {
					sendName = name2
				}
				info["lastChat"] = map[string]interface{}{
					"name": sendName,
					"msg":  lastChat.Content,
					"time": lastChat.Time,
				}
			}
			ret = append(ret, info)
		}
	}
	sort.Slice(ret, func(i, j int) bool {
		if ret[i]["lastChat"] == nil {
			return false
		}
		if ret[j]["lastChat"] == nil {
			return false
		}
		return ut.Int(ut.MapInterface(ret[i]["lastChat"])["time"]) > ut.Int(ut.MapInterface(ret[j]["lastChat"])["time"])
	})
	return ret
}

// 获取聊天信息pb 根据联盟id
func (this *Model) GetChatsPbByAlliUID(uid string, alliUid, lang string) ([]*pb.ChatInfo, map[string]int64) {
	this.Chats.RLock()
	defer this.Chats.RUnlock()
	chats := []*pb.ChatInfo{}
	canCloseTimeMap := map[string]int64{}
	alliChannel := "1_" + alliUid
	now := ut.Now()
	for channel, list := range this.Chats.Map {
		if channel == "0" || channel == "3" || channel == alliChannel { // 世界,系统,联盟
			chats = append(chats, array.Map(list, func(m *ChatInfo, _ int) *pb.ChatInfo { return m.ToPb() })...)
		} else if strings.Contains(channel, uid) { // 私聊
			chats = append(chats, array.Map(list, func(m *ChatInfo, _ int) *pb.ChatInfo { return m.ToPb() })...)
			// 只有是自己发起的私聊才有关闭时间
			if arr := strings.Split(channel, "_"); len(arr) == 4 && arr[3] == uid {
				canCloseTimeMap[channel] = ut.MaxInt64(0, ut.TIME_HOUR*12-(now-this.Chats.AddTimeMap[channel]))
			}
		} else if subArr := strings.Split(channel, "_"); len(subArr) > 1 {
			if subArr[0] == "4" && subArr[1] == alliUid {
				// 联盟副频
				chats = append(chats, array.Map(list, func(m *ChatInfo, _ int) *pb.ChatInfo { return m.ToPb() })...)
			} else if subArr[0] == "5" && subArr[1] == slg.LANG_CHANNEL_MAP[lang] {
				// 世界语言副频
				chats = append(chats, array.Map(list, func(m *ChatInfo, _ int) *pb.ChatInfo { return m.ToPb() })...)
			}
		}
	}
	return chats, canCloseTimeMap
}

// 发送聊天
func (this *Model) SendChat(uid, content string, emoji int32, portrayalInfo *pb.PortrayalInfo, equipInfo *pb.EquipInfo, channel string, sender string, bannedSurplusTime int64,
	hidePChatUID, senderNickname, senderHeadicon string, senderTitle int32, battleInfo *pb.ShareBattleInfo, replyUid string,
) *ChatInfo {
	this.Chats.Lock()
	defer this.Chats.Unlock()
	arr := this.Chats.Map[channel]
	if arr == nil {
		arr = []*ChatInfo{}
	}
	// 添加私聊发起时间
	if strings.Index(channel, "2_") == 0 && (len(arr) == 0 || arr[len(arr)-1].UID == hidePChatUID) {
		this.Chats.AddTimeMap[channel] = ut.Now()
	}
	// 更新db标记
	if _, ok := this.Chats.DbMap[channel]; !ok {
		this.Chats.DbMap[channel] = len(arr)
	}
	var replyInfo *pb.ReplyChatInfo = nil
	if replyUid != "" {
		if chat := array.Find(arr, func(m *ChatInfo) bool { return m.UID == replyUid }); chat != nil && chat.Content != "" {
			senderNickname := chat.SenderNickname
			if senderNickname == "" {
				senderNickname = this.GetTempPlayerNickname(chat.Sender)
			}
			replyInfo = &pb.ReplyChatInfo{Uid: chat.UID, Text: senderNickname + ": " + chat.Content}
		}
	}
	chat := &ChatInfo{
		UID:               uid,
		SID:               this.room.GetSID(),
		Channel:           channel,
		Sender:            sender,
		Content:           content,
		Emoji:             emoji,
		PortrayalInfo:     portrayalInfo,
		EquipInfo:         equipInfo,
		Time:              time.Now().UnixMilli(),
		BannedSurplusTime: bannedSurplusTime,
		SenderNickname:    senderNickname,
		SenderHeadicon:    senderHeadicon,
		SenderTitle:       senderTitle,
		BattleInfo:        battleInfo,
		ReplyInfo:         replyInfo,
	}
	arr = append(arr, chat)
	this.Chats.Map[channel] = arr
	return chat
}

// 获取联盟副频道
func (this *Model) GetAlliChatChannel(alliUid, uid string) string {
	return "4_" + alliUid + "_" + uid
}

// 根据玩家uid获取私聊频道
func (this *Model) GetPChatByUserIds(uid1, uid2 string) *ChatInfo {
	// 私聊channel结构为 "2_uid1_uid2_发起人uid" uid1和uid2升序排列
	channelPre := "2_" + uid1 + "_" + uid2
	if uid1 > uid2 {
		channelPre = "2_" + uid2 + "_" + uid1
	}
	// 分别尝试用两个玩家id作为发起人
	for _, uid := range []string{uid1, uid2} {
		channel := channelPre + "_" + uid
		if arr := this.Chats.Map[channel]; len(arr) > 0 {
			return arr[len(arr)-1]
		}
	}
	return nil
}
