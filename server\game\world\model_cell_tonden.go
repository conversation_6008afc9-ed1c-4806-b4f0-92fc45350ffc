package world

import (
	"time"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/effect"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

type CellTondenData struct {
	Map    map[int32]*TondenInfo
	RbTree *ut.RbTreeContainer[int32]
	deadlock.RWMutex
}

type TondenInfo struct {
	Auid    string `bson:"auid"`    // 军队uid
	EndTime int64  `bson:"endtime"` // 结束时间
}

func (this *Model) GetCellTondenTimeByIndex(index int32) int64 {
	this.CellTondenData.RLock()
	defer this.CellTondenData.RUnlock()
	if info := this.CellTondenData.Map[index]; info != nil {
		return info.EndTime
	}
	return 0
}

// 是否屯田区域
func (this *Model) IsCellTondenArea(index int32) bool {
	return this.GetCellTondenTimeByIndex(index) > 0
}

// 屯田ToPb
func (this *Model) ToCellTondenAreasPb() map[int32]*pb.CellTondenInfo {
	data := map[int32]*pb.CellTondenInfo{}
	now := time.Now().UnixMilli()
	this.CellTondenData.RLock()
	for k, v := range this.CellTondenData.Map {
		data[k] = &pb.CellTondenInfo{
			Auid: v.Auid,
			Time: int32(v.EndTime - now),
		}
	}
	this.CellTondenData.RUnlock()
	return data
}

// 添加屯田
func (this *Model) AddCellTonden(index int32, userId, auid string) {
	now := time.Now().UnixMilli()
	this.CellTondenData.RLock()
	_, ok := this.CellTondenData.Map[index]
	this.CellTondenData.RUnlock()
	if ok {
		// 屯田已存在
		return
	}

	area, army := this.GetAreaAndArmy(index, auid)
	if area == nil || army == nil {
		return
	}
	if area.CityId != 0 { // 已有城市
		return
	} else if this.CheckCellBTCitying(index) { // 修建中
		return
	} else if area.IsBattle() { // 战斗中
		return
	}

	// 根据地块难度获取屯田时间
	json := this.GetLandAttrConf(index, userId)
	if json == nil {
		return
	}
	var cd float64
	if tondenCd := this.GetPlayerPolicyEffectFloatByUid(userId, effect.CELL_TONDEN_CD); tondenCd > 0 {
		cd += tondenCd * 0.01
	}
	army.StartCellTonden()
	costTime := int64(ut.Round(ut.Float64(ut.Int64(json["tonden_time"])) * 1000 * (1 - cd)))
	endTime := now + costTime
	this.CellTondenData.Lock()
	this.CellTondenData.Map[index] = &TondenInfo{Auid: auid, EndTime: endTime}
	this.CellTondenData.RbTree.AddElement(int(endTime), index)
	this.CellTondenData.Unlock()
	this.PutNotifyQueue(constant.NQ_CELL_TONDEN, &pb.OnUpdateWorldInfoNotify{
		Data_79: &pb.CellTondenInfo{
			Index: index,
			Time:  int32(endTime - now),
			Auid:  auid,
		},
	})
}

// 删除屯田
func (this *Model) RemoveCellTonden(index int32) {
	area := this.GetArea(index)
	if area == nil {
		return
	}
	this.CellTondenData.Lock()
	if info, ok := this.CellTondenData.Map[index]; ok {
		this.DeleteCellTonden(index, info)
		if army := area.GetArmyByUid(info.Auid); army != nil {
			army.EndCellTonden()
		}
	}
	this.CellTondenData.Unlock()
}

// 删除指定玩家所有屯田
func (this *Model) RemovePlayerAllCellTonden(uid string) {
	this.CellTondenData.Lock()
	for index, info := range this.CellTondenData.Map {
		area := this.GetArea(index)
		if area == nil {
			continue
		}
		if army := area.GetArmyByUid(info.Auid); army == nil || army.Owner == uid {
			this.DeleteCellTonden(index, info)
		}
	}
	this.CellTondenData.Unlock()
}

// 刷新屯田信息
func (this *Model) CheckUpdateAreaCellTonden() {
	now := time.Now().UnixMilli()
	// 遍历之前先检测最快的屯田是否结束
	this.CellTondenData.RLock()
	if len(this.CellTondenData.Map) == 0 {
		this.CellTondenData.RUnlock()
		return
	}
	minTreeNode := this.CellTondenData.RbTree.FindMinElements()
	if minTreeNode == nil {
		this.CellTondenData.RUnlock()
		return
	} else {
		if ut.Int64(minTreeNode.Key) > now {
			// 结束时间最快的屯田都没结束
			this.CellTondenData.RUnlock()
			return
		}
	}
	this.CellTondenData.RUnlock()

	finishMap := map[int32]string{}
	this.CellTondenData.Lock()
	for m := this.CellTondenData.RbTree.FindMinElements(); m != nil; m = this.CellTondenData.RbTree.FindMinElements() {
		endTime := ut.Int64(m.Key)
		indexList := m.Value.([]int32)
		if endTime > now {
			// 结束时间最快的屯田都没结束
			break
		}
		for i := len(indexList) - 1; i >= 0; i-- {
			index := indexList[i]
			info := this.CellTondenData.Map[index]
			delete(this.CellTondenData.Map, index)
			if info == nil {
				log.Warning("CheckUpdateAreaCellTonden info nil index: %v", index)
				continue
			}
			finishMap[index] = info.Auid
		}
		this.CellTondenData.RbTree.Remove(m.Key)
	}
	this.CellTondenData.Unlock()

	if len(finishMap) > 0 {
		for index, auid := range finishMap {
			// 屯田完成 发放奖励
			treasureIds := this.CellTondenComplete(index, auid)
			// 通知
			this.PutNotifyQueue(constant.NQ_CELL_TONDEN, &pb.OnUpdateWorldInfoNotify{
				Data_79: &pb.CellTondenInfo{Index: index, Auid: auid, TreasureIds: treasureIds},
			})
		}
	}
}

// 删除屯田 外部加锁
func (this *Model) DeleteCellTonden(index int32, info *TondenInfo) {
	delete(this.CellTondenData.Map, index)
	this.CellTondenData.RbTree.RemoveElement(int(info.EndTime), index)
	// 通知
	this.PutNotifyQueue(constant.NQ_CELL_TONDEN, &pb.OnUpdateWorldInfoNotify{
		Data_79: &pb.CellTondenInfo{Index: index, Auid: info.Auid},
	})
}

func (this *CellTondenData) GetCellTondenClone() map[int32]*TondenInfo {
	rst := map[int32]*TondenInfo{}
	this.RLock()
	for k, v := range this.Map {
		rst[k] = v
	}
	this.RUnlock()
	return rst
}
