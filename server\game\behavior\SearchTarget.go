package behavior

import (
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/eeffect"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 寻找最近目标
type SearchTarget struct {
	BaseAction
}

func (this *SearchTarget) OnTick(dt int32) Status {
	if ut.Bool(this.GetTreeBlackboardData("isSearchTarget")) && this.target.GetAttackTarget() != nil {
		return FAILURE
	}
	this.SetTreeBlackboardData("isSearchTarget", true) // 记录是否已经搜索过了
	return ut.If(this.getTarget(), SUCCESS, FAILURE)
}

func (this *SearchTarget) getTarget() bool {
	targets := this.target.GetCanAttackTargets()
	if len(targets) > 0 {
		ok := false
		var it g.CanAttackTargetInfo
		if this.target.GetEquipEffectByType(eeffect.SILVER_SNAKE_WHIP) != nil {
			ok, it = this.randomTarget()
		} else if this.target.IsHasBuff(bufftype.RAGE) {
			ok, it = this.randomMinDisTargetInAR()
		}
		if !ok {
			it = targets[0]
		}
		if it.AttackTargetPoint != nil {
			targetPoint := it.AttackTargetPoint
			movePoint := this.target.GetPoint()
			pathLen := len(it.Paths)
			if pathLen > 0 {
				movePoint = it.Paths[pathLen-1]
			}
			attackTargetPoint := it.Target.GetPoint()
			enemyCount := this.GetCtrl().GetFighterCountByPoint(attackTargetPoint)
			if it.Target.GetPawnType() == constant.PAWN_TYPE_BUILD || enemyCount == 1 {
				// 该点位只有一个敌人时 设置锁定目标点
				this.GetCtrl().SetLockMovePointFighter(targetPoint, this.target, it.MoveWeight, movePoint)
			}
		}
		this.target.ChangeAttackTarget(it.Target)
		return true
	}
	this.target.ChangeAttackTarget(nil)
	return false
}

func (this *SearchTarget) randomTarget() (ok bool, it g.CanAttackTargetInfo) {
	allTargets := array.Filter(this.target.GetCanAttackTargets(), func(m g.CanAttackTargetInfo, _ int) bool { return this.target.CheckInMyAttackRange(m.Target) })
	if l := len(allTargets); l > 0 {
		return true, allTargets[this.GetCtrl().GetRandom().Get(0, l-1)]
	}
	ok = false
	return
}

// 随机最近的目标
func (this *SearchTarget) randomMinDisTargetInAR() (ok bool, it g.CanAttackTargetInfo) {
	allTargets := array.Filter(this.target.GetCanAttackTargets(), func(m g.CanAttackTargetInfo, _ int) bool { return this.target.CheckInMyAttackRange(m.Target) })
	arr := []g.CanAttackTargetInfo{}
	minDis := int32(100000)
	for _, m := range allTargets {
		dis := this.target.GetMinDis(m.Target)
		if dis < minDis {
			minDis = dis
			arr = []g.CanAttackTargetInfo{m}
		} else if dis == minDis {
			arr = append(arr, m)
		}
	}
	if l := len(arr); l > 0 {
		return true, arr[this.GetCtrl().GetRandom().Get(0, l-1)]
	}
	ok = false
	return
}
