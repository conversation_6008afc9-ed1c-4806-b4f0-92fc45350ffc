package lobby

import (
	"context"
	"time"

	slg "slgsrv/server/common"
	mgo "slgsrv/utils/mgodb"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
)

// 玩家道具记录数据库
var (
	RecordDatabase = "slg_record"
	RecordClient   *mongo.Client

	itemRecordDb = &Mongodb{slg.DB_COLLECTION_NAME_USER_ITEM_RECORD}
)

func InitRecordDB(url, dbname string) {
	RecordClient = mgo.CreateClient(url)
	RecordDatabase = dbname
}

func GetRecordCol(table string) *mongo.Collection {
	return RecordClient.Database(RecordDatabase).Collection(table)
}

// 玩家道具记录
type ItemRecord struct {
	Uid    string `json:"uid" bson:"uid"`       // 玩家uid
	Extend string `json:"extend" bson:"extend"` // 备注

	Time     int64 `json:"time" bson:"time"`           // 时间
	ItemType int32 `json:"item_type" bson:"item_type"` // 道具类型
	Id       int32 `json:"id" bson:"id"`               // 道具id
	Val      int32 `json:"val" bson:"val"`             // 改变数量
	After    int32 `json:"after" bson:"after"`         // 改变后数量
	Reason   int32 `json:"reason" bson:"reason"`       // 原因
}

// 添加道具变动记录
func AddItemRecord(uid string, tp, id, val, after, reason int32, extend string) {
	go goAddItemRecord(uid, tp, id, val, after, reason, extend)
}

func goAddItemRecord(uid string, tp, id, val, after, reason int32, extend string) {
	if val == 0 {
		return
	}
	defer func() {
		if err := recover(); err != nil {
			log.Error("AddItemRecord catch error: %v", err)
		}
	}()

	// 按日期命名的集合
	collectionName := getItemRecordColName(time.Now())
	record := &ItemRecord{
		Uid:      uid,
		ItemType: tp,
		Id:       id,
		Val:      val,
		After:    after,
		Time:     time.Now().UnixMilli(),
		Reason:   reason,
		Extend:   extend,
	}
	_, e := GetRecordCol(collectionName).InsertOne(context.TODO(), record)
	if e != nil {
		log.Error("AddItemRecord error: %v", e)
	}
}

// 获取玩家道具变动日志
func GetItemRecordList(sTime, eTime int64, uid string, tp, id, size, skip int) (results []ItemRecord, err error) {
	results = []ItemRecord{}
	// 时间戳转换
	startDate := time.UnixMilli(sTime)
	endDate := time.UnixMilli(eTime)

	// 遍历日期范围内的每一天
	for date := endDate; !date.Before(startDate); date = date.AddDate(0, 0, -1) {
		// 格式化为集合名
		collectionName := getItemRecordColName(date)
		collection := GetRecordCol(collectionName)
		// 查询条件
		filter := bson.M{
			"uid":       uid,
			"item_type": tp,
			"$and": bson.A{
				bson.M{"time": bson.M{"$gte": sTime}},
				bson.M{"time": bson.M{"$lte": eTime}},
			},
		}
		if id > 0 {
			filter["id"] = id
		}
		opt := options.Find()
		opt.Sort = bson.M{"time": -1}

		// 执行查询
		cursor, err := collection.Find(context.Background(), filter, opt)
		if err != nil {
			if err == mongo.ErrNilDocument {
				continue // 如果集合不存在，则跳过该日期
			}
			return nil, err
		}

		// 解析查询结果
		var logs []ItemRecord
		if err := cursor.All(context.Background(), &logs); err != nil {
			return nil, err
		}

		results = append(results, logs...)
	}

	// 返回分页后的结果
	start := skip
	end := skip + size
	if start > len(results) {
		return []ItemRecord{}, nil
	}
	if end > len(results) {
		end = len(results)
	}

	return results[start:end], nil
}

// 根据日期获取道具日志集合名
func getItemRecordColName(date time.Time) string {
	return slg.DB_COLLECTION_NAME_USER_ITEM_RECORD + "_" + date.Format("20060102")
}
