package lobby

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	lc "slgsrv/server/lobby/common"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/gate"
)

// 画像
func (this *Lobby) InitHDPortrayal() {
	this.GetServer().RegisterGO("HD_Pointsets", this.pointsets)                     // 点将
	this.GetServer().RegisterGO("HD_PortrayalComp", this.portrayalComp)             // 合成
	this.GetServer().RegisterGO("HD_CompPortrayalDebris", this.compPortrayalDebris) // 合成指定残卷
	this.GetServer().RegisterGO("HD_SavePortrayal", this.savePortrayal)             // 保存画像
	this.GetServer().RegisterGO("HD_RestorePortrayal", this.restorePortrayal)       // 还原画像
	this.GetServer().RegisterGO("HD_BuyPortrayalSlot", this.buyPortrayalSlot)       // 购买画像保存栏位
}

// 点将
func (this *Lobby) pointsets(session gate.Session, msg *pb.LOBBY_HD_POINTSETS_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	count := msg.GetCount()
	isUseGold := count == 50 // 表示使用金币
	var warTokenCost, goldCost int32
	if isUseGold {
		count = 5
		goldCost = lc.POINTSETS_ONE_GOLD_COST
		// 扣除金币
		if user.ChangeGold(-goldCost, constant.GOLD_CHANGE_POINTSETS_COST) == -1 {
			return nil, ecode.GOLD_NOT_ENOUGH.String()
		}
	} else {
		warTokenCost = lc.POINTSETS_ONE_COST * count
		// 扣除兵符
		if user.ChangeWarToken(-warTokenCost, constant.GOLD_CHANGE_POINTSETS_COST) == -1 {
			return nil, ecode.WAR_TOKEN_NOT_ENOUGH.String()
		}
	}
	// 开始点将
	ids, counts := user.Pointsets(count)
	user.FlagUpdateDB()
	for i, count := range counts {
		if count > 3 {
			// 多倍公告
			id := ids[i]
			index := i
			if msg.GetSkip() {
				index = -1
			}
			this.NotifyMulPortrayal(user.Nickname, count, id, index)
		}
	}
	// 上报
	ta.Track(user.SID, user.UID, user.DistinctId, 0, "ta_pointsets", map[string]interface{}{
		"ids":            ids,
		"counts":         counts,
		"war_token_cost": warTokenCost,
		"gold_cost":      goldCost,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_POINTSETS_S2C{
		Ids:        ids,
		Portrayals: user.ToPortrayalsPb(),
		WarToken:   user.WarToken,
		Gold:       user.Gold,
		Counts:     counts,
	})
}

// 合成
func (this *Lobby) portrayalComp(session gate.Session, msg *pb.LOBBY_HD_PORTRAYALCOMP_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	info := user.GetPortrayalInfo(id)
	if info == nil || info.Debris < lc.PORTRAYAL_COMP_NEED_COUNT {
		return nil, ecode.DEBRIS_NOT_ENOUGH.String()
	}
	// 扣除碎片
	info.Debris -= lc.PORTRAYAL_COMP_NEED_COUNT
	// 是否重绘
	if info.IsUnlock() {
		info.RecompCount += 1
	}
	// 直接随机
	isChosenOneOld, isChosenOne := info.RandomInfo()
	// 标记更新
	user.FlagUpdateDB()
	// 这里需要通知游戏服 画像改变
	if info.RecompCount > 0 {
		this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_CHANGE_HERO_ATTR, user.UID, id, info.Attrs)
	}
	// 公告
	if !isChosenOneOld && isChosenOne {
		this.NotifyCompOpcPortrayal(user.Nickname, slg.RECOMPOSE_HERO_NOTICE_ID, info.ID)
	}
	// 上报
	ta.Track(user.SID, user.UID, user.DistinctId, 0, "ta_compPortrayal", map[string]interface{}{
		"id":          id,
		"isChosenOne": isChosenOne,
		"recompCount": info.RecompCount,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_PORTRAYALCOMP_S2C{Info: info.ToPb()})
}

// 合成指定残卷
func (this *Lobby) compPortrayalDebris(session gate.Session, msg *pb.LOBBY_HD_COMPPORTRAYALDEBRIS_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id, countMap := msg.GetId(), msg.GetIdMap()
	if countMap[id] > 0 {
		return nil, ecode.UNKNOWN.String()
	}
	json := config.GetJsonData("portrayalBase", id)
	if json == nil {
		return nil, ecode.HERO_NOT_EXIST.String()
	}
	infos := []*g.PortrayalInfo{}
	var costTotal int32 // 合成消耗的残卷数量
	for k, v := range countMap {
		if info := user.GetPortrayalInfo(k); info != nil && info.Debris >= v {
			infos = append(infos, info)
			costTotal += v
		} else {
			return nil, ecode.DEBRIS_NOT_ENOUGH.String()
		}
	}
	if costTotal < 3 {
		return nil, ecode.DEBRIS_NOT_ENOUGH.String()
	}
	composeCount := costTotal / 3 // 合成得到的残卷数量
	costTotal = composeCount * 3  // 实际消耗的残卷数量
	// 添加碎片
	user.AddPortrayalDebrisCount(id, composeCount)
	// 扣除碎片
	var curCostCount int32 // 当前消耗的残卷数量
	for _, m := range infos {
		count := countMap[m.ID]
		count = ut.MinInt32(costTotal-curCostCount, count)
		if count == 0 {
			break
		}
		if count != countMap[m.ID] {
			// 不同则设置实际消耗的数量方便上报
			countMap[m.ID] = count
		}
		m.Debris -= count
		// 添加残卷日志记录
		AddItemRecord(user.UID, ctype.HERO_DEBRIS, id, -count, m.Debris, 0, "")
		if m.Debris <= 0 && !m.IsUnlock() {
			user.RemovePortrayal(m.ID)
		}
		curCostCount += count
		if curCostCount >= costTotal {
			break
		}
	}
	// 标记更新
	user.FlagUpdateDB()
	// 上报
	ta.Track(user.SID, user.UID, user.DistinctId, 0, "ta_compPortrayalDebris", map[string]interface{}{
		"id":  id,
		"ids": countMap,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_COMPPORTRAYALDEBRIS_S2C{
		Portrayals: user.ToPortrayalsPb(),
	})
}

// 保存画像
func (this *Lobby) savePortrayal(session gate.Session, msg *pb.LOBBY_HD_SAVEPORTRAYAL_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id, index := msg.GetId(), ut.Int(msg.GetSlotIndex())
	info := user.GetPortrayalInfo(id)
	if info == nil {
		return nil, ecode.HERO_NOT_EXIST.String()
	} else if !info.IsUnlock() {
		return nil, ecode.HERO_NOT_EXIST.String()
	}
	slotInfo := info.GetSlotByIndex(index)
	if slotInfo == nil { // 保存槽位不存在
		return nil, ecode.PORTRAYAL_SLOT_NOT_EXIST.String()
	}

	// 获取历史记录属性
	info.StoreSlotsLock.RLock()
	defer info.StoreSlotsLock.RUnlock()
	history := info.History
	if len(history) == 0 {
		return nil, ecode.PORTRAYAL_HISTORY_NOT_EXIST.String()
	}
	historyIndex := ut.Int(msg.GetHistoryIndex())
	if historyIndex < 0 || historyIndex >= len(history) {
		return nil, ecode.PORTRAYAL_HISTORY_NOT_EXIST.String()
	}

	historyInfo := history[historyIndex]
	if info.IsAttrsChosenOne(slotInfo.Attrs) && !info.IsAttrsChosenOne(historyInfo.Attrs) {
		// 不能用非天选属性覆盖天选属性
		return nil, ecode.CANT_RESTORE_CHOOSEN_ONE.String()
	}

	tp := msg.GetCostType()
	var needWarToken, needGold int32
	if tp == 0 { // 表示兵符
		needWarToken = lc.RESTORE_PORTRAYAL_WAR_TOKEN_COST
		if user.ChangeWarToken(-needWarToken, constant.GOLD_CHANGE_SAVE_PORTRAYAL_COST) == -1 {
			return nil, ecode.WAR_TOKEN_NOT_ENOUGH.String()
		}
	} else if tp == 1 { // 表示金币
		needGold = lc.RESTORE_PORTRAYAL_GOLD_COST
		if user.ChangeGold(-needGold, constant.GOLD_CHANGE_SAVE_PORTRAYAL_COST) == -1 {
			return nil, ecode.GOLD_NOT_ENOUGH.String()
		}
	}
	storeAttr := historyInfo.Attrs
	// 保存属性
	slotInfo.StoreAttr(storeAttr)
	// 标记更新
	user.FlagUpdateDB()
	return pb.ProtoMarshal(&pb.LOBBY_HD_SAVEPORTRAYAL_S2C{
		Info:     info.ToPb(),
		WarToken: user.WarToken,
		Gold:     user.Gold,
	})
}

// 还原画像
func (this *Lobby) restorePortrayal(session gate.Session, msg *pb.LOBBY_HD_RESTOREPORTRAYAL_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id, index := msg.GetId(), ut.Int(msg.GetSlotIndex())
	info := user.GetPortrayalInfo(id)
	if info == nil {
		return nil, ecode.HERO_NOT_EXIST.String()
	} else if !info.IsUnlock() {
		return nil, ecode.HERO_NOT_EXIST.String()
	}
	slotInfo := info.GetSlotByIndex(index)
	if slotInfo == nil { // 保存槽位不存在
		return nil, ecode.PORTRAYAL_SLOT_NOT_EXIST.String()
	} else if len(slotInfo.Attrs) == 0 { // 未保存数据
		return nil, ecode.NOT_CAN_RESTORE_PORTRAYAL.String()
	} else if info.IsChosenOne() && !info.IsAttrsChosenOne(slotInfo.Attrs) {
		// 不能用非天选属性覆盖天选属性
		return nil, ecode.CANT_RESTORE_CHOOSEN_ONE.String()
	}

	// 直接还原
	info.RestoreAttr(slotInfo)
	// 标记更新
	user.FlagUpdateDB()
	// 这里需要通知游戏服 画像改变
	this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_CHANGE_HERO_ATTR, user.UID, id, info.Attrs)

	return pb.ProtoMarshal(&pb.LOBBY_HD_RESTOREPORTRAYAL_S2C{
		Info: info.ToPb(),
	})
}

// 购买画像保存栏位
func (this *Lobby) buyPortrayalSlot(session gate.Session, msg *pb.LOBBY_HD_RESTOREPORTRAYAL_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	info := user.GetPortrayalInfo(id)
	if info == nil {
		return nil, ecode.HERO_NOT_EXIST.String()
	} else if info.GetSlotByIndex(lc.PORTRAYAL_SAVE_SLOT_MAX-1) != nil { // 画像保存槽位上限
		return nil, ecode.PORTRAYAL_SLOT_LIMIT.String()
	}

	// 扣除元宝
	if user.ChangeGold(-lc.PORTRAYAL_SAVE_SLOT_PRICE, constant.GOLD_CHANGE_BUY_PORTRAYAL_SLOT) == -1 {
		return nil, ecode.INGOT_NOT_ENOUGH.String()
	}
	// 添加槽位
	info.AddSaveSlot()

	return pb.ProtoMarshal(&pb.LOBBY_HD_BUYPORTRAYALSLOT_S2C{
		Info:  info.ToPb(),
		Ingot: user.Ingot,
	})
}
