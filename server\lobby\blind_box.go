package lobby

import (
	"math/rand"
	"strings"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/game/common/config"
	ut "slgsrv/utils"
)

// 皮肤盲盒组
type BlindSkinBoxGroup struct {
	NormalList []int32 // 普通皮肤id列表
	StartTime  int64   // 限定开始时间
	EndTime    int64   // 限定结束时间

	Id     int32 // 组id
	Price  int32 // 价格
	RareId int32 // 稀有皮肤id
}

// 皮肤盲盒模块
type BlindSkinBoxModel struct {
	GroupMap   *ut.MapLock[int32, *BlindSkinBoxGroup] // 盲盒组map
	InitFinish bool                                   // 是否初始化完成
}

var (
	BlindBoxModel = &BlindSkinBoxModel{}

	// 稀有皮肤概率
	BLIND_BOX_RARE_ODDS = 1
	// 盲盒ID起始
	BLIND_BOX_COND_ID_START int32 = 100

	BLIND_BOX_NORMAL_SKIN_TYPE = 1 // 盲盒普通皮肤类型
	BLIND_BOX_RARE_SKIN_TYPE   = 2 // 盲盒稀有皮肤类型
)

// 初始化盲盒模块
func InitBlindBoxModel() {
	BlindBoxModel.GroupMap = ut.NewMapLock[int32, *BlindSkinBoxGroup]()
	skinCfg := config.GetJson("pawnSkin")
	timeLayout := "2006-01-02-15-04"
	now := time.Now().UnixMilli()
	for _, cfg := range skinCfg.Datas {
		id := ut.Int32(cfg["id"])
		boxId := ut.Int32(cfg["cond"])
		if boxId < BLIND_BOX_COND_ID_START {
			// 盲盒皮肤通过cond字段区分
			continue
		}

		// 获取限定时间
		serverArea := ut.If(slg.IsDebug(), "test", slg.SERVER_AREA)
		limitTimeArr := strings.Split(ut.String(cfg["limit_time_"+serverArea]), "|")
		if len(limitTimeArr) < 2 {
			continue
		}
		loc, _ := time.LoadLocation("Local")
		startTimeStr := limitTimeArr[0]
		endTimeStr := limitTimeArr[1]
		startTime, err := time.ParseInLocation(timeLayout, startTimeStr, loc)
		if err != nil {
			continue
		}
		endTime, err := time.ParseInLocation(timeLayout, endTimeStr, loc)
		if err != nil {
			continue
		}
		startTimeStp := startTime.UnixMilli()
		endTimeStp := endTime.UnixMilli()
		if now > endTimeStp {
			// 已过期则不加载
			continue
		}

		// 放入缓存
		boxGroup := BlindBoxModel.GroupMap.Get(boxId)
		if boxGroup == nil {
			boxGroup = &BlindSkinBoxGroup{
				Id:         boxId,
				NormalList: []int32{},
				StartTime:  startTimeStp,
				EndTime:    endTimeStp,
			}
			BlindBoxModel.GroupMap.Set(boxId, boxGroup)
		}
		price := ut.Int32(cfg["ingot"])
		if price > boxGroup.Price {
			boxGroup.Price = price
		}
		skinType := ut.Int(cfg["value"])
		if skinType == BLIND_BOX_RARE_SKIN_TYPE {
			boxGroup.RareId = id
		} else {
			boxGroup.NormalList = append(boxGroup.NormalList, id)
		}
	}
	BlindBoxModel.InitFinish = true
}

// 获取盲盒组
func (this *BlindSkinBoxModel) GetBindBox(id int32) (box *BlindSkinBoxGroup, err string) {
	if !this.InitFinish {
		return nil, ecode.ACTIVITY_NOT_EXIST.String()
	}
	box = this.GroupMap.Get(id)
	if box == nil {
		return nil, ecode.ACTIVITY_NOT_EXIST.String()
	}
	now := time.Now().UnixMilli()
	// 判断是否在可购买时间内
	if now < box.StartTime || now > box.EndTime {
		return nil, ecode.ACTIVITY_NOT_EXIST.String()
	}
	if box.RareId == 0 || box.Price <= 0 || box.NormalList == nil || len(box.NormalList) == 0 {
		err = ecode.ACTIVITY_NOT_EXIST.String()
	}
	return
}

// 开启盲盒
func (this *BlindSkinBoxGroup) OpenBlindBox() (skinId int32, isRare bool, err string) {
	normalLen := len(this.NormalList)
	if normalLen <= 0 {
		return 0, false, ecode.ACTIVITY_NOT_EXIST.String()
	} else if ut.Chance(BLIND_BOX_RARE_ODDS) { // 先随机是否稀有
		skinId = this.RareId
		isRare = true
		return
	}
	// 普通皮肤每个概率相同
	index := rand.Intn(normalLen)
	skinId = this.NormalList[index]
	return
}

// 是否稀有皮肤
func IsRareSkin(id int32) bool {
	cfg := config.GetJsonData("pawnSkin", id)
	if cfg == nil {
		return false
	}
	return ut.Int32(cfg["cond"]) >= BLIND_BOX_COND_ID_START && ut.Int(cfg["value"]) == BLIND_BOX_RARE_SKIN_TYPE
}
