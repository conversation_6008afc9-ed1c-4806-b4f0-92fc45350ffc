[{"id": 100001, "type": 0, "sub_type": 1, "cond": "4,2002,1", "prev_id": -1, "next_id": 100002, "reward": "2,0,150|3,0,150", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2002", "tip": "", "help": "", "sort": 1}, {"id": 100002, "type": 0, "sub_type": 1, "cond": "4,2001,2", "prev_id": 100001, "next_id": 100003, "reward": "1,0,100", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2001|2", "tip": "", "help": "", "sort": 1}, {"id": 100003, "type": 0, "sub_type": 1, "cond": "1018,0,3", "prev_id": 100002, "next_id": 100004, "reward": "2,0,50|3,0,50", "recreate_reward": "", "show_progress": "", "desc": "taskText.1012", "params": 3, "tip": "", "help": "", "sort": 1}, {"id": 100004, "type": 0, "sub_type": 1, "cond": "4,2003,1", "prev_id": 100003, "next_id": 100005, "reward": "5,0,5", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2003", "tip": "", "help": "", "sort": 1}, {"id": 100005, "type": 0, "sub_type": 1, "cond": "4,2016,1", "prev_id": 100004, "next_id": 100007, "reward": "5,0,5", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2016", "tip": "", "help": "", "sort": 1}, {"id": 100007, "type": 0, "sub_type": 1, "cond": "4,2004,2", "prev_id": 100005, "next_id": 100008, "reward": "1,0,150", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2004|2", "tip": "", "help": "", "sort": 1}, {"id": 100008, "type": 0, "sub_type": 1, "cond": "1030,3,2", "prev_id": 100007, "next_id": 100009, "reward": "5,0,10", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1028", "params": "2|ui.pawn_type_3", "tip": "", "help": "", "sort": 1}, {"id": 100009, "type": 0, "sub_type": 1, "cond": "4,2008,1", "prev_id": 100008, "next_id": 100010, "reward": "2,0,150|3,0,150", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2008", "tip": "", "help": "", "sort": 1}, {"id": 100010, "type": 0, "sub_type": 1, "cond": "1039,0,1", "prev_id": 100009, "next_id": 100011, "reward": "9,0,1", "recreate_reward": "", "show_progress": "", "desc": "taskText.1037", "params": 1, "tip": "", "help": "", "sort": 1}, {"id": 100011, "type": 0, "sub_type": 1, "cond": "100001,0,1", "prev_id": 100010, "next_id": 100012, "reward": "5,0,10", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1043", "params": "", "tip": "", "help": "", "sort": 1}, {"id": 100012, "type": 0, "sub_type": 1, "cond": "100002,0,1", "prev_id": 100011, "next_id": 100013, "reward": "5,0,10", "recreate_reward": "", "show_progress": "", "desc": "taskText.1044", "params": "", "tip": "", "help": "taskText.10007", "sort": 1}, {"id": 100013, "type": 0, "sub_type": 1, "cond": "4,2008,3", "prev_id": 100012, "next_id": 100014, "reward": "9,0,2", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2008|3", "tip": "", "help": "", "sort": 1}, {"id": 100014, "type": 0, "sub_type": 1, "cond": "1022,3,2", "prev_id": 100013, "next_id": 100015, "reward": "2,0,150|3,0,150", "recreate_reward": "", "show_progress": "", "desc": "taskText.1016", "params": "1|ui.ceri_type_name_3", "tip": "", "help": "", "sort": 1}, {"id": 100015, "type": 0, "sub_type": 1, "cond": "1039,0,2", "prev_id": 100014, "next_id": 100016, "reward": "9,0,1", "recreate_reward": "", "show_progress": "", "desc": "taskText.1037", "params": 2, "tip": "", "help": "", "sort": 1}, {"id": 100016, "type": 0, "sub_type": 1, "cond": "3,1001,1", "prev_id": 100015, "next_id": 100017, "reward": "1,0,175|2,0,175|3,0,175", "recreate_reward": "", "show_progress": "", "desc": "taskText.1045", "params": "", "tip": "taskText.10004", "help": "", "sort": 1}, {"id": 100017, "type": 0, "sub_type": 1, "cond": "4,2001,5", "prev_id": 100016, "next_id": 100018, "reward": "5,0,10", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2001|5", "tip": "", "help": "", "sort": 1}, {"id": 100018, "type": 0, "sub_type": 1, "cond": "1035,0,1", "prev_id": 100017, "next_id": 100019, "reward": "5,0,10", "recreate_reward": "", "show_progress": "", "desc": "taskText.1034", "params": 1, "tip": "", "help": "", "sort": 1}, {"id": 100019, "type": 0, "sub_type": 1, "cond": "1041,1,1", "prev_id": 100018, "next_id": "", "reward": "5,0,10", "recreate_reward": "", "show_progress": "", "desc": "taskText.1049", "params": 1, "tip": "", "help": "", "sort": 1}, {"id": 100101, "type": 0, "sub_type": 1, "cond": "12,1,3", "prev_id": -1, "next_id": 100102, "reward": "5,0,5", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1010", "params": 3, "tip": "taskText.10001", "help": "", "sort": 2}, {"id": 100102, "type": 0, "sub_type": 1, "cond": "3,1002,1", "prev_id": 100101, "next_id": 100103, "reward": "1,0,75|2,0,75|3,0,75", "recreate_reward": "", "show_progress": "", "desc": "taskText.1023", "params": "", "tip": "", "help": "", "sort": 0}, {"id": 100103, "type": 0, "sub_type": 1, "cond": "1001,1,8", "prev_id": 100102, "next_id": 100104, "reward": "1,0,125|2,0,125|3,0,125", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1007", "params": "8|ui.land_301", "tip": "", "help": "", "sort": 2}, {"id": 100104, "type": 0, "sub_type": 1, "cond": "1005,3,1", "prev_id": 100103, "next_id": 100105, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1007", "params": "1|ui.land_base_3", "tip": "taskText.10005", "help": "", "sort": 2}, {"id": 100105, "type": 0, "sub_type": 1, "cond": "1005,5,2", "prev_id": 100104, "next_id": 100106, "reward": "1,0,150|2,0,150|3,0,150", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1007", "params": "2|ui.land_base_5", "tip": "", "help": "", "sort": 2}, {"id": 100106, "type": 0, "sub_type": 1, "cond": "1001,1,12", "prev_id": 100105, "next_id": 100107, "reward": "1,0,125|2,0,125|3,0,125", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1007", "params": "12|ui.land_301", "tip": "", "help": "", "sort": 2}, {"id": 100107, "type": 0, "sub_type": 1, "cond": "1001,2,5", "prev_id": 100106, "next_id": 100108, "reward": "1,0,175|2,0,175|3,0,175", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "5|2", "tip": "", "help": "", "sort": 2}, {"id": 100108, "type": 0, "sub_type": 1, "cond": "1001,3,1", "prev_id": 100107, "next_id": "", "reward": "1,0,200|2,0,200|3,0,200", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "1|3", "tip": "", "help": "", "sort": 2}, {"id": 100201, "type": 0, "sub_type": 2, "cond": "3,1003,1", "prev_id": -1, "next_id": 100202, "reward": "5,0,15", "recreate_reward": "", "show_progress": "", "desc": "taskText.1046", "params": "", "tip": "", "help": "", "sort": 0}, {"id": 100202, "type": 0, "sub_type": 2, "cond": "3,1004,1", "prev_id": 100201, "next_id": 100203, "reward": "5,0,15", "recreate_reward": "", "show_progress": "", "desc": "taskText.1025", "params": "", "tip": "", "help": "", "sort": 3}, {"id": 100203, "type": 0, "sub_type": 2, "cond": "3,1005,1", "prev_id": 100202, "next_id": "", "reward": "23,0,5", "recreate_reward": "", "show_progress": "", "desc": "taskText.1047", "params": "", "tip": "", "help": "", "sort": 3}, {"id": 100301, "type": 1, "sub_type": 3, "cond": "3,1006,1", "prev_id": -1, "next_id": "", "reward": "1,0,50", "recreate_reward": "", "show_progress": "", "desc": "taskText.1048", "params": 1, "tip": "", "help": "", "sort": 0}, {"id": 10102201, "type": 1, "sub_type": 1, "cond": "1022,2,1", "prev_id": 0, "next_id": "", "reward": "1,0,30|2,0,30|3,0,30", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1016", "params": "1|ui.ceri_type_name_2", "tip": "", "help": "", "sort": 1}, {"id": 10101701, "type": 1, "sub_type": 1, "cond": "1017,0,2", "prev_id": 10102201, "next_id": "", "reward": "1,0,50|2,0,50|3,0,50", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1011", "params": 2, "tip": "", "help": "", "sort": 1}, {"id": 10000402, "type": 1, "sub_type": 1, "cond": "4,2002,1", "prev_id": 10101701, "next_id": "", "reward": "1,0,75|2,0,75|3,0,75", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2002", "tip": "", "help": "", "sort": 1}, {"id": 10000403, "type": 1, "sub_type": 1, "cond": "4,2003,1", "prev_id": 10000402, "next_id": "", "reward": "1,0,75|2,0,75|3,0,75", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2003", "tip": "", "help": "", "sort": 1}, {"id": 10000415, "type": 1, "sub_type": 1, "cond": "4,2016,1", "prev_id": 10000403, "next_id": "", "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2016", "tip": "", "help": "", "sort": 1}, {"id": 10000404, "type": 1, "sub_type": 1, "cond": "4,2001,2", "prev_id": 10000415, "next_id": "", "reward": "1,0,125|2,0,125|3,0,125", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2001|2", "tip": "", "help": "", "sort": 1}, {"id": 10000405, "type": 1, "sub_type": 1, "cond": "4,2004,2", "prev_id": 10000404, "next_id": "", "reward": "1,0,125|2,0,125|3,0,125", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2004|2", "tip": "", "help": "", "sort": 1}, {"id": 10103001, "type": 1, "sub_type": 1, "cond": "1030,3,3", "prev_id": 10000405, "next_id": "", "reward": "1,0,150|2,0,150|3,0,150", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1028", "params": "3|ui.pawn_type_3", "tip": "", "help": "", "sort": 1}, {"id": 10000416, "type": 1, "sub_type": 1, "cond": "4,2001,3", "prev_id": 10103001, "next_id": "", "reward": "1,0,175|2,0,175|3,0,175", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2001|3", "tip": "", "help": "", "sort": 1}, {"id": 10102301, "type": 1, "sub_type": 1, "cond": "1022,1,1", "prev_id": 10000416, "next_id": "", "reward": "1,0,200|2,0,200|3,0,200", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1039", "params": 1, "tip": "", "help": "", "sort": 1}, {"id": 10000407, "type": 1, "sub_type": 1, "cond": "4,2008,1", "prev_id": 10102301, "next_id": "", "reward": "9,0,2", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2008", "tip": "", "help": "", "sort": 1}, {"id": 10102102, "type": 1, "sub_type": 1, "cond": "1039,0,1", "prev_id": 10000407, "next_id": "", "reward": "1,0,250|2,0,250|3,0,250", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1037", "params": 1, "tip": "", "help": "", "sort": 1}, {"id": 10104101, "type": 1, "sub_type": 1, "cond": "1041,0,1", "prev_id": 10102102, "next_id": "", "reward": "1,0,250|2,0,250|3,0,250", "recreate_reward": "", "show_progress": "", "desc": "taskText.1040", "params": "", "tip": "", "help": "", "sort": 1}, {"id": 10000417, "type": 1, "sub_type": 1, "cond": "4,2008,3", "prev_id": 10104101, "next_id": "", "reward": "9,0,2", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2008|3", "tip": "", "help": "", "sort": 1}, {"id": 10103901, "type": 1, "sub_type": 1, "cond": "1039,0,2", "prev_id": 10000417, "next_id": "", "reward": "9,0,2", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1037", "params": 2, "tip": "", "help": "", "sort": 1}, {"id": 10000409, "type": 1, "sub_type": 1, "cond": "4,2001,5", "prev_id": 10103901, "next_id": "", "reward": "1,0,350|2,0,350|3,0,350", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2001|5", "tip": "", "help": "", "sort": 1}, {"id": 10000406, "type": 1, "sub_type": 1, "cond": "4,2011,1", "prev_id": 10000409, "next_id": "", "reward": "7,0,1", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2011", "tip": "", "help": "", "sort": 1}, {"id": 10102001, "type": 1, "sub_type": 1, "cond": "1027,0,2", "prev_id": 10000406, "next_id": "", "reward": "7,0,1", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1020", "params": 2, "tip": "", "help": "", "sort": 1}, {"id": 10000410, "type": 1, "sub_type": 1, "cond": "4,2001,10", "prev_id": 10102001, "next_id": "", "reward": "1,0,500|2,0,500|3,0,500", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2001|10", "tip": "", "help": "", "sort": 1}, {"id": 10000412, "type": 1, "sub_type": 1, "cond": "4,2015,1", "prev_id": 10000410, "next_id": "", "reward": "7,0,1", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2015", "tip": "", "help": "", "sort": 1}, {"id": 10103401, "type": 1, "sub_type": 1, "cond": "1034,0,1", "prev_id": 10000412, "next_id": "", "reward": "7,0,2", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1033", "params": 1, "tip": "", "help": "", "sort": 1}, {"id": 10000413, "type": 1, "sub_type": 1, "cond": "4,2008,10", "prev_id": 10103401, "next_id": "", "reward": "9,0,4", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2008|10", "tip": "", "help": "", "sort": 1}, {"id": 10103501, "type": 1, "sub_type": 1, "cond": "1035,0,1", "prev_id": 10000413, "next_id": "", "reward": "14,0,1", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1034", "params": 1, "tip": "", "help": "", "sort": 1}, {"id": 10104201, "type": 1, "sub_type": 1, "cond": "1042,0,1", "prev_id": 10103501, "next_id": "", "reward": "14,0,1", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1041", "params": 1, "tip": "", "help": "", "sort": 1}, {"id": 10000414, "type": 1, "sub_type": 1, "cond": "4,2001,15", "prev_id": 10104201, "next_id": "", "reward": "13,0,1|23,0,12", "recreate_reward": "13,0,1", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2001|15", "tip": "", "help": "", "sort": 1}, {"id": 20100101, "type": 1, "sub_type": 2, "cond": "12,1,1", "prev_id": 0, "next_id": "", "reward": "1,0,50|2,0,50|3,0,50", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1010", "params": 1, "tip": "", "help": "", "sort": 2}, {"id": 20001205, "type": 1, "sub_type": 2, "cond": "12,1,3", "prev_id": 20100101, "next_id": "", "reward": "1,0,75|2,0,75|3,0,75", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1010", "params": 3, "tip": "", "help": "", "sort": 2}, {"id": 20100102, "type": 1, "sub_type": 2, "cond": "1001,2,1", "prev_id": 20001205, "next_id": "", "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "1|2", "tip": "", "help": "", "sort": 2}, {"id": 20001206, "type": 1, "sub_type": 2, "cond": "12,1,10", "prev_id": 20100102, "next_id": "", "reward": "1,0,125|2,0,125|3,0,125", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1010", "params": 10, "tip": "", "help": "", "sort": 2}, {"id": 20100501, "type": 1, "sub_type": 2, "cond": "1005,3,2", "prev_id": 20001206, "next_id": "", "reward": "1,0,150|2,0,150|3,0,150", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1007", "params": "2|ui.land_base_3", "tip": "", "help": "", "sort": 2}, {"id": 20100502, "type": 1, "sub_type": 2, "cond": "1005,4,3", "prev_id": 20100501, "next_id": "", "reward": "1,0,175|2,0,175|3,0,175", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1007", "params": "3|ui.land_base_4", "tip": "", "help": "", "sort": 2}, {"id": 20100503, "type": 1, "sub_type": 2, "cond": "1005,5,4", "prev_id": 20100502, "next_id": "", "reward": "1,0,200|2,0,200|3,0,200", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1007", "params": "4|ui.land_base_5", "tip": "", "help": "", "sort": 2}, {"id": 20100103, "type": 1, "sub_type": 2, "cond": "1001,3,1", "prev_id": 20100503, "next_id": "", "reward": "1,0,250|2,0,250|3,0,250", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "1|3", "tip": "", "help": "", "sort": 2}, {"id": 20001201, "type": 1, "sub_type": 2, "cond": "12,1,25", "prev_id": 20100103, "next_id": "", "reward": "1,0,300|2,0,300|3,0,300", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1010", "params": 25, "tip": "", "help": "", "sort": 2}, {"id": 20103601, "type": 1, "sub_type": 2, "cond": "1036,0,30", "prev_id": 20001201, "next_id": "", "reward": "1,0,350|2,0,350|3,0,350", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1035", "params": 30, "tip": "", "help": "", "sort": 2}, {"id": 20001202, "type": 1, "sub_type": 2, "cond": "12,1,50", "prev_id": 20103601, "next_id": "", "reward": "23,0,5", "recreate_reward": "1,0,350|2,0,350|3,0,350", "show_progress": 1, "desc": "taskText.1010", "params": 50, "tip": "", "help": "", "sort": 2}, {"id": 20100104, "type": 1, "sub_type": 2, "cond": "1001,4,1", "prev_id": 20001202, "next_id": "", "reward": "1,0,400|2,0,400|3,0,400", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "1|4", "tip": "", "help": "", "sort": 2}, {"id": 20102501, "type": 1, "sub_type": 2, "cond": "1025,0,1", "prev_id": 20100104, "next_id": "", "reward": "1,0,500|2,0,500|3,0,500", "recreate_reward": "", "show_progress": "", "desc": "taskText.1019", "params": 1, "tip": "", "help": "", "sort": 2}, {"id": 20001203, "type": 1, "sub_type": 2, "cond": "12,1,75", "prev_id": 20102501, "next_id": "", "reward": "1,0,600|2,0,600|3,0,600", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1010", "params": 75, "tip": "", "help": "", "sort": 2}, {"id": 20102601, "type": 1, "sub_type": 2, "cond": "1026,2102,1", "prev_id": 20001203, "next_id": "", "reward": "1,0,700|2,0,700|3,0,700", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "cityText.name_2102", "tip": "", "help": "", "sort": 2}, {"id": 20001204, "type": 1, "sub_type": 2, "cond": "12,1,100", "prev_id": 20102601, "next_id": "", "reward": "23,0,8", "recreate_reward": "1,0,800|2,0,800|3,0,800", "show_progress": 1, "desc": "taskText.1010", "params": 100, "tip": "", "help": "", "sort": 2}, {"id": 20100105, "type": 1, "sub_type": 2, "cond": "1001,5,1", "prev_id": 20001204, "next_id": "", "reward": "1,0,1000|2,0,1000|3,0,1000", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "1|5", "tip": "", "help": "", "sort": 2}]