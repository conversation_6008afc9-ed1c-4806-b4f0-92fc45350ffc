package world

import (
	"fmt"
	"sync"

	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
)

// 点位模板 - 用于共享相同配置的Area的点位数据
type AreaPointTemplate struct {
	areaSize             *ut.Vec2        // 区域大小
	buildSize            *ut.Vec2        // 建筑大小
	isBoss               bool            // 是否为Boss区域
	buildMapPoints       []*ut.Vec2      // 建筑点位
	battleMapPoints      []*ut.Vec2      // 战斗点位
	banPlacePawnPointMap map[string]bool // 禁止放置点位
	passPoints           []*ut.Vec2      // 关口位置
	doorPoints           []*ut.Vec2      // 城门口位置
	mainPoints           []*ut.Vec2      // 主位置
	refCount             int32           // 引用计数
}

// 点位模板管理器
type AreaPointTemplateManager struct {
	templates map[string]*AreaPointTemplate // key: "areaSize_buildSize"
	mutex     sync.RWMutex
}

var (
	globalTemplateManager *AreaPointTemplateManager
	templateManagerOnce   sync.Once
)

// 获取全局模板管理器
func GetAreaPointTemplateManager() *AreaPointTemplateManager {
	templateManagerOnce.Do(func() {
		globalTemplateManager = &AreaPointTemplateManager{
			templates: make(map[string]*AreaPointTemplate),
		}
	})
	return globalTemplateManager
}

// 生成模板key
func (m *AreaPointTemplateManager) getTemplateKey(areaSize, buildSize *ut.Vec2, hasOwner, isBoss bool) string {
	key := areaSize.Join("x") + "_" + buildSize.Join("x")
	if hasOwner {
		key += "_owner"
	}
	if isBoss {
		key += "_boss"
	}
	return key
}

// 获取或创建点位模板
func (m *AreaPointTemplateManager) GetTemplate(areaSize, buildSize *ut.Vec2, hasOwner, isBoss bool) *AreaPointTemplate {
	key := m.getTemplateKey(areaSize, buildSize, hasOwner, isBoss)

	m.mutex.RLock()
	template, exists := m.templates[key]
	if exists {
		template.refCount++
		m.mutex.RUnlock()
		return template
	}
	m.mutex.RUnlock()

	// 创建新模板
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 双重检查
	if template, exists := m.templates[key]; exists {
		template.refCount++
		return template
	}

	// 创建模板
	template = &AreaPointTemplate{
		areaSize:  areaSize.Clone(),
		buildSize: buildSize.Clone(),
		isBoss:    isBoss,
		refCount:  1,
	}

	// 计算建筑区域的起点
	buildOrigin := areaSize.Sub(buildSize).DivSelf(2)

	// 生成所有点位数据
	template.buildMapPoints = helper.GenPointsBySize(
		ut.NewVec2(buildSize.X-2, buildSize.Y-2),
		ut.NewVec2(1, 1),
	)
	template.passPoints = helper.GetPassPoints(areaSize)
	template.doorPoints = helper.GetDoorPoints(areaSize, buildSize, buildOrigin)
	template.battleMapPoints = helper.GetBattlePoints(areaSize, buildSize, buildOrigin, template.passPoints)

	// 主位置点
	if isBoss {
		template.mainPoints = helper.GetMainPointsByBoss(buildSize, buildOrigin)
	} else {
		template.mainPoints = helper.GetMainPoints(areaSize, buildSize, buildOrigin)
	}

	// 禁止放置点位
	if hasOwner {
		template.banPlacePawnPointMap = helper.GetBanPlacePawnPoints(areaSize)
	} else {
		template.banPlacePawnPointMap = map[string]bool{}
	}

	m.templates[key] = template
	return template
}

// 释放模板引用
func (m *AreaPointTemplateManager) ReleaseTemplate(areaSize, buildSize *ut.Vec2, hasOwner, isBoss bool) {
	key := m.getTemplateKey(areaSize, buildSize, hasOwner, isBoss)

	m.mutex.Lock()
	defer m.mutex.Unlock()

	if template, exists := m.templates[key]; exists {
		template.refCount--
		if template.refCount <= 0 {
			delete(m.templates, key)
		}
	}
}

// 获取统计信息
func (m *AreaPointTemplateManager) GetStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	totalTemplates := len(m.templates)
	totalRefs := int32(0)
	zeroRefTemplates := 0

	for _, template := range m.templates {
		totalRefs += template.refCount
		if template.refCount <= 0 {
			zeroRefTemplates++
		}
	}

	return map[string]interface{}{
		"total_templates":    totalTemplates,
		"total_references":   totalRefs,
		"zero_ref_templates": zeroRefTemplates,
		"memory_saved_mb":    m.estimateMemorySaved(),
	}
}

// 验证所有模板的引用计数
func (m *AreaPointTemplateManager) ValidateRefCounts() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var issues []string

	for key, template := range m.templates {
		if template.refCount <= 0 {
			issues = append(issues, fmt.Sprintf("Template %s has invalid ref count: %d", key, template.refCount))
		}
		if template.refCount > 10000 { // 异常高的引用计数
			issues = append(issues, fmt.Sprintf("Template %s has suspiciously high ref count: %d", key, template.refCount))
		}
	}

	return issues
}

// 强制清理引用计数为0的模板
func (m *AreaPointTemplateManager) CleanupZeroRefTemplates() int {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	cleanedCount := 0
	for key, template := range m.templates {
		if template.refCount <= 0 {
			delete(m.templates, key)
			cleanedCount++
		}
	}

	return cleanedCount
}

// 估算节省的内存
func (m *AreaPointTemplateManager) estimateMemorySaved() int64 {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	savedMemory := int64(0)
	pointSize := int64(16) // Vec2大小

	for _, template := range m.templates {
		if template.refCount > 1 {
			// 每个引用节省的内存 = (点位数量 * 点位大小) * (引用数 - 1)
			pointCount := int64(len(template.buildMapPoints) + len(template.battleMapPoints) + len(template.banPlacePawnPointMap))
			savedMemory += pointCount * pointSize * int64(template.refCount-1)
		}
	}

	return savedMemory / 1024 / 1024 // 转换为MB
}

// Area的优化版本 - 使用共享模板
type OptimizedArea struct {
	*Area
	pointTemplate *AreaPointTemplate
	templateKey   string
}

// 创建优化的Area
func NewOptimizedArea(index int32, wld g.World) *OptimizedArea {
	area := NewArea(index, wld)
	return &OptimizedArea{
		Area: area,
	}
}

// 优化版本已经不需要单独的初始化方法，因为在Init中就会初始化

// 优化版本的清理 - 直接调用基类的Clear方法
func (this *OptimizedArea) ClearUnusedPoints(maxIdleTime int64) bool {
	// 检查是否已初始化
	if !this.IsInitialized() {
		return false
	}

	if len(this.Builds.List) > 0 || len(this.Armys.List) > 0 {
		return false
	}

	if ut.Now()-this.lastAccessTime > maxIdleTime {
		// 直接调用Clear方法
		this.Clear()
		return true
	}

	return false
}

// 方案3: 压缩点位存储
type CompressedPointStorage struct {
	// 使用位图存储点位信息，大幅减少内存使用
	buildPointsBitmap  []uint64 // 每个bit代表一个点位
	battlePointsBitmap []uint64
	banPointsBitmap    []uint64
	areaSize           *ut.Vec2
	bitmapSize         int
}

// 创建压缩存储
func NewCompressedPointStorage(areaSize *ut.Vec2) *CompressedPointStorage {
	totalPoints := int(areaSize.X * areaSize.Y)
	bitmapSize := (totalPoints + 63) / 64 // 向上取整到64的倍数

	return &CompressedPointStorage{
		buildPointsBitmap:  make([]uint64, bitmapSize),
		battlePointsBitmap: make([]uint64, bitmapSize),
		banPointsBitmap:    make([]uint64, bitmapSize),
		areaSize:           areaSize.Clone(),
		bitmapSize:         bitmapSize,
	}
}

// 设置点位
func (c *CompressedPointStorage) SetPoint(x, y int32, pointType int) {
	index := y*c.areaSize.X + x
	wordIndex := index / 64
	bitIndex := index % 64

	if wordIndex >= int32(c.bitmapSize) {
		return
	}

	mask := uint64(1) << bitIndex

	switch pointType {
	case 0: // build point
		c.buildPointsBitmap[wordIndex] |= mask
	case 1: // battle point
		c.battlePointsBitmap[wordIndex] |= mask
	case 2: // ban point
		c.banPointsBitmap[wordIndex] |= mask
	}
}

// 检查点位
func (c *CompressedPointStorage) HasPoint(x, y int32, pointType int) bool {
	index := y*c.areaSize.X + x
	wordIndex := index / 64
	bitIndex := index % 64

	if wordIndex >= int32(c.bitmapSize) {
		return false
	}

	mask := uint64(1) << bitIndex

	switch pointType {
	case 0: // build point
		return c.buildPointsBitmap[wordIndex]&mask != 0
	case 1: // battle point
		return c.battlePointsBitmap[wordIndex]&mask != 0
	case 2: // ban point
		return c.banPointsBitmap[wordIndex]&mask != 0
	}

	return false
}

// 获取所有指定类型的点位
func (c *CompressedPointStorage) GetPoints(pointType int) []*ut.Vec2 {
	points := []*ut.Vec2{}

	for y := int32(0); y < c.areaSize.Y; y++ {
		for x := int32(0); x < c.areaSize.X; x++ {
			if c.HasPoint(x, y, pointType) {
				points = append(points, ut.NewVec2(x, y))
			}
		}
	}

	return points
}

// 内存使用估算
func (c *CompressedPointStorage) EstimateMemoryUsage() int64 {
	// 3个bitmap * bitmapSize * 8字节
	return int64(c.bitmapSize * 3 * 8)
}
