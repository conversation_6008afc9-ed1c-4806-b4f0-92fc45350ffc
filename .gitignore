# Compiled Object files, Static and Dynamic libs (Shared Objects)
.idea
.vscode
bin/bi
bin/logs
bin/sslkey
bin/server
bin/client
src/gopkg.in
# src/github.com/quick-know-master
pkg
node_modules
*.o
*.a
*.so
*.iml

# Folders
_obj
_test

# Architecture specific extensions/prefixes
*.[568vq]
[568vq].out

*.cgo1.go
*.cgo2.c
_cgo_defun.c
_cgo_gotypes.go
_cgo_export.*

_testmain.go

__debug_bin.*
*.exe
*.test
*.prof
*.lnk

server.conf.bat

*.txt

bin/conf/server.json
server/common/local_config.go
_test/
_numerical/
_http/
_gener/
_db/
proto/msg.js
proto/msg.d.ts
ecosystem.config.js
/bin/maps
