package sensitive

import (
	"encoding/base64"
	"strconv"
	"strings"

	v20201229 "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tms/v20201229"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
)

const (
	TX_SECRET_ID  = "AKIDgEdvypSvBE4hBPnCmFTXO4XpskRtLA0d" // 腾讯api id
	TX_SECRET_KEY = "Xt25uXxEG5h5II4d0A7qRyNaaOzMYhfP"     // 腾讯api key
	TX_REGION     = "ap-shanghai"                          // 腾讯api 地域
)

type TencentMod struct {
	initEnd    bool
	secretId   string
	secretKey  string
	credential *common.Credential
	client     *v20201229.Client

	chineseCode map[string]string // 中文标点符号
	_l          deadlock.RWMutex
}

var (
	txMod   *TencentMod
	lockObj deadlock.RWMutex
)

func GlobalGetTencentMod() *TencentMod {
	if txMod == nil {
		lockObj.Lock()
		if txMod == nil {
			txMod = &TencentMod{
				secretId:  TX_SECRET_ID,
				secretKey: TX_SECRET_KEY,
			}
			txMod.init()
		}
		lockObj.Unlock()
	}
	return txMod
}

func (this *TencentMod) init() {
	this._l.Lock()
	defer this._l.Unlock()
	if this.initEnd {
		return
	}
	this.credential = common.NewCredential(
		this.secretId,
		this.secretKey,
	)
	// clientProfile是可选的
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "tms.tencentcloudapi.com"
	this.client, _ = v20201229.NewClient(this.credential, TX_REGION, cpf)
	// search website : https://unicode.yunser.com/unicode
	this.chineseCode = map[string]string{
		"8211":  "–",
		"8212":  "—",
		"8216":  "‘",
		"8217":  "’",
		"8220":  "“",
		"8221":  "”",
		"8230":  "…",
		"12289": "、",
		"12290": "。",
		"12296": "〈",
		"12297": "〉",
		"12298": "《",
		"12299": "》",
		"12300": "「",
		"12301": "」",
		"12302": "『",
		"12303": "』",
		"12304": "【",
		"12305": "】",
		"12308": "〔",
		"12309": "〕",
		"65281": "！",
		"65288": "（",
		"65289": "）",
		"65292": "，",
		"65294": "．",
		"65306": "：",
		"65307": "；",
		"65311": "？",
	}
	this.initEnd = true
}

// TextModeration 敏感词检测 返回true为不通过,同时返回会把content中的敏感词击中部分替换成*,如果content本身为空,则返回空字符串并且返回true
func (this *TencentMod) TextModeration(content string) (bool, string) {
	// 如果是空串，没得检查的必要
	if strings.ReplaceAll(content, " ", "") == "" {
		return true, ""
	}
	// 如果字符串用制表符或者空格隔开，腾讯的检测就会失效，所以要先处理这些字符
	temp := content
	// 处理空格
	temp = strings.ReplaceAll(temp, " ", "")
	// 处理除了正常识别字符以外的所有字符
	restoreMap := map[int]string{}
	idx := -1
	for _, t := range temp {
		idx++
		// 中文
		if len(string(t)) == 3 && this.chineseCode[strconv.Itoa(int(t))] == "" {
			continue
		}
		// a-z A-Z
		if (t >= 65 && t <= 90) || (t >= 97 && t <= 127) {
			continue
		}
		restoreMap[idx] = string(t)
		temp = strings.ReplaceAll(temp, string(t), "")
	}

	if temp == "" {
		// 通过
		return false, content
	}

	bs := base64.StdEncoding.EncodeToString([]byte(temp))
	request := v20201229.NewTextModerationRequest()
	request.Content = &bs
	bt := "game_001"
	request.BizType = &bt
	response, err := this.client.TextModeration(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		log.Error(err.Error(), content)
		return true, content
	}
	//@see : https://cloud.tencent.com/document/product/1124/51860
	if *response.Response.Label == "Normal" {
		return false, content
	}

	return true, "***"
}
