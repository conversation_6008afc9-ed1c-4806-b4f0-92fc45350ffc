// 好友聊天管理
package chat

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

const (
	CHAT_COUNT_IN_CACHE = 1000 // 内存中的聊天数量
)

var (
	chatCache         *ut.LFUCache         // 好友聊天缓存
	friendChatsDbChan chan *FriendChatList // 好友聊天db队列
)

// 好友聊天初始化
func InitFriendChat() {
	chatCache = ut.NewLFUCache(CHAT_COUNT_IN_CACHE)
	friendChatsDbChan = make(chan *FriendChatList, CHAT_COUNT_IN_CACHE)
}

// 获取好友聊天
func GetFriendChat(channel string) (chatList *FriendChatList) {
	rst, ok := chatCache.Get(channel)
	if !ok {
		// 不存在缓存中 从数据库获取
		data, err := friendChatDb.FindChat(channel)
		if err == nil && data != nil {
			// 放入缓存
			chatCache.Set(channel, data)
			chatList = data
		}
	} else {
		chatList = rst.(*FriendChatList)
	}
	return
}

// 添加好友聊天
func AddFriendChat(channel string, chat *pb.ChatInfo) {
	chatInfo := FriendChatFromPb(chat)
	chatList := GetFriendChat(channel)
	if chatList == nil {
		chatList = &FriendChatList{
			Channel: channel,
			List:    []*FriendChatInfo{},
		}
	}
	chatList.Lock()
	if len(chatList.List) >= slg.FRIEND_CHAT_SHOW_MAX {
		chatList.List = chatList.List[:slg.FRIEND_CHAT_SHOW_MAX-1]
	}
	chatList.List = append([]*FriendChatInfo{chatInfo}, chatList.List...)
	// 需要更新到数据库
	friendChatsDbChan <- chatList
	chatList.Unlock()
	chatCache.Set(channel, chatList)
}

// db tick
func FriendChatDbTick() {
	go func() {
		tiker := time.NewTicker(time.Second * 10)
		for isRunning {
			<-tiker.C
			UpdateFriendChatDb(false)
		}
	}()
}

// 更新到数据库
func UpdateFriendChatDb(close bool) {
	sum := len(friendChatsDbChan)
	if sum == 0 {
		return
	}
	channelMap := map[string]*FriendChatList{}
	for len(friendChatsDbChan) > 0 {
		friendChatList := <-friendChatsDbChan
		channel := friendChatList.Channel
		if channelMap[channel] != nil {
			continue
		}
		channelMap[channel] = friendChatList
		if len(channelMap) >= CHAT_COUNT_IN_CACHE {
			log.Warning("UpdateChatDb len over limit")
			break
		}
	}
	for channel, chatList := range channelMap {
		chatList.RLock()
		friendChatDb.UpdateFriendChats(channel, chatList.List)
		chatList.RUnlock()
	}
	surplus := len(friendChatsDbChan)
	if close && surplus > 0 {
		UpdateFriendChatDb(close)
	}
}

type FriendChatInfo struct {
	UID     string `bson:"uid"`
	Sender  string `bson:"sender"`  // 发送者
	Content string `bson:"content"` // 内容
	Emoji   int    `bson:"emoji"`   // 表情
	Time    int    `bson:"time"`    // 发送时间
}

type FriendChatList struct {
	Channel string            `json:"channel" bson:"channel"` // 频道
	List    []*FriendChatInfo `json:"list" bson:"list"`       // 聊天
	deadlock.RWMutex
}

func (this *FriendChatList) ToPb() []*pb.ChatInfo {
	arr := []*pb.ChatInfo{}
	if this.List == nil {
		return arr
	}
	this.RLock()
	defer this.RUnlock()
	for _, v := range this.List {
		arr = append(arr, v.ToPb())
	}
	return arr
}

func (this *FriendChatInfo) ToPb() *pb.ChatInfo {
	return &pb.ChatInfo{
		Uid:     this.UID,
		Sender:  this.Sender,
		Content: this.Content,
		Emoji:   int32(this.Emoji),
		Time:    int64(this.Time),
	}
}

func FriendChatFromPb(chat *pb.ChatInfo) *FriendChatInfo {
	return &FriendChatInfo{
		UID:     chat.Uid,
		Sender:  chat.Sender,
		Content: chat.Content,
		Emoji:   int(chat.Emoji),
		Time:    int(chat.Time),
	}
}
