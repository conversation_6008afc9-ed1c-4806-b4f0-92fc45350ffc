package mail

import (
	slg "slgsrv/server/common"
	"slgsrv/server/game/common/g"

	"github.com/huyangv/vmqant/log"
)

func (this *Mail) InitRpc() {
	this.GetServer().RegisterGO(slg.RPC_SEND_MAIL_ITEM_ONE, this.sendMailItemOne)
	this.GetServer().RegisterGO(slg.RPC_SEND_MAIL_MANY, this.sendMailMany)
	this.GetServer().RegisterGO(slg.RPC_DEL_MAIL_BY_SID, this.delMailBySid)
}

// 发送邮件个人
func (this *Mail) sendMailItemOne(sid, contentId int32, title, content, sender, receiver string, items []*g.TypeObj) (result []byte, err string) {
	if receiver == "0" {
		// 兼容全服
		err = this.SendMailAll(sid, contentId, title, content, sender, items)
	} else {
		// 个人
		err = this.SendMailItemOne(sid, contentId, title, content, sender, receiver, items)
	}
	return
}

// 发送邮件 多人
func (this *Mail) sendMailMany(sid, contentId int32, title, content, sender string, uids []string, items []*g.TypeObj) (result []byte, err string) {
	err = this.SendMailMany(sid, contentId, title, content, sender, uids, items)
	return
}

// 删除指定区服的邮件
func (this *Mail) delMailBySid(sid int32) (result []byte, err string) {
	if sid <= 0 {
		return
	}
	mailList, e := mailDb.FindMailsBySid(sid)
	if e != nil {
		log.Error("delMailBySid FindMailsBySid err: %v, sid: %v", e, sid)
		return
	}
	// mailDb.DeleteMailsBySid(sid)
	for _, mailInfo := range mailList {
		this.SysDelMail(mailInfo)
	}
	return
}
