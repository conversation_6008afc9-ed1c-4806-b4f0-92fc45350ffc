package rds

import (
	"time"

	ut "slgsrv/utils"
)

// redis键
const (
	RDS_LOBBY_LOAD_MAP_KEY       = "lobby_load_map"        // 大厅服负载map
	RDS_USER_LID_MAP_KEY         = "user_lid_map"          // 玩家lid map
	RDS_USER_LID_LOCK_KEY        = "user_lid_lock_"        // 分配玩家lid锁 key+uid 值为加锁的时间
	RDS_LOBBY_TEAM_LID_MAP_KEY   = "team_lid_map"          // 队伍所在大厅服map
	RDS_LOBBY_CHAT_KEY           = "lobby_chat_"           // 大厅聊天list key+channel
	RDS_MATCH_POOL_MAP           = "match_pool_map"        // 匹配池信息map
	RDS_MATCH_WAIT_OPEN_MAP_KEY  = "match_wait_open_map"   // 匹配池待开启的区服map k=>sid v=>开启时间
	RDS_MATCH_TEAM_MAP_KEY       = "match_team_map_"       // 匹配池中的队伍map key+匹配池uid
	RDS_CREATE_ROOM_TEAM_MAP_KEY = "create_room_team_map_" // 创建中区服的队伍map key+sid
	RDS_LOBBY_QUEUE              = "lobby_queue"           // 大厅服登录排队list
	RDS_LOGIN_LIMIT_TYPE_KEY     = "login_limit_type"      // 登录限制类型
	RDS_LOGIN_WHITE_MAP_KEY      = "login_white_map"       // 登录白名单
	RDS_LOGIN_BLACK_MAP_KEY      = "login_black_map"       // 登录黑名单

	RDS_GAME_PAWN_USE_LIST_KEY = "game_pawn_use_list" // 区服使用士兵数据list
	RDS_GAME_PAWN_COST_MAP_KEY = "game_pawn_cost_map" // 士兵资源消耗map
)

// redis用户数据field
const (
	RDS_USER_FIELD_LID               = "lid"
	RDS_USER_FIELD_TOKEN             = "token"
	RDS_USER_FIELD_LAST_OFFLINE_TIME = "last_offline_time"
	RDS_USER_FIELD_PLAY_SID          = "play_sid"

	RDS_USER_EXPIRE_TIME_DURATION = time.Hour * 24 * 30 // 用户缓存数据有效期30天
	RDS_USER_EXPIRE_TIME          = ut.TIME_DAY * 30    // 用户缓存数据有效期30天
)

// redis用户订阅频道
const (
	RDS_USER_SUB_CHANNEL_TEAM = "team_" // key+队伍uid
)
