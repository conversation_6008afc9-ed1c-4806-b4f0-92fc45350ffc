#!/bin/bash

# 进入项目目录
cd /projects/slg-server

# BACKUP_DIR = /projects/slg-server-backup

# # 备份当前代码
# cp -r . "$BACKUP_DIR/backup_$(date +"%Y%m%d_%H%M%S")"

# 执行 Git 更新
git fetch || exit 1
git pull || exit 1

# 设置 Go 相关环境变量
export GOROOT=/usr/local/go
export GOPATH=/root/go
export PATH=$PATH:$GOROOT/bin

# 执行构建脚本，只有在 git pull 成功后才会执行
bash build_release.sh || exit 1

# 记录更新日志
echo "Updated at $(date)" >> /projects/slg-server/bin/logs/update.log