#!/bin/bash
# 下面这一条是现成的线上服务器的数据导出执行方案。
# sh mongo_export.sh -u=mongouser -w=jiuwanmu996 -d=slgsrv -h=************ -a=admin

# mongo地址 默认是127.0.0.1
host='127.0.0.1'
#mongo port 默认是27017
port='27017'
#目标db
dbName=''
#mongo用户名
uname=''
#mongo密码
password=''
# 区服 不输入就会去数据库取出来提示选择
sid=''
# 验证
tdb=""

target=""
path=""

declare SUGGESTION_INPUT
declare CMD_TEMP=''
declare CMD_MONGO=''
declare CMD_MONGO_EXPORT=''
declare MONGO_VERSION=''
# 这里是腾讯云的工具目录 已经把mongo bin扩展命令存放进去了
declare COS_URL='https://twgame-jwm-inland-1257666530.cos.ap-guangzhou.myqcloud.com/tools/'
# 可下载文件列表
declare download=(mongo mongoexport mongoimport)
show_usage() {
  cat <<EOF
  说明: 参数传递格式是[key=value],带*则是必传参数。
  例如: sh mongo_export.sh -u=mongouser -w=jiuwanmu996 -d=slgsrv -h=************ -a=admin
  可传递参数列表如下：
  1、-h   数据库mongo的地址，如果不传递则默认使用127.0.0.1
  2、-p   数据库mongo的端口，如果不传递则默认使用27017
  3、-u * 数据库mongo的用户名，必须传递...如果数据库没开验证,脚本不能运行.
  4、-w * 数据库mongo的密码，必须传递...如果数据库没开验证,脚本不能运行.
  5、-a * 数据库身份验证库,腾讯云的都是admin,自建db的验证一般在db里面.
  6、-d * 目标数据库,如果登录验证是admin,则脚本不能运行。
  7、-s   导出数据的目标服务器id,如果不传递会弹出选择项。
  8、-o   导出数据压缩后存放路径
EOF
}

#询问是否要下载mongoexport
ask_for_download_mongo() {
  local cmd=$1
  if [[ $(echo "${download[@]}" | grep "${cmd}") == "" ]]; then
    exit 14
  fi
  local answer=''
  while [[ ${answer} != "yes" && ${answer} != "no" ]]; do
    printf "%-35s:" "需要下载${cmd}的可执行文件,是否继续(yes/no)"
    read answer
    answer=$(echo "${answer,,}")
  done
  if [[ "${answer}" == "yes" ]]; then
    if [[ $cmd == "mongo" ]]; then
      # mongodb 下载安装
      curl $COS_URL/mongo/"mongo.gz" -o ~/"mongo.gz"
      tar -zxvf ~/mongo.gz -C /usr/local/bin/ >/dev/null 2>&1
      CMD_MONGO="/usr/local/bin/$cmd"
      ln -s /usr/local/bin/mongodb-linux-x86_64-rhel80-5.0.14/bin/mongo /usr/local/bin/mongo
      rm -rf ~/mongo.gz
    fi
    if [[ $cmd == "mongoexport" ]]; then
      # mongoexport 下载
      curl $COS_URL/mongo/"$cmd" -o /usr/local/bin/"$cmd"
      CMD_MONGO_EXPORT="/usr/local/bin/$cmd"
      chmod a+x "$CMD_MONGO_EXPORT"
    fi
    printf "%-35s\n" "文件[$cmd]下载成功!"
  else
    echo "exit..."
    exit 0
  fi
}

# 检查mongoexport导出命令
check_mongoexport() {
  printf "%-20s" '[mongoexport]'
  local mongoexport=$(whereis mongoexport | awk '{printf $2}')
  if [[ $mongoexport == '' ]]; then
    printf "%-10s\n" '本机找不到该命令'
    suggestion "mongoexport"
    if [[ $? == 10 ]]; then
      #尝试去下载mongoexport
      ask_for_download_mongo "mongoexport"
    fi
  fi
  CMD_MONGO_EXPORT=$mongoexport
  printf "%-25s\n" "$mongoexport"
}
# 检查mongo连接命令
check_mongo() {
  local clink=0
  printf "%-20s" '[mongo]'
  local mongo=$(whereis mongo | awk '{printf $2}')
  if [[ $mongo == '' ]]; then
    printf "%-10s\n" '本机找不到该命令'
    suggestion "mongo"
    if [[ $? == 10 ]]; then
      #尝试去下载mongo
      ask_for_download_mongo "mongo"
    else
      CMD_MONGO=$CMD_TEMP
      if [[ $CMD_MONGO == '' ]]; then
        printf "%-12s\n" '命令结果错误'
        exit 254
      fi
    fi
    clink=1
    return 0
  fi
  printf "%-25s\n" "$mongo"
  CMD_MONGO=$mongo
  # 查看命令并获取版本信息
  local ver=$($CMD_MONGO --version | grep -e "MongoDB shell version" | awk '{printf $4}')
  if [[ $ver == '' ]]; then
    printf "%-12s\n" '不能获取mongo版本'
    exit 254
  fi
  if [[ $clink -eq 1 ]]; then
    #创建软连接
    ln -s "$CMD_MONGO" /usr/local/bin/mongo
  fi
  printf "%-14s\n" "[mongo]版本:$ver"
  MONGO_VERSION=${ver#*v}
}
#读取建议输入
function read_suggestion_input {
  local answer=blah
  while [[ ${answer} -lt $1 || ${answer} > $2 ]]; do
    printf "%-10s:" "请选择命令目录($1 - $2)"
    read answer
    answer=$(echo "${answer,,}")
  done
  SUGGESTION_INPUT="$answer"
}
#当前环境变量找不到命令时，用find找到结果并给出提示 返回结果
suggestion() {
  local cmd=$1
  local index=0
  # 错误重定向，find会提示 /proc/xx 之类的
  local result=$(find / -name "$cmd" 2>/dev/null)
  local arr=()
  if [[ $result == '' ]]; then
    printf "%-30s\n" "命令[$1]找不到任何可执行文件输入建议"
    return 10
  fi
  printf "%-30s\n" "命令[$1]的可执行文件建议列表(如果你不确定选项,请选择0):"
  printf "%-2s %-2d %-100s\n" "  " $index "去腾讯云工具库下载."
  ((index++))
  for r in $result; do
    printf "%-2s %-2d %-100s\n" "  " $index "$r"
    arr["$index"]="$r"
    ((index++))
  done
  read_suggestion_input 0 $index
  if [[ $SUGGESTION_INPUT -eq 0 ]]; then
    # 返回10 去下载
    return 10
  fi
  printf "%-22s: %-s\n" "您为命令[$1]选择了目录" "${arr[SUGGESTION_INPUT]}"
  CMD_TEMP=${arr[SUGGESTION_INPUT]}
  return 0
}
check_environment() {
  echo "检查环境......"
  check_mongo
  check_mongoexport
}

# 检查选择的区服 如果没有输入就要提示选择
print_out_area() {
  if [[ $sid != '' ]]; then
    return 0
  fi
  echo "加载区服数据......"
  local result=$($CMD_MONGO mongodb://"$uname":"$password"@"$host":"$port"/"$dbName" --authenticationDatabase="$tdb" --eval "db.room.find({},{id:1})")
  if [[ $(echo "$result" | grep -e "exception") != '' ]]; then
    printf "%-s\n" "执行db语句出错: $(echo "$result" | grep -e "Error")"
    exit 9
  fi
  local docs=${result#*"MongoDB server version"}
  docs=${docs#*"{"}
  docs="{ $docs"
  if [[ $docs == '' ]]; then
    printf "%-s" "没有查到任何区服记录,无法完成数据导出."
    exit 8
  fi
  #echo "$docs"
  local index=0
  local len=0
  local sidArr=()
  echo "------------------------------------------------"
  printf "%-1s %-34s %-14s %-1s\n" "|" "文档id" "|服务器id" "|"
  for r in $docs; do
    if [[ $index -eq 3 ]]; then
      r=${r%\"*}
      r=${r#*\"}
      printf "%-1s %-33s" "|" "$r"
    fi
    if [[ $index -eq 6 ]]; then
      sidArr["$len"]=$r
      ((len++))
      printf "%-11s %-1s\n" "|$r" "|"
    fi
    if [[ $index -eq 7 ]]; then
      index=-1
    fi
    ((index++))
  done
  echo "------------------------------------------------"
  local answer=blah
  while [[ $(echo "${sidArr[@]}" | grep "${answer}") == "" ]]; do
    printf "%-10s:" "请选择服务器"
    read answer
    answer=$(echo "${answer,,}")
  done
  sid=$answer
}

# 导出联盟数据
function export_alliance {
  echo "=====================  export_alliance "
  echo ""
  local col="alliance"
  local out="alliance"
  # $CMD_MONGO_EXPORT -h "$host" --port "$port" -u "$uname" -p "$password" --authenticationDatabase="$tdb" -d "$dbName" -c "$col" -q '{"sid":'"$sid"'}' | sed '/"_id":/s/"_id":[^,]*,//' >"$target/$out"
  $CMD_MONGO_EXPORT -h "$host" --port "$port" -u "$uname" -p "$password" --authenticationDatabase="$tdb" -d "$dbName" -c "$col" -o "$target/$out" -q '{"sid":'"$sid"'}'
  #mongoexport -h localhost --port 27017 -u slg_test -p slg_test --authenticationDatabase=slgsrv_test -d slgsrv_test -c alliance -o ./alliance -q '{"sid":7}'
}
# 导出市场数据
function export_bazaar {
  echo "=====================  export_bazaar "
  echo ""
  local col="bazaar_$sid"
  local out="bazaar"
  $CMD_MONGO_EXPORT -h "$host" --port "$port" -u "$uname" -p "$password" --authenticationDatabase="$tdb" -d "$dbName" -c "$col" -o "$target/$out"
}
# 导出game数据
function export_game {
  echo "=====================  export_game "
  echo ""
  local col="game"
  local out="game"
  $CMD_MONGO_EXPORT -h "$host" --port "$port" -u "$uname" -p "$password" --authenticationDatabase="$tdb" -d "$dbName" -c "$col" -o "$target/$out" -q '{"sid":'"$sid"'}'
}
# 导出user数据 user是一个单独的数据库表
function export_user {
  echo "=====================  export_user "
  echo ""
  local col="user"
  local out="user"
  $CMD_MONGO_EXPORT -h "************" --port "$port" -u "$uname" -p "$password" --authenticationDatabase="$tdb" -d "$dbName" -c "$col" -o "$target/$out" -q '{"sid":'"$sid"'}'
}
# 导出player数据
function export_player {
  echo "=====================  export_player "
  echo ""
  local col="player_$sid"
  local out="player"
  $CMD_MONGO_EXPORT -h "$host" --port "$port" -u "$uname" -p "$password" --authenticationDatabase="$tdb" -d "$dbName" -c "$col" -o "$target/$out"
}
# 导出room数据
function export_room {
  echo "=====================  export_room "
  echo ""
  local col="room"
  local out="room"
  $CMD_MONGO_EXPORT -h "$host" --port "$port" -u "$uname" -p "$password" --authenticationDatabase="$tdb" -d "$dbName" -c "$col" -o "$target/$out" -q '{"id":'"$sid"'}'
}
# 导出world数据
function export_world {
  echo "=====================  export_world "
  echo ""
  local col="world_$sid"
  local out="world"
  $CMD_MONGO_EXPORT -h "$host" --port "$port" -u "$uname" -p "$password" --authenticationDatabase="$tdb" -d "$dbName" -c "$col" -o "$target/$out"
}
# 创建存放目录
create_dir() {
  if [[ $target != "" ]]; then
    mkdir -p "$target"
    path=$target
    return 0
  fi
  local time=$(date "+%Y%m%d%H%M")
  target=export_$time
  path=~/export_$time
}
_done() {
  tar -cvf "$target.tar.gz" "$path/" >/dev/null 2>&1
  printf "%-s\n" "文件已经压缩:$target.tar.gz, 数据导出完成."
  rm -rf "$path"
}
function main {
  if [[ $# -lt 1 ]]; then
    show_usage
    exit 8
  fi
  for i in "$@"; do
    local r=${i#*-h=}
    if [[ $r != '' ]] && [[ $r != $i ]]; then
      host=$r
    fi
    r=${i#*-p=}
    if [[ $r != '' ]] && [[ $r != $i ]]; then
      port=$r
    fi
    r=${i#*-u=}
    if [[ $r != '' ]] && [[ $r != $i ]]; then
      uname=$r
    fi
    r=${i#*-w=}
    if [[ $r != '' ]] && [[ $r != $i ]]; then
      password=$r
    fi
    r=${i#*-a=}
    if [[ $r != '' ]] && [[ $r != $i ]]; then
      tdb=$r
    fi
    r=${i#*-d=}
    if [[ $r != '' ]] && [[ $r != $i ]]; then
      dbName=$r
    fi
    r=${i#*-s=}
    if [[ $r != '' ]] && [[ $r != $i ]]; then
      sid=$r
    fi
    r=${i#*-o=}
    if [[ $r != '' ]] && [[ $r != $i ]]; then
      target=$r
    fi
  done
  if [[ $uname == '' ]] || [[ $password == '' ]] || [[ $dbName == '' ]] || [[ $tdb == '' ]]; then
    show_usage
    exit 7
  fi

  check_environment
  print_out_area
  create_dir
  export_alliance
  echo ""
  export_bazaar
  echo ""
  export_game
  echo ""
  export_user
  echo ""
  export_player
  echo ""
  export_room
  echo ""
  export_world
  echo ""
  _done
}

main "$@"
