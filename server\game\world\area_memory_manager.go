package world

import (
	"sync"
	"time"

	"github.com/huyangv/vmqant/log"
)

// Area内存管理器
type AreaMemoryManager struct {
	world         *Model
	isRunning     bool
	stopChan      chan bool
	cleanInterval time.Duration // 清理间隔
	maxIdleTime   int64         // 最大空闲时间(秒)
	mutex         sync.RWMutex

	// 统计信息
	totalAreas    int32
	activeAreas   int32
	cleanedAreas  int32
	lastCleanTime time.Time
}

// 创建Area内存管理器
func NewAreaMemoryManager(world *Model) *AreaMemoryManager {
	return &AreaMemoryManager{
		world:         world,
		isRunning:     false,
		stopChan:      make(chan bool),
		cleanInterval: 5 * time.Minute, // 每5分钟清理一次
		maxIdleTime:   10 * 60,         // 10分钟未使用就清理
		lastCleanTime: time.Now(),
	}
}

// 启动内存管理器
func (m *AreaMemoryManager) Start() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.isRunning {
		return
	}

	m.isRunning = true
	go m.run()
	log.Info("Area memory manager started, clean interval: %v, max idle time: %ds",
		m.cleanInterval, m.maxIdleTime)
}

// 停止内存管理器
func (m *AreaMemoryManager) Stop() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.isRunning {
		return
	}

	m.isRunning = false
	close(m.stopChan)
	log.Info("Area memory manager stopped")
}

// 主运行循环
func (m *AreaMemoryManager) run() {
	ticker := time.NewTicker(m.cleanInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.cleanUnusedAreaPoints()
		case <-m.stopChan:
			return
		}
	}
}

// 清理未使用的Area点位数据
func (m *AreaMemoryManager) cleanUnusedAreaPoints() {
	startTime := time.Now()
	cleanedCount := int32(0)
	totalCount := int32(0)
	activeCount := int32(0)

	// 遍历所有Area
	for index := int32(0); index < m.world.room.GetMapSize().X*m.world.room.GetMapSize().Y; index++ {
		area := m.world.GetArea(index)
		if area == nil {
			continue
		}

		totalCount++

		// 检查是否有活动（军队或建筑）
		if len(area.Builds.List) > 0 || len(area.Armys.List) > 0 {
			activeCount++
			continue
		}

		// 尝试清理未使用的点位数据
		if area.ClearUnusedPoints(m.maxIdleTime) {
			cleanedCount++
		}
	}

	// 更新统计信息
	m.mutex.Lock()
	m.totalAreas = totalCount
	m.activeAreas = activeCount
	m.cleanedAreas += cleanedCount
	m.lastCleanTime = time.Now()
	m.mutex.Unlock()

	duration := time.Since(startTime)
	if cleanedCount > 0 {
		log.Info("Area memory cleanup completed: cleaned %d areas, total %d areas, active %d areas, took %v",
			cleanedCount, totalCount, activeCount, duration)
	}

	// 如果清理了很多Area，建议执行GC
	if cleanedCount > 1000 {
		log.Info("Cleaned %d areas, suggesting GC", cleanedCount)
		// 这里可以触发GC，但要谨慎使用
		// runtime.GC()
	}
}

// 获取统计信息
func (m *AreaMemoryManager) GetStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return map[string]interface{}{
		"total_areas":     m.totalAreas,
		"active_areas":    m.activeAreas,
		"cleaned_areas":   m.cleanedAreas,
		"last_clean_time": m.lastCleanTime.Unix(),
		"clean_interval":  m.cleanInterval.Seconds(),
		"max_idle_time":   m.maxIdleTime,
		"is_running":      m.isRunning,
	}
}

// 手动触发清理
func (m *AreaMemoryManager) ForceClean() {
	go m.cleanUnusedAreaPoints()
}

// 设置清理参数
func (m *AreaMemoryManager) SetCleanParams(cleanInterval time.Duration, maxIdleTime int64) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.cleanInterval = cleanInterval
	m.maxIdleTime = maxIdleTime

	log.Info("Area memory manager params updated: clean interval: %v, max idle time: %ds",
		cleanInterval, maxIdleTime)
}

// 获取内存使用估算
func (m *AreaMemoryManager) EstimateMemoryUsage() map[string]int64 {
	totalPoints := int64(0)
	initializedAreas := int64(0)

	// 估算每个点位的内存使用
	// Vec2: 16字节 (X:4 + Y:4 + 指针开销:8)
	// slice开销: 24字节 (ptr:8 + len:8 + cap:8)
	pointMemory := int64(16)
	sliceOverhead := int64(24)

	for index := int32(0); index < m.world.room.GetMapSize().X*m.world.room.GetMapSize().Y; index++ {
		area := m.world.GetArea(index)
		if area == nil || !area.IsInitialized() {
			continue
		}

		initializedAreas++
		// 估算：buildMapPoints(~81) + battleMapPoints(~77) + banPlacePawnPointMap(~40)
		totalPoints += 198 // 平均点位数
	}

	return map[string]int64{
		"initialized_areas":  initializedAreas,
		"total_points":       totalPoints,
		"points_memory_mb":   (totalPoints * pointMemory) / 1024 / 1024,
		"slice_overhead_mb":  (initializedAreas * sliceOverhead * 3) / 1024 / 1024, // 3个slice per area
		"estimated_total_mb": ((totalPoints * pointMemory) + (initializedAreas * sliceOverhead * 3)) / 1024 / 1024,
	}
}

// 全局Area内存管理器实例
var globalAreaMemoryManager *AreaMemoryManager

// 初始化全局Area内存管理器
func InitGlobalAreaMemoryManager(world *Model) {
	globalAreaMemoryManager = NewAreaMemoryManager(world)
	globalAreaMemoryManager.Start()
}

// 获取全局Area内存管理器
func GetGlobalAreaMemoryManager() *AreaMemoryManager {
	return globalAreaMemoryManager
}

// 停止全局Area内存管理器
func StopGlobalAreaMemoryManager() {
	if globalAreaMemoryManager != nil {
		globalAreaMemoryManager.Stop()
	}
}

// 获取Area内存使用报告
func GetAreaMemoryReport() map[string]interface{} {
	if globalAreaMemoryManager == nil {
		return map[string]interface{}{"error": "memory manager not initialized"}
	}

	stats := globalAreaMemoryManager.GetStats()
	memUsage := globalAreaMemoryManager.EstimateMemoryUsage()

	return map[string]interface{}{
		"stats":        stats,
		"memory_usage": memUsage,
		"timestamp":    time.Now().Unix(),
	}
}
