package player

import (
	"strings"
	"time"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
)

// 强化装备信息
type ForgeEquipInfo struct {
	StartTime  int64  `json:"start_time" bson:"start_time"` // 开始时间
	UID        string `json:"uid" bson:"uid"`
	ID         int32  `json:"id" bson:"id"`
	NeedTime   int32  `json:"need_time" bson:"need_time"` // 需要时间
	LockEffect int32  `json:"lock_effect" bson:"lock_effect"`
}

func (this *ForgeEquipInfo) GetEquipID() int32 {
	return int32(ut.Atoi(strings.Split(this.UID, "_")[0]))
}

func (this *Model) ToForgeEquipDataPb() *pb.ForgeEquipInfo {
	if this.CurrForgeEquip == nil {
		return nil
	}
	m := this.CurrForgeEquip
	return &pb.ForgeEquipInfo{
		Uid:         m.UID,
		NeedTime:    m.NeedTime,
		SurplusTime: int32(ut.MaxInt64(m.StartTime+int64(m.NeedTime)-time.Now().UnixMilli(), 0)),
		LockEffect:  m.LockEffect,
	}
}

func (this *Model) ToEquips() []*g.EquipInfo {
	return this.room.GetWorld().GetPlayerEquips(this.Uid)
}

func (this *Model) ToEquipsPb() []*pb.EquipInfo {
	arr := []*pb.EquipInfo{}
	equips := this.room.GetWorld().GetPlayerEquips(this.Uid)
	for _, v := range equips {
		arr = append(arr, v.ToPb())
	}
	return arr
}

func (this *Model) ToConfigPawnMap() map[int32]*g.PawnConfigInfo {
	return this.room.GetWorld().ToPlayerConfigPawnMap(this.Uid)
}

func (this *Model) ToConfigPawnMapPb() map[int32]*pb.PawnConfigInfo {
	return this.room.GetWorld().ToPlayerConfigPawnMapPb(this.Uid)
}

func (this *Model) GetEquipByUID(uid string) *g.EquipInfo {
	return this.room.GetWorld().GetPlayerEquipByUid(this.Uid, uid)
}

// 是否正在打造装备中
func (this *Model) IsForgeEquiping() bool {
	return this.CurrForgeEquip != nil
}

// 打造装备
func (this *Model) ForgeEquip(uid string, needTime, lockEffect int32, isRecast bool) {
	cd := this.GetEffectFloat(effect.FORGE_CD) // 这里享受 减少速度
	needTime = ut.MaxInt32(int32(float64(needTime)*1000.0*(1.0-cd*0.01)), 3000)
	this.CurrForgeEquip = &ForgeEquipInfo{
		UID:        uid,
		StartTime:  ut.Now(),
		NeedTime:   needTime,
		LockEffect: lockEffect,
	}
	// 添加玩家离线消息检测
	var msgType int32 = constant.OFFLINE_MSG_TYPE_FORGE
	if isRecast {
		msgType = constant.OFFLINE_MSG_TYPE_RESTORE_FORGE
	}
	this.room.GetWorld().AddCheckPlayerOfflineMsg(this.Uid, msgType, this.CurrForgeEquip.StartTime+int64(this.CurrForgeEquip.NeedTime), ut.String(this.CurrForgeEquip.GetEquipID()))
}

// 立即完成打造
func (this *Model) CompleteForge() {
	if this.CurrForgeEquip != nil {
		// 移除玩家离线消息检测
		this.room.GetWorld().RemoveCheckPlayerOfflineMsg(this.Uid, constant.OFFLINE_MSG_TYPE_FORGE, ut.String(this.CurrForgeEquip.GetEquipID()))
		this.room.GetWorld().RemoveCheckPlayerOfflineMsg(this.Uid, constant.OFFLINE_MSG_TYPE_RESTORE_FORGE, ut.String(this.CurrForgeEquip.GetEquipID()))
		this.CheckUpdateForgeEquip(this.CurrForgeEquip.StartTime + int64(this.CurrForgeEquip.NeedTime) + 10)
	}
}

// 刷新打造装备
func (this *Model) CheckUpdateForgeEquip(now int64) {
	if this.CurrForgeEquip == nil {
		return
	} else if now-this.CurrForgeEquip.StartTime < int64(this.CurrForgeEquip.NeedTime) {
		return
	}
	uid := this.CurrForgeEquip.UID
	lockEffect := this.CurrForgeEquip.LockEffect
	this.CurrForgeEquip = nil // 标记为nil
	equip := this.room.GetWorld().ForgePlayerEquip(this.Uid, uid, lockEffect)
	// 触发任务
	this.TriggerTask(tctype.EQUIP_RECAST_COUNT, this.room.GetWorld().GetPlayerTotalEquipRecastCount(this.Uid), 0)
	if equip.IsMaxAttr() {
		// 触发任务
		this.TriggerTask(tctype.EQUIP_ALL_ATTR_MAX, 1, 0)
	}
	// 通知
	this.PutNotifyQueue(constant.NQ_FORGE_EQUIP_RET, &pb.OnUpdatePlayerInfoNotify{Data_34: equip.ToPb()})
}

//////////////////////////////////////////////////////////熔炼//////////////////////////////////////////////////////////

// 熔炼装备信息
type SmeltEquipData struct {
	UID       string    `bson:"uid"`        // 主装备uid
	ViceAttrs [][]int32 `bson:"vice_attrs"` // 熔炼信息
	StartTime int64     `bson:"start_time"` // 开始时间
	NeedTime  int32     `bson:"need_time"`  // 需要时间
}

func (this *Model) ToSmeltEquipDataPb() *pb.SmeltEquipInfo {
	if this.CurSmeltEquipData == nil {
		return nil
	}
	m := this.CurSmeltEquipData
	return &pb.SmeltEquipInfo{
		Uid:         m.UID,
		ViceAttrs:   g.CloneEffectAttrsToPb(m.ViceAttrs),
		NeedTime:    m.NeedTime,
		SurplusTime: int32(ut.MaxInt64(m.StartTime+int64(m.NeedTime)-time.Now().UnixMilli(), 0)),
	}
}

// 是否正在融炼中
func (this *Model) IsInSmeltingEquiping() bool {
	return this.CurSmeltEquipData != nil
}

// 融炼
func (this *Model) SmeltEquip(mainEquip *g.EquipInfo, viceAttrs [][]int32) *pb.SmeltEquipInfo {
	this.CurSmeltEquipData = &SmeltEquipData{
		UID:       mainEquip.UID,
		ViceAttrs: viceAttrs,
		StartTime: ut.Now(),
		NeedTime:  constant.SMELTING_EQUIP_TIME, // 融炼时间
	}
	return this.ToSmeltEquipDataPb()
}

// 刷新融炼装备
func (this *Model) CheckUpdateSmeltEquip(now int64) {
	if this.CurSmeltEquipData == nil {
		return
	} else if int32(now-this.CurSmeltEquipData.StartTime) < this.CurSmeltEquipData.NeedTime {
		return
	}
	mainUid, viceAttrs := this.CurSmeltEquipData.UID, this.CurSmeltEquipData.ViceAttrs
	this.CurSmeltEquipData = nil // 标记为nil
	// 融炼完成
	equip := this.room.GetWorld().SmeltPlayerEquip(this.Uid, mainUid, viceAttrs)
	// 触发任务
	this.room.GetWorld().TriggerTask(this.Uid, tctype.SMELT_EQUIP, 1, equip.ID)
	// 通知
	if equip != nil {
		this.PutNotifyQueue(constant.NQ_SMELT_EQUIP_RET, &pb.OnUpdatePlayerInfoNotify{Data_64: equip.ToPb()})
	}
}
