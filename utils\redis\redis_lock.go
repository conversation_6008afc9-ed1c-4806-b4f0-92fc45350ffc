package rds

import (
	"context"
	"errors"
	"time"

	ut "slgsrv/utils"
)

// redis分布式锁
type RedisLock struct {
	key string
	val string
}

// 获取锁
func GetRedisLock(key string) (rdsLock *RedisLock, err error) {
	if Client == nil {
		return nil, errors.New("redis client nil")
	}
	key = "lock:" + key
	val := ut.ID()
	rst, err := Client.SetNX(context.Background(), key, val, time.Second*10).Result()
	if err == nil && rst {
		rdsLock = &RedisLock{
			key: key,
			val: val,
		}
	}
	return
}

// 释放锁
func (this *RedisLock) Release() {
	RdsEvalHashByCmd(RDS_SCRIPT_CMD_RELEASE_LOCK, []string{this.key}, this.val)
}
