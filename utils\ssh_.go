package ut

import (
	"crypto/ecdsa"
	"crypto/x509"
	"encoding/base64"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/huyangv/vmqant/log"
	"golang.org/x/crypto/ssh"
)

// ------------------------------ ssh相关 --------------------------------

// 远程执行脚本
func SshExcuteShell(remoteAddr, cmd string, args ...string) (rst string, err error) {
	log.Info("sshExcuteShell start remoteAddr: %v, cmd: %v, args: %v", remoteAddr, cmd, args)
	pemPath := "./jiuwanmu.pem"
	key, err := loadPEM(pemPath)
	if err != nil {
		log.Error("Failed to load PEM file: %v", err)
	}

	// SSH 连接配置
	config := &ssh.ClientConfig{
		User: "root",
		Auth: []ssh.AuthMethod{
			ssh.PublicKeys(key),
		},
		// Auth: []ssh.AuthMethod{
		// 	ssh.Password("password"),
		// },
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// 建立 SSH 连接
	conn, err := ssh.Dial("tcp", fmt.Sprintf("%s:22", remoteAddr), config)
	if err != nil {
		log.Error("Failed to dial: %v", err)
	}
	defer conn.Close()

	// 创建 SSH 会话
	session, err := conn.NewSession()
	if err != nil {
		log.Error("Failed to create session: %v", err)
	}
	defer session.Close()

	// 执行远程命令
	output, err := session.CombinedOutput(cmd + " " + escapeArgs(args))
	if err != nil {
		log.Error("Failed to execute command: %v", err)
	}

	// 输出命令执行结果
	rst = string(output)
	log.Info("sshExcuteShell rst: %v", rst)
	return
}

// 加载 PEM 文件
func loadPEM(pemPath string) (ssh.Signer, error) {
	pemBytes, err := os.ReadFile(pemPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read PEM file: %v", err)
	}

	signer, err := ssh.ParsePrivateKey(pemBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	return signer, nil
}

// 转义参数
func escapeArgs(args []string) string {
	escapedArgs := make([]string, len(args))
	for i, arg := range args {
		escapedArgs[i] = fmt.Sprintf("%q", arg)
	}
	return strings.Join(escapedArgs, " ")
}

// 根据x509证书字符串解析证书
func GetCertByCertStr(certStr string) (cert *x509.Certificate, err error) {
	// 解码Base64编码的证书字符串
	derBytes, err := base64.StdEncoding.DecodeString(certStr)
	if err != nil {
		log.Error("getpublicKeyByCertStr base64 certStr: %v err: %v", certStr, err)
		return
	}

	// 解析X.509证书
	cert, err = x509.ParseCertificate(derBytes)
	if err != nil {
		log.Error("getpublicKeyByCertStr cert parse certStr: %v err: %v", certStr, err)
		return
	}
	return
}

// 验证证书信任链 [终端证书 中间证书 根证书]
func VerifyCertChain(certChain []*x509.Certificate) error {
	if len(certChain) < 3 {
		return errors.New("certChain len error")
	}
	// 创建一个根证书池，用于验证证书链中的证书
	roots := x509.NewCertPool()
	roots.AddCert(certChain[2]) // 将根证书添加到根证书池

	// 验证证书链
	opts := x509.VerifyOptions{
		Roots:         x509.NewCertPool(),                      // 根证书池
		CurrentTime:   time.Now(),                              // 指定当前时间
		KeyUsages:     []x509.ExtKeyUsage{x509.ExtKeyUsageAny}, // 指定密钥用途（可根据需要调整）
		Intermediates: x509.NewCertPool(),                      // 中间证书池
	}
	opts.Roots.AddCert(certChain[2])         // 将根证书添加到根证书池
	opts.Intermediates.AddCert(certChain[1]) // 将中间证书添加到中间证书池

	// 验证证书
	_, err := certChain[0].Verify(opts)
	return err
}

// 解析JWS数据
func ParseJwsToken(signedPayload string, publicKey *ecdsa.PublicKey) (jwt.MapClaims, error) {
	// 解析JWS格式的推送信息
	retToken, err := jwt.Parse(signedPayload, func(token *jwt.Token) (interface{}, error) {
		return publicKey, nil
	})
	if err != nil {
		log.Error("ParseJwsToken reponse jwt err: %v", err)
		return nil, err
	}
	mapClaims := retToken.Claims.(jwt.MapClaims)
	if mapClaims == nil {
		errStr := "ParseJwsToken mapClaims nil"
		log.Error(errStr)
		err = errors.New(errStr)
		return nil, err
	}
	return mapClaims, nil
}
