package fsp

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/g"
)

type Building struct {
	Fighter
}

func (this *Building) Init(pawn g.Pawn, camp, roundCount int32, ctrl *FSPBattleController) *Building {
	this.Fighter.Init(pawn, camp, roundCount, ctrl)
	return this
}

func (this *Building) CheckBattleBeginTrigger() {
}

func (this *Building) Strip() *g.FigherStrip {
	isFalg := this.IsFlag()
	return &g.FigherStrip{
		Uid:         this.GetUID(),
		Id:          this.GetID(),
		Lv:          this.GetLV(),
		EnterDir:    this.entity.GetEnterDir(),
		Camp:        this.camp,
		AttackIndex: this.attackIndex,
		RoundCount:  this.roundCount,
		AttackCount: this.attackCount,
		Point:       this.entity.GetPoint(),
		Hp:          []int32{this.entity.GetCurHP(), this.entity.GetMaxHP()},
		Owner:       this.entity.GetOwner(),
		IsFalg:      isFalg,  // 是否军旗
		IsNoncombat: !isFalg, // 目前只要不是军旗就是其他非战斗单位
	}
}

func (this *Building) ToPb() *pb.FighterInfo {
	isFalg := this.IsFlag()
	return &pb.FighterInfo{
		Uid:         this.GetUID(),
		Id:          int32(this.GetID()),
		Lv:          int32(this.GetLV()),
		EnterDir:    int32(this.entity.GetEnterDir()),
		Camp:        int32(this.camp),
		AttackIndex: int32(this.attackIndex),
		RoundCount:  int32(this.roundCount),
		AttackCount: int32(this.attackCount),
		Point:       pb.NewVec2(this.entity.GetPoint()),
		Hp:          []int32{int32(this.entity.GetCurHP()), int32(this.entity.GetMaxHP())},
		Owner:       this.entity.GetOwner(),
		IsFalg:      isFalg,
		IsNoncombat: !isFalg,
	}
}

func (this *Building) ToDB() *g.FigherStrip {
	return this.Strip()
}

func (this *Building) IsPawn() bool  { return false }
func (this *Building) IsTower() bool { return false }
func (this *Building) IsBuild() bool { return true }
func (this *Building) IsFlag() bool  { return this.entity != nil && this.entity.GetID() == 3601 } // 是否军旗
func (this *Building) IsHero() bool  { return false }

func (this *Building) InitSkillAttackAnimTime() {
}
