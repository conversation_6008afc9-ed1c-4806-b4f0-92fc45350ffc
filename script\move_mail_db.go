package main

// 3.0版本移动并整理邮件表
// func main() {
// 	log.Info("move mail db start")
// 	// 连接游戏服数据库
// 	clientGame, err := mongo.NewClient(options.Client().ApplyURI(gameDbUri))
// 	if err != nil {
// 		log.Error("parse gameDbUri err: %v", err)
// 	}
// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()
// 	err = clientGame.Connect(ctx)
// 	if err != nil {
// 		log.Error("connect gameDb err: %v", err)
// 	}
// 	defer clientGame.Disconnect(ctx)
// 	gameDb := clientGame.Database(gameDbName)

// 	// 连接大厅服数据库
// 	clientLobby, err := mongo.NewClient(options.Client().ApplyURI(lobbyDbUri))
// 	if err != nil {
// 		log.Error("parse lobbyDbUri err: %v", err)
// 	}
// 	err = clientLobby.Connect(ctx)
// 	if err != nil {
// 		log.Error("connect lobbyDb err: %v", err)
// 	}
// 	defer clientLobby.Disconnect(ctx)
// 	lobbyDb := clientLobby.Database(lobbyDbName)

// 	// 遍历所有房间 删除已关服的邮件
// 	cur, e := gameDb.Collection(slg.DB_COLLECTION_NAME_ROOM).Find(context.TODO(), bson.D{})
// 	defer func() {
// 		_ = cur.Close(context.TODO())
// 	}()
// 	if e == nil {
// 		var list []r.RoomTableData
// 		cur.All(context.TODO(), &list)
// 		now := ut.Now()
// 		for _, v := range list {
// 			if v.GameOverInfo != nil && now-v.GameOverInfo.EndTime >= ut.TIME_DAY {
// 				// 游戏服已关闭则删除相关邮件
// 				gameDb.Collection(slg.DB_COLLECTION_NAME_MAIL).DeleteMany(context.TODO(), bson.M{"sid": v.Id})
// 			}
// 		}
// 	}

// 	// 遍历游戏服邮件表并移到大厅服数据库
// 	cursor, e := gameDb.Collection(slg.DB_COLLECTION_NAME_MAIL).Find(context.TODO(), bson.D{})
// 	defer func() {
// 		_ = cursor.Close(context.TODO())
// 	}()
// 	count := 0
// 	if e == nil {
// 		var list []mail.Mail
// 		cursor.All(context.TODO(), &list)
// 		total := len(list)
// 		log.Info("start move mail total: %v", total)
// 		for _, v := range list {
// 			// gameDb.Collection(slg.DB_COLLECTION_NAME_MAIL).DeleteOne(context.TODO(), bson.M{"uid": v.UID})
// 			mail := &v
// 			// 兼容字段
// 			mail.ReadList = []string{}
// 			if mail.IsRead != "" {
// 				arr := strings.Split(mail.IsRead, "|")
// 				for _, uid := range arr {
// 					mail.ReadList = append(mail.ReadList, uid)
// 				}
// 				mail.IsRead = ""
// 			}
// 			mail.ClaimList = []string{}
// 			if mail.IsClaim != "" {
// 				arr := strings.Split(mail.IsClaim, "|")
// 				for _, uid := range arr {
// 					mail.ClaimList = append(mail.ClaimList, uid)
// 				}
// 				mail.IsClaim = ""
// 			}
// 			lobbyDb.Collection(slg.DB_COLLECTION_NAME_MAIL).InsertOne(context.TODO(), mail)
// 			count++
// 			if count%100 == 0 {
// 				log.Info("cur count: %v, total: %v", count, total)
// 			}
// 		}
// 	}
// 	log.Info("move mail db finish!")
// }
