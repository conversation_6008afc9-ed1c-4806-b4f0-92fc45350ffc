package g

import (
	"slgsrv/server/game/common/config"
	ut "slgsrv/utils"
)

// 一个政策信息
type PolicyInfo struct {
	ID   int32 `json:"id" bson:"id"`
	Type int32
}

func NewPolicyInfo(id int32) *PolicyInfo {
	if id <= 0 {
		return nil
	}
	json := config.GetJsonData("policy", id)
	if json == nil {
		return nil
	}
	info := &PolicyInfo{ID: id}
	info.Type = ut.Int32(json["type"])
	return info
}

func InitPolicyByDB(policys map[int32]*PolicyInfo) map[int32]*PolicyInfo {
	ret := map[int32]*PolicyInfo{}
	for k, m := range policys {
		if json := config.GetJsonData("policy", m.ID); json != nil {
			m.Type = ut.Int32(json["type"])
			ret[k] = m
		}
	}
	return ret
}

func (this *PolicyInfo) GetEffect() int32 {
	return this.Type
}

func (this *PolicyInfo) GetUseCond() interface{} {
	json := config.GetJsonData("policy", this.ID)
	if json == nil {
		return nil
	}
	return json["use_cond"]
}
