package rds

import (
	"fmt"

	slg "slgsrv/server/common"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
)

const (
	LOBBY_ASSIGN_MIN_NUM    = 100                // 大厅服负载均衡最少人数
	LOBBY_ASSIGN_MAX_NUM    = 10000              // 大厅服负载均衡大厅人数上限
	CHAT_STORE_MAX_NUM      = 50                 // 聊天保存最大数量
	USER_MALLOC_LOCK_EXPIRE = ut.TIME_SECOND * 5 // 玩家大厅服分配锁超时时间

	GAME_PAWN_USE_ROOM_COUNT = 20 // 士兵使用统计采样区服数量
)

const (
	RDS_SCRIPT_CMD_UNKONW               = iota
	RDS_SCRIPT_CMD_LOBBY_LOAD_UPDATE    // 更新大厅服负载
	RDS_SCRIPT_CMD_LOBBY_GET_TEAM_LID   // 获取队伍所在大厅服id
	RDS_SCRIPT_CMD_CHAT_STORE           // 聊天保存
	RDS_SCRIPT_CMD_RELEASE_LOCK         // 释放锁
	RDS_SCRIPT_CMD_SET_USER_LID         // 设置玩家大厅服id
	RDS_SCRIPT_CMD_SET_USER_STATE       // 设置玩家状态
	RDS_SCRIPT_CMD_SET_TEAM_LID         // 设置队伍大厅服id
	RDS_SCRIPT_CMD_GET_USER_MALLOC_LOCK // 获取玩家大厅服分配锁是否生效
	RDS_SCRIPT_CMD_TEST                 // 测试
	RDS_SCRIPT_CMD_ADD_ROOM_PAWN_USE    // 添加新的士兵使用统计的区服
)

var scriptCmdMap = map[int]string{
	RDS_SCRIPT_CMD_LOBBY_LOAD_UPDATE:    LOBBY_LOAD_UPDATE,
	RDS_SCRIPT_CMD_LOBBY_GET_TEAM_LID:   LOBBY_GET_TEAM_LID_SCRIPT,
	RDS_SCRIPT_CMD_CHAT_STORE:           CHAT_STORE_SCRIPT,
	RDS_SCRIPT_CMD_RELEASE_LOCK:         RELEASE_LOCK_SCRIPT,
	RDS_SCRIPT_CMD_SET_USER_LID:         SET_USER_LID_SCRIPT,
	RDS_SCRIPT_CMD_SET_USER_STATE:       SET_USER_STATE_SCRIPT,
	RDS_SCRIPT_CMD_SET_TEAM_LID:         SET_TEAM_LID_SCRIPT,
	RDS_SCRIPT_CMD_GET_USER_MALLOC_LOCK: GET_USER_MALLOC_LOCK,
	RDS_SCRIPT_CMD_TEST:                 SCRIPT_TEST,
	RDS_SCRIPT_CMD_ADD_ROOM_PAWN_USE:    ADD_ROOM_PAWN_USE,
}

var scriptHashMap = ut.NewMapLock[int, string]()

// 初始化redis脚本
func InitRdsScript() {
	for cmd, script := range scriptCmdMap {
		hash, err := RdsLoadScript(script)
		if err != nil {
			log.Error("InitRdsScript cmd: %v, err: %v", cmd, err)
		} else {
			scriptHashMap.Set(cmd, hash)
		}
	}
}

// 大厅服负载更新
var LOBBY_LOAD_UPDATE = `
local lobbyLoadKey = KEYS[1]
local lid = ARGV[1]
local num = tonumber(ARGV[2])
-- 设置大厅服负载
local loadNum = redis.call('hget', lobbyLoadKey, lid)
if loadNum then
    loadNum = loadNum + num    
else
    loadNum = num
end
if loadNum < 0 then
    loadNum = 0
end
redis.call('hset', lobbyLoadKey, lid, loadNum)
return lid
`

// 设置玩家大厅服id
var SET_USER_LID_SCRIPT = `
local key = KEYS[1]
local uid = ARGV[1]
local curLid = ARGV[2]
local lid = redis.call('hget', key, uid)
if lid then
    -- 该用户已在其他大厅服
    return lid
else
    -- 没在其他大厅则设置为分配的大厅服
    lid = curLid
    -- 设置用户大厅服id
    redis.call('hset', key, uid, lid)
    return lid
end
`

// 离线设置玩家状态
var SET_USER_STATE_SCRIPT = fmt.Sprintf(`
local key = KEYS[1]
local uid = ARGV[1]
local oldState = redis.call('hget', key, uid)
if oldState and (oldState == %v or oldState == %v) then
    -- 之前状态为在线或者内存中 则可设置为离线
    redis.call('hdel', key, uid)
    return 1
else
    -- 否则不处理
    return 0
end
`, slg.USER_STATE_ONLINE, slg.USER_STATE_IN_CACHE)

// 获取玩家大厅服分配锁是否生效
var GET_USER_MALLOC_LOCK = fmt.Sprintf(`
local listKey = KEYS[1]
local now = ARGV[1]
local listLength = redis.call('LLEN', listKey)
local timeExpire = %v
if listLength == 0 then
    -- 长度为0 锁不生效
    return 0
end
-- 获取最新的时间戳判断是否过期
local lastTime = redis.call('LINDEX', listKey, listLength - 1)
if lastTime + timeExpire < tonumber(now) then
    -- 已过期则不生效 移除列表
    redis.call('DEL', listKey)
    return 0
end
return 1
`, USER_MALLOC_LOCK_EXPIRE)

// 获取队伍所在大厅服id
var LOBBY_GET_TEAM_LID_SCRIPT = `
local teamLidKey = KEYS[1]
local uid = ARGV[1]
local curLid = ARGV[2]
local lid = redis.call('hget', teamLidKey, uid)
if lid then
    -- 该队伍已在其他大厅服
    return lid
else
    -- 没在其他大厅则设置为该大厅服
    lid = curLid
    -- 设置用户大厅服id
    redis.call('hset', teamLidKey, uid, lid)
    return lid
end
`

// 设置队伍大厅服id
var SET_TEAM_LID_SCRIPT = `
local key = KEYS[1]
local curLid = ARGV[1]
local uid = ARGV[2]
local lid = redis.call('hget', key, uid)
if lid then
    -- 该队伍已在其他大厅服
    return lid
else
    -- 没在其他大厅则设置为分配的大厅服
    lid = curLid
    -- 设置队伍大厅服id
    redis.call('hset', key, uid, lid)
    return lid
end
`

// 聊天保存
var CHAT_STORE_SCRIPT = fmt.Sprintf(`
local channelKey = KEYS[1]
local chatData = ARGV[1]
local len = redis.call('llen', channelKey)
if len then
    -- 没有数据直接插入
    redis.call('lpush', channelKey, chatData)
else
    if len >= %v then
        -- 超过数量限制从头部移除一个
        redis.call('rpop', channelKey)
    end
    -- 从尾部插入
    redis.call('lpush', channelKey, chatData)
end
`, CHAT_STORE_MAX_NUM)

// 释放锁脚本
var RELEASE_LOCK_SCRIPT = `
local key = KEYS[1]
local val = ARGV[1]
local rst = redis.call('get', key)
if rst and rst == val then
    redis.call('del', key)
    return 1
end
return 0
`

var SCRIPT_TEST = `
local uid = KEYS[1]
-- 设置登录锁
local rst = redis.call('set', uid .. '_login', 1, 'ex', 5, 'nx')
if rst == nil then
    return 0
end
return 1
`

// 添加新的士兵使用统计的区服
var ADD_ROOM_PAWN_USE = fmt.Sprintf(`
local key = KEYS[1]
local useData = ARGV[1]
local len = tonumber(redis.call('llen', key))
if len >= %v then
    -- 超过数量限制从右边移除一个
    redis.call('rpop', key)
    len = len - 1
end
-- 从左边插入
redis.call('lpush', key, useData)
len = len + 1
return len
`, GAME_PAWN_USE_ROOM_COUNT)

// // 用户登录脚本
// var USER_LOGIN_SCRIPT = fmt.Sprintf(`
// local uid = KEYS[1]
// local lobbyLoadKey = 'lobby_load_map'
// local lobbyMinCfg = %v
// local lobbyMaxCfg = %v
// local lid = redis.call('hget', uid, 'lid')
// -- 用户已在大厅 则直接返回大厅id
// if lid then
//     return lid
// end
// local lobbyNum = redis.call('hlen', lobbyLoadKey)
// if lobbyNum == 0 then
//     return 0
// end
// -- 获取大厅服负载map
// local lobbyLoadArr = redis.call('hgetall', lobbyLoadKey)
// if lobbyLoadArr then
//     local maxNum = 0
//     local maxLid = 0
//     local minNum = lobbyMaxCfg
//     local minLid = 0
//     -- 大厅服负载均衡
//     for i = 1, #lobbyLoadArr, 2 do
//         local key = lobbyLoadArr[i]
//         local value = lobbyLoadArr[i+1]
//         local userNum = tonumber(value)
//         if lobbyNum == 1 and userNum < lobbyMaxCfg then
//             -- 只有一个大厅服且未达到上限
//             lid = key
//             break
//         end
//         if userNum > maxNum then
//             maxNum = userNum
//             maxLid = key
//         elseif maxLid == 0 and maxNum == 0 then
//             maxLid = key
//         end
//         if userNum < minNum then
//             minNum = userNum
//             minLid = key
//         end
//     end
//     if lid == 0 then
//         if maxNum < lobbyMinCfg then
//             -- 所有服都未达到大厅服最小人数 则返回负载最大的大厅服id
//             lid = maxLid
//         else
//             -- 否则返回负载最小的大厅服id
//             lid = minLid
//         end
//     end
//     -- 设置用户大厅服id
//     if lid ~= 0 then
//         redis.call('hset', uid, 'lid', lid)
//     end
//     return lid
// else
//     return 0
// end
// `, LOBBY_ASSIGN_MIN_NUM, LOBBY_ASSIGN_MAX_NUM)
