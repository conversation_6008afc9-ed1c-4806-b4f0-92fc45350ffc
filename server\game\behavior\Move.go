package behavior

import (
	"math"

	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/hero"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

type Move struct {
	BaseAction

	isChangeFighterState bool // 是否改变士兵的状态了
}

func (this *Move) OnOpen() {
	this.SetTreeBlackboardData("isMove", true) // 记录已经移动过了
	this.isChangeFighterState = false
}

func (this *Move) OnTick(dt int32) Status {
	if this.target.GetAttackTarget() == nil {
		return FAILURE
	}
	paths, _ := this.GetBlackboardData("paths").([]*ut.Vec2)
	needMoveTime := ut.Int32(this.GetBlackboardData("needMoveTime"))
	if paths == nil {
		paths = this.searchPath(this.target.GetAttackTarget())
		this.SetBlackboardData("paths", paths)
		needMoveTime = helper.GetMoveNeedTime(paths, this.target.GetEntity().GetMoveVelocity())
		this.SetBlackboardData("needMoveTime", needMoveTime)
	}
	pathLen := len(paths)
	if pathLen == 0 {
		return SUCCESS
	}
	// 一直检测时间
	currMoveTime := ut.Int32(this.GetBlackboardData("currMoveTime"))
	if currMoveTime >= needMoveTime {
		movePathLen := ut.Max(0, pathLen-1)
		// this.SetTreeBlackboardData("movePathLen", movePathLen)
		// 夏侯渊
		if heroSkill := this.target.CheckPortrayalSkill(hero.XIA_HOUYUAN); heroSkill != nil {
			buff := this.target.GetBuffOrAdd(bufftype.LONG_RANGE_RAID, this.target.GetUID())
			if maxVal := heroSkill.GetValue(); buff.Value < maxVal {
				buff.Value = math.Min(buff.Value+float64(movePathLen), maxVal)
			}
		}
		this.target.SetPoint(paths[pathLen-1])
		this.target.ChangeState(constant.PAWN_STATE_STAND)
		this.GetCtrl().UpdateFighterPointMap()
		return SUCCESS
	}
	this.SetBlackboardData("currMoveTime", currMoveTime+dt)
	// 设置士兵状态 更新视图信息
	if !this.isChangeFighterState {
		this.isChangeFighterState = true
		this.target.ChangeState(constant.PAWN_STATE_MOVE)
	}
	return RUNNING
}

// 搜索路径
func (this *Move) searchPath(attackTarget g.IFighter) []*ut.Vec2 {
	uid := attackTarget.GetUID()
	data := array.Find(this.target.GetCanAttackTargets(), func(m g.CanAttackTargetInfo) bool { return m.UID == uid })
	if len(data.Paths) > 0 {
		return data.Paths
	}
	return []*ut.Vec2{} // 到不了 就不动了
}
