package chat

import (
	slg "slgsrv/server/common"
	ut "slgsrv/utils"
)

func (this *Chat) InitHttp() {
	// 获取大厅聊天
	this.GetServer().RegisterGO("/http/getLobbyChats", this.httpGetLobbyChats)
	// 获取聊天频道队伍队伍列表
	this.GetServer().RegisterGO("/http/getChatTeamList", this.httpGetChatTeamList)
	// 删除指定聊天
	this.GetServer().RegisterGO("/http/delLobbyChatByUid", this.httpDelLobbyChatByUid)
}

func (this *Chat) httpGetLobbyChats(channel, index, size, startTime, endTime, filterUid string) (ret map[string]interface{}, err error) {
	arr := GetLobbyChatsByChannel(channel, ut.Int(index), ut.Int(size), ut.Int(startTime), ut.Int(endTime), filterUid)
	return slg.HttpResponseSuccessWithDataNoDesc(arr), nil
}

func (this *Chat) httpGetChatTeamList(uid string) (map[string]interface{}, error) {
	arr := GetChatTeamList(uid)
	return slg.HttpResponseSuccessWithDataNoDesc(arr), nil
}

func (this *Chat) httpDelLobbyChatByUid(channel, uid string) (map[string]interface{}, error) {
	rst := DelLobbyChatByUid(channel, uid)
	if !rst {
		return slg.HttpResponseErrorNoDataWithDesc("删除失败"), nil
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}
