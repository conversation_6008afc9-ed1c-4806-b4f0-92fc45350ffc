package behavior

import (
	"slgsrv/utils"
)

// 优先顺序选择
type Priority struct {
	BaseComposite
}

func (this *Priority) OnOpen() {
	this.SetBlackboardData("startIndex", 0)
}

func (this *Priority) OnTick(dt int32) Status {
	startIndex := ut.Int(this.GetBlackboardData("startIndex"))
	for i, l := startIndex, this.GetChildrenCount(); i < l; i++ {
		state := this.children[i].Execute(dt)
		if state == FAILURE {
			continue
		} else if state == RUNNING {
			this.SetBlackboardData("startIndex", i) // 这里记录运行中的 下次继续从这里执行
		}
		return state
	}
	return FAILURE
}
