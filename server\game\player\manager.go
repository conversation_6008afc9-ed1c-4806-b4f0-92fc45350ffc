package player

import (
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"

	"go.mongodb.org/mongo-driver/mongo"

	"github.com/huyangv/vmqant/log"
)

// 玩家管理中心
type Manager struct {
	db   *Mongodb
	sid  int32
	Pers int32 // 人数
}

func NewManager(sid int32) *Manager {
	return &Manager{
		sid:  sid,
		db:   &Mongodb{"player_" + g.ServerIdToString(sid)},
		Pers: 0,
	}
}

func (this *Manager) FindByUid(room g.Room, uid string) *Model {
	now := ut.Now()
	data, err := this.db.FindByUid(uid)
	if dt := ut.Now() - now; dt > 1000 {
		log.Info("FindByUid uid: %v, dt: %v", uid, dt)
	}
	if err == "" {
		return NewModelByDB(room, data)
	} else {
		// log.Error("player.FindByUid! " + err)
	}
	return nil
}

// 创建一个玩家
func (this *Manager) InsertOne(data TableData) (err string) {
	if err = this.db.InsertOne(data); err != "" {
		log.Error("player InsertOne error. " + err)
	} else {
		this.Pers += 1 // 添加一个玩家
	}
	return
}

// 更新一条
func (this *Manager) UpdateOne(uid string, data TableData) (err string) {
	if err = this.db.UpdateOne(uid, data); err != "" {
		log.Error("player UpdateOne error. " + err)
	} else {
		// log.Info("player UpdateOne uid=" + uid + ", name=" + data.Nickname)
	}
	return
}

// 删除一条
func (this *Manager) DeleteOne(uid string) (err string) {
	if err = this.db.DeleteOne(uid); err != "" {
		log.Error("player DeleteOne error. " + err)
	} else {
		this.Pers -= 1
	}
	return
}

// 查找所有玩家
func (this *Manager) Find() map[string]TableData {
	list, err := this.db.Find()
	if err != "" {
		log.Error("player Find error. " + err)
		list = map[string]TableData{}
	}
	this.Pers = int32(len(list))
	return list
}

// GetDbCollection 获取db链接
func (this *Manager) GetDbCollection() *mongo.Collection {
	return this.db.getCollection()
}
