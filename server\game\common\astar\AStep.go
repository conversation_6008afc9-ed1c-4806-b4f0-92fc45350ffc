package astar

import (
	"sync"

	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 一个节点
type AStep struct {
	parent   *AStep
	points   []*ut.Vec2
	last     *ut.Vec2
	pathLens []int32

	M int32 // 路径总长度
	G int32 // 总次数
	F int32
}

// AStep对象池
var aStepPool = sync.Pool{
	New: func() interface{} { return &AStep{} },
}

func NewAStep() *AStep {
	return aStepPool.Get().(*AStep)
}

// 清理对象 并放回对象池
func (this *AStep) Clean() {
	this.parent = nil
	this.points = nil
	this.last = nil
	this.pathLens = nil
	this.M = 0
	this.G = 0
	this.F = 0
	aStepPool.Put(this)
}

func (this *AStep) ID() string {
	return this.last.ID()
}

func (this *AStep) ToString() {
	// cc.log(this.points.map(m => m.ID()), 'F=' + this.F, 'G=' + this.G, 'M=' + this.M)
}

func (this *AStep) Init(node *AStarNode, parent *AStep) *AStep {
	this.parent = parent
	this.last = node.point.Clone()
	if parent != nil {
		this.G = parent.G + 1
	} else {
		this.G = 0
	}
	this.F = node.F
	this.points = []*ut.Vec2{}
	for node.parent != nil {
		this.points = append(this.points, node.point)
		node = node.parent
	}
	if node != nil {
		this.points = append(this.points, node.point)
	}
	this.points = array.Reverse(this.points)
	this.M = int32(len(this.points)) - 1
	if parent != nil {
		this.M += parent.M
	}
	this.initPathLens()
	return this
}

func (this *AStep) initPathLens() {
	this.pathLens = []int32{}
	step := this
	for step.parent != nil {
		this.pathLens = append(this.pathLens, int32(len(step.points))-1)
		step = step.parent
	}
	this.pathLens = array.Reverse(this.pathLens)
}

// 比较两个
func (this *AStep) Equals(step *AStep) int32 {
	if this.M != step.M {
		return this.M - step.M
	}
	// 这里需要从第一个开始比较 越往前长度越大 越好
	return this.getPointsW() - step.getPointsW()
}

func (this *AStep) getPointsW() int32 {
	var w, i int32 = 1, 0
	// 最后一个权重倒着算 因为如果最后一个路径最短最好 前面的路径最长越好
	for l := int32(len(this.pathLens)) - 1; i < l; i++ {
		w = w*10 + this.pathLens[i]
	}
	w = w*10 + (10 - this.pathLens[i])
	return w
}

// 判断前面的是否都走满了的 最后一个可以没有满
func (this *AStep) IsPerfectPath(moveRange int32) bool {
	for i, l := 0, len(this.pathLens)-1; i < l; i++ {
		if this.pathLens[i] != moveRange {
			return false
		}
	}
	return true
}
