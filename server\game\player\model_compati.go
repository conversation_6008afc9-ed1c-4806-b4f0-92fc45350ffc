package player

import (
	slg "slgsrv/server/common"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

const VERSION = 24

// 兼容 英雄技能效果
func (this *Model) compatiPortrayalAttr(id int32, oMin, oMax, nMin, nMax float64) {
	wld := this.room.GetWorld()
	slots := wld.GetPlayerHeroSlots(this.Uid)
	slot := array.Find(slots, func(m *g.HeroSlotInfo) bool { return m.GetHeroID() == id })
	if slot != nil {
		portrayal := slot.Hero
		attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
		if attr != nil {
			attr[2] = slg.CompatiValueInt32(oMin, oMax, nMin, nMax, attr[2])
			portrayal.UpdateAttr()
			wld.UpdatePlayerHeroAttr(this.Uid, portrayal.ID, portrayal.Attrs)
		}
	}
}

// 兼容 装备效果
func (this *Model) compatiEquipAttr(id int32, oMin, oMax, nMin, nMax float64) {
	if equip := this.room.GetWorld().GetPlayerEquip(this.Uid, id); equip != nil {
		attr := array.Find(equip.Attrs, func(arr []int32) bool { return arr[0] == 2 })
		if attr != nil {
			attr[2] = slg.CompatiValueInt32(oMin, oMax, nMin, nMax, attr[2])
			equip.UpdateAttr()
		}
		attr = array.Find(equip.LastAttrs, func(arr []int32) bool { return arr[0] == 2 })
		if attr != nil {
			attr[2] = slg.CompatiValueInt32(oMin, oMax, nMin, nMax, attr[2])
		}
	}
}

// 兼容 只有概率的装备效果
func (this *Model) compatiEquipOdds(id int32, oMin, oMax, nMin, nMax float64, oIndex int) {
	if equip := this.room.GetWorld().GetPlayerEquip(this.Uid, id); equip != nil {
		attrIndex := array.FindIndex(equip.Attrs, func(arr []int32) bool { return arr[0] == 2 })
		if attrIndex != -1 {
			attr := equip.Attrs[attrIndex]
			if len(attr) == 3 {
				equip.Attrs[attrIndex] = append(equip.Attrs[attrIndex], 0)
				attr = equip.Attrs[attrIndex]
			}
			attr[3] = slg.CompatiValueInt32(oMin, oMax, nMin, nMax, attr[oIndex])
			attr[2] = 0
			equip.UpdateAttr()
		}
		attrIndex = array.FindIndex(equip.LastAttrs, func(arr []int32) bool { return arr[0] == 2 })
		if attrIndex != -1 {
			attr := equip.LastAttrs[attrIndex]
			if len(attr) == 3 {
				equip.LastAttrs[attrIndex] = append(equip.LastAttrs[attrIndex], 0)
				attr = equip.LastAttrs[attrIndex]
			}
			attr[3] = slg.CompatiValueInt32(oMin, oMax, nMin, nMax, attr[oIndex])
			attr[2] = 0
		}
	}
}

func (this *Model) Compati(ver int32) {
	if ver == 18 || ver == 19 {
		// 兼容 周瑜
		this.compatiPortrayalAttr(310302, 10, 30, 20, 40)
		// 兼容 秦琼
		this.compatiPortrayalAttr(310603, 50, 100, 20, 30)
		// 兼容 裴行俨
		this.compatiPortrayalAttr(340402, 10, 20, 20, 30)
		// 兼容 养由基
		this.compatiPortrayalAttr(330401, 10, 20, 200, 300)
		// 兼容 孙权
		this.compatiPortrayalAttr(330402, 50, 100, 5, 15)
		this.Compati(20)
		return
	} else if ver == 20 {
		// 兼容 养由基
		this.compatiPortrayalAttr(330401, 200, 300, 100, 150)
	} else if ver == 21 {
		// 兼容 养由基
		this.compatiPortrayalAttr(330401, 100, 150, 10, 30)
		// 兼容 邓艾
		this.compatiPortrayalAttr(320502, 5, 10, 10, 30)
	} else if ver == 22 {
		// 兼容 姜维
		this.compatiPortrayalAttr(310501, 5, 15, 5, 10)
		// 兼容 张辽
		this.compatiPortrayalAttr(340101, 10, 30, 20, 50)
		// 兼容 霍去病
		this.compatiPortrayalAttr(340102, 50, 100, 5, 10)
		// 兼容医馆通知
		this.room.GetWorld().SetHospitalFullNoticeFlag(this.Uid, int32(constant.INJURY_PAWN_MAX_COUNT))
	} else if ver == 23 {
		// 供奉英雄的兵种 添加到额外士兵槽位
		heroPawnMap := map[int32]bool{}
		heroSlots := this.ToHeroSlotsPb()
		for _, v := range heroSlots {
			if v.Hero == nil {
				continue
			}
			json := config.GetJsonData("portrayalBase", v.Hero.Id)
			if json == nil {
				continue
			}
			pawnId := ut.Int32(json["avatar_pawn"])
			if heroPawnMap[pawnId] {
				continue
			}
			heroPawnMap[pawnId] = true
			// 供奉英雄解锁士兵
			extraSlotLv := int32(constant.PAWN_CERI_SLOT_EXTRA_INIT_ID + v.Lv)
			extraSlot := this.PawnCeriSlots.GetSlotByLv(extraSlotLv)
			if extraSlot == nil {
				extraSlot = this.PawnCeriSlots.AddCeriSlot(extraSlotLv)
			}
			extraSlot.ID = pawnId
		}

		// 研究槽位默认添加长枪、刀盾、剑骑
		pawnCeriArr := []*CeriSlotInfo{{Type: constant.CERI_CONF_TYPE_PAWN, value: 3101}, {Type: constant.CERI_CONF_TYPE_PAWN, value: 3201}, {Type: constant.CERI_CONF_TYPE_PAWN, value: 3401}}
		// 研究槽位默认添加铁剑和头盔
		equipCeriArr := []*CeriSlotInfo{{Type: constant.CERI_CONF_TYPE_EQUIP, value: 6001}, {Type: constant.CERI_CONF_TYPE_EQUIP, value: 6002}}
		for _, ceri := range this.CeriSlots {
			if ceri.value == 0 {
				continue
			}
			if ceri.Type == constant.CERI_CONF_TYPE_PAWN && ceri.value != 3502 && !heroPawnMap[ceri.value] { // 解锁士兵(排除弩车)
				pawnCeriArr = append(pawnCeriArr, ceri)
			} else if ceri.Type == constant.CERI_CONF_TYPE_EQUIP || ceri.Type == 4 { // 解锁的装备和专武
				equipCeriArr = append(equipCeriArr, ceri)
			}
		}

		//兼容研究所士兵
		for i := 0; i < len(pawnCeriArr); i++ {
			id := pawnCeriArr[i].value
			if heroPawnMap[id] {
				continue
			}
			lv := config.GetPawnNeedLv(id)
			if lv <= 0 {
				continue
			}
			slotInfo := this.PawnCeriSlots.GetSlotByLv(lv)
			if slotInfo == nil {
				slotInfo = this.PawnCeriSlots.AddCeriSlot(lv)
			}
			if slotInfo.ID == 0 {
				// 该槽位未研究士兵 添加到研究列表
				slotInfo.ID = id
				pawnCeriArr = append(pawnCeriArr[:i], pawnCeriArr[i+1:]...)
				i-- // 由于删除了当前元素，需要回退索引
			}
		}
		if len(pawnCeriArr) > 0 {
			// 研究所还剩余士兵为兼容 添加到额外解锁列表
			for _, v := range pawnCeriArr {
				this.AddExtraUnlockPawn(v.value)
			}
		}

		// 兼容研究所装备 先兼容已打造的装备
		equips := this.room.GetWorld().GetPlayerEquips(this.Uid)
		for _, equip := range equips {
			lv := equip.GetSlotLv()
			if lv <= 0 || lv > 20 {
				continue
			}
			slotInfo := this.EquipCeriSlots.AddCeriSlot(lv)
			slotInfo.ID = equip.ID
			// 已打造的装备从研究列表移除
			equipCeriArr = array.Delete(equipCeriArr, func(m *CeriSlotInfo) bool { return m.value == equip.ID })
		}
		// 再遍历每个槽位等级 从研究列表中按顺序取出赋值
		for lv := int32(1); lv <= 20; lv++ {
			if _, ok := constant.EQUIP_SLOT_CONF_MAP[lv]; !ok {
				continue
			}
			if len(equipCeriArr) == 0 {
				break
			}

			slotInfo := this.EquipCeriSlots.GetSlotByLv(lv)
			if slotInfo != nil && slotInfo.ID != 0 {
				continue
			}
			slotInfo = this.EquipCeriSlots.AddCeriSlot(lv)
			// 按顺序从研究所中取出已研究的装备放到新的装备研究槽位中
			var curType int32 = constant.CERI_CONF_TYPE_EQUIP
			if _, ok := constant.EXCLUSIVE_EQUIP_SLOT_CONF_MAP[lv]; ok {
				curType = 4
			}
			for i := 0; i < len(equipCeriArr); i++ {
				ceriInfo := equipCeriArr[i]
				if ceriInfo.Type == curType {
					// 装备槽位类型和研究所的类型一致时赋值
					slotInfo.ID = ceriInfo.value
					equipCeriArr = append(equipCeriArr[:i], equipCeriArr[i+1:]...)
					break
				}
			}
		}
		// 再兼容一下装备的uid
		for _, equip := range equips {
			lv := equip.GetSlotLv()
			if lv > 0 && lv <= 20 { // 已正确兼容
				continue
			}
			lv = this.EquipCeriSlots.GetLvById(equip.ID)
			if lv <= 0 {
				continue
			}
			equip.UID = ut.String(equip.ID) + "_" + ut.String(lv)
		}

		// 移除建筑队列中的研究所和马厩
		this.BTQueues.Lock()
		for i := len(this.BTQueues.List) - 1; i >= 0; i-- {
			bt := this.BTQueues.List[i]
			if bt.Bid == constant.CERI_BUILD_ID || bt.Bid == constant.STABLE_BUILD_ID {
				this.BTQueues.List = append(this.BTQueues.List[:i], this.BTQueues.List[i+1:]...)
			}
		}
		this.BTQueues.Unlock()

		// 兼容装备打造信息
		if this.CurrForgeEquip != nil && this.CurrForgeEquip.UID == "" && this.CurrForgeEquip.ID != 0 {
			if equip := this.room.GetWorld().GetPlayerEquip(this.Uid, this.CurrForgeEquip.ID); equip != nil {
				// 装备已存在 直接赋值uid
				this.CurrForgeEquip.UID = equip.UID
			} else {
				// 不存在则生成uid
				lv := this.EquipCeriSlots.GetLvById(this.CurrForgeEquip.ID)
				this.CurrForgeEquip.UID = ut.String(this.CurrForgeEquip.ID) + "_" + ut.String(lv)
			}
		}

		// 兼容装备属性
		this.compatiEquipOdds(6004, 1, 6, 5, 20, 2)    // 锁子甲
		this.compatiEquipOdds(6008, 1, 5, 20, 50, 2)   // 反伤甲
		this.compatiEquipOdds(6033, 10, 25, 10, 35, 3) // 眩目坠
		this.compatiEquipAttr(6036, 5, 15, 5, 10)      // 钩镰

		// 兼容地图标记
		for i, v := range this.MapMarks {
			if i > 9 {
				break
			}
			index := helper.Vec2ToIndex(v.Point, this.room.GetMapSize())
			this.MapMarskMap.Set(index, g.NewMapMarkInfo(index, int32(i)+1, v.Name))
		}
	} else {
		this.Version = VERSION
		return
	}
	this.Compati(ver + 1)
}
