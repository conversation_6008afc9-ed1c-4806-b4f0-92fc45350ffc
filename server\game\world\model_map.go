package world

import (
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/helper"
)

// 是否障碍
func (this *Model) CheckHasPassByPoint(x, y int32) bool {
	size := this.room.GetMapSize()
	return helper.IsPointInMap(x, y, size) && this.CheckLandCanOccupy(helper.PointToIndex(x, y, size))
}

// 是否有被占领地块
func (this *Model) HasCell(index int32, uid string) bool {
	cell := this.Cells.Map[index]
	if cell == nil || cell.Owner == "" {
		return false
	}
	return uid == "" || uid != cell.Owner
}

// 是否离古城太近
func (this *Model) CheckNearbyAncient(index int32) bool {
	if this.AncientCityMap.Count() == 0 {
		return false
	}
	var minDis int32 = 10000
	this.AncientCityMap.ForEach(func(v *AncientInfo, k int32) bool {
		dis := this.GetToMapCellDis(index, v.Index)
		if dis < minDis {
			minDis = dis
		}
		return true
	})
	return minDis <= constant.ANCIENT_CITY_SEARCH_DIS_MIN
}

func (this *Model) FilterMainCityIndexs(indexs []int32) []int32 {
	if this.AncientCityMap.Count() > 0 {
		cnt := len(indexs)
		// 先删除离古城太近的点
		for i := cnt - 1; i >= 0; i-- {
			index := indexs[i]
			if this.CheckNearbyAncient(index) {
				indexs = append(indexs[:i], indexs[i+1:]...)
			}
		}
	}
	this.Cells.RLock()
	defer this.Cells.RUnlock()
	return helper.FilterMainCityIndexs(indexs, this.room.GetMapSize(), this.CheckHasPassByPoint, this.HasCell)
}

// 检测单个位置是否合法
func (this *Model) CheckMainCityIndex(index int32, uid string) bool {
	if this.CheckNearbyAncient(index) {
		return false
	}
	this.Cells.RLock()
	defer this.Cells.RUnlock()
	return helper.CheckMainCityIndex(index, uid, this.room.GetMapSize(), this.CheckHasPassByPoint, this.HasCell)
}
