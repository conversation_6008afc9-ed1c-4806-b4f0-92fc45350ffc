package astar

import ut "slgsrv/utils"

// 一个节点
type ANode struct {
	uid    string
	point  *ut.Vec2
	parent *ANode
	dir    *ut.Vec2 // 相对上一个格子的方向
	F      int32
	G      int32
	H      int32
	T      int32 // 拐弯次数
}

func (this *ANode) Init(x, y int32) *ANode {
	this.uid = ut.Itoa(x) + "_" + ut.Itoa(y)
	this.point = ut.NewVec2(x, y)
	this.dir = ut.NewVec2(0, 0)
	this.parent = nil
	this.F = 0
	this.G = 0
	this.H = 0
	this.T = 0
	return this
}

func (this *ANode) Has(x, y int32) bool {
	return this.point.X == x && this.point.Y == y
}

func (this *ANode) UpdateParent(node *ANode, tag int32) {
	this.parent = node
	this.dir = this.point.Sub(node.point)
	if !node.dir.Equals(this.dir) {
		this.T += 1 // 说明转弯了
	}
	this.G = node.G + tag
	this.F = this.H + this.G
}
