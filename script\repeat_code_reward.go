package main

// import (
// 	"context"
// 	slg "slgsrv/server/common"
// 	"slgsrv/server/lobby"
// 	ut "slgsrv/utils"
// 	"sort"
// 	"time"

// 	"github.com/huyangv/vmqant/log"
// 	"github.com/xuri/excelize/v2"
// 	"go.mongodb.org/mongo-driver/bson"
// 	"go.mongodb.org/mongo-driver/mongo"
// 	"go.mongodb.org/mongo-driver/mongo/options"
// )

// const (
// 	lobbyDbUri  = "mongodb://localhost:27017" //大厅服数据库地址
// 	lobbyDbName = "slg_record"                //大厅服数据库名

// 	code      = "NEWYEAR2025"
// 	recordCol = slg.DB_COLLECTION_NAME_USER_ITEM_RECORD + "_" + "20241231"
// )

// func main() {
// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()
// 	// 连接大厅服数据库
// 	clientLobby, err := mongo.NewClient(options.Client().ApplyURI(lobbyDbUri))
// 	if err != nil {
// 		log.Error("parse lobbyDbUri err: %v", err)
// 	}
// 	err = clientLobby.Connect(ctx)
// 	if err != nil {
// 		log.Error("connect lobbyDb err: %v", err)
// 	}
// 	defer clientLobby.Disconnect(ctx)
// 	lobbyDb := clientLobby.Database(lobbyDbName)

// 	// // 获取指定兑换码的邮件
// 	// cur, e := lobbyDb.Collection(slg.DB_COLLECTION_NAME_MAIL).Find(context.TODO(), bson.M{"content": code})
// 	// defer func() {
// 	// 	_ = cur.Close(context.TODO())
// 	// }()
// 	// if e == nil {
// 	// 	userMailMap := map[string][]string{}
// 	// 	var list []mail.MailBaseInfo
// 	// 	cur.All(context.TODO(), &list)
// 	// 	for _, v := range list {
// 	// 		if v.Receiver == "" {
// 	// 			continue
// 	// 		}
// 	// 		if userMailMap[v.Receiver] == nil {
// 	// 			userMailMap[v.Receiver] = []string{}
// 	// 		}
// 	// 		userMailMap[v.Receiver] = append(userMailMap[v.Receiver], v.UID)
// 	// 	}
// 	// 	// 查询目前重复领取的邮件
// 	// 	for uid, mailArr := range userMailMap {
// 	// 		if len(mailArr) == 1 {
// 	// 			delete(userMailMap, uid)
// 	// 			continue
// 	// 		}
// 	// 		count := len(mailArr)
// 	// 		rewardCount := 0
// 	// 		for _, mailUid := range mailArr {
// 	// 			userMailUid := mailUid + "_" + uid
// 	// 			var userMailData mail.UserMailInfo
// 	// 			e := lobbyDb.Collection(slg.DB_COLLECTION_NAME_USER_MAIL).FindOne(context.TODO(), bson.M{"uid": userMailUid}).Decode(&userMailData)
// 	// 			if e == nil && userMailData.IsClaim {
// 	// 				rewardCount++
// 	// 			} else {
// 	// 				log.Info("mail repeat not claim uid: %v, mailUid: %v", uid, mailUid)
// 	// 			}
// 	// 		}
// 	// 		log.Info("mail repeat uid: %v, count: %v, rewardCount: %v", uid, count, rewardCount)
// 	// 	}
// 	// }

// 	// 遍历金币记录
// 	cur, e := lobbyDb.Collection(recordCol).Find(context.TODO(), bson.M{"reason": 2, "item_type": 5, "val": 100})
// 	defer func() {
// 		_ = cur.Close(context.TODO())
// 	}()
// 	if e == nil {
// 		var list []lobby.ItemRecord
// 		cur.All(context.TODO(), &list)
// 		userCountMap := map[string]int{}
// 		for _, v := range list {
// 			userCountMap[v.Uid]++
// 		}
// 		arr := []map[string]string{}
// 		for uid, count := range userCountMap {
// 			if count <= 2 {
// 				continue
// 			}
// 			arr = append(arr, map[string]string{"uid": uid, "count": ut.String(count)})
// 		}
// 		sort.Slice(arr, func(i, j int) bool {
// 			return ut.Int(arr[i]["count"]) > ut.Int(arr[j]["count"])
// 		})
// 		for _, data := range arr {
// 			uid := data["uid"]
// 			count := data["count"]
// 			log.Info("repeat reward uid: %v, count: %v", uid, count)
// 		}
// 		log.Info("repeat total: %v", len(arr))

// 		// 创建一个新的 Excel 文件
// 		f := excelize.NewFile()
// 		sheetName := "Sheet1"

// 		// 写入表头
// 		headers := []string{"UID", "领取次数", "需扣除金币", "需扣除兵符"}
// 		for i, header := range headers {
// 			cell, _ := excelize.CoordinatesToCellName(i+1, 1) // 第一行写表头
// 			if err := f.SetCellValue(sheetName, cell, header); err != nil {
// 				log.Error("写入表头失败: %v", err)
// 			}
// 		}

// 		// 写入数据
// 		for rowIndex, data := range arr {
// 			uid := data["uid"]
// 			count := ut.Int(data["count"])

// 			uidCell, _ := excelize.CoordinatesToCellName(1, rowIndex+2) // 数据从第2行开始
// 			if err := f.SetCellValue(sheetName, uidCell, uid); err != nil {
// 				log.Error("写入 UID 失败: %v", err)
// 			}

// 			countCell, _ := excelize.CoordinatesToCellName(2, rowIndex+2)
// 			if err := f.SetCellValue(sheetName, countCell, count); err != nil {
// 				log.Error("写入 Count 失败: %v", err)
// 			}

// 			reduceGold := 100 * (count - 2)
// 			reduceGoldCell, _ := excelize.CoordinatesToCellName(3, rowIndex+2)
// 			if err := f.SetCellValue(sheetName, reduceGoldCell, reduceGold); err != nil {
// 				log.Error("写入 reduceGold 失败: %v", err)
// 			}

// 			reduceToken := 20 * (count - 2)
// 			reduceTokenCell, _ := excelize.CoordinatesToCellName(4, rowIndex+2)
// 			if err := f.SetCellValue(sheetName, reduceTokenCell, reduceToken); err != nil {
// 				log.Error("写入 reduceToken 失败: %v", err)
// 			}
// 		}

// 		// 保存 Excel 文件
// 		if err := f.SaveAs("output.xlsx"); err != nil {
// 			log.Error("保存文件失败: %v", err)
// 		}
// 	}
// }
