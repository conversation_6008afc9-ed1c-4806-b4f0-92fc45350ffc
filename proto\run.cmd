protoc --go_out=../server/common/ ./*.proto
set ClientDir=D:/Projects/slg-client/client/assets/app/proto/
@REM protoc --js_out=import_style=commonjs,binary:%ClientDir% --plugin=protoc-gen-ts=%CD%/node_modules/.bin/protoc-gen-ts.cmd --ts_out=%ClientDir%  ./*.proto
@REM protoc --ts_out=%ClientDir% --plugin=protoc-gen-ts=%CD%/node_modules/.bin/protoc-gen-ts.cmd ./*.proto
@REM py .\format.py %ClientDir%
call npm run build-proto
xcopy /f "%CD%\msg.js" "%ClientDir%msg.js" /Y
xcopy /f "%CD%\msg.d.ts" "%ClientDir%msg.d.ts" /Y