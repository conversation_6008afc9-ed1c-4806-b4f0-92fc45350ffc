package main

import (
	"context"
	"fmt"
	"time"

	gener "slgsrv/gener"
	slg "slgsrv/server/common"
	"slgsrv/server/game/common/constant"
	r "slgsrv/server/game/room"
	ut "slgsrv/utils"
	mgo "slgsrv/utils/mgodb"

	"github.com/huyangv/vmqant/log"
)

const (
	RoomId   = 1000009 // 1000001 20
	RoomType = 1       // 对局类型 0.普通区 1.新手区 2.排位区
)

func main() {
	CreateWorld(RoomId, RoomType, 0, 11, "/bin/maps/", "local")
}

// 只创建地图数据
func CreateWorld(sid int32, roomType, subType uint8, mapId int32, filePath string, serverArea string) error {
	switch serverArea {
	case "hk":
		mgo.Init("************************************************************************************", "slgsrv", "game") // 香港
	case "china":
		mgo.Init("*************************************************************************************", "slgsrv", "game") // 国内
	case "test":
		mgo.Init("mongodb://slg_test:<EMAIL>:27017/slgsrv_test?w=majority", "slgsrv_test", "game") // 测试服
	case "singapore":
		mgo.Init("******************************************************************", "slgsrv_test", "development") // 测试服-新加坡
	case "local":
		mgo.Init("mongodb://127.0.0.1:27017/slgsrv?w=majority", "slgsrv", "game") // 本地
	}
	now := time.Now().UnixMilli()
	id := ut.String(mapId)
	mainCitys := gener.LoadMainCitys(id, filePath)
	size := ut.NewVec2(600, 600)
	winCond := GetWinCond(roomType)
	staminas := []int32{100, 50}
	if roomType == 0 && subType == 1 {
		staminas = []int32{200, 150} // 自由区
	}
	farmType := constant.FARM_TYPE_NORMAL // 默认正常模式
	if roomType == slg.ROOKIE_SERVER_TYPE {
		// 新手区开荒默认保护模式
		farmType = constant.FARM_TYPE_PROTECTION
	}
	fmt.Println("Create room id=", sid)
	roomData := r.RoomTableData{
		Id:        sid,
		Type:      roomType,
		SubType:   subType,
		InitTime:  now,
		MainCitys: mainCitys,
		MapSize:   size,
		WinCond:   winCond,
		State:     0,
		ApplyUids: [][]string{},
		Staminas:  staminas,
		MapId:     mapId,
		FarmType:  int32(farmType),
	}
	if _, e := mgo.GetCollection("room").InsertOne(context.TODO(), roomData); e != nil {
		fmt.Println(e.Error())
		return e
	}
	log.Info("Create world_%v path: %v, mapId: %v", sid, filePath, mapId)
	maps := gener.LoadMapsConf(id, filePath)
	r.InitRoom(sid, roomType, subType, now, size, winCond, staminas, mainCitys, maps, mapId)
	log.Info("Create room done !!!")
	return nil
}

// 获取胜利条件
func GetWinCond(roomType uint8) []int32 {
	switch roomType {
	case slg.ROOKIE_SERVER_TYPE: // 新手区 遗迹
		return slg.WIN_COND_ANCIENT
	case slg.NORMAL_SERVER_TYPE: // 自由区 遗迹
		return slg.WIN_COND_ANCIENT
	case slg.RANK_SERVER_TYPE: // 排位区 血战到底
		return slg.WIN_COND_CONQUER
	default:
		return nil
	}
}
