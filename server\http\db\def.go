package db

import (
	"context"
	"sync"
	"time"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// 每个表都要来这里声明,需要注意的是如果表是直接写死的可以使用NewTableNameDef
// 如果表是根据Sid动态的，则需要在DelayNewTableNameDef()去创建

var (
	USER          = NewTableNameDef("userCol")        // 用户数据表
	CODE          = NewTableNameDef("goCodeCol")      // go脚本保存表
	MAP           = NewTableNameDef("mapCol")         // 服务器放在http服务器的公共key-val
	ERROR_REPORT  = NewTableNameDef("errorReportCol") // 客户端错误上报表
	HOT_UPDATE    = NewTableNameDef("hotUpdateCol")   // 热更新上报表
	OPERATION_LOG = NewTableNameDef("opLogCol")       // 操作日志表
	MAP_USE_INFO  = NewTableNameDef("mapUseCol")      // 地图使用信息表
	CDK_MODEL     = NewTableNameDef("cdkModelCol")    // cdk模版表
	FEEDBACK      = NewTableNameDef("feedbackCol")    // 用户反馈
	LOGIN_MACH    = NewTableNameDef("login_mach")     // 登录服物理机
)

func DelayNewTableNameDef() {
}

type TableDef struct {
	name  string          // 表名称
	index map[string]bool // 索引信息,放入map的字段均会被创建索引，如果是值是true则该字段创建为唯一索引
	first sync.Once       // 保证索引创建只被调用一次
}

// Index 配置索引信息
func (this *TableDef) Index(imap map[string]bool) *TableDef {
	this.index = imap
	return this
}

// Get 获取表定义的名称
func (this *TableDef) Get() string {
	return this.name
}

// CreateIndex 首次运行时会根据配置index map异步创建本表索引
func (this *TableDef) CreateIndex(collection *mongo.Collection) {
	if this.index != nil {
		this.first.Do(func() {
			go func() {
				log.Info("Start CreateIndex ,table: %s", this.name)
				sTime := time.Now()
				indexModels := make([]mongo.IndexModel, 0)
				// 遍历配置表
				for key, unique := range this.index {
					if key != "" {
						indexModels = append(indexModels, mongo.IndexModel{
							Keys:    bson.M{key: 1},
							Options: options.Index().SetUnique(unique),
						})
					}
				}

				indexView := collection.Indexes()
				// 创建多个索引
				if _, err := indexView.CreateMany(context.Background(), indexModels); err != nil {
					log.Error("创建索引失败：", err)
				}
				log.Info("End CreateIndex ,table: %s during :%fs", this.name, time.Since(sTime).Seconds())
			}()
		})
	}
}

func NewTableNameDef(name string) *TableDef {
	if name == "" {
		panic("table name 不能是空值")
	}
	return &TableDef{
		name: name,
	}
}
