package world

import (
	"sort"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 获取玩家军队分布
func (this *Model) GetPlayerArmyDist(uid string) map[int32]bool {
	dists := map[int32]bool{}
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.armyMutex.RLock()
		for _, index := range plr.Armys {
			dists[index] = true
		}
		plr.armyMutex.RUnlock()
	}
	return dists
}

// 改变军队位置
func (this *Model) ChangeArmyDistIndex(owner string, uid string, index int32) {
	if plr := this.GetTempPlayer(owner); plr != nil {
		plr.armyMutex.Lock()
		if index == -1 {
			delete(plr.Armys, uid)
		} else {
			plr.Armys[uid] = index
		}
		plr.armyMutex.Unlock()
	}
}

// 删除玩家军队分布
func (this *Model) RemovePlayerArmyDist(uid string) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.armyMutex.Lock()
		plr.Armys = map[string]int32{}
		plr.armyMutex.Unlock()
	}
}

// 获取玩家军队分布 pb数组形式
func (this *Model) GetPlayerArmyDistArrayPb(uid string, withPawnsInfo bool) []*pb.AreaDistInfo {
	dists := this.GetPlayerArmyDist(uid)
	arr := []*pb.AreaDistInfo{}
	for index := range dists {
		area := this.GetArea(index)
		if area == nil {
			continue
		}
		armys := []*pb.AreaArmyInfo{}
		list := area.GetArmysClone()
		for _, army := range list {
			if army.Owner == uid {
				if withPawnsInfo {
					armys = append(armys, army.ToShortDataPb(area.ToArmyState(army)))
				} else {
					armys = append(armys, army.ToBaseDataPb(area.ToArmyState(army)))
				}
			}
		}
		if len(armys) > 0 {
			arr = append(arr, &pb.AreaDistInfo{
				Index: index,
				Armys: armys,
			})
		}
	}
	return arr
}

// 获取玩家军队中最多的士兵数
func (this *Model) GetMaxPawnCountInArmy(uid string) int32 {
	armyDist := this.GetPlayerArmyDistArrayPb(uid, true)
	var mCount int32
	for _, v := range armyDist {
		for _, a := range v.Armys {
			if cnt := int32(len(a.Pawns)); cnt > mCount {
				mCount = cnt
			}
		}
	}
	return mCount
}

// 更新玩家的军队分布信息
func (this *Model) UpdatePlayerArmyDist(area *Area) {
	list := area.GetArmysClone()
	for _, m := range list {
		this.ChangeArmyDistIndex(m.Owner, m.Uid, area.index)
	}
}

// 检测军队是否空闲中
func (this *Model) CheckArmyIdle(area *Area, army *AreaArmy) bool {
	if area.IsBattle() || this.CheckArmyMarchingByUID(army.Uid) {
		return false
	}
	return !army.IsPawnDrilling() && !this.CheckArmyLving(army) && !army.IsPawnCuring() && !army.IsCellTonden()
}

// 获取玩家的军队信息 t=1 表示只获取空闲的 ignoreIndex:表示要忽略的区域
func (this *Model) GetPlayerArmysPb(uid string, t, ignoreIndex int32) []*pb.AreaArmyInfo {
	dist := this.GetPlayerArmyDist(uid)
	arr := []*pb.AreaArmyInfo{}
	for index := range dist {
		area := this.GetArea(index)
		if area == nil || index == ignoreIndex {
			continue
		}
		list := area.GetArmysClone()
		for _, army := range list {
			if army.Owner != uid {
				continue
			} else if t == constant.GAT_IDLE && (!this.CheckArmyIdle(area, army) || army.GetPawnCount() == 0) {
				continue
			}
			arr = append(arr, army.ToShortDataPb(area.ToArmyState(army)))
		}
	}
	return arr
}

// 获取非主城的空闲军队
func (this *Model) GetPlayerNotMainIdleArmys(uid string, ignoreIndex int32, ignores []*AreaArmy) []*AreaArmy {
	dist := this.GetPlayerArmyDist(uid)
	arr := []*AreaArmy{}
	obj := map[string]bool{}
	for _, m := range ignores {
		obj[m.Uid] = true
	}
	for index := range dist {
		area := this.GetArea(index)
		if area == nil || index == ignoreIndex {
			continue
		}
		list := area.GetArmysClone()
		for _, army := range list {
			if army.Owner != uid {
				continue
			} else if !this.CheckArmyIdle(area, army) || army.GetPawnCount() == 0 || obj[army.Uid] {
				continue
			}
			arr = append(arr, army)
		}
	}
	return arr
}

// 获取玩家的军队数量
func (this *Model) GetPlayerArmyCount(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.armyMutex.RLock()
		defer plr.armyMutex.RUnlock()
		return int32(len(plr.Armys))
	}
	return 0
}

// 获取玩家的军队
func (this *Model) GetPlayerArmyHero(uid string, armyUid string) *g.PortrayalInfo {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.armyMutex.RLock()
		index, ok := plr.Armys[armyUid]
		plr.armyMutex.RUnlock()
		if !ok {
		} else if _, army := this.GetAreaAndArmy(index, armyUid); army != nil {
			return army.GetHero()
		}
	}
	return nil
}

// 获取玩家的士兵上报信息
func (this *Model) GetPlayerPawnTrackInfo(uid string) []map[string]interface{} {
	roles := []map[string]interface{}{}
	dist := this.GetPlayerArmyDist(uid)
	for index := range dist {
		area := this.GetArea(index)
		if area == nil {
			continue
		}
		list := area.GetArmysClone()
		for _, army := range list {
			if army.Owner != uid {
				continue
			}
			army.Pawns.RLock()
			for _, pawn := range army.Pawns.List {
				roles = append(roles, map[string]interface{}{
					"role_id":     pawn.Id,
					"role_lv":     pawn.Lv,
					"role_count":  1,
					"equip_id":    pawn.Equip.ID,
					"equip_count": 1,
					"army_name":   army.Name,
					"army_uid":    army.Uid,
				})
			}
			army.Pawns.RUnlock()
		}
	}
	if len(roles) == 0 {
		roles = append(roles, map[string]interface{}{})
	}
	return roles
}

// 删除玩家所有军队
func (this *Model) RemovePlayerAllArmy(uid string, isFail bool) {
	dist := this.GetPlayerArmyDist(uid)
	mainCityIndex := this.GetPlayerMainIndex(uid)
	for index := range dist {
		area := this.GetArea(index)
		if area == nil {
			continue
		}
		if isFail && index == mainCityIndex { // 出局的玩家主城的军队不删除
			continue
		}
		for i := int32(len(area.Armys.List)) - 1; i >= 0; i-- {
			army := area.Armys.List[i]
			if army.Owner != uid {
				continue
			} else if this.CheckArmyMarchingByUID(army.Uid) { // 如果在行军还要删除行军
				this.RemoveMarchByArmyUid(army.Uid)
			}
			area.FspMutex.RLock()
			if area.IsBattle() {
				area.RemoveArmyByBattle(army) // 如果在战斗中
			} else {
				area.RemoveArmyByIndex(army.Uid, i) // 这里删除
			}
			area.FspMutex.RUnlock()
			// 记录解散
			this.room.GetRecord().AddArmyMarchRecord(constant.MARCH_TYPE_REMOVE_ARMY, army.Owner, army.Uid, army.Name, army.AIndex, 0)
		}
	}
	this.RemovePlayerArmyDist(uid)
}

// 检测是否有相同的军队名字
func (this *Model) CheckArmyNameEqual(uid string, name string) bool {
	dist := this.GetPlayerArmyDist(uid)
	for index := range dist {
		area := this.GetArea(index)
		if area == nil {
			continue
		}
		area.Armys.RLock()
		for _, army := range area.Armys.List {
			if army.Owner == uid && army.Name == name {
				area.Armys.RUnlock()
				return true
			}
		}
		area.Armys.RUnlock()
	}
	return false
}

// 刷新士兵的装备信息
func (this *Model) UpdatePlayerPawnEquipInfo(uid string, euid string, attrs [][]int32) {
	dist := this.GetPlayerArmyDist(uid)
	for index := range dist {
		area := this.GetArea(index)
		if area == nil || area.IsBattle() { // 这里在战斗不更新装备
			continue
		}
		has, isRecoverPawnHP := false, area.IsRecoverPawnHP()
		list := area.GetArmysClone()
		for _, army := range list {
			if army.Owner != uid {
				continue
			} else if army.UpdatePawnEquipAttr(euid, attrs, isRecoverPawnHP) {
				has = true
			}
		}
		if has {
			this.TagUpdateDBByIndex(index)
		}
	}
}

// 刷新士兵的英雄信息
func (this *Model) UpdatePlayerPawnHeroInfo(uid string, id int32, attrs [][]int32) {
	dist := this.GetPlayerArmyDist(uid)
	for index := range dist {
		area := this.GetArea(index)
		if area == nil || area.IsBattle() { // 这里在战斗不更新装备
			continue
		}
		has, isRecoverPawnHP := false, area.IsRecoverPawnHP()
		list := area.GetArmysClone()
		for _, army := range list {
			if army.Owner != uid {
				continue
			} else if army.UpdatePawnHeroAttr(id, attrs, isRecoverPawnHP) {
				has = true
			}
		}
		if has {
			this.TagUpdateDBByIndex(index)
		}
	}
}

// 获取玩家的粮耗
func (this *Model) GetPlayerCerealConsume(uid string) float64 {
	count := 0.0
	dist := this.GetPlayerArmyDist(uid)
	for index := range dist {
		area := this.GetArea(index)
		if area == nil {
			continue
		}
		list := area.GetArmysClone()
		for _, army := range list {
			if army.Owner == uid {
				count += army.GetAllPawnCerealConsume()
			}
		}
	}
	return count
}

// 删除士兵来之逃兵
func (this *Model) RemovePawnByDeserter(uid string, count int) {
	pawns := []*AreaPawn{}
	armyNameMap := map[string]string{}
	dist := this.GetPlayerArmyDist(uid)
	for index := range dist {
		area := this.GetArea(index)
		if area == nil || area.IsBattle() { // 战斗中不会删除
			continue
		}
		list := area.GetArmysClone()
		for _, army := range list {
			if army.Owner == uid && !this.CheckArmyMarchingByUID(army.Uid) {
				army.Pawns.RLock()
				for _, pawn := range army.Pawns.List {
					if !this.CheckPawnLving(pawn.Uid) { // 如果在训练中 也不会删除
						pawns = append(pawns, pawn)
					}
				}
				army.Pawns.RUnlock()
				armyNameMap[army.Uid] = army.Name
			}
		}
	}
	if len(pawns) == 0 {
		return
	}
	sort.Slice(pawns, func(i, j int) bool {
		a, b := pawns[i], pawns[j]
		av := a.Lv*10000 + a.curHp
		bv := b.Lv*10000 + b.curHp
		return av < bv
	})
	// 删除第一个
	pawn := pawns[0]
	if area := this.GetArea(pawn.AIndex); area != nil && !area.IsBattle() {
		rst, _ := area.RemoveArmyPawn(pawn.ArmyUid, pawn.Uid, false)
		if rst {
			// 发送一封邮件
			content := ut.StringJoin("|", armyNameMap[pawn.ArmyUid], pawn.Lv, "pawnText.name_"+ut.Itoa(pawn.Id))
			id := ut.If(pawn.GetPawnType() == constant.PAWN_TYPE_MACHINE, 100019, 100005)
			this.room.SendMailOne(id, "", content, "-1", pawn.Owner)
		}
	}
}

// 获取玩家宝箱信息
func (this *Model) GetPlayerTreasureInfo(uid string, auid string, puid string, tuid string) (*Area, *AreaArmy, *AreaPawn, *g.TreasureInfo) {
	dist := this.GetPlayerArmyDist(uid)
	for index := range dist {
		area := this.GetArea(index)
		if area == nil {
			continue
		}
		list := area.GetArmysClone()
		for _, army := range list {
			if army.Owner == uid && army.Uid == auid {
				if pawn := army.GetPawnByUID(puid); pawn != nil {
					return area, army, pawn, pawn.GetTreasureByUID(tuid)
				}
			}
		}
	}
	return nil, nil, nil, nil
}

// 检测玩家是否有新的宝箱
func (this *Model) CheckPlayerHasTreasure(uid string) bool {
	dist := this.GetPlayerArmyDist(uid)
	for index := range dist {
		area := this.GetArea(index)
		if area == nil {
			continue
		}
		list := area.GetArmysClone()
		for _, army := range list {
			if army.Owner == uid && array.Some(army.GetPawnsClone(), func(m *AreaPawn) bool { return m.GetTreasureCount() > 0 }) {
				return true
			}
		}
	}
	return false
}

// 通知玩家有新宝箱
func (this *Model) SendPlayerHasTreasure(uid string) {
	this.room.PutNotifyPlayerHasTreasure(uid)
}

// 卸下所有指定士兵id的指定皮肤
func (this *Model) ClearPawnsSkin(uid string, pawnId, skinId int32) {
	dist := this.GetPlayerArmyDist(uid)
	for index := range dist {
		area := this.GetArea(index)
		if area == nil {
			continue
		}
		list := area.GetArmysClone()
		for _, army := range list {
			if army.Owner != uid {
				continue
			}
			army.Pawns.RLock()
			for _, pawn := range army.Pawns.List {
				if pawn.Id == pawnId && pawn.SkinId == skinId {
					pawn.SkinId = 0
				}
			}
			army.Pawns.RUnlock()
		}
	}
}
