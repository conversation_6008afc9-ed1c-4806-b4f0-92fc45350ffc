package player

import (
	"math"
	"time"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/effect"
	ut "slgsrv/utils"
)

/**
产出相关
*/

type BeginDeserterData struct {
	Time     int64 `bson:"time"`     // 开始时间
	Interval int32 `bson:"interval"` // 间隔
}

type Output struct {
	Value    int32 `json:"value"`     // 当前值
	AccTotal int32 `json:"acc_total"` // 累计获得的值

	opHour  float64 // 小时产
	opSec   float64 // 秒产
	opCount float64 // 产出数量
}

func NewOutput(val int32, accTotal int32) *Output {
	return &Output{Value: val, AccTotal: ut.If(accTotal == 0, val, accTotal)}
}

func (this *Output) Strip() map[string]interface{} {
	return map[string]interface{}{
		"value":  this.Value,
		"opHour": this.opHour,
	}
}

func (this *Output) ToPb() *pb.OutPutInfo {
	return &pb.OutPutInfo{
		Value:  int32(this.Value),
		OpHour: int32(this.opHour),
	}
}

func (this *Output) GetOpHour() int {
	return int(this.opHour)
}

// 设置产出
func (this *Output) InitOutput(val float64) int32 {
	this.opHour = val
	this.opSec = this.opHour / ut.TIME_HOUR * 1000 // 每秒产
	if this.opSec <= 0 {
		return 0
	}
	return ut.MaxInt32(int32(1.0/this.opSec), 10) * 1000 // 最小10秒
}

// 产出
func (this *Output) Do(dt int32, cap int32) bool {
	if this.Value >= cap {
		return false // 已经满了
	} else if this.opSec < 0 && this.Value <= 0 {
		return false // 如果是扣 并且没有了
	}
	this.opCount += (float64(dt) / 1000.0) * this.opSec
	if math.Abs(this.opCount) < 1 {
		return false
	}
	cnt := int32(this.opCount)
	val := this.Value + cnt
	if val >= cap {
		cnt = cap - this.Value
		this.opCount = 0
	} else if val < 0 {
		cnt = -this.Value
		this.opCount = 0
	} else {
		this.opCount -= float64(cnt)
	}
	this.Change(cnt, cap)
	return true
}

// 改变
func (this *Output) Change(val int32, cap int32) int32 {
	count := this.Value + val
	if count < 0 {
		val = -this.Value
	} else if count > cap {
		val = cap - this.Value
	}
	this.Value += val
	if val > 0 {
		this.AccTotal += val // 记录累计获得
	}
	return val
}

// 刷新产量
func (this *Model) UpdateOpSec(isNotify bool) {
	if this.IsSpectator() {
		return
	}
	this.outputMutex.Lock()
	defer this.outputMutex.Unlock()
	// 计算占领的资源田
	cereal, timber, stone := this.room.GetWorld().GetPlayerCellOutput(this.Uid)
	// 加上内政增加 固定值
	val := this.GetPolicyEffectFloat(effect.RES_OUTPUT)
	cereal += val
	timber += val
	stone += val
	// 加上商城购买 百分比
	cereal *= ut.If(this.AddOutputEndTime.Get(ctype.CEREAL) > 0, constant.ADD_OUTPUT_RATIO, 1.0)
	timber *= ut.If(this.AddOutputEndTime.Get(ctype.TIMBER) > 0, constant.ADD_OUTPUT_RATIO, 1.0)
	stone *= ut.If(this.AddOutputEndTime.Get(ctype.STONE) > 0, constant.ADD_OUTPUT_RATIO, 1.0)
	// 扣除粮耗
	this.cerealConsume = this.room.GetWorld().GetPlayerCerealConsume(this.Uid)
	cereal -= this.cerealConsume
	// 取出最大的产出时间
	this.outputInterval = this.Cereal.InitOutput(cereal)
	if t := this.Timber.InitOutput(timber); t > this.outputInterval {
		this.outputInterval = t
	}
	if t := this.Stone.InitOutput(stone); t > this.outputInterval {
		this.outputInterval = t
	}
	if isNotify {
		data := &pb.UpdateOutPut{
			Cereal:        this.Cereal.ToPb(),
			Timber:        this.Timber.ToPb(),
			Stone:         this.Stone.ToPb(),
			CerealConsume: this.cerealConsume,
			Flag:          pb.AddFlags(int64(pb.OutPutFlagEnum_Cereal), int64(pb.OutPutFlagEnum_Timber), int64(pb.OutPutFlagEnum_Stone), int64(pb.OutPutFlagEnum_CerealConsume)),
		}
		this.PutNotifyQueue(constant.NQ_OUTPUT, &pb.OnUpdatePlayerInfoNotify{Data_1: data})
	}
}

// 获取粮食容量
func (this *Model) GetGranaryCap() int32 {
	return constant.INIT_RES_CAP + int32(this.effects[effect.GRANARY_CAP]) + this.GetExtraCap(constant.GRANARY_BUILD_ID)
}

// 获取仓库容量
func (this *Model) GetWarehouseCap() int32 {
	return constant.INIT_RES_CAP + int32(this.effects[effect.WAREHOUSE_CAP]) + this.GetExtraCap(constant.WAREHOUSE_BUILD_ID)
}

// 获取额外容量
func (this *Model) GetExtraCap(id int32) int32 {
	if val := this.GetPolicyEffectInt(effect.GW_CAP); val > 0 {
		return int32(this.room.GetWorld().GetPlayerBuildsSumLvById(this.Uid, id) * val)
	}
	return 0
}

func (this *Model) CheckUpdateOutput(now int64, init bool) {
	this.CheckAddOutputTime(now, init) // 刷新添加的产量是否过期
	dt := int32(now - this.LastOutputTime)
	if dt < this.outputInterval {
		return
	}

	// 是否跨天 凌晨0点为间隔
	nowZeroTime := ut.NowZeroTime()
	if nowZeroTime >= this.LastOutputTime {
		// 书铁每天增加相关政策
		addPer := this.GetPolicyEffectFloat(effect.RARE_RES_OUTPUT)
		addPerCond := this.GetPolicyEffectFloat(effect.MORE_RARE_RES)
		days := (nowZeroTime-this.LastOutputTime)/ut.TIME_DAY + 1
		bookAddPer := addPer
		// 书或铁超过100触发政策
		if this.ExpBook >= 100 {
			bookAddPer += addPerCond
		}
		ironAddPer := addPer
		if this.Iron >= 100 {
			ironAddPer += addPerCond
		}
		for i := 0; i < int(days); i++ {
			this.ExpBook += ut.RoundInt32(float64(this.ExpBook) * (bookAddPer / 100))
			this.Iron += ut.RoundInt32(float64(this.Iron) * (ironAddPer / 100))
		}
		// 通知
		this.PutNotifyQueue(constant.NQ_OUTPUT, &pb.OnUpdatePlayerInfoNotify{Data_1: &pb.UpdateOutPut{
			ExpBook: this.ExpBook,
			Iron:    this.Iron,
			Flag:    pb.AddFlags(int64(pb.OutPutFlagEnum_ExpBook), int64(pb.OutPutFlagEnum_Iron)),
		}})
	}

	this.LastOutputTime = now
	this.outputMutex.Lock()
	granaryCap, warehouseCap := this.GetGranaryCap(), this.GetWarehouseCap()
	// 计算产出前的三资总数
	beforeTotal := this.Cereal.Value + this.Timber.Value + this.Stone.Value
	// 粮食
	if this.Cereal.Do(dt, granaryCap) && !init {
		this.PutNotifyQueue(constant.NQ_OUTPUT, &pb.OnUpdatePlayerInfoNotify{Data_1: &pb.UpdateOutPut{Cereal: this.Cereal.ToPb(), Flag: pb.AddFlags(int64(pb.OutPutFlagEnum_Cereal))}})
	}
	// 木头
	if this.Timber.Do(dt, warehouseCap) && !init {
		this.PutNotifyQueue(constant.NQ_OUTPUT, &pb.OnUpdatePlayerInfoNotify{Data_1: &pb.UpdateOutPut{Timber: this.Timber.ToPb(), Flag: pb.AddFlags(int64(pb.OutPutFlagEnum_Timber))}})
	}
	// 石头
	if this.Stone.Do(dt, warehouseCap) && !init {
		this.PutNotifyQueue(constant.NQ_OUTPUT, &pb.OnUpdatePlayerInfoNotify{Data_1: &pb.UpdateOutPut{Stone: this.Stone.ToPb(), Flag: pb.AddFlags(int64(pb.OutPutFlagEnum_Stone))}})
	}
	// 计算产出后的三资总数
	afterTotal := this.Cereal.Value + this.Timber.Value + this.Stone.Value
	if afterTotal > beforeTotal {
		// 统计总资源
		this.room.GetWorld().ChangePlayerResAcc(this.Uid, afterTotal-beforeTotal)
	}
	this.outputMutex.Unlock()
	// 检测是否因为没有粮食而开始逃兵了
	this.CheckCanDeserter(now)
}

// 检测添加产量时间是否过期
func (this *Model) CheckAddOutputTime(now int64, init bool) {
	isPastDue := false
	this.AddOutputEndTime.DeleteEach(func(v int64, k int32) bool {
		if v > 0 && now >= v {
			isPastDue = true
			return true
		}
		return false
	})
	if isPastDue {
		this.UpdateOpSec(!init)
		if !init {
			this.PutNotifyQueue(constant.NQ_ADD_OUTPUT_TIME, &pb.OnUpdatePlayerInfoNotify{
				Data_38: this.ToAddOutputSurplusTimePb(),
			})
		}
	}
}

// 添加 添加产量时间
func (this *Model) AddAddOutputTime(t int32) {
	if val := this.AddOutputEndTime.Get(t); val > 0 {
		this.AddOutputEndTime.Set(t, val+constant.ADD_OUTPUT_TIME)
	} else {
		this.AddOutputEndTime.Set(t, time.Now().UnixMilli()+constant.ADD_OUTPUT_TIME)
		this.UpdateOpSec(false)
	}
}

func (this *Model) ToAddOutputSurplusTimePb() map[int32]int32 {
	now := time.Now().UnixMilli()
	data := map[int32]int32{}
	this.AddOutputEndTime.ForEach(func(v int64, k int32) bool {
		data[k] = int32(ut.Max(0, int(v-now)))
		return true
	})
	return data
}

// 检测是否开始逃兵
func (this *Model) CheckCanDeserter(now int64) {
	if this.Cereal.Value > 0 {
		this.BeginDeserterInfo = nil
		return
	} else if this.BeginDeserterInfo == nil {
		this.BeginDeserterInfo = &BeginDeserterData{
			Time:     now,
			Interval: int32(ut.Random(constant.DESERTER_INTERVAL_BEGIN[0], constant.DESERTER_INTERVAL_BEGIN[1])),
		}
		return
	} else if int32(now-this.BeginDeserterInfo.Time) > this.BeginDeserterInfo.Interval { // 开始逃兵
		this.BeginDeserterInfo.Time = now
		this.BeginDeserterInfo.Interval = int32(ut.Random(constant.DESERTER_INTERVAL[0], constant.DESERTER_INTERVAL[1]))
		this.room.GetWorld().RemovePawnByDeserter(this.Uid, 1) // 删除一个
	}
}

// 检测粮耗是否足
func (this *Model) CheckCerealConsume(count float64) bool {
	return this.Cereal.opHour >= count
}

// 资源满了离线检测
func (this *Model) OutputOfflineCheck() {
	granaryCap, warehouseCap := this.GetGranaryCap(), this.GetWarehouseCap()
	now := time.Now().UnixMilli()
	if this.Cereal.Value < granaryCap && this.Cereal.opSec > 0 {
		// 粮食
		endTime := now + int64((float64(granaryCap-this.Cereal.Value)/this.Cereal.opSec)*1000)
		this.room.GetWorld().AddCheckPlayerOfflineMsg(this.Uid, constant.OFFLINE_MSG_TYPE_RES_FULL, endTime, ut.String(ctype.CEREAL))
	}
	if this.Timber.Value < warehouseCap && this.Timber.opSec > 0 {
		// 木头
		endTime := now + int64((float64(warehouseCap-this.Timber.Value)/this.Timber.opSec)*1000)
		this.room.GetWorld().AddCheckPlayerOfflineMsg(this.Uid, constant.OFFLINE_MSG_TYPE_RES_FULL, endTime, ut.String(ctype.TIMBER))
	}
	if this.Stone.Value < warehouseCap && this.Stone.opSec > 0 {
		// 石头
		endTime := now + int64((float64(warehouseCap-this.Stone.Value)/this.Stone.opSec)*1000)
		this.room.GetWorld().AddCheckPlayerOfflineMsg(this.Uid, constant.OFFLINE_MSG_TYPE_RES_FULL, endTime, ut.String(ctype.TIMBER))
	}
}

// 检测城市产出
func (this *Model) CheckUpdateCityOutput(now int64, init bool) {
	dt := now - this.LastCityOutputTime
	if dt < constant.CITY_OUTPUT_TIME_INTERVAL {
		return
	}
	this.LastCityOutputTime = now
	this.room.GetWorld().UpdatePlayerCityOutput(this.Uid, int32(dt/constant.CITY_OUTPUT_TIME_INTERVAL), init)
}
