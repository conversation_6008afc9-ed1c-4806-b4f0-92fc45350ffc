package game

import (
	"slgsrv/server/common/pb"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDRank() {
	this.GetServer().RegisterGO("HD_GetPlayerRankList", this.getPlayerRankList)   // 获取玩家排行榜
	this.GetServer().RegisterGO("HD_GetAlliRankList", this.getAlliRankList)       // 获取联盟排行榜
	this.GetServer().RegisterGO("HD_GetPlayerScoreList", this.getPlayerScoreList) // 获取玩家积分排行榜
}

// 获取玩家排行榜
func (this *Game) getPlayerRankList(session gate.Session, msg *pb.GAME_HD_GETPLAYERRANKLIST_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	list, no := wld.GetPlayerRankList(plr.Uid)
	return pb.ProtoMarshal(&pb.GAME_HD_GETPLAYERRANKLIST_S2C{
		List: list,
		No:   int32(no),
	})
}

// 获取联盟排行榜
func (this *Game) getAlliRankList(session gate.Session, msg *pb.GAME_HD_GETALLIRANKLIST_C2S) (ret []byte, err string) {
	e, _, _, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	list := wld.GetAlliRankList()
	return pb.ProtoMarshal(&pb.GAME_HD_GETALLIRANKLIST_S2C{
		List: list,
	})
}

// 获取玩家积分排行榜
func (this *Game) getPlayerScoreList(session gate.Session, msg *pb.GAME_HD_GETPLAYERSCORELIST_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	list, no, me := wld.GetPlayerScoreList(plr.Uid, int(msg.GetType()))
	return pb.ProtoMarshal(&pb.GAME_HD_GETPLAYERSCORELIST_S2C{
		List: list,
		No:   int32(no),
		Me:   me,
	})
}
