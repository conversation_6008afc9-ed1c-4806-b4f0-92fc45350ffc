package match

import (
	"fmt"
	"os"
	"strings"
	"sync"

	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
)

// 地图使用信息
type MapUseInfo struct {
	SidList []int32 `bson:"sid_list"` // 使用该地图的区服id列表
	Id      int32   `bson:"id"`       // 地图id
}

var (
	mapGenId     int32 = 1
	mapGenIdLock sync.Once

	mapUseInfoMap = map[int32]*MapUseInfo{} // 地图使用情况
	mapUseLock    sync.Once
)

// 获取当前创建地图id
func getMapGenId() int32 {
	mapGenIdLock.Do(func() {
		val, err := db_room_gen.GetRoomGenIntByKey(ROOM_GEN_COL_KEY_MAP_GEN_ID)
		if err == nil {
			mapGenId = val
		}
	})
	return mapGenId
}

// 设置创建地图id
func setMapGenId() {
	mapGenId++
	db_room_gen.SetRoomGenInfoInt(&RoomGenCol[int32]{Key: ROOM_GEN_COL_KEY_MAP_GEN_ID, Val: mapGenId})
}

// 获取地图使用情况
func getMapUseInfo() map[int32]*MapUseInfo {
	mapUseLock.Do(func() {
		mapsFolderPath := fmt.Sprintf("%v/bin/maps", ut.WorkDir())
		if _, err := os.Stat(mapsFolderPath); os.IsNotExist(err) {
			// 地图目录不存在则创建
			err := os.Mkdir(mapsFolderPath, 0o777)
			if err != nil {
				log.Error("getMapUseInfo create maps folder err: %v", err)
				return
			}
		}
		// 第一次获取遍历地图文件
		files, err := os.ReadDir(mapsFolderPath)
		if err != nil {
			log.Error("getMapUseInfo ReadDir err: %v", err)
		}
		for _, f := range files {
			fullName := f.Name()
			fullNameArr := strings.Split(fullName, ".")
			if len(fullNameArr) < 2 {
				continue
			}
			if fullNameArr[1] != "json" {
				continue
			}
			fname := fullNameArr[0]
			fnameArr := strings.Split(fname, "_")
			if len(fnameArr) < 2 {
				continue
			}
			mapId := ut.Int32(fnameArr[1])
			_, ok := mapUseInfoMap[mapId]
			if !ok {
				mapUseInfoMap[mapId] = &MapUseInfo{
					Id:      mapId,
					SidList: []int32{},
				}
			}
		}
		arr, err := db_map_use.FindAllMapUseInfo()
		for _, v := range arr {
			if mapUseInfoMap[v.Id] != nil {
				mapUseInfoMap[v.Id].SidList = v.SidList
			}
		}
	})
	return mapUseInfoMap
}

// 设置地图使用情况
func setMapUseInfo(mapId, sid int32) {
	mapUseInfo := getMapUseInfo()
	info, ok := mapUseInfo[mapId]
	if !ok {
		info = &MapUseInfo{
			Id:      mapId,
			SidList: []int32{},
		}
		mapUseInfo[mapId] = info
	}
	info.SidList = append(info.SidList, sid)
	db_map_use.UpdateMapUse(info)
}
