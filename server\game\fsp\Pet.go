package fsp

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/g"
)

type Pet struct {
	Fighter
}

func (this *Pet) Init(pawn g.Pawn, camp, roundCount int32, ctrl *FSPBattleController) *Pet {
	this.Fighter.Init(pawn, camp, roundCount, ctrl)
	return this
}

func (this *Pet) Strip() *g.FigherStrip {
	attackTarget := ""
	if this.attackTarget != nil {
		attackTarget = this.attackTarget.GetUID()
	}
	return &g.FigherStrip{
		Uid:          this.GetUID(),
		Camp:         this.camp,
		AttackIndex:  this.attackIndex,
		RoundCount:   this.roundCount,
		WaitRound:    this.waitRound,
		AttackTarget: attackTarget,
		AttackCount:  this.attackCount,
		Point:        this.entity.GetPoint(),
		Hp:           []int32{this.entity.GetCurHP(), this.entity.GetMaxHP()},
		Owner:        this.entity.GetOwner(),
		Id:           this.GetID(),
		Lv:           this.GetLV(),
		IsPet:        true,
		Buffs:        this.entity.BuffsClone(),
	}
}

func (this *Pet) ToPb() *pb.FighterInfo {
	attackTarget := ""
	if this.attackTarget != nil {
		attackTarget = this.attackTarget.GetUID()
	}
	return &pb.FighterInfo{
		Uid:          this.GetUID(),
		Camp:         int32(this.camp),
		AttackIndex:  int32(this.attackIndex),
		RoundCount:   int32(this.roundCount),
		WaitRound:    int32(this.waitRound),
		AttackTarget: attackTarget,
		AttackCount:  int32(this.attackCount),
		Point:        pb.NewVec2(this.entity.GetPoint()),
		Hp:           []int32{int32(this.entity.GetCurHP()), int32(this.entity.GetMaxHP())},
		Owner:        this.entity.GetOwner(),
		Id:           int32(this.GetID()),
		Lv:           int32(this.GetLV()),
		IsPet:        true,
		Buffs:        this.entity.ToBuffsPb(),
	}
}

func (this *Pet) ToDB() *g.FigherStrip {
	return this.Strip()
}
