package http

import (
	"fmt"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/lobby"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
)

var nextNoticeId = 0

// 公告
func (this *Http) GameNotice(sid, id int32, parames []string) error {
	json := config.GetJsonData("noticeText", id)
	if json == nil {
		return fmt.Errorf("文本不存在")
	}
	text := map[string]string{}
	if textJson, ok := json["text"].(map[string]interface{}); ok {
		for k, v := range textJson {
			text[k] = ut.String(v)
		}
	}
	this.NotifyLobbyAllUserNR(sid, &pb.OnUpdateLobbyNotify{
		Type: int32(lobby.NQ_SYS_NOTICE),
		Data_2: &pb.SysNoticeInfo{
			Id:      id,
			Parames: parames,
		},
	})
	return nil
}

// 通知大厅所有用户
func (this *Http) NotifyLobbyAllUserNR(sid int32, data *pb.OnUpdateLobbyNotify) {
	msg := &pb.LOBBY_ONNOTIFY_NOTIFY{List: []*pb.OnUpdateLobbyNotify{data}}
	msgMap := map[int32][]byte{}
	if body, err := pb.ProtoMarshal(msg); err == "" {
		msgMap[sid] = body
	} else {
		log.Error("NotifyLobbyAllUserNR error: %v", err)
		return
	}
	serverSessions := this.GetApp().GetServersByType("lobby")
	msgMapBytes := ut.Bytes(msgMap)
	for _, m := range serverSessions {
		m.CallNR(slg.RPC_LOBYY_NOTIFY_ALL_USER, msgMapBytes)
	}
}
