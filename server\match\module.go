package match

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/dh"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
	"github.com/huyangv/vmqant/server"
)

var Module = func() module.Module {
	return new(Match)
}

type Match struct {
	basemodule.BaseModule
}

func (this *Match) GetType() string {
	return "match" // 很关键,需要与配置文件中的Module配置对应
}

func (this *Match) Version() string {
	return "1.0.0" // 可以在监控时了解代码版本
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Match) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
	if serverType := ut.String(app.GetSettings().Settings["ServerType"]); serverType == "match" || serverType == "development" {
		url := app.GetSettings().Settings["GameMongodbURL"].(string)
		dbname := app.GetSettings().Settings["GameMongodbDB"].(string)
		InitRoomDB(url, dbname)
		LoadLobbyMachs()
		LoadGameMachs()
		LoadAllRoom(this)
		LoadSupMachs()
		LoadAllCustomRooms()
	}
}

func (this *Match) OnInit(app module.App, settings *conf.ModuleSettings) {
	id := settings.ID
	this.BaseModule.OnInit(this, app, settings, server.ID(id))
	this.InitRpc()
	this.InitHttp()
	dh.InitDhLog()

	this.GetServer().RegisterGO("HD_GetCustomRooms", this.getCustomRooms)
	this.GetServer().RegisterGO("HD_GetCustomRoomInfo", this.getCustomRoomInfo)
	this.GetServer().RegisterGO("HD_OpenCustomRoomInfo", this.openCustomRoomInfo)
}

func (this *Match) Run(closeSig chan bool) {
	this.InitMatchInfo()
	RunTick(this)
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	StopTick()
}

func (this *Match) OnDestroy() {
	this.BaseModule.OnDestroy()
}

// 获取自定义房间列表
func (this *Match) getCustomRooms(session gate.Session, msg *pb.MATCH_HD_GETCUSTOMROOMS_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	s2c := &pb.MATCH_HD_GETCUSTOMROOMS_S2C{List: []*pb.CustomRoomInfo{}}
	CustinRooms.ForEach(func(v *CustomRoom, k int32) bool {
		if v.Opened {
			return true
		}
		s2c.List = append(s2c.List, v.ToPb(false))
		return true
	})
	return pb.ProtoMarshal(s2c)
}

// 获取自定义房间信息
func (this *Match) getCustomRoomInfo(session gate.Session, msg *pb.MATCH_HD_GETCUSTOMROOMINFO_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	sid := msg.GetSid()
	room := CustinRooms.Get(sid)
	if room == nil {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	return pb.ProtoMarshal(&pb.MATCH_HD_GETCUSTOMROOMINFO_S2C{Info: room.ToPb(true)})
}

// 开启自定义区服
func (this *Match) openCustomRoomInfo(session gate.Session, msg *pb.MATCH_HD_OPENCUSTOMROOMINFO_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	sid := msg.GetSid()
	room := CustinRooms.Get(sid)
	if room == nil {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	if room.Creator != uid {
		return nil, ecode.NOT_OPERATING_AUTH.String()
	}
	this.OpenCustomRoom(room)
	return pb.ProtoMarshal(&pb.MATCH_HD_OPENCUSTOMROOMINFO_S2C{})
}

func checkSession(session gate.Session) string {
	if session == nil {
		return ""
	} else {
		return session.GetUserID()
	}
}

func getInvokeRpcInfo(serverType, id string, params []interface{}) (string, []interface{}) {
	moduleType := serverType
	if serverType == slg.MACH_SERVER_TYPE_LOBBY || serverType == slg.MACH_SERVER_TYPE_GAME {
		moduleType = serverType + "@" + serverType + id
	}
	return moduleType, array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
}

// 发送Rpc到lobby
func (this *Match) InvokeLobbyRpc(id, _func string, params ...interface{}) (result interface{}, err string) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_LOBBY, id, params)
	return this.InvokeWithCleanup(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到lobby
func (this *Match) InvokeLobbyRpcNR(id, _func string, params ...interface{}) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_LOBBY, id, params)
	this.InvokeNR(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到指定队伍的大厅服
func (this *Match) InvokeTeamFunc(uid string, _func string, params ...interface{}) (result interface{}, err string) {
	lid := rds.MallocTeamLid(uid)
	if lid == "" {
		log.Error("InvokeTeamFunc rds get lid uid:%v, _func: %v, params: %v", uid, _func, params)
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	params = append([]interface{}{uid}, params...)
	result, err = this.InvokeLobbyRpc(lid, _func, params...)
	return
}

// 发送Rpc到指定队伍的大厅服 不需要回复
func (this *Match) InvokeTeamFuncNR(uid string, _func string, params ...interface{}) {
	lid := rds.MallocTeamLid(uid)
	if lid == "" {
		log.Error("InvokeTeamFuncNR rds get lid uid:%v, _func: %v, params: %v", uid, _func, params)
	}
	params = append([]interface{}{uid}, params...)
	this.InvokeLobbyRpc(lid, _func, params...)
}

// 发送Rpc到Game
func (this *Match) InvokeGameRpc(id int32, _func string, params ...interface{}) (result interface{}, err string) {
	if id == 0 {
		return
	}
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_GAME, ut.String(id), params)
	return this.InvokeWithCleanup(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到Game
func (this *Match) InvokeGameRpcNR(id int32, _func string, params ...interface{}) {
	if id == 0 {
		return
	}
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_GAME, ut.String(id), params)
	this.InvokeNR(moduleType, _func, paramsBytesArr...)
}
