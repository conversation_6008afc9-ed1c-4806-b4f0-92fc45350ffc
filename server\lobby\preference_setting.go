package lobby

type PreferenceInfo struct {
	uid   string
	key   string
	value interface{}
}

// var preferenceQueue = make(chan PreferenceInfo, 10000)

// 设置偏好设置
func PutPreferenceData(uid string, key string, val interface{}) {
	// preferenceQueue <- PreferenceInfo{uid: uid, key: key, value: val}
}

// 保存偏好设置
func UpdatePreferenceData() {
	//if len(preferenceQueue) == 0 {
	//	return
	//}
	//dataMap := map[string][]PreferenceInfo{}
	//for len(preferenceQueue) > 0 {
	//	info := <-preferenceQueue
	//	arr := dataMap[info.uid]
	//	if arr == nil {
	//		arr = []PreferenceInfo{}
	//	}
	//	dataMap[info.uid] = append(arr, info)
	//}
	//for uid, arr := range dataMap {
	//	data, err := db.FindByUid(uid)
	//	if err != "" {
	//		continue
	//	} else if data.PreferenceMap == nil {
	//		data.PreferenceMap = map[string]interface{}{}
	//	}
	//	for _, m := range arr {
	//		data.PreferenceMap[m.key] = m.value
	//	}
	//	db.UpdateOne(uid, "preferencemap", data.PreferenceMap)
	//}
}
