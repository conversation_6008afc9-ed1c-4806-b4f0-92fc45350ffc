package astar

import (
	"sort"

	ut "slgsrv/utils"
)

type ClosedInfo struct {
	G  int32
	F  int32
	M  int32
	NG int32
}

// A星寻路
type AStarRange struct {
	checkHasPass func(x, y int32, camp int32) int32 // 检测方法
}

func (this *AStarRange) Init(checkHasPass func(x, y int32, camp int32) int32) *AStarRange {
	this.checkHasPass = checkHasPass
	return this
}

// // 新建一个节点
// func (this *AStarRange) newNode(x, y int32) *AStarNode {
// 	return new(AStarNode).Init(x, y)
// }

// // 新建一个节点
// func (this *AStarRange) newStep(node *AStarNode, parent *AStep) *AStep {
// 	return new(AStep).Init(node, parent)
// }

// 新建一个节点
func (this *AStarRange) newNode(x, y int32) *AStarNode {
	return NewAStartNode().Init(x, y)
}

// 新建一个节点
func (this *AStarRange) newStep(node *AStarNode, parent *AStep) *AStep {
	return NewAStep().Init(node, parent)
}

// 寻路
// 一次移动一次搜索
func (this *AStarRange) Search(start *ut.Vec2, end *ut.Vec2, dir, moveRange, camp, maxSearchCount int32) [][]*ut.Vec2 {
	if start.Equals(end) {
		return [][]*ut.Vec2{}
	}
	// 记录所有AStep和AStarNode 寻路完成后清理并放回对象池
	var allAStep []*AStep
	var allAStarNode []*AStarNode
	defer func() {
		for _, v := range allAStep {
			v.Clean()
		}
		for _, v := range allAStarNode {
			v.Clean()
		}
	}()
	dirPoints := DIR_POINTS_4_TO_SEARCH[2]
	dirCount := int32(len(dirPoints))
	if dir >= 0 && dir < dirCount {
		dirPoints = DIR_POINTS_4_TO_SEARCH[dir]
	}
	beginNode := this.newNode(start.X, start.Y)
	allAStarNode = append(allAStarNode, beginNode)
	begin := this.newStep(beginNode, nil)
	allAStep = append(allAStep, begin)
	steps := []*AStep{begin}
	stepMap := map[string]*AStep{begin.ID(): begin}
	closed := map[string]*ClosedInfo{}
	maxG := int32(ut.Ceil(float64(this.GetPointToPointDis(start, end)) / float64(moveRange)))
	var minG int32 = 100
	var minStep *AStep = nil
	for len(steps) > 0 {
		begin = steps[0]
		steps = steps[1:]
		delete(stepMap, begin.ID())
		if begin.G > maxSearchCount {
			continue // 最大搜索深度
		} else if begin.G > minG {
			continue
		} else if begin.last.Equals(end) {
			if minStep == nil || (begin.G < minStep.G || (begin.G == minStep.G && begin.Equals(minStep) > 0)) {
				minStep = begin
				minG = begin.G
				// 这里可以看是否是最短 并且都是走满了的 就直接结束搜索
				if minG <= maxG && begin.IsPerfectPath(moveRange) {
					break
				}
			}
			// break
			continue
		}
		node := this.newNode(begin.last.X, begin.last.Y)
		allAStarNode = append(allAStarNode, node)
		opened := []*AStarNode{node}
		openedMap := map[string]*AStarNode{node.ID(): node}
		stepGMap := map[string]*AStep{}
		for _, m := range steps {
			this.addStepGMap(m, stepGMap)
		}
		// 找出周围可移动到的路径点
		for len(opened) > 0 {
			node = opened[0]
			opened = opened[1:]
			delete(openedMap, node.ID())
			if node.point.Equals(end) {
				break
			} else if node.G >= moveRange {
				continue
			}
			closed[node.uid] = &ClosedInfo{G: begin.G, F: begin.F, M: begin.M, NG: node.G}
			// 找周围的是否可以移动
			for i := int32(0); i < dirCount; i++ {
				d := dirPoints[i]
				x := node.point.X + d.point.X
				y := node.point.Y + d.point.Y
				state := this.checkHasPass(x, y, camp)
				if state == 0 {
					continue // 不可通行
				}
				// 是否找过了
				id := ut.Itoa(x) + "_" + ut.Itoa(y)
				info := closed[id]
				if info == nil {
				} else if (node.G+1 < moveRange || state == 1) && this.equalsStep(begin, info, node.G) {
					// 如果当前不是找最后一个点 或者找个位置没有人
					// 如果当前次数小于那么就换成找个
					info.G = begin.G
					info.F = begin.F
				} else {
					continue
				}
				step := stepGMap[id]
				if step != nil && begin.G+1 > step.G {
					continue // 如果有更短的就放弃这个
				}
				// 如果开启列表中已经有了 那么看现在这个节点到那的距离是否短一点
				it := openedMap[id]
				if it == nil {
					it = this.newNode(x, y)
					allAStarNode = append(allAStarNode, it)
					it.H = this.GetPointToPointDis(end, it.point)
					it.UpdateParent(node, d.tag, ut.If(state == 2, int32(1), 0))
					opened = append(opened, it)
					openedMap[it.uid] = it
				} else if node.G+node.T+d.tag < it.G+it.T {
					it.UpdateParent(node, d.tag, ut.If(state == 2, int32(1), 0))
				}
				if it.G >= moveRange || it.point.Equals(end) {
					n := it
					if state == 2 { // 如果这个位置有人 就继续上看直到没有人为止
						n = it.parent
						for n != nil && n.P > 0 {
							delete(closed, n.ID())
							n = n.parent
						}
						if n != nil && n.point.Equals(begin.last) {
							n = nil
						}
					}
					if n != nil {
						s := stepMap[n.ID()]
						if s == nil || begin.G+1 < s.G {
							if s != nil {
								s.Init(n, begin)
							} else {
								s = this.newStep(n, begin)
								steps = append(steps, s)
								allAStep = append(allAStep, s)
							}
							s.F += moveRange - int32(len(s.points)) + 1
							stepMap[s.ID()] = s
							this.addStepGMap(s, stepGMap)
						}
					}
				}
			}
			// 排序
			sort.SliceStable(opened, func(i, j int) bool {
				return opened[i].F < opened[j].F
			})
		}
		sort.SliceStable(steps, func(i, j int) bool {
			return this.getStepWeight(steps[i], end) > this.getStepWeight(steps[j], end)
		})
		//     // cc.log('------------------------------------------')
		//     // steps.forEach(m => m.toString())
	}
	//
	step := minStep
	if step == nil {
		step = begin
	}
	points := [][]*ut.Vec2{}
	for step.parent != nil {
		points = append(points, step.points)
		step = step.parent
	}

	// 克隆并倒序
	rst := [][]*ut.Vec2{}
	for i := len(points) - 1; i >= 0; i-- {
		for j, l := 0, len(points[i]); j < l; j++ {
			points[i][j] = points[i][j].Clone()
		}
		rst = append(rst, points[i])
	}

	// fmt.Println("---------------------------需要" + ut.Itoa(len(points)) + "次走完------------------------------")
	// for _, arr := range points {
	// 	fmt.Println(array.Map(arr, func(p *ut.Vec2, _ int32) string { return p.ID() }))
	// }
	return rst
}

func (this *AStarRange) GetPointToPointDis(a *ut.Vec2, b *ut.Vec2) int32 {
	return ut.AbsInt32(a.X-b.X) + ut.AbsInt32(a.Y-b.Y)
}

func (this *AStarRange) equalsStep(step *AStep, info *ClosedInfo, ng int32) bool {
	if step.G != info.G {
		return step.G < info.G
	}
	ag, bg := step.F+ng, info.F+info.NG
	if ag != bg {
		return ag < bg
	}
	return step.M > info.M
}

func (this *AStarRange) addStepGMap(step *AStep, out map[string]*AStep) {
	for i, l := 1, len(step.points); i < l; i++ {
		key := step.points[i].ID()
		st := out[key]
		if st == nil || step.G < st.G {
			out[key] = step
		}
	}
}

func (this *AStarRange) getStepWeight(step *AStep, end *ut.Vec2) int32 {
	aw := ut.If(step.last.Equals(end), int32(2), 1)
	aw = aw*99 + (99 - step.G)
	aw = aw*100 + (99 - step.F)
	// aw = aw * 10 + (9 - step.points.length)
	aw = aw*10 + int32(len(step.points))
	return aw
}
