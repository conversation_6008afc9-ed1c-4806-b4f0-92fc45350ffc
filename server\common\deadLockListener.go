package slg

import (
	"os"
	"strings"
	"time"

	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

func InitDeadlockListener() {
	deadlock.Opts.DeadlockTimeout = time.Millisecond * 100
	deadlock.Opts.OnPotentialDeadlock = onDeadLock
	deadlock.Opts.LogBuf = &DeadLockLogger{}
	deadlock.Opts.Disable = !IsDebug()
	str := "====init dead lock listener==== Disable:" + ut.String(deadlock.Opts.Disable)
	deadlock.Opts.LogBuf.Write([]byte(str))
}

func onDeadLock() {
	// do nothing here
	deadlock.Opts.LogBuf.Write([]byte("===== on dead lock end ====="))
	os.Stderr.Write([]byte("deadlock happened!"))
}

type DeadLockLogger struct{}

func (this *DeadLockLogger) Write(p []byte) (n int, err error) {
	if len(p) == 0 {
		return 0, nil
	}
	str := string(p)
	if strings.ReplaceAll(str, " ", "") == "\n" {
		return 0, nil
	}
	// writeMsg will always add a '\n' character
	if p[len(p)-1] == '\n' {
		p = p[0 : len(p)-1]
	}
	log.Info(string(p))
	return 0, err
}
