package world

import (
	"math"
	"sort"
	"time"

	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// 一个成员
type AlliMember struct {
	Uid               string `json:"uid"`
	nickname          string // 名字
	headIcon          string // 头像
	BattleScoreRecord *pb.BattleScoreRecordByDate

	JoinTime    int64 `json:"join_time"` // 加入时间
	offlineTime int64 // 离线时间
	Job         int32 `json:"job"` // 职位 0.创始人 1.副盟主 2.军师 10.普通成员
	embassyLv   int32 // 大使馆等级
}
type AlliMemberList struct {
	deadlock.RWMutex
	List []*AlliMember
}

func (this *AlliMember) GetOfflineTime() int32 {
	if this.offlineTime > 0 {
		return int32(time.Now().UnixMilli() - this.offlineTime)
	}
	return 0
}

// 一个申请
type AlliApply struct {
	Uid         string `json:"uid"`
	Desc        string `json:"desc"` // 说明
	Time        int64  `json:"time"` // 申请时间
	offlineTime int64  // 离线时间
}
type AlliApplyList struct {
	deadlock.RWMutex
	List []*AlliApply
}

type SelectPolicysMap struct {
	deadlock.RWMutex
	Map map[int32][]int32
}

type AlliLog struct {
	Time   int64    `bson:"time"`   // 时间
	Params []string `bson:"params"` // 参数
	Type   int32    `bson:"type"`   // 日志类型
}

type AlliLogList struct {
	deadlock.RWMutex
	List []*AlliLog
}

type AlliChannel struct {
	Uid          string   `bson:"uid"`           // uid
	Name         string   `bson:"name"`          // 频道名
	Color        string   `bson:"color"`         // 颜色
	MemberUids   []string `bson:"member_uids"`   // 频道成员uid列表
	MemberFilter bool     `bson:"member_filter"` // 是否开启成员过滤
}

type AlliChannelList struct {
	deadlock.RWMutex
	List []*AlliChannel
}

// 盟主投票数据
type AlliLeaderVoteList struct {
	deadlock.RWMutex
	List []*AlliLeaderVoteInfo
	Map  map[string]int32 // 成员投票次数map
}

type AlliLeaderVoteInfo struct {
	Uid  string   `bson:"uid"`  // 候选人uid
	List []string `bson:"list"` // 投票的玩家uid列表
}

func (this *AlliLeaderVoteList) ToPb() []*pb.AlliLeaderVoteInfo {
	ret := []*pb.AlliLeaderVoteInfo{}
	this.RLock()
	for _, v := range this.List {
		ret = append(ret, &pb.AlliLeaderVoteInfo{List: array.Clone(v.List), Uid: v.Uid})
	}
	this.RUnlock()
	return ret
}

// 一个联盟
type Alliance struct {
	Uid       string
	Name      string
	Creater   string // 创建者
	Notice    string // 联盟公告
	ApplyDesc string // 申请说明

	Members       *AlliMemberList                    // 成员列表
	Applys        *AlliApplyList                     // 申请列表
	MapFlag       *ut.MapLock[int32, *g.MapMarkInfo] // 地图标记
	Policys       *PolicyMap                         // 当前政策
	SelectPolicys *SelectPolicysMap                  // 当前可选择的政策
	Logs          *AlliLogList                       // 联盟日志
	ChatChannels  *AlliChannelList                   // 聊天频道
	LeaderVotes   *AlliLeaderVoteList                // 盟主投票信息

	CreateTime           int64 // 创建时间
	LastEditNoticeTime   int64 // 最后一次修改公告时间
	LeaderConfirmEndTime int64 // 盟主确认结束时间

	Icon            int32 // 图标
	MemberPersLimit int32 // 成员人数上限
	SumEmbassyLv    int32 // 成员总大使馆等级
	NoAvoidWar      bool  // 取消免战
	IsConquer       bool  // 是否血战到底模式
}

func NewAlliance() *Alliance {
	return &Alliance{}
}

func (this *Alliance) Init(name string, icon, buildLv int32, creater string, isConquer bool) *Alliance {
	this.Uid = ut.ID()
	this.Name = name
	this.Icon = icon
	this.Creater = creater
	this.CreateTime = time.Now().UnixMilli()
	this.Notice = ""
	this.ApplyDesc = ""
	this.LastEditNoticeTime = 0
	this.Members = &AlliMemberList{List: []*AlliMember{}}
	this.Applys = &AlliApplyList{List: []*AlliApply{}}
	this.MapFlag = ut.NewMapLock[int32, *g.MapMarkInfo]()
	this.Policys = &PolicyMap{Map: map[int32]*g.PolicyInfo{}}
	this.SelectPolicys = &SelectPolicysMap{Map: map[int32][]int32{}}
	this.Logs = &AlliLogList{List: []*AlliLog{}}
	this.MemberPersLimit = buildLv * 2
	this.SumEmbassyLv = buildLv
	this.ChatChannels = &AlliChannelList{List: []*AlliChannel{}}
	this.LeaderVotes = &AlliLeaderVoteList{List: []*AlliLeaderVoteInfo{}, Map: map[string]int32{}}
	this.IsConquer = isConquer
	return this
}

func (this *Alliance) FromDB(data AllianceTableData, isConquer bool) *Alliance {
	this.Uid = data.Uid
	this.Name = data.Name
	this.Icon = data.Icon
	this.Creater = data.Creater
	this.CreateTime = data.CreateTime
	this.Notice = data.Notice
	this.ApplyDesc = data.ApplyDesc
	this.LastEditNoticeTime = data.LastEditNoticeTime
	this.Members = &AlliMemberList{List: data.Members}
	this.Applys = &AlliApplyList{List: data.Applys}
	this.MapFlag = ut.NewMapLock[int32, *g.MapMarkInfo]().FromMap(data.MapMarkMap)
	this.Policys = &PolicyMap{Map: g.InitPolicyByDB(data.Policys)}
	this.SelectPolicys = &SelectPolicysMap{Map: data.SelectPolicys}
	this.MemberPersLimit = data.MemberPersLimit
	this.SumEmbassyLv = constant.CREATE_ALLI_MAX_LV
	this.Logs = &AlliLogList{List: data.AlliLogs}
	this.NoAvoidWar = data.NoAvoidWar
	this.ChatChannels = &AlliChannelList{List: data.ChatChannels}
	this.LeaderVotes = &AlliLeaderVoteList{List: data.LeaderVotes, Map: data.VoteCountMap}
	this.LeaderConfirmEndTime = data.LeaderConfirmEndTime
	this.IsConquer = isConquer
	if this.Creater != "" {
		if this.Icon == 0 {
			this.Icon = int32(ut.Random(1001, 1050)*100) + 1 // 100101
		}
		// if member := this.GetMember(this.Creater); member != nil && member.Job != 0 {
		// 	member.Job = 0 // 兼容盟主职位
		// }
	}
	if this.MapFlag == nil {
		this.MapFlag = ut.NewMapLock[int32, *g.MapMarkInfo]()
	}
	if this.Policys.Map == nil {
		this.Policys.Map = map[int32]*g.PolicyInfo{}
	}
	if this.SelectPolicys.Map == nil {
		this.SelectPolicys.Map = map[int32][]int32{}
	}
	if this.Logs.List == nil {
		this.Logs.List = []*AlliLog{}
	}
	if this.MemberPersLimit == 0 {
		if isConquer {
			this.MemberPersLimit = int32(len(this.Members.List))
		} else {
			this.MemberPersLimit = constant.ALLI_INIT_PERS
		}
	}
	return this
}

func (this *Alliance) ToDB() AllianceTableData {
	voteList, voteMap := this.ToVoteInfoDb()
	return AllianceTableData{
		Uid:                  this.Uid,
		Name:                 this.Name,
		Icon:                 this.Icon,
		Creater:              this.Creater,
		CreateTime:           this.CreateTime,
		Notice:               this.Notice,
		ApplyDesc:            this.ApplyDesc,
		LastEditNoticeTime:   this.LastEditNoticeTime,
		Members:              this.Members.List,
		Applys:               this.Applys.List,
		MapMarkMap:           this.MapFlag.Clone(),
		Policys:              this.ToPolicys(),
		SelectPolicys:        toAlliSelectPolicys[int32](this.SelectPolicys),
		AlliLogs:             this.ToLogs(),
		NoAvoidWar:           this.NoAvoidWar,
		ChatChannels:         this.ToAlliChannels(),
		LeaderVotes:          voteList,
		VoteCountMap:         voteMap,
		LeaderConfirmEndTime: this.LeaderConfirmEndTime,
		MemberPersLimit:      this.MemberPersLimit,
	}
}

func (this *Alliance) ToVoteInfoDb() ([]*AlliLeaderVoteInfo, map[string]int32) {
	arr := []*AlliLeaderVoteInfo{}
	timesMap := map[string]int32{}
	this.LeaderVotes.RLock()
	defer this.LeaderVotes.RUnlock()
	for _, v := range this.LeaderVotes.List {
		voteInfo := &AlliLeaderVoteInfo{Uid: v.Uid, List: array.Clone(v.List)}
		arr = append(arr, voteInfo)
	}
	for k, v := range this.LeaderVotes.Map {
		timesMap[k] = v
	}
	return arr, timesMap
}

func (this *Alliance) ToPb(wld *Model, userId string) *pb.AllianceInfo {
	var voteCount int32
	if this.Creater == "" {
		voteCount = this.GetVoteTimes(userId)
	}
	return &pb.AllianceInfo{
		Uid:                this.Uid,
		Name:               this.Name,
		Icon:               this.Icon,
		Creater:            this.Creater,
		CreateTime:         int64(this.CreateTime),
		Notice:             this.Notice,
		ApplyDesc:          this.ApplyDesc,
		Members:            this.ToMembersPb(wld),
		Applys:             this.ToApplysPb(),
		PersLimit:          this.MemberPersLimit,
		EditNoticeCD:       this.GetEditNoticeCD(),
		MapFlag:            this.ToMapFlagPb(),
		SumEmbassyLv:       this.SumEmbassyLv,
		SelectPolicys:      this.ToSelectPolicysPb(),
		ChatChannels:       this.ToAlliChannelsPb(userId),
		Leadervotes:        this.LeaderVotes.ToPb(),
		ConfirmSurplusTime: int32(ut.MaxInt64(0, this.LeaderConfirmEndTime-ut.Now())),
		VoteTimes:          voteCount,
	}
}

func (this *Alliance) ToBaseInfoPb(createrIndex int32) *pb.AlliBaseInfo {
	info := &pb.AlliBaseInfo{
		Uid:          this.Uid,
		Icon:         this.Icon,
		SumEmbassyLv: this.SumEmbassyLv,
		Policys:      this.ToPolicyPb(),
		CreaterIndex: createrIndex,
	}
	return info
}

// Strip2 给web接口用
func (this *Alliance) Strip2() map[string]interface{} {
	return map[string]interface{}{
		"uid":        this.Uid,
		"name":       this.Name,
		"creater":    this.Creater,
		"createTime": this.CreateTime,
		"count":      len(this.Members.List),
		"notice":     this.Notice,
	}
}

func (this *Alliance) ToShortData() *pb.AllianceShortInfo {
	return &pb.AllianceShortInfo{
		Uid:       this.Uid,
		Name:      this.Name,
		Icon:      this.Icon,
		Pers:      int32(len(this.Members.List)),
		PersLimit: this.MemberPersLimit,
		ApplyDesc: this.ApplyDesc,
	}
}

func (this *Alliance) ToMembersPb(wld *Model) []*pb.AllianceMemberInfo {
	arr := []*pb.AllianceMemberInfo{}
	now := ut.Now()
	for _, m := range this.Members.List {
		landScore, alliScore, landScoreTop, alliScoreTop := wld.GetPlayerLandScoreAndAlliScore(m.Uid)
		exclusives := wld.GetPlayerExclusiveEquips(m.Uid)
		arr = append(arr, &pb.AllianceMemberInfo{
			Uid:             m.Uid,
			Job:             m.Job,
			JoinTime:        m.JoinTime,
			Nickname:        m.nickname,
			HeadIcon:        m.headIcon,
			OfflineTime:     int32(ut.If(m.offlineTime > 0, now-m.offlineTime, 0)),
			AlliScores:      []int32{alliScore, alliScoreTop},
			LandScores:      []int32{landScore, landScoreTop},
			EmbassyLv:       m.embassyLv,
			ExclusiveEquips: array.Map(exclusives, func(m *g.EquipInfo, _ int) *pb.EquipInfo { return m.ToBasePb() }),
		})
	}
	return arr
}

func (this *Alliance) ToApplysPb() []*pb.AllianceApplyInfo {
	arr := []*pb.AllianceApplyInfo{}
	now := ut.Now()
	for _, m := range this.Applys.List {
		arr = append(arr, &pb.AllianceApplyInfo{
			Uid:         m.Uid,
			Time:        int64(m.Time),
			Desc:        m.Desc,
			OfflineTime: int32(ut.If(m.offlineTime > 0, now-m.offlineTime, 0)),
		})
	}
	return arr
}

func (this *Alliance) ToMapFlagPb() map[int32]*pb.MapMarkInfo {
	ret := map[int32]*pb.MapMarkInfo{}
	this.MapFlag.ForEach(func(v *g.MapMarkInfo, k int32) bool {
		ret[k] = v.Topb()
		return true
	})
	return ret
}

func (this *Alliance) ToPolicys() map[int32]*g.PolicyInfo {
	ret := map[int32]*g.PolicyInfo{}
	this.Policys.RLock()
	defer this.Policys.RUnlock()
	for k, v := range this.Policys.Map {
		ret[k] = v
	}
	return ret
}

func (this *Alliance) ToPolicyPb() map[int32]int32 {
	ret := map[int32]int32{}
	this.Policys.RLock()
	defer this.Policys.RUnlock()
	for k, v := range this.Policys.Map {
		ret[k] = v.ID
	}
	return ret
}

func toAlliSelectPolicys[T int32 | int](obj *SelectPolicysMap) map[T][]T {
	ret := map[T][]T{}
	obj.RLock()
	defer obj.RUnlock()
	for k, v := range obj.Map {
		ret[T(k)] = array.Map(v, func(m int32, _ int) T { return T(m) })
	}
	return ret
}

func (this *Alliance) ToSelectPolicysPb() map[int32]*pb.Int32ArrayInfo {
	ret := map[int32]*pb.Int32ArrayInfo{}
	this.SelectPolicys.RLock()
	defer this.SelectPolicys.RUnlock()
	for k, v := range this.SelectPolicys.Map {
		ret[int32(k)] = &pb.Int32ArrayInfo{Arr: array.Map(v, func(m int32, _ int) int32 { return int32(m) })}
	}
	return ret
}

// 用于上报
func (this *Alliance) ToPolicyTrack() []map[string]interface{} {
	this.Policys.RLock()
	defer this.Policys.RUnlock()
	policys := []map[string]interface{}{}
	for _, m := range this.Policys.Map {
		policys = append(policys, map[string]interface{}{"policy_id": m.ID})
	}
	return policys
}

// 设置盟主
func (this *Alliance) SetLeader(leaderUid string) {
	if this.Creater != "" {
		return
	}
	member := this.GetMember(leaderUid)
	if member == nil {
		log.Error("SetLeader error! leaderUid: %v", leaderUid)
		return
	}
	// 设置职位
	member.Job = 0
	// 设置盟主
	this.Creater = leaderUid
	this.LeaderConfirmEndTime = ut.Now() + constant.ALLI_CONFIRN_TIME
}

// 盟主确认
func (this *Alliance) LeaderConfirm(name string, icon int32) {
	this.Name = name
	this.Icon = icon
	this.LeaderConfirmEndTime = 0
	// 清理投票数据
	this.LeaderVotes.Lock()
	this.LeaderVotes.List = []*AlliLeaderVoteInfo{}
	this.LeaderVotes.Map = map[string]int32{}
	this.LeaderVotes.Unlock()
}

// 检测更新可选择的政策列表
func (this *Alliance) CheckUpdateSelectPolicys() {
	this.Policys.RLock()
	defer this.Policys.RUnlock()
	this.SelectPolicys.Lock()
	defer this.SelectPolicys.Unlock()
	if len(this.Policys.Map) >= 3 {
		this.SelectPolicys = &SelectPolicysMap{Map: map[int32][]int32{}}
		return
	}
	preIdMap := map[int32]bool{}
	for _, m := range this.Policys.Map {
		preIdMap[m.ID] = true
	}
	for i, l := int32(0), int32(len(constant.ALLI_POLICY_SLOT_CONF)); i < l; i++ {
		lv := constant.ALLI_POLICY_SLOT_CONF[i]
		if p := this.Policys.Map[i]; p != nil {
			delete(this.SelectPolicys.Map, i)
			continue
		} else if !this.CheckPolicySlot(i) {
			break
		} else if cnt := int((i + 1) * 3); this.SelectPolicys.Map[i] == nil || len(this.SelectPolicys.Map[i]) != cnt {
			datas := array.Filter(config.GetJson("policy").Datas, func(m map[string]interface{}, _ int) bool {
				return ut.Int32(m["need_embassy_lv"]) <= lv && !preIdMap[ut.Int32(m["id"])] && ut.Int(m["weight"]) > 0
			})
			this.SelectPolicys.Map[i] = g.RandomPolicyIds(datas, cnt)
		}
		break
	}
	// 兼容 删除多的
	if len(this.SelectPolicys.Map) >= 2 {
		cnt := 0
		for i, l := int32(0), int32(len(constant.ALLI_POLICY_SLOT_CONF)); i < l; i++ {
			if arr := this.SelectPolicys.Map[i]; arr != nil {
				if len(arr) > 0 && cnt == 0 {
					cnt += 1
				} else {
					delete(this.SelectPolicys.Map, i)
				}
			}
		}
	}
}

// 当前成员人数
func (this *Alliance) GetMemberCount() int {
	this.Members.RLock()
	defer this.Members.RUnlock()
	return len(this.Members.List)
}

// 添加成员
func (this *Alliance) AddMember(uid string) *AlliMember {
	this.Members.Lock()
	defer this.Members.Unlock()
	member := &AlliMember{Uid: uid}
	this.Members.List = append(this.Members.List, member)
	return member
}

// 删除成员
func (this *Alliance) RemoveMember(uid string) {
	this.Members.Lock()
	defer this.Members.Unlock()
	this.Members.List = array.RemoveItem(this.Members.List, func(m *AlliMember) bool { return m.Uid == uid })
}

// 获取成员
func (this *Alliance) GetMember(uid string) *AlliMember {
	this.Members.Lock()
	defer this.Members.Unlock()
	if member := array.Find(this.Members.List, func(m *AlliMember) bool { return m.Uid == uid }); member != nil {
		return member
	}
	return nil
}

// 获取成员的uids
func (this *Alliance) GetMemberUids() []string {
	this.Members.Lock()
	defer this.Members.Unlock()
	uids := []string{}
	for _, m := range this.Members.List {
		uids = append(uids, m.Uid)
	}
	return uids
}

// 获取成员的uids
func (this *Alliance) GetMemberUidAndNames() [][]string {
	this.Members.Lock()
	defer this.Members.Unlock()
	arr := [][]string{}
	for _, m := range this.Members.List {
		arr = append(arr, []string{m.Uid, m.nickname, ut.String(m.Job)})
	}
	return arr
}

// 获取管理apply成员
func (this *Alliance) GetMemberUidsByApplyMgr() []string {
	this.Members.Lock()
	defer this.Members.Unlock()
	uids := []string{}
	for _, m := range this.Members.List {
		if m.Job <= 1 {
			uids = append(uids, m.Uid)
		}
	}
	return uids
}

// 是否成员
func (this *Alliance) IsInMember(uid string) bool {
	this.Members.RLock()
	defer this.Members.RUnlock()
	for _, m := range this.Members.List {
		if m.Uid == uid {
			return true
		}
	}
	return false
}

// 是否在申请列表中
func (this *Alliance) IsInApplyList(uid string) bool {
	this.Applys.RLock()
	defer this.Applys.RUnlock()
	for _, m := range this.Applys.List {
		if m.Uid == uid {
			return true
		}
	}
	return false
}

// 添加申请
func (this *Alliance) AddApply(uid string) *AlliApply {
	this.Applys.Lock()
	defer this.Applys.Unlock()
	apply := &AlliApply{Uid: uid, Time: time.Now().UnixMilli()}
	this.Applys.List = append(this.Applys.List, apply)
	return apply
}

// 删除申请
func (this *Alliance) RemoveApply(uid string) {
	this.Applys.Lock()
	defer this.Applys.Unlock()
	for i := len(this.Applys.List) - 1; i >= 0; i-- {
		if this.Applys.List[i].Uid == uid {
			this.Applys.List = append(this.Applys.List[:i], this.Applys.List[i+1:]...)
			return
		}
	}
}

// 刷新成员在线状态
func (this *Alliance) UpdateMemberOnlineState(uid string, time int64) bool {
	this.Members.RLock()
	defer this.Members.RUnlock()
	for _, m := range this.Members.List {
		if m.Uid == uid {
			m.offlineTime = time
			return true
		}
	}
	return false
}

// 刷新申请人员在线状态
func (this *Alliance) UpdateApplyOnlineState(uid string, time int64) bool {
	this.Applys.RLock()
	defer this.Applys.RUnlock()
	for _, m := range this.Applys.List {
		if m.Uid == uid {
			m.offlineTime = time
			return true
		}
	}
	return false
}

// 刷新成员昵称头像信息
func (this *Alliance) updateMemberBaseInfo(uid string, nickname string, headIcon string) {
	this.Members.RLock()
	defer this.Members.RUnlock()
	for _, m := range this.Members.List {
		if m.Uid == uid {
			m.nickname = nickname
			m.headIcon = headIcon
			return
		}
	}
}

// 获取修改联盟公告CD
func (this *Alliance) GetEditNoticeCD() int32 {
	t := constant.ALLI_EDIT_NOTICE_INTERVAL - (time.Now().UnixMilli() - this.LastEditNoticeTime)
	if t < 0 {
		return 0
	}
	return int32(t)
}

// 获取职位数量
func (this *Alliance) GetJobCount(job int32) int {
	this.Members.RLock()
	defer this.Members.RUnlock()
	return len(array.Filter(this.Members.List, func(m *AlliMember, _ int) bool { return m.Job == job }))
}

// 获取成员职位
func (this *Alliance) GetMemberJob(uid string) int32 {
	this.Members.RLock()
	defer this.Members.RUnlock()
	for _, m := range this.Members.List {
		if m.Uid == uid {
			return m.Job
		}
	}
	return constant.MEMBER
}

// 是否盟主或者副盟主
func (this *Alliance) IsCreaterOrVice(uid string) bool {
	return uid == this.Creater || this.GetMemberJob(uid) == constant.CREATER_VICE
}

// 是否盟主或者军师
func (this *Alliance) IsCreaterOrMilitary(uid string) bool {
	return uid == this.Creater || this.GetMemberJob(uid) == constant.MILITARY
}

// 获取投票次数
func (this *Alliance) GetVoteTimes(userId string) int32 {
	this.LeaderVotes.RLock()
	defer this.LeaderVotes.RUnlock()
	return this.LeaderVotes.Map[userId]
}

// 盟主投票
func (this *Alliance) VoteLeader(leaderUid, userId string) bool {
	member := this.GetMember(userId)
	if member == nil {
		return false
	}
	if this.Creater != "" {
		return false
	}
	this.LeaderVotes.Lock()
	defer this.LeaderVotes.Unlock()
	exist := false
	voteTimes := this.LeaderVotes.Map[userId]
	if voteTimes >= constant.ALLI_VOTE_MAX { // 改票次数上限
		return false
	}
	this.LeaderVotes.Map[userId] = voteTimes + 1
	for _, v := range this.LeaderVotes.List {
		if v.Uid == leaderUid {
			// 投票给该候选人
			if !array.Has(v.List, userId) {
				v.List = append(v.List, userId)
			}
			exist = true
		} else if index := array.FindIndex(v.List, func(m string) bool { return m == userId }); index >= 0 {
			// 其他候选人有该玩家的投票则移除
			v.List = append(v.List[:index], v.List[index+1:]...)
		}
	}
	if !exist {
		// 不存在则添加候选人信息
		this.LeaderVotes.List = append(this.LeaderVotes.List, &AlliLeaderVoteInfo{Uid: leaderUid, List: []string{userId}})
	}

	// 按投票人数降序
	sort.Slice(this.LeaderVotes.List, func(i, j int) bool {
		return len(this.LeaderVotes.List[i].List) > len(this.LeaderVotes.List[j].List)
	})
	// 检测当选条件
	total, maxCnt, otherCnt := 0, 0, 0
	for i, v := range this.LeaderVotes.List {
		total += len(v.List)
		if i == 0 {
			maxCnt = len(v.List)
		} else {
			otherCnt += len(v.List)
		}
	}
	notVoteCnt := len(this.Members.List) - total
	if maxCnt > notVoteCnt+otherCnt {
		// 最多的票数大于未投票人数加其余候选人总票数 则该候选人直接当选
		this.SetLeader(this.LeaderVotes.List[0].Uid)
	} else if notVoteCnt == 0 {
		// 所有人都已投票 票数最高的人当选
		maxVoteCount := 0 // 并列最高票数的人数
		for _, v := range this.LeaderVotes.List {
			if len(v.List) == maxCnt {
				maxVoteCount++
			} else {
				break
			}
		}
		leaderUid := this.LeaderVotes.List[0].Uid
		if maxVoteCount > 1 {
			// 多人并列最高票数 随机一个
			index := ut.Random(0, maxVoteCount-1)
			leaderUid = this.LeaderVotes.List[index].Uid
		}
		this.SetLeader(leaderUid)
	}
	return true
}

// 添加地图标记
func (this *Alliance) AddMapFlag(index int32, flag int32, desc string) {
	flag = ut.ClampInt32(flag, 1, 10)
	// 删除相同类型的标记
	this.MapFlag.DeleteEach(func(v *g.MapMarkInfo, k int32) bool {
		return v.Flag == flag
	})
	var mark *g.MapMarkInfo
	if mark = this.MapFlag.Get(index); mark == nil {
		mark = g.NewMapMarkInfo(index, flag, desc)
		this.MapFlag.Set(index, mark)
	} else {
		mark.Flag = flag
		mark.Desc = desc
	}
}

// 删除地图标记
func (this *Alliance) RemoveMapFlag(index int32) {
	this.MapFlag.Del(index)
}

// 删除所有标记
func (this *Alliance) RemoveAllMapFlag() {
	this.MapFlag.Clean()
}

// 设置联盟政策
func (this *Alliance) SetPolicy(index int32, id int32) (*g.PolicyInfo, string) {
	this.Policys.Lock()
	defer this.Policys.Unlock()
	if this.Policys.Map[index] != nil {
		return nil, ecode.YET_SELECT.String()
	}
	this.SelectPolicys.Lock()
	defer this.SelectPolicys.Unlock()
	arr := this.SelectPolicys.Map[index]
	if arr == nil || !array.Has(arr, id) {
		return nil, ecode.UNKNOWN.String()
	}
	// 直接设置
	policy := g.NewPolicyInfo(id)
	this.Policys.Map[index] = policy
	// 删除选择过的
	delete(this.SelectPolicys.Map, index)
	return policy, ""
}

// 获取联盟政策效果
func (this *Alliance) GetPolicyEffectByType(tp int32) int32 {
	this.Policys.RLock()
	defer this.Policys.RUnlock()
	for i, m := range this.Policys.Map {
		if !this.CheckPolicySlot(i) { // 这里需要赛选满足等级的
		} else if effectType := m.GetEffect(); effectType > 0 && effectType == tp {
			return m.ID
		}
	}
	return 0
}

// 添加联盟日志
func (this *Alliance) AddAlliLog(logType int32, params ...string) {
	this.Logs.Lock()
	defer this.Logs.Unlock()
	newLog := &AlliLog{
		Type:   logType,
		Time:   time.Now().UnixMilli(),
		Params: params,
	}
	this.Logs.List = append([]*AlliLog{newLog}, this.Logs.List...)
}

// 日志转pb
func (this *Alliance) ToLogsPb(start, count int) []*pb.AllienceLogInfo {
	arr := []*pb.AllienceLogInfo{}
	this.Logs.RLock()
	for i, v := range this.Logs.List {
		if i >= count {
			break
		}
		arr = append(arr, &pb.AllienceLogInfo{
			Type:   v.Type,
			Time:   v.Time,
			Params: v.Params,
		})
	}
	this.Logs.RUnlock()
	return arr
}

// 创建联盟聊天副频道
func (this *Alliance) CreateChatChannel(name, uid, color string, memberUids []string, memberFilter bool) (alliChannel *AlliChannel, err string) {
	this.ChatChannels.Lock()
	defer this.ChatChannels.Unlock()
	if len(this.ChatChannels.List) >= constant.ALLI_CHAT_CHANNEL_MAX {
		return nil, ecode.ALLI_CHAT_CHANNEL_LIMIT.String()
	}
	exist := array.Find(this.ChatChannels.List, func(m *AlliChannel) bool { return m.Name == name })
	if exist != nil {
		// 频道名称已存在
		return nil, ecode.NAME_EXIST.String()
	}
	alliChannel = &AlliChannel{Uid: uid, Name: name, Color: color, MemberUids: memberUids, MemberFilter: memberFilter}
	this.ChatChannels.List = append(this.ChatChannels.List, alliChannel)
	return
}

// 删除联盟聊天副频道
func (this *Alliance) DelChatChannel(uid string) string {
	this.ChatChannels.Lock()
	defer this.ChatChannels.Unlock()
	index := array.FindIndex(this.ChatChannels.List, func(m *AlliChannel) bool { return m.Uid == uid })
	if index == -1 {
		return ""
	}
	name := this.ChatChannels.List[index].Uid
	this.ChatChannels.List = append(this.ChatChannels.List[:index], this.ChatChannels.List[index+1:]...)
	return name
}

// 更新联盟聊天副频道
func (this *Alliance) UpdateChatChannel(uid string, memberUids []string, memberFilter bool) (alliChannel *AlliChannel, err string) {
	this.ChatChannels.Lock()
	defer this.ChatChannels.Unlock()
	alliChannel = array.Find(this.ChatChannels.List, func(m *AlliChannel) bool { return m.Uid == uid })
	if alliChannel == nil {
		// 频道名称不存在
		err = ecode.ALLI_CHAT_NOT_EXIST.String()
		return
	}
	alliChannel.MemberUids = memberUids
	alliChannel.MemberFilter = memberFilter
	return
}

// 获取联盟聊天副频道
func (this *Alliance) GetChatChannel(uid string) *AlliChannel {
	this.ChatChannels.RLock()
	defer this.ChatChannels.RUnlock()
	return array.Find(this.ChatChannels.List, func(m *AlliChannel) bool { return m.Uid == uid })
}

// 检测指定槽位联盟政策是否满足等级
func (this *Alliance) CheckPolicySlot(index int32) bool {
	lv := constant.ALLI_POLICY_SLOT_CONF[index]
	memberCount := this.GetMemberCount()
	if this.IsConquer {
		// 血战到底每个槽位需要的等级按人数比例计算
		lv = int32(math.Ceil(float64(memberCount) * float64(lv) / float64(constant.ALLI_MAX_PERS)))
	}
	return this.SumEmbassyLv >= lv
}

func (this *Alliance) ToLogs() []*AlliLog {
	arr := []*AlliLog{}
	this.Logs.RLock()
	for _, v := range this.Logs.List {
		arr = append(arr, v)
	}
	this.Logs.RUnlock()
	return arr
}

func (this *Alliance) ToAlliChannels() []*AlliChannel {
	arr := []*AlliChannel{}
	this.ChatChannels.RLock()
	for _, v := range this.ChatChannels.List {
		arr = append(arr, v)
	}
	this.ChatChannels.RUnlock()
	return arr
}

func (this *Alliance) ToAlliChannelsPb(userId string) []*pb.AlliChannelInfo {
	arr := []*pb.AlliChannelInfo{}
	this.ChatChannels.RLock()
	for _, v := range this.ChatChannels.List {
		if v.MemberFilter && !array.Has(v.MemberUids, userId) {
			continue
		}
		arr = append(arr, v.ToPb())
	}
	this.ChatChannels.RUnlock()
	return arr
}

func (this *AlliChannel) ToPb() *pb.AlliChannelInfo {
	return &pb.AlliChannelInfo{
		Uid:          this.Uid,
		Name:         this.Name,
		Color:        this.Color,
		MemberUids:   this.MemberUids,
		MemberFilter: this.MemberFilter,
	}
}
