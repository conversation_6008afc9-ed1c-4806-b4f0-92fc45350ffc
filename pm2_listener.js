const pm2 = require('/usr/local/lib/node_modules/pm2');
const http = require('https');

// 飞书告警 URL
const webhookUrl = 'https://open.feishu.cn/open-apis/bot/v2/hook/9c3c4b20-2760-4d8e-9113-fd78d7b9e61d';

const myProcessName = 'event-listener';

// 定义时间间隔（毫秒）
const intervals = [10 * 60 * 1000, 30 * 60 * 1000, 60 * 60 * 1000, 600 * 60 * 1000]; // 10分钟, 30分钟, 60分钟, 600分钟

// 定义全局变量来跟踪通知的状态
let lastNotificationTime = null;
let notificationCount = 0;

// 消息内容
function createMessage(errorText) {
  return JSON.stringify({
    msg_type: 'text',
    content: {
      text: errorText,
    },
  });
}

// 发送HTTP请求的函数
function sendErrorNotification(errorMessage) {
  const message = createMessage(errorMessage);

  const options = {
    hostname: new URL(webhookUrl).hostname,
    port: 443,
    path: new URL(webhookUrl).pathname + new URL(webhookUrl).search,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(message),
    },
  };

  const req = http.request(options, (res) => {
    let responseBody = '';

    res.on('data', (chunk) => {
      responseBody += chunk;
    });

    res.on('end', () => {
      if (res.statusCode === 200) {
        console.log('Message sent successfully.');
        notificationCount++;
        lastNotificationTime = Date.now();
      } else {
        console.log('Failed to send message:', res.statusCode);
      }
    });
  });

  req.on('error', (error) => {
    console.error('Error sending message:', error.message);
  });

  req.write(message);
  req.end();
}

// 检查是否可以发送通知
function shouldSendNotification() {
  if (!lastNotificationTime) {
    return true;
  }
  
  const now = Date.now();
  const interval = notificationCount < intervals.length ? intervals[notificationCount] : intervals[intervals.length - 1];
  
  return (now - lastNotificationTime) >= interval;
}

pm2.connect(function(err) {
  if (err) {
    console.error('无法连接到PM2:', err);
    process.exit(2);
  }

  pm2.launchBus(function(err, bus) {
    if (err) {
      console.error('无法启动PM2事件总线:', err);
      return;
    }

    // 监听普通输出日志事件
    bus.on('log:out', function(packet) {
      const proc = packet.process;

      // 检查进程名称或ID来确定是否是当前进程的日志
      if (proc.name !== myProcessName) {
        console.log(`进程: ${proc.name}  日志: ${packet.data}`);
      }
    });

    // 监听错误日志事件
    bus.on('log:err', function(packet) {
      const proc = packet.process;
      if (proc.name !== myProcessName) {
        const errorMessage = `当前进程 ${proc.name} (ID: ${proc.pm_id}) 错误: ${packet.data}`;
        console.log(errorMessage);
        // 检查是否应该发送通知
        if (shouldSendNotification()) {
          sendErrorNotification(errorMessage);
        } else {
          console.log('Notification skipped due to timing policy.');
        }   
      }
    });

  });
});