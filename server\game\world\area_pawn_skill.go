package world

import (
	"strings"

	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
)

// 技能
type PawnSkill struct {
	Params interface{}
	json   map[string]interface{}

	Value         float64
	Id            int32
	BaseId        int32
	Type          int32
	Lv            int32
	IntensifyType int32 // 强化类型

	UseType int32 // 使用类型
	Target  int32

	needAttackTime int32 // 需要攻击的时间
	needHitTime    int32 // 需要击中的时间
	bulletId       int32 // 子弹id
}

func NewPawnSkill(id int32) *PawnSkill {
	return new(PawnSkill).Init(id)
}

func (this *PawnSkill) Init(id int32) *PawnSkill {
	json := config.GetJsonData("pawnSkill", id)
	if json == nil {
		return nil
	}
	this.Id = id
	this.Type = ut.Int32(json["type"])
	this.UseType = ut.Int32(json["use_type"])
	this.Target = ut.Int32(json["target"])
	this.Value = ut.Float64(json["value"])
	this.Params = json["params"]
	this.json = json
	this.BaseId = id / 100
	this.Lv = id % 100
	this.InitAttackAnimTime(nil)
	return this
}

func (this *PawnSkill) InitAttackAnimTime(pawn g.Pawn) {
	attackAnimTimeStr := ut.String(this.json["anim_time"])
	if pawn == nil {
	} else if portrayal := pawn.GetPortrayal(); portrayal != nil {
		if s := ut.String(portrayal.GetJson()["skill_anim_time"]); s != "" {
			idStr := ut.Itoa(this.BaseId)
			strArr := strings.Split(s, "|")
			for _, str := range strArr {
				arr := strings.Split(str, ":")
				if len(arr) == 1 {
					attackAnimTimeStr = arr[0]
				} else if len(arr) == 2 && arr[0] == idStr {
					attackAnimTimeStr = arr[1]
				} else {
					continue
				}
				break
			}
		}
	} else if skinId := pawn.GetSkinID(); skinId > 0 {
		if json := config.GetJsonData("pawnSkin", skinId); json != nil {
			if s := ut.String(json["skill_anim_time"]); s != "" {
				attackAnimTimeStr = s
			}
		}
	}
	if arr := ut.StringToFloats(attackAnimTimeStr, ","); len(arr) == 3 {
		this.needAttackTime = int32(arr[0] * 1000.0)
		this.needHitTime = int32(arr[1] * 1000.0)
		this.bulletId = int32(arr[2])
	}
}

func (this *PawnSkill) GetId() int32              { return this.Id }
func (this *PawnSkill) GetBaseId() int32          { return this.BaseId }
func (this *PawnSkill) GetType() int32            { return this.Type }
func (this *PawnSkill) GetLv() int32              { return this.Lv }
func (this *PawnSkill) GetUseType() int32         { return this.UseType }
func (this *PawnSkill) GetTarget() int32          { return this.Target }
func (this *PawnSkill) GetValue() float64         { return this.Value }
func (this *PawnSkill) GetNeedAttackTime() int32  { return this.needAttackTime }
func (this *PawnSkill) GetNeedHitTime() int32     { return this.needHitTime }
func (this *PawnSkill) GetBulletId() int32        { return this.bulletId }
func (this *PawnSkill) GetParamsInt() int32       { return ut.Int32(this.Params) }
func (this *PawnSkill) GetParamsFloat64() float64 { return ut.Float64(this.Params) }
func (this *PawnSkill) GetParamsString() string   { return ut.String(this.Params) }

func (this *PawnSkill) GetParamsInts() []int32 {
	return ut.StringToInt32s(ut.String(this.Params), ",")
}

func (this *PawnSkill) GetParamsFloats() []float64 {
	return ut.StringToFloats(ut.String(this.Params), ",")
}

// 设置强化类型
func (this *PawnSkill) SetIntensifyType(tp int32) {
	this.IntensifyType = tp
}
func (this *PawnSkill) GetIntensifyType() int32 { return this.IntensifyType }

// 是否专属强化
func (this *PawnSkill) IsExclusiveIntensify() bool { return this.IntensifyType == 1 }
