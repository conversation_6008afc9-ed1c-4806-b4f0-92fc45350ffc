package ut

import "github.com/emirpasic/gods/trees/redblacktree"

/*
* 红黑树容器
* k => int 同一键值的数据放在同一个切片中
* 非线程安全 需要外部加锁
 */

type RbTreeContainer[T comparable] struct {
	*redblacktree.Tree
}

// 向红黑树中的某个键添加一个值
func (this *RbTreeContainer[T]) AddElement(key int, value T) {
	if values, found := this.Get(key); found {
		// 如果键已经存在，则将新值追加到切片中
		this.Put(key, append(values.([]T), value))
	} else {
		// 如果键不存在，则创建一个新的切片
		this.Put(key, []T{value})
	}
}

// 从红黑树中的某个键移除一个值
func (this *RbTreeContainer[T]) RemoveElement(key int, value T) {
	if values, found := this.Get(key); found {
		// 查找到该键的值切片并尝试移除特定值
		newValues := this.removeValueNoLock(values.([]T), value)
		if len(newValues) > 0 {
			this.Put(key, newValues)
		} else {
			this.Remove(key)
		}
	}
}

// 从切片中移除特定值
func (this *RbTreeContainer[T]) removeValueNoLock(slice []T, value T) []T {
	for i, v := range slice {
		if v == value {
			return append(slice[:i], slice[i+1:]...)
		}
	}
	return slice
}

// FindElements 查找红黑树中的元素列表
func (this *RbTreeContainer[T]) FindElementsNoLock(key int) ([]T, bool) {
	if values, found := this.Get(key); found {
		return values.([]T), true
	}
	return nil, false
}

// 找到最小值
func (this *RbTreeContainer[T]) FindMinElements() *redblacktree.Node {
	return this.Left()
}

// 找到最大值
func (this *RbTreeContainer[T]) FindMaxElements() *redblacktree.Node {
	return this.Right()
}
