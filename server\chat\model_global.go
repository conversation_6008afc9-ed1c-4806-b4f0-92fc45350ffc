package chat

import "time"

type GlobalData struct {
	SumLikeJwmCount int `bson:"sum_like_jwm_count"` // 总点赞数量
}

var global = &GlobalData{
	SumLikeJwmCount: 0,
}

func InitGlobalData() {
	if data, err := globalDb.FindGlobal(); err == nil {
		global = data
	}
}

// db tick
func GlobalDbTick() {
	go func() {
		tiker := time.NewTicker(time.Hour * 1)
		for isRunning {
			<-tiker.C
			UpdateGlobalChatDb()
		}
	}()
}

func UpdateGlobalChatDb() {
	globalDb.UpdateGlobal()
}
