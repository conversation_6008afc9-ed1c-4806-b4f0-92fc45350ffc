package random

import (
	"math"

	ut "slgsrv/utils"
)

// 伪随机数
type Random struct {
	seed int // 种子
}

func NewRandom(seed int) *Random {
	return &Random{
		seed: seed,
	}
}

func (this *Random) SetSeed(seed int) {
	this.seed = seed
}

func (this *Random) GetSeed() int {
	return this.seed
}

func (this *Random) Numn() float64 {
	this.seed = (this.seed*9301 + 49297) % 233280
	rand := math.Floor((float64(this.seed)/233280.0)*10000) * 0.0001
	return rand
}

func (this *Random) Get(min, max int) int {
	if min >= max {
		return min
	}
	return int(math.Floor(this.Numn()*float64(ut.Max(max-min, 0)+1))) + min
}

func (this *Random) GetInt32(min, max int32) int32 {
	if min >= max {
		return min
	}
	return int32(math.Floor(this.Numn()*float64(ut.MaxInt32(max-min, 0)+1))) + min
}

// 概率
func (this *Random) Chance(odds int) bool {
	if odds <= 0 {
		return false
	}
	mul := 100
	return this.Get(0, 100*mul-1) < odds*mul
}

func (this *Random) ChanceInt32(odds int32) bool {
	if odds <= 0 {
		return false
	}
	var mul int32 = 100
	return this.GetInt32(0, 100*mul-1) < odds*mul
}
