package game

import (
	"go/constant"
	"go/token"
	"reflect"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/bazaar"
	"slgsrv/server/game/behavior"
	"slgsrv/server/game/common/astar"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	"slgsrv/server/game/fsp"
	"slgsrv/server/game/player"
	"slgsrv/server/game/record"
	r "slgsrv/server/game/room"
	"slgsrv/server/game/world"
	ut "slgsrv/utils"
	"slgsrv/utils/random"
)

/**
 * 本描述符文件仅适用于game模块
 * 说明 yaegi是一个bin文件,需要单独下载,放在go/bin下面
 * mac download url : https://twgame-jwm-inland-1257666530.cos.ap-guangzhou.myqcloud.com/tools/yaegi
 * window 需要去github上clone项目然后自己编译可执行文件
 * init方法内的每个Symbols map都是要导出使用的，init方法之后的相关定义重新生成时需要删除并重新覆盖
 * 逐条执行下面的语句,每次执行会生成/覆盖一个game.go, 将文件中的map符号内容和定义扩展复制过来
 * Symbols 的key不要修改,不要使用generate生成的那个，测试会报错，不方便引入。
 *
 */
//go:generate yaegi extract ./world
//go:generate yaegi extract ./room
//go:generate yaegi extract ./record
//go:generate yaegi extract ./player
//go:generate yaegi extract ./mail
//go:generate yaegi extract ./fsp
//go:generate yaegi extract ./bazaar
//go:generate yaegi extract ./behavior
//go:generate yaegi extract ../../utils
//go:generate yaegi extract ./common/g
//go:generate yaegi extract ./common/helper

var (
	WorldSymbols        = map[string]map[string]reflect.Value{}
	RoomSymbols         = map[string]map[string]reflect.Value{}
	UtilSymbols         = map[string]map[string]reflect.Value{}
	RecordSymbols       = map[string]map[string]reflect.Value{}
	PlayerSymbols       = map[string]map[string]reflect.Value{}
	MailSymbols         = map[string]map[string]reflect.Value{}
	FspSymbols          = map[string]map[string]reflect.Value{}
	BehaviorSymbols     = map[string]map[string]reflect.Value{}
	BazaarSymbols       = map[string]map[string]reflect.Value{}
	CommonSymbols       = map[string]map[string]reflect.Value{}
	CommonHelperSymbols = map[string]map[string]reflect.Value{}
)

func init() {
	WorldSymbols["slgsrv/server/game/world/world/"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"ALLIANCE":              reflect.ValueOf(constant.MakeFromLiteral("\"alliance\"", token.STRING, 0)),
		"AddAncientRes":         reflect.ValueOf(world.AddAncientRes),
		"CHAT_MAX_COUNT":        reflect.ValueOf(constant.MakeFromLiteral("50", token.INT, 0)),
		"CHAT_MAX_DB_COUNT_PER": reflect.ValueOf(constant.MakeFromLiteral("200", token.INT, 0)),
		"GAME":                  reflect.ValueOf(constant.MakeFromLiteral("\"game\"", token.STRING, 0)),
		"LandsBytesCompress":    reflect.ValueOf(world.LandsBytesCompress),
		"NewAareDrillPawnQueue": reflect.ValueOf(world.NewAareDrillPawnQueue),
		"NewAarePawnLvingQueue": reflect.ValueOf(world.NewAarePawnLvingQueue),
		"NewAlliance":           reflect.ValueOf(world.NewAlliance),
		"NewAncientInfo":        reflect.ValueOf(world.NewAncientInfo),
		"NewArea":               reflect.ValueOf(world.NewArea),
		"NewAreaArmy":           reflect.ValueOf(world.NewAreaArmy),
		"NewAreaBuild":          reflect.ValueOf(world.NewAreaBuild),
		"NewAreaPawn":           reflect.ValueOf(world.NewAreaPawn),
		"NewBTAncientInfo":      reflect.ValueOf(world.NewBTAncientInfo),
		"NewBTCityInfo":         reflect.ValueOf(world.NewBTCityInfo),
		"NewCell":               reflect.ValueOf(world.NewCell),
		"NewCellByDB":           reflect.ValueOf(world.NewCellByDB),
		"NewDrillPawnInfo":      reflect.ValueOf(world.NewDrillPawnInfo),
		"NewMarch":              reflect.ValueOf(world.NewMarch),
		"NewModel":              reflect.ValueOf(world.NewModel),
		"NewMongoDB":            reflect.ValueOf(world.NewMongoDB),
		"NewPawnLvingInfo":      reflect.ValueOf(world.NewPawnLvingInfo),
		"NewPawnSkill":          reflect.ValueOf(world.NewPawnSkill),
		"NewTransit":            reflect.ValueOf(world.NewTransit),

		// type definitions
		"AarePawnLvingQueue":       reflect.ValueOf((*world.AarePawnLvingQueue)(nil)),
		"AllTempPlayerMap":         reflect.ValueOf((*world.AllTempPlayerMap)(nil)),
		"AlliApply":                reflect.ValueOf((*world.AlliApply)(nil)),
		"AlliApplyList":            reflect.ValueOf((*world.AlliApplyList)(nil)),
		"AlliChannel":              reflect.ValueOf((*world.AlliChannel)(nil)),
		"AlliChannelList":          reflect.ValueOf((*world.AlliChannelList)(nil)),
		"AlliLog":                  reflect.ValueOf((*world.AlliLog)(nil)),
		"AlliLogList":              reflect.ValueOf((*world.AlliLogList)(nil)),
		"AlliMember":               reflect.ValueOf((*world.AlliMember)(nil)),
		"AlliMemberList":           reflect.ValueOf((*world.AlliMemberList)(nil)),
		"Alliance":                 reflect.ValueOf((*world.Alliance)(nil)),
		"AllianceBaseData":         reflect.ValueOf((*world.AllianceBaseData)(nil)),
		"AllianceMap":              reflect.ValueOf((*world.AllianceMap)(nil)),
		"AllianceTableData":        reflect.ValueOf((*world.AllianceTableData)(nil)),
		"AncientContributeLog":     reflect.ValueOf((*world.AncientContributeLog)(nil)),
		"AncientContributeLogList": reflect.ValueOf((*world.AncientContributeLogList)(nil)),
		"AncientInfo":              reflect.ValueOf((*world.AncientInfo)(nil)),
		"AncientInfoListTableData": reflect.ValueOf((*world.AncientInfoListTableData)(nil)),
		"AncientInfoTableData":     reflect.ValueOf((*world.AncientInfoTableData)(nil)),
		"AncientLvUpResList":       reflect.ValueOf((*world.AncientLvUpResList)(nil)),
		"Area":                     reflect.ValueOf((*world.Area)(nil)),
		"AreaArmy":                 reflect.ValueOf((*world.AreaArmy)(nil)),
		"AreaBuild":                reflect.ValueOf((*world.AreaBuild)(nil)),
		"AreaDrillPawnQueue":       reflect.ValueOf((*world.AreaDrillPawnQueue)(nil)),
		"AreaMap":                  reflect.ValueOf((*world.AreaMap)(nil)),
		"AreaPawn":                 reflect.ValueOf((*world.AreaPawn)(nil)),
		"ArmyList":                 reflect.ValueOf((*world.ArmyList)(nil)),
		"AvoidWarData":             reflect.ValueOf((*world.AvoidWarData)(nil)),
		"AvoidWarTableData":        reflect.ValueOf((*world.AvoidWarTableData)(nil)),
		"BTAncientInfo":            reflect.ValueOf((*world.BTAncientInfo)(nil)),
		"BTCityInfo":               reflect.ValueOf((*world.BTCityInfo)(nil)),
		"BTCityQueueList":          reflect.ValueOf((*world.BTCityQueueList)(nil)),
		"BTCityTableData":          reflect.ValueOf((*world.BTCityTableData)(nil)),
		"BattleDistMap":            reflect.ValueOf((*world.BattleDistMap)(nil)),
		"BattlePreArmy":            reflect.ValueOf((*world.BattlePreArmy)(nil)),
		"BuildList":                reflect.ValueOf((*world.BuildList)(nil)),
		"Cell":                     reflect.ValueOf((*world.Cell)(nil)),
		"CellMap":                  reflect.ValueOf((*world.CellMap)(nil)),
		"ChatInfo":                 reflect.ValueOf((*world.ChatInfo)(nil)),
		"ChatMap":                  reflect.ValueOf((*world.ChatMap)(nil)),
		"ChatTableData":            reflect.ValueOf((*world.ChatTableData)(nil)),
		"CitySkinTableData":        reflect.ValueOf((*world.CitySkinTableData)(nil)),
		"DrillPawnInfo":            reflect.ValueOf((*world.DrillPawnInfo)(nil)),
		"DrillPawnList":            reflect.ValueOf((*world.DrillPawnList)(nil)),
		"DrillPawnQueueMap":        reflect.ValueOf((*world.DrillPawnQueueMap)(nil)),
		"DrillPawnTableData":       reflect.ValueOf((*world.DrillPawnTableData)(nil)),
		"FspAddPawns":              reflect.ValueOf((*world.FspAddPawns)(nil)),
		"March":                    reflect.ValueOf((*world.March)(nil)),
		"MarchList":                reflect.ValueOf((*world.MarchList)(nil)),
		"MarchTableData":           reflect.ValueOf((*world.MarchTableData)(nil)),
		"Model":                    reflect.ValueOf((*world.Model)(nil)),
		"Mongodb":                  reflect.ValueOf((*world.Mongodb)(nil)),
		"PawnList":                 reflect.ValueOf((*world.PawnList)(nil)),
		"PawnLvingCompleteInfo":    reflect.ValueOf((*world.PawnLvingCompleteInfo)(nil)),
		"PawnLvingInfo":            reflect.ValueOf((*world.PawnLvingInfo)(nil)),
		"PawnLvingQueueList":       reflect.ValueOf((*world.PawnLvingQueueList)(nil)),
		"PawnLvingTableData":       reflect.ValueOf((*world.PawnLvingTableData)(nil)),
		"PawnSkill":                reflect.ValueOf((*world.PawnSkill)(nil)),
		"PlayerCellMap":            reflect.ValueOf((*world.PlayerCellMap)(nil)),
		"PlayerOfflineMsgInfo":     reflect.ValueOf((*world.PlayerOfflineMsgInfo)(nil)),
		"PolicyMap":                reflect.ValueOf((*world.PolicyMap)(nil)),
		"RankManager":              reflect.ValueOf((*world.RankManager)(nil)),
		"ReqAreaPlayerMap":         reflect.ValueOf((*world.ReqAreaPlayerMap)(nil)),
		"ScoreInfo":                reflect.ValueOf((*world.ScoreInfo)(nil)),
		"SelectPolicysMap":         reflect.ValueOf((*world.SelectPolicysMap)(nil)),
		"TempPlayer":               reflect.ValueOf((*world.TempPlayer)(nil)),
		"Transit":                  reflect.ValueOf((*world.Transit)(nil)),
		"TransitList":              reflect.ValueOf((*world.TransitList)(nil)),
		"TransitTableData":         reflect.ValueOf((*world.TransitTableData)(nil)),
		"TreasureWeightInfo":       reflect.ValueOf((*world.TreasureWeightInfo)(nil)),
		"TriggerTaskInfo":          reflect.ValueOf((*world.TriggerTaskInfo)(nil)),
		"WorldTableData":           reflect.ValueOf((*world.WorldTableData)(nil)),
	}
	RoomSymbols["slgsrv/server/game/room/room"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"CreateRoomMallocPlayerPos": reflect.ValueOf(r.CreateRoomMallocPlayerPos),
		"GetBaseRooms":              reflect.ValueOf(r.GetBaseRooms),
		"GetRoomById":               reflect.ValueOf(r.GetRoomById),
		"GetRoomIds":                reflect.ValueOf(r.GetRoomIds),
		"InitRoom":                  reflect.ValueOf(r.InitRoom),
		"LoadAllRoom":               reflect.ValueOf(r.LoadAllRoom),
		"LoadRoomById":              reflect.ValueOf(r.LoadRoomById),
		"NewRoom":                   reflect.ValueOf(r.NewRoom),
		"NewRoomByDB":               reflect.ValueOf(r.NewRoomByDB),
		"NotifyRoomsToMatch":        reflect.ValueOf(r.NotifyRoomsToMatch),
		"RunAllRoom":                reflect.ValueOf(r.RunAllRoom),
		"SaveAllRoom":               reflect.ValueOf(r.SaveAllRoom),

		// type definitions
		"ApplyPlayer":         reflect.ValueOf((*r.ApplyPlayer)(nil)),
		"DeletePlayerInfo":    reflect.ValueOf((*r.DeletePlayerInfo)(nil)),
		"Model":               reflect.ValueOf((*r.Model)(nil)),
		"NotifyAllPlayerInfo": reflect.ValueOf((*r.NotifyAllPlayerInfo)(nil)),
		"PlayerMap":           reflect.ValueOf((*r.PlayerMap)(nil)),
		"PlayerNotifyInfo":    reflect.ValueOf((*r.PlayerNotifyInfo)(nil)),
		"RoomMap":             reflect.ValueOf((*r.RoomMap)(nil)),
		"RoomTableData":       reflect.ValueOf((*r.RoomTableData)(nil)),
	}
	UtilSymbols["slgsrv/utils/utils"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"AESDecrypt":                  reflect.ValueOf(ut.AESDecrypt),
		"AESEncrypt":                  reflect.ValueOf(ut.AESEncrypt),
		"Abs":                         reflect.ValueOf(ut.Abs),
		"AbsInt32":                    reflect.ValueOf(ut.AbsInt32),
		"Atof":                        reflect.ValueOf(ut.Atof),
		"Atoi":                        reflect.ValueOf(ut.Atoi),
		"Bool":                        reflect.ValueOf(ut.Bool),
		"Byte":                        reflect.ValueOf(ut.Byte),
		"Bytes":                       reflect.ValueOf(ut.Bytes),
		"Ceil":                        reflect.ValueOf(ut.Ceil),
		"Chance":                      reflect.ValueOf(ut.Chance),
		"ChanceInt32":                 reflect.ValueOf(ut.ChanceInt32),
		"CheckVersion":                reflect.ValueOf(ut.CheckVersion),
		"Clamp":                       reflect.ValueOf(ut.Clamp),
		"ClampInt32":                  reflect.ValueOf(ut.ClampInt32),
		"DateFormat":                  reflect.ValueOf(ut.DateFormat),
		"DateZeroTime":                reflect.ValueOf(ut.DateZeroTime),
		"DecodeBase62":                reflect.ValueOf(ut.DecodeBase62),
		"EncodeBase62":                reflect.ValueOf(ut.EncodeBase62),
		"Float32":                     reflect.ValueOf(ut.Float32),
		"Float32Round":                reflect.ValueOf(ut.Float32Round),
		"Float64":                     reflect.ValueOf(ut.Float64),
		"Float64Round":                reflect.ValueOf(ut.Float64Round),
		"Floor":                       reflect.ValueOf(ut.Floor),
		"GetCertByCertStr":            reflect.ValueOf(ut.GetCertByCertStr),
		"GetCurrentMonthDayTime":      reflect.ValueOf(ut.GetCurrentMonthDayTime),
		"GetCurrentWeekDayTime":       reflect.ValueOf(ut.GetCurrentWeekDayTime),
		"GetNextMonthDayTime":         reflect.ValueOf(ut.GetNextMonthDayTime),
		"GetNextWeekDayTime":          reflect.ValueOf(ut.GetNextWeekDayTime),
		"GetNumberLen":                reflect.ValueOf(ut.GetNumberLen),
		"GetStringLen":                reflect.ValueOf(ut.GetStringLen),
		"GetToday4Date":               reflect.ValueOf(ut.GetToday4Date),
		"GetTodayDate":                reflect.ValueOf(ut.GetTodayDate),
		"ID":                          reflect.ValueOf(ut.ID),
		"InitialIsChar":               reflect.ValueOf(ut.InitialIsChar),
		"Int":                         reflect.ValueOf(ut.Int),
		"Int32":                       reflect.ValueOf(ut.Int32),
		"Int32Array":                  reflect.ValueOf(ut.Int32Array),
		"Int64":                       reflect.ValueOf(ut.Int64),
		"IntArray":                    reflect.ValueOf(ut.IntArray),
		"IntCompressToBytes":          reflect.ValueOf(ut.IntCompressToBytes),
		"IsEmpty":                     reflect.ValueOf(ut.IsEmpty),
		"Itoa":                        reflect.ValueOf(ut.Itoa),
		"LoadJson":                    reflect.ValueOf(ut.LoadJson),
		"LoadJsonByPath":              reflect.ValueOf(ut.LoadJsonByPath),
		"LoopValue":                   reflect.ValueOf(ut.LoopValue),
		"MD5":                         reflect.ValueOf(ut.MD5),
		"MapArray":                    reflect.ValueOf(ut.MapArray),
		"MapInt32Bool":                reflect.ValueOf(ut.MapInt32Bool),
		"MapInt32Int32":               reflect.ValueOf(ut.MapInt32Int32),
		"MapIntBool":                  reflect.ValueOf(ut.MapIntBool),
		"MapIntInt":                   reflect.ValueOf(ut.MapIntInt),
		"MapInterface":                reflect.ValueOf(ut.MapInterface),
		"MapStringBool":               reflect.ValueOf(ut.MapStringBool),
		"MapStringInt":                reflect.ValueOf(ut.MapStringInt),
		"Max":                         reflect.ValueOf(ut.Max),
		"MaxInt32":                    reflect.ValueOf(ut.MaxInt32),
		"MaxInt64":                    reflect.ValueOf(ut.MaxInt64),
		"Min":                         reflect.ValueOf(ut.Min),
		"MinInt32":                    reflect.ValueOf(ut.MinInt32),
		"MinInt64":                    reflect.ValueOf(ut.MinInt64),
		"NewLFUCache":                 reflect.ValueOf(ut.NewLFUCache),
		"NewVec2":                     reflect.ValueOf(ut.NewVec2),
		"NewVec2ByObj":                reflect.ValueOf(ut.NewVec2ByObj),
		"NewVec2ByString":             reflect.ValueOf(ut.NewVec2ByString),
		"NormalizeNumber":             reflect.ValueOf(ut.NormalizeNumber),
		"Now":                         reflect.ValueOf(ut.Now),
		"NowFourTime":                 reflect.ValueOf(ut.NowFourTime),
		"NowZeroTime":                 reflect.ValueOf(ut.NowZeroTime),
		"ParseDateStr2TimeStamps":     reflect.ValueOf(ut.ParseDateStr2TimeStamps),
		"ParseJwsToken":               reflect.ValueOf(ut.ParseJwsToken),
		"Random":                      reflect.ValueOf(ut.Random),
		"RandomIndexByDataWeight":     reflect.ValueOf(ut.RandomIndexByDataWeight),
		"RandomIndexByWeight":         reflect.ValueOf(ut.RandomIndexByWeight),
		"RandomIndexByWeightHasTotal": reflect.ValueOf(ut.RandomIndexByWeightHasTotal),
		"RandomInt32":                 reflect.ValueOf(ut.RandomInt32),
		"Round":                       reflect.ValueOf(ut.Round),
		"RoundInt32":                  reflect.ValueOf(ut.RoundInt32),
		"RpcBool":                     reflect.ValueOf(ut.RpcBool),
		"RpcBytes":                    reflect.ValueOf(ut.RpcBytes),
		"RpcInt":                      reflect.ValueOf(ut.RpcInt),
		"RpcInterfaceMap":             reflect.ValueOf(ut.RpcInterfaceMap),
		"SetTimeout":                  reflect.ValueOf(ut.SetTimeout),
		"SshExcuteShell":              reflect.ValueOf(ut.SshExcuteShell),
		"String":                      reflect.ValueOf(ut.String),
		"StringArray":                 reflect.ValueOf(ut.StringArray),
		"StringJoin":                  reflect.ValueOf(ut.StringJoin),
		"StringToFloats":              reflect.ValueOf(ut.StringToFloats),
		"StringToInt32s":              reflect.ValueOf(ut.StringToInt32s),
		"StringToInts":                reflect.ValueOf(ut.StringToInts),
		"TIME_DAY":                    reflect.ValueOf(constant.MakeFromLiteral("86400000", token.INT, 0)),
		"TIME_HOUR":                   reflect.ValueOf(constant.MakeFromLiteral("3600000", token.INT, 0)),
		"TIME_MINUTE":                 reflect.ValueOf(constant.MakeFromLiteral("60000", token.INT, 0)),
		"TIME_SECOND":                 reflect.ValueOf(constant.MakeFromLiteral("1000", token.INT, 0)),
		"TodayHourTime":               reflect.ValueOf(ut.TodayHourTime),
		"TraceMemStats":               reflect.ValueOf(ut.TraceMemStats),
		"Trim":                        reflect.ValueOf(ut.Trim),
		"TruncateString":              reflect.ValueOf(ut.TruncateString),
		"UID6":                        reflect.ValueOf(ut.UID6),
		"UID7":                        reflect.ValueOf(ut.UID7),
		"UID8":                        reflect.ValueOf(ut.UID8),
		"VerifyCertChain":             reflect.ValueOf(ut.VerifyCertChain),
		"VerifyEmailFormat":           reflect.ValueOf(ut.VerifyEmailFormat),
		"WorkDir":                     reflect.ValueOf(ut.WorkDir),

		// type definitions
		"CacheItem":     reflect.ValueOf((*ut.CacheItem)(nil)),
		"DataWeight":    reflect.ValueOf((*ut.DataWeight)(nil)),
		"LFUCache":      reflect.ValueOf((*ut.LFUCache)(nil)),
		"PriorityQueue": reflect.ValueOf((*ut.PriorityQueue)(nil)),
		"Vec2":          reflect.ValueOf((*ut.Vec2)(nil)),

		// interface wrapper definitions
		"_DataWeight": reflect.ValueOf((*_game_DataWeight)(nil)),
	}
	RecordSymbols["slgsrv/server/game/record/record"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"ARMY":                   reflect.ValueOf(constant.MakeFromLiteral("\"record_army\"", token.STRING, 0)),
		"ArmyInfoBsonToPb":       reflect.ValueOf(record.ArmyInfoBsonToPb),
		"BATTLE":                 reflect.ValueOf(constant.MakeFromLiteral("\"record_battle\"", token.STRING, 0)),
		"BATTLE_RECORD_VERSION":  reflect.ValueOf(constant.MakeFromLiteral("14", token.INT, 0)),
		"BAZAAR":                 reflect.ValueOf(constant.MakeFromLiteral("\"record_bazaar\"", token.STRING, 0)),
		"BuildInfoBsonToPb":      reflect.ValueOf(record.BuildInfoBsonToPb),
		"FORGE":                  reflect.ValueOf(constant.MakeFromLiteral("\"record_forge\"", token.STRING, 0)),
		"MergeBattleScoreRecord": reflect.ValueOf(record.MergeBattleScoreRecord),
		"NewModel":               reflect.ValueOf(record.NewModel),
		"PawnInfoBsonToPb":       reflect.ValueOf(record.PawnInfoBsonToPb),
		"SCORE":                  reflect.ValueOf(constant.MakeFromLiteral("\"record_battle_score\"", token.STRING, 0)),
		"SCORE_PLAYER":           reflect.ValueOf(constant.MakeFromLiteral("\"record_battle_score_player\"", token.STRING, 0)),

		// type definitions
		"ArmyRecordData":   reflect.ValueOf((*record.ArmyRecordData)(nil)),
		"BattleRecordData": reflect.ValueOf((*record.BattleRecordData)(nil)),
		"BazaarRecordData": reflect.ValueOf((*record.BazaarRecordData)(nil)),
		"Model":            reflect.ValueOf((*record.Model)(nil)),
		"Mongodb":          reflect.ValueOf((*record.Mongodb)(nil)),
	}
	PlayerSymbols["slgsrv/server/game/player/player"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"InitModel":                reflect.ValueOf(player.InitModel),
		"InitSpectatorModel":       reflect.ValueOf(player.InitSpectatorModel),
		"InitTaskByDB":             reflect.ValueOf(player.InitTaskByDB),
		"IsProgressByData":         reflect.ValueOf(player.IsProgressByData),
		"NewBTInfo":                reflect.ValueOf(player.NewBTInfo),
		"NewManager":               reflect.ValueOf(player.NewManager),
		"NewModelByDB":             reflect.ValueOf(player.NewModelByDB),
		"NewOutput":                reflect.ValueOf(player.NewOutput),
		"UpdateTaskProgressByData": reflect.ValueOf(player.UpdateTaskProgressByData),
		"VERSION":                  reflect.ValueOf(constant.MakeFromLiteral("21", token.INT, 0)),

		// type definitions
		"AntiCheatQuestion": reflect.ValueOf((*player.AntiCheatQuestion)(nil)),
		"BTInfo":            reflect.ValueOf((*player.BTInfo)(nil)),
		"BTQueueList":       reflect.ValueOf((*player.BTQueueList)(nil)),
		"BeginDeserterData": reflect.ValueOf((*player.BeginDeserterData)(nil)),
		"CeriSlotInfo":      reflect.ValueOf((*player.CeriSlotInfo)(nil)),
		"ForgeEquipInfo":    reflect.ValueOf((*player.ForgeEquipInfo)(nil)),
		"Manager":           reflect.ValueOf((*player.Manager)(nil)),
		"MapMarkInfo":       reflect.ValueOf((*player.MapMarkInfo)(nil)),
		"Merchant":          reflect.ValueOf((*player.Merchant)(nil)),
		"MerchantList":      reflect.ValueOf((*player.MerchantList)(nil)),
		"Model":             reflect.ValueOf((*player.Model)(nil)),
		"Mongodb":           reflect.ValueOf((*player.Mongodb)(nil)),
		"Output":            reflect.ValueOf((*player.Output)(nil)),
		"TableData":         reflect.ValueOf((*player.TableData)(nil)),
		"TaskList":          reflect.ValueOf((*player.TaskList)(nil)),
	}
	FspSymbols["slgsrv/server/game/fsp/fsp"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"FightersJsonToPb": reflect.ValueOf(fsp.FightersJsonToPb),
		"NewFSPModel":      reflect.ValueOf(fsp.NewFSPModel),
		"NewFSPPlayBack":   reflect.ValueOf(fsp.NewFSPPlayBack),

		// type definitions
		"Area":                  reflect.ValueOf((*fsp.Area)(nil)),
		"BaseFighter":           reflect.ValueOf((*fsp.BaseFighter)(nil)),
		"Building":              reflect.ValueOf((*fsp.Building)(nil)),
		"FSPBattleController":   reflect.ValueOf((*fsp.FSPBattleController)(nil)),
		"FSPModel":              reflect.ValueOf((*fsp.FSPModel)(nil)),
		"FSPParam":              reflect.ValueOf((*fsp.FSPParam)(nil)),
		"FSPPlayBack":           reflect.ValueOf((*fsp.FSPPlayBack)(nil)),
		"FSPPlayBackController": reflect.ValueOf((*fsp.FSPPlayBackController)(nil)),
		"Fighter":               reflect.ValueOf((*fsp.Fighter)(nil)),
		"MainDoor":              reflect.ValueOf((*fsp.MainDoor)(nil)),
		"Pet":                   reflect.ValueOf((*fsp.Pet)(nil)),
		"Tower":                 reflect.ValueOf((*fsp.Tower)(nil)),

		// interface wrapper definitions
		"_Area": reflect.ValueOf((*_game_Area)(nil)),
	}
	BehaviorSymbols["slgsrv/server/game/behavior/behavior"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"ACTION":          reflect.ValueOf(constant.MakeFromLiteral("\"action\"", token.STRING, 0)),
		"COMPOSITE":       reflect.ValueOf(constant.MakeFromLiteral("\"composite\"", token.STRING, 0)),
		"CONDITION":       reflect.ValueOf(constant.MakeFromLiteral("\"condition\"", token.STRING, 0)),
		"DECORATOR":       reflect.ValueOf(constant.MakeFromLiteral("\"decorator\"", token.STRING, 0)),
		"ERROR":           reflect.ValueOf(behavior.ERROR),
		"FAILURE":         reflect.ValueOf(behavior.FAILURE),
		"GetBuffIDBySkin": reflect.ValueOf(behavior.GetBuffIDBySkin),
		"NewBehaviorTree": reflect.ValueOf(behavior.NewBehaviorTree),
		"RUNNING":         reflect.ValueOf(behavior.RUNNING),
		"SKIN_BUFF_CONF":  reflect.ValueOf(&behavior.SKIN_BUFF_CONF).Elem(),
		"SUCCESS":         reflect.ValueOf(behavior.SUCCESS),

		// type definitions
		"Attack":                reflect.ValueOf((*behavior.Attack)(nil)),
		"BaseAction":            reflect.ValueOf((*behavior.BaseAction)(nil)),
		"BaseComposite":         reflect.ValueOf((*behavior.BaseComposite)(nil)),
		"BaseCondition":         reflect.ValueOf((*behavior.BaseCondition)(nil)),
		"BaseDecorator":         reflect.ValueOf((*behavior.BaseDecorator)(nil)),
		"BaseNode":              reflect.ValueOf((*behavior.BaseNode)(nil)),
		"BaseWorker":            reflect.ValueOf((*behavior.BaseWorker)(nil)),
		"BehaviorTree":          reflect.ValueOf((*behavior.BehaviorTree)(nil)),
		"CanMove":               reflect.ValueOf((*behavior.CanMove)(nil)),
		"CheckBeginBlood":       reflect.ValueOf((*behavior.CheckBeginBlood)(nil)),
		"CheckBeginDeductHp":    reflect.ValueOf((*behavior.CheckBeginDeductHp)(nil)),
		"CheckRoundBegin":       reflect.ValueOf((*behavior.CheckRoundBegin)(nil)),
		"CheckUseSkillAttack":   reflect.ValueOf((*behavior.CheckUseSkillAttack)(nil)),
		"DashData":              reflect.ValueOf((*behavior.DashData)(nil)),
		"EndRound":              reflect.ValueOf((*behavior.EndRound)(nil)),
		"HasAttackTarget":       reflect.ValueOf((*behavior.HasAttackTarget)(nil)),
		"IBaseComposite":        reflect.ValueOf((*behavior.IBaseComposite)(nil)),
		"IBaseDecorator":        reflect.ValueOf((*behavior.IBaseDecorator)(nil)),
		"IBaseNode":             reflect.ValueOf((*behavior.IBaseNode)(nil)),
		"IBaseWorker":           reflect.ValueOf((*behavior.IBaseWorker)(nil)),
		"InAttackRange":         reflect.ValueOf((*behavior.InAttackRange)(nil)),
		"Move":                  reflect.ValueOf((*behavior.Move)(nil)),
		"Parallel":              reflect.ValueOf((*behavior.Parallel)(nil)),
		"Priority":              reflect.ValueOf((*behavior.Priority)(nil)),
		"Probability":           reflect.ValueOf((*behavior.Probability)(nil)),
		"RegisterStructMaps":    reflect.ValueOf((*behavior.RegisterStructMaps)(nil)),
		"SearchCanAttackTarget": reflect.ValueOf((*behavior.SearchCanAttackTarget)(nil)),
		"SearchTarget":          reflect.ValueOf((*behavior.SearchTarget)(nil)),
		"Sequence":              reflect.ValueOf((*behavior.Sequence)(nil)),
		"Status":                reflect.ValueOf((*behavior.Status)(nil)),

		// interface wrapper definitions
		"_IBaseComposite": reflect.ValueOf((*_game_IBaseComposite)(nil)),
		"_IBaseDecorator": reflect.ValueOf((*_game_IBaseDecorator)(nil)),
		"_IBaseNode":      reflect.ValueOf((*_game_IBaseNode)(nil)),
		"_IBaseWorker":    reflect.ValueOf((*_game_IBaseWorker)(nil)),
	}
	BazaarSymbols["slgsrv/server/game/bazaar/bazaar"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"CheckTradeInfluence": reflect.ValueOf(bazaar.CheckTradeInfluence),
		"NewModel":            reflect.ValueOf(bazaar.NewModel),

		// type definitions
		"LowerPriceData": reflect.ValueOf((*bazaar.LowerPriceData)(nil)),
		"Model":          reflect.ValueOf((*bazaar.Model)(nil)),
		"Mongodb":        reflect.ValueOf((*bazaar.Mongodb)(nil)),
		"TRes":           reflect.ValueOf((*bazaar.TRes)(nil)),
		"TradRecord":     reflect.ValueOf((*bazaar.TradRecord)(nil)),
		"TradRecordList": reflect.ValueOf((*bazaar.TradRecordList)(nil)),
		"TradingResList": reflect.ValueOf((*bazaar.TradingResList)(nil)),
	}
	CommonSymbols["slgsrv/server/game/common/g/g"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"CloneAttrs":                   reflect.ValueOf(g.CloneAttrs),
		"CloneEffectAttrsToPb":         reflect.ValueOf(g.CloneEffectAttrsToPb),
		"ClonePbToEffectAttrs":         reflect.ValueOf(g.ClonePbToEffectAttrs),
		"EMPTY_BUFF":                   reflect.ValueOf(&g.EMPTY_BUFF).Elem(),
		"GetTreasureExpectationResMap": reflect.ValueOf(g.GetTreasureExpectationResMap),
		"HERO_REVIVES_TIME":            reflect.ValueOf(&g.HERO_REVIVES_TIME).Elem(),
		"InitPolicyByDB":               reflect.ValueOf(g.InitPolicyByDB),
		"MergeTypeObjsCount":           reflect.ValueOf(g.MergeTypeObjsCount),
		"NewBuffByJson":                reflect.ValueOf(g.NewBuffByJson),
		"NewBuffByPb":                  reflect.ValueOf(g.NewBuffByPb),
		"NewEffectObj":                 reflect.ValueOf(g.NewEffectObj),
		"NewEquip":                     reflect.ValueOf(g.NewEquip),
		"NewEquipByJson":               reflect.ValueOf(g.NewEquipByJson),
		"NewEquipByPb":                 reflect.ValueOf(g.NewEquipByPb),
		"NewEquipEffectObj":            reflect.ValueOf(g.NewEquipEffectObj),
		"NewPolicyInfo":                reflect.ValueOf(g.NewPolicyInfo),
		"NewPortrayal":                 reflect.ValueOf(g.NewPortrayal),
		"NewPortrayalByJson":           reflect.ValueOf(g.NewPortrayalByJson),
		"NewPortrayalByPb":             reflect.ValueOf(g.NewPortrayalByPb),
		"NewPortrayalPbByJson":         reflect.ValueOf(g.NewPortrayalPbByJson),
		"NewPortrayalSkill":            reflect.ValueOf(g.NewPortrayalSkill),
		"NewStrategyObj":               reflect.ValueOf(g.NewStrategyObj),
		"NewTaskCondByString":          reflect.ValueOf(g.NewTaskCondByString),
		"NewTaskInfo":                  reflect.ValueOf(g.NewTaskInfo),
		"NewTaskInfoByReCreate":        reflect.ValueOf(g.NewTaskInfoByReCreate),
		"NewTreasure":                  reflect.ValueOf(g.NewTreasure),
		"NewTreasureByJson":            reflect.ValueOf(g.NewTreasureByJson),
		"NewTypeObj":                   reflect.ValueOf(g.NewTypeObj),
		"NewTypeObjByJson":             reflect.ValueOf(g.NewTypeObjByJson),
		"NewTypeObjForMul":             reflect.ValueOf(g.NewTypeObjForMul),
		"NewTypeObjNotId":              reflect.ValueOf(g.NewTypeObjNotId),
		"PORTRAYAL_CHOSEN_ONE_ODD_MAX": reflect.ValueOf(&g.PORTRAYAL_CHOSEN_ONE_ODD_MAX).Elem(),
		"RandomRewardsByFixed":         reflect.ValueOf(g.RandomRewardsByFixed),
		"RandomRewardsByWeight":        reflect.ValueOf(g.RandomRewardsByWeight),
		"ResolutionItems":              reflect.ValueOf(g.ResolutionItems),
		"ServerIdToString":             reflect.ValueOf(g.ServerIdToString),
		"StringToEffectObjs":           reflect.ValueOf(g.StringToEffectObjs),
		"StringToTypeObjRandCount":     reflect.ValueOf(g.StringToTypeObjRandCount),
		"StringToTypeObjs":             reflect.ValueOf(g.StringToTypeObjs),
		"ToTypeObjsPb":                 reflect.ValueOf(g.ToTypeObjsPb),

		// type definitions
		"AttackMovePointLockInfo": reflect.ValueOf((*g.AttackMovePointLockInfo)(nil)),
		"BattleCtrl":              reflect.ValueOf((*g.BattleCtrl)(nil)),
		"BattleScoreRecordData":   reflect.ValueOf((*g.BattleScoreRecordData)(nil)),
		"Bazaar":                  reflect.ValueOf((*g.Bazaar)(nil)),
		"BuffObj":                 reflect.ValueOf((*g.BuffObj)(nil)),
		"Build":                   reflect.ValueOf((*g.Build)(nil)),
		"CanAttackTargetInfo":     reflect.ValueOf((*g.CanAttackTargetInfo)(nil)),
		"EffectObj":               reflect.ValueOf((*g.EffectObj)(nil)),
		"EquipEffectObj":          reflect.ValueOf((*g.EquipEffectObj)(nil)),
		"EquipInfo":               reflect.ValueOf((*g.EquipInfo)(nil)),
		"HeroSlotInfo":            reflect.ValueOf((*g.HeroSlotInfo)(nil)),
		"IFighter":                reflect.ValueOf((*g.IFighter)(nil)),
		"PawmDeadRecord":          reflect.ValueOf((*g.PawmDeadRecord)(nil)),
		"Pawn":                    reflect.ValueOf((*g.Pawn)(nil)),
		"PawnConfigInfo":          reflect.ValueOf((*g.PawnConfigInfo)(nil)),
		"PawnSkill":               reflect.ValueOf((*g.PawnSkill)(nil)),
		"Player":                  reflect.ValueOf((*g.Player)(nil)),
		"PolicyInfo":              reflect.ValueOf((*g.PolicyInfo)(nil)),
		"PortrayalInfo":           reflect.ValueOf((*g.PortrayalInfo)(nil)),
		"PortrayalSkill":          reflect.ValueOf((*g.PortrayalSkill)(nil)),
		"Record":                  reflect.ValueOf((*g.Record)(nil)),
		"Room":                    reflect.ValueOf((*g.Room)(nil)),
		"Season":                  reflect.ValueOf((*g.Season)(nil)),
		"StrategyObj":             reflect.ValueOf((*g.StrategyObj)(nil)),
		"TaskCondInfo":            reflect.ValueOf((*g.TaskCondInfo)(nil)),
		"TaskInfo":                reflect.ValueOf((*g.TaskInfo)(nil)),
		"TreasureInfo":            reflect.ValueOf((*g.TreasureInfo)(nil)),
		"TypeObj":                 reflect.ValueOf((*g.TypeObj)(nil)),
		"World":                   reflect.ValueOf((*g.World)(nil)),

		// interface wrapper definitions
		"_BattleCtrl": reflect.ValueOf((*_game_BattleCtrl)(nil)),
		"_Bazaar":     reflect.ValueOf((*_game_Bazaar)(nil)),
		"_Build":      reflect.ValueOf((*_game_Build)(nil)),
		"_IFighter":   reflect.ValueOf((*_game_IFighter)(nil)),
		"_Pawn":       reflect.ValueOf((*_game_Pawn)(nil)),
		"_PawnSkill":  reflect.ValueOf((*_game_PawnSkill)(nil)),
		"_Player":     reflect.ValueOf((*_game_Player)(nil)),
		"_Record":     reflect.ValueOf((*_game_Record)(nil)),
		"_Room":       reflect.ValueOf((*_game_Room)(nil)),
		"_Season":     reflect.ValueOf((*_game_Season)(nil)),
		"_World":      reflect.ValueOf((*_game_World)(nil)),
	}
	CommonHelperSymbols["slgsrv/server/game/common/helper/helper"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"CIRCLE_COUNT":               reflect.ValueOf(constant.MakeFromLiteral("5", token.INT, 0)),
		"ChangeNumByMerchantCap":     reflect.ValueOf(helper.ChangeNumByMerchantCap),
		"CheckActivityAutoDate":      reflect.ValueOf(helper.CheckActivityAutoDate),
		"CheckActivityDate":          reflect.ValueOf(helper.CheckActivityDate),
		"CheckCommunityActivityDate": reflect.ValueOf(helper.CheckCommunityActivityDate),
		"CheckMainCityIndex":         reflect.ValueOf(helper.CheckMainCityIndex),
		"FilterMainCityIndexs":       reflect.ValueOf(helper.FilterMainCityIndexs),
		"FindCanMovePoints":          reflect.ValueOf(helper.FindCanMovePoints),
		"GenPointsBySize":            reflect.ValueOf(helper.GenPointsBySize),
		"GetBanPlacePawnPoints":      reflect.ValueOf(helper.GetBanPlacePawnPoints),
		"GetBattlePoints":            reflect.ValueOf(helper.GetBattlePoints),
		"GetDirByIndex":              reflect.ValueOf(helper.GetDirByIndex),
		"GetDirByPoint":              reflect.ValueOf(helper.GetDirByPoint),
		"GetDoorPoints":              reflect.ValueOf(helper.GetDoorPoints),
		"GetIndexToIndexDis":         reflect.ValueOf(helper.GetIndexToIndexDis),
		"GetIrregularPointsOuter":    reflect.ValueOf(helper.GetIrregularPointsOuter),
		"GetMainPoints":              reflect.ValueOf(helper.GetMainPoints),
		"GetMinDis":                  reflect.ValueOf(helper.GetMinDis),
		"GetMinDisIndex":             reflect.ValueOf(helper.GetMinDisIndex),
		"GetMinDisPoint":             reflect.ValueOf(helper.GetMinDisPoint),
		"GetMoveNeedTime":            reflect.ValueOf(helper.GetMoveNeedTime),
		"GetPassPoints":              reflect.ValueOf(helper.GetPassPoints),
		"GetPawnPointsByDoor":        reflect.ValueOf(helper.GetPawnPointsByDoor),
		"GetPawnPointsByDoorOne":     reflect.ValueOf(helper.GetPawnPointsByDoorOne),
		"GetPixelByPoint":            reflect.ValueOf(helper.GetPixelByPoint),
		"GetPointByPixel":            reflect.ValueOf(helper.GetPointByPixel),
		"GetPointDisSortVal":         reflect.ValueOf(helper.GetPointDisSortVal),
		"GetPointToPointDis":         reflect.ValueOf(helper.GetPointToPointDis),
		"GetPointsOuter":             reflect.ValueOf(helper.GetPointsOuter),
		"IndexToPoint":               reflect.ValueOf(helper.IndexToPoint),
		"IsCanOccupyAncientTime":     reflect.ValueOf(helper.IsCanOccupyAncientTime),
		"IsCanOccupyTime":            reflect.ValueOf(helper.IsCanOccupyTime),
		"IsNotJoinAlliTime":          reflect.ValueOf(helper.IsNotJoinAlliTime),
		"IsPointInMap":               reflect.ValueOf(helper.IsPointInMap),
		"MIN_CELL_COUNT":             reflect.ValueOf(constant.MakeFromLiteral("80", token.INT, 0)),
		"PointToIndex":               reflect.ValueOf(helper.PointToIndex),
		"RANGE_NOT_USE_COUNT":        reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"Vec2ToIndex":                reflect.ValueOf(helper.Vec2ToIndex),

		// type definitions
		"FinderMainCity": reflect.ValueOf((*helper.FinderMainCity)(nil)),
		"FinderMove":     reflect.ValueOf((*helper.FinderMove)(nil)),
	}
}

// ====== 每次重新生成

// _game_IBaseComposite is an interface wrapper for IBaseComposite type
type _game_IBaseComposite struct {
	IValue             interface{}
	WAddChild          func(child behavior.IBaseNode)
	WCtor              func()
	WExecute           func(dt int32) behavior.Status
	WGetCategory       func() string
	WGetChild          func(i int) behavior.IBaseNode
	WGetChildrenCount  func() int
	WInit              func(conf map[string]interface{}, target g.IFighter, index int32)
	WSetBaseNodeWorker func(worker behavior.IBaseWorker)
}

func (W _game_IBaseComposite) AddChild(child behavior.IBaseNode) {
	W.WAddChild(child)
}

func (W _game_IBaseComposite) Ctor() {
	W.WCtor()
}

func (W _game_IBaseComposite) Execute(dt int32) behavior.Status {
	return W.WExecute(dt)
}

func (W _game_IBaseComposite) GetCategory() string {
	return W.WGetCategory()
}

func (W _game_IBaseComposite) GetChild(i int) behavior.IBaseNode {
	return W.WGetChild(i)
}

func (W _game_IBaseComposite) GetChildrenCount() int {
	return W.WGetChildrenCount()
}

func (W _game_IBaseComposite) Init(conf map[string]interface{}, target g.IFighter, index int32) {
	W.WInit(conf, target, index)
}

func (W _game_IBaseComposite) SetBaseNodeWorker(worker behavior.IBaseWorker) {
	W.WSetBaseNodeWorker(worker)
}

// _game_IBaseDecorator is an interface wrapper for IBaseDecorator type
type _game_IBaseDecorator struct {
	IValue             interface{}
	WAddChild          func(child behavior.IBaseNode)
	WCtor              func()
	WExecute           func(dt int32) behavior.Status
	WGetCategory       func() string
	WGetChild          func() behavior.IBaseNode
	WInit              func(conf map[string]interface{}, target g.IFighter, index int32)
	WSetBaseNodeWorker func(worker behavior.IBaseWorker)
}

func (W _game_IBaseDecorator) AddChild(child behavior.IBaseNode) {
	W.WAddChild(child)
}

func (W _game_IBaseDecorator) Ctor() {
	W.WCtor()
}

func (W _game_IBaseDecorator) Execute(dt int32) behavior.Status {
	return W.WExecute(dt)
}

func (W _game_IBaseDecorator) GetCategory() string {
	return W.WGetCategory()
}

func (W _game_IBaseDecorator) GetChild() behavior.IBaseNode {
	return W.WGetChild()
}

func (W _game_IBaseDecorator) Init(conf map[string]interface{}, target g.IFighter, index int32) {
	W.WInit(conf, target, index)
}

func (W _game_IBaseDecorator) SetBaseNodeWorker(worker behavior.IBaseWorker) {
	W.WSetBaseNodeWorker(worker)
}

// _game_IBaseNode is an interface wrapper for IBaseNode type
type _game_IBaseNode struct {
	IValue             interface{}
	WCtor              func()
	WExecute           func(dt int32) behavior.Status
	WGetCategory       func() string
	WInit              func(conf map[string]interface{}, target g.IFighter, index int32)
	WSetBaseNodeWorker func(worker behavior.IBaseWorker)
}

func (W _game_IBaseNode) Ctor() {
	W.WCtor()
}

func (W _game_IBaseNode) Execute(dt int32) behavior.Status {
	return W.WExecute(dt)
}

func (W _game_IBaseNode) GetCategory() string {
	return W.WGetCategory()
}

func (W _game_IBaseNode) Init(conf map[string]interface{}, target g.IFighter, index int32) {
	W.WInit(conf, target, index)
}

func (W _game_IBaseNode) SetBaseNodeWorker(worker behavior.IBaseWorker) {
	W.WSetBaseNodeWorker(worker)
}

// _game_IBaseWorker is an interface wrapper for IBaseWorker type
type _game_IBaseWorker struct {
	IValue   interface{}
	WOnClean func()
	WOnEnter func()
	WOnInit  func(conf map[string]interface{})
	WOnLeave func(state behavior.Status)
	WOnOpen  func()
	WOnTick  func(dt int32) behavior.Status
}

func (W _game_IBaseWorker) OnClean() {
	W.WOnClean()
}

func (W _game_IBaseWorker) OnEnter() {
	W.WOnEnter()
}

func (W _game_IBaseWorker) OnInit(conf map[string]interface{}) {
	W.WOnInit(conf)
}

func (W _game_IBaseWorker) OnLeave(state behavior.Status) {
	W.WOnLeave(state)
}

func (W _game_IBaseWorker) OnOpen() {
	W.WOnOpen()
}

func (W _game_IBaseWorker) OnTick(dt int32) behavior.Status {
	return W.WOnTick(dt)
}

// _game_Area is an interface wrapper for Area type
type _game_Area struct {
	IValue                      interface{}
	WAddFightersToBattle        func()
	WBattleEnd                  func(fighters []string)
	WCheckBattleEndTime         func() bool
	WCheckIsBattleArea          func(x int32, y int32) bool
	WCheckIsBuildArea           func(x int32, y int32) bool
	WCreateBuildFlagPawn        func(uid string, hp []int32, point *ut.Vec2, owner string) g.Pawn
	WCreateNoncombatPawn        func(uid string, id int32, lv int32, point *ut.Vec2, owner string, enterDir int32) g.Pawn
	WCreatePetPawn              func(uid string, id int32, lv int32, point *ut.Vec2, owner string) g.Pawn
	WGetAreaIBuildById          func(id int32) g.Build
	WGetAreaSize                func() *ut.Vec2
	WGetBattleCurrentFrameIndex func() int32
	WGetBattleTime              func() int32
	WGetBuildPawnInfo           func() (id int32, lv int32)
	WGetCityID                  func() int32
	WGetCurHP                   func() int32
	WGetIndex                   func() int32
	WGetMainPoints              func() []*ut.Vec2
	WGetMaxHP                   func() int32
	WGetOwner                   func() string
	WGetWorld                   func() g.World
	WNotifyCheckFrame           func(msg *pb.GAME_ONFSPCHECKFRAME_NOTIFY)
	WRecordLastAttackMainPlayer func()
	WRemoveArmyPawn             func(auid string, uid string, isDead bool) (bool, map[int32]int32)
	WSetCurHP                   func(val int32)
	WSetMaxHP                   func(val int32)
	WToArmysStrip2              func() []map[string]interface{}
	WToBuildsStrip              func() []map[string]interface{}
}

func (W _game_Area) AddFightersToBattle() {
	W.WAddFightersToBattle()
}

func (W _game_Area) BattleEnd(fighters []string) {
	W.WBattleEnd(fighters)
}

func (W _game_Area) CheckBattleEndTime() bool {
	return W.WCheckBattleEndTime()
}

func (W _game_Area) CheckIsBattleArea(x int32, y int32) bool {
	return W.WCheckIsBattleArea(x, y)
}

func (W _game_Area) CheckIsBuildArea(x int32, y int32) bool {
	return W.WCheckIsBuildArea(x, y)
}

func (W _game_Area) CreateBuildFlagPawn(uid string, hp []int32, point *ut.Vec2, owner string) g.Pawn {
	return W.WCreateBuildFlagPawn(uid, hp, point, owner)
}

func (W _game_Area) CreateNoncombatPawn(uid string, id int32, lv int32, point *ut.Vec2, owner string, enterDir int32) g.Pawn {
	return W.WCreateNoncombatPawn(uid, id, lv, point, owner, enterDir)
}

func (W _game_Area) CreatePetPawn(uid string, id int32, lv int32, point *ut.Vec2, owner string) g.Pawn {
	return W.WCreatePetPawn(uid, id, lv, point, owner)
}

func (W _game_Area) GetAreaIBuildById(id int32) g.Build {
	return W.WGetAreaIBuildById(id)
}

func (W _game_Area) GetAreaSize() *ut.Vec2 {
	return W.WGetAreaSize()
}

func (W _game_Area) GetBattleCurrentFrameIndex() int32 {
	return W.WGetBattleCurrentFrameIndex()
}

func (W _game_Area) GetBattleTime() int32 {
	return W.WGetBattleTime()
}

func (W _game_Area) GetBuildPawnInfo() (id int32, lv int32) {
	return W.WGetBuildPawnInfo()
}

func (W _game_Area) GetCityID() int32 {
	return W.WGetCityID()
}

func (W _game_Area) GetCurHP() int32 {
	return W.WGetCurHP()
}

func (W _game_Area) GetIndex() int32 {
	return W.WGetIndex()
}

func (W _game_Area) GetMainPoints() []*ut.Vec2 {
	return W.WGetMainPoints()
}

func (W _game_Area) GetMaxHP() int32 {
	return W.WGetMaxHP()
}

func (W _game_Area) GetOwner() string {
	return W.WGetOwner()
}

func (W _game_Area) GetWorld() g.World {
	return W.WGetWorld()
}

func (W _game_Area) NotifyCheckFrame(msg *pb.GAME_ONFSPCHECKFRAME_NOTIFY) {
	W.WNotifyCheckFrame(msg)
}

func (W _game_Area) RecordLastAttackMainPlayer() {
	W.WRecordLastAttackMainPlayer()
}

func (W _game_Area) RemoveArmyPawn(auid string, uid string, isDead bool) (bool, map[int32]int32) {
	return W.WRemoveArmyPawn(auid, uid, isDead)
}

func (W _game_Area) SetCurHP(val int32) {
	W.WSetCurHP(val)
}

func (W _game_Area) SetMaxHP(val int32) {
	W.WSetMaxHP(val)
}

func (W _game_Area) ToArmysStrip2() []map[string]interface{} {
	return W.WToArmysStrip2()
}

func (W _game_Area) ToBuildsStrip() []map[string]interface{} {
	return W.WToBuildsStrip()
}

// _game_DataWeight is an interface wrapper for DataWeight type
type _game_DataWeight struct {
	IValue     interface{}
	WGetWeight func() int
}

func (W _game_DataWeight) GetWeight() int {
	return W.WGetWeight()
}

// _game_BattleCtrl is an interface wrapper for BattleCtrl type
type _game_BattleCtrl struct {
	IValue                         interface{}
	WAddFighterBattleDamageInfo    func(attackerUID string, defender g.IFighter, damage int32)
	WAddFighterBattleInfo          func(uid string, owner string, tp int32, val int32)
	WAddNoncombat                  func(uid string, id int32, lv int32, target *ut.Vec2, camp int32, owner string, params map[string]interface{})
	WAddPetPawn                    func(uid string, id int32, lv int32, target *ut.Vec2, camp int32, owner string)
	WCheckDashPoint                func(point *ut.Vec2, camp int32) bool
	WCheckHasFighter               func(x int32, y int32) bool
	WCheckHasFighterById           func(x int32, y int32, id int32) bool
	WCheckIsBattleArea             func(x int32, y int32) bool
	WDelLockMovePointFighter       func(point *ut.Vec2) bool
	WDoAttackAfter                 func(attacker g.IFighter, defender g.IFighter, data map[string]interface{}) int32
	WGetAreaSize                   func() *ut.Vec2
	WGetAttackDamage               func(attacker g.IFighter, defender g.IFighter, data map[string]interface{}) (int32, int32, int32, bool)
	WGetBowmanMovePaths            func(attacker g.IFighter) (*ut.Vec2, string)
	WGetCurrentFrameIndex          func() int32
	WGetFighter                    func(uid string) g.IFighter
	WGetFighterCountByPoint        func(point *ut.Vec2) int32
	WGetFighterCountForCampByPoint func(point *ut.Vec2, camp int32, reverse bool) int32
	WGetFighters                   func() []g.IFighter
	WGetFightersByPoint            func(x int32, y int32) []g.IFighter
	WGetHeroFighters               func(skiilId int32, camp int32, ignoreUid string) []g.IFighter
	WGetLockMovePointFighter       func(point *ut.Vec2) *g.AttackMovePointLockInfo
	WGetRandom                     func() *random.Random
	WNormalizeVec2                 func(point *ut.Vec2) *ut.Vec2
	WRemoveDieFighters             func() string
	WRemoveHasFighterPointByLast   func(paths []*ut.Vec2) []*ut.Vec2
	WRemoveNoncombat               func(uid string)
	WSearchDiaupPoint              func(point *ut.Vec2, points []*ut.Vec2) (*ut.Vec2, bool)
	WSearchIdlePoint               func(point *ut.Vec2, rang int32) (*ut.Vec2, bool)
	WSetLockMovePointFighter       func(point *ut.Vec2, fighter g.IFighter, weight int64, movePoint *ut.Vec2) bool
	WTriggerTaskAfterDamage        func(attacker g.IFighter, defender g.IFighter, damage int32, hpRatioBefore float64)
	WUpdateFighterPointMap         func()
}

func (W _game_BattleCtrl) AddFighterBattleDamageInfo(attackerUID string, defender g.IFighter, damage int32) {
	W.WAddFighterBattleDamageInfo(attackerUID, defender, damage)
}

func (W _game_BattleCtrl) AddFighterBattleInfo(uid string, owner string, tp int32, val int32) {
	W.WAddFighterBattleInfo(uid, owner, tp, val)
}

func (W _game_BattleCtrl) AddNoncombat(uid string, id int32, lv int32, target *ut.Vec2, camp int32, owner string, params map[string]interface{}) {
	W.WAddNoncombat(uid, id, lv, target, camp, owner, params)
}

func (W _game_BattleCtrl) AddPetPawn(uid string, id int32, lv int32, target *ut.Vec2, camp int32, owner string) {
	W.WAddPetPawn(uid, id, lv, target, camp, owner)
}

func (W _game_BattleCtrl) CheckDashPoint(point *ut.Vec2, camp int32) bool {
	return W.WCheckDashPoint(point, camp)
}

func (W _game_BattleCtrl) CheckHasFighter(x int32, y int32) bool {
	return W.WCheckHasFighter(x, y)
}

func (W _game_BattleCtrl) CheckHasFighterById(x int32, y int32, id int32) bool {
	return W.WCheckHasFighterById(x, y, id)
}

func (W _game_BattleCtrl) CheckIsBattleArea(x int32, y int32) bool {
	return W.WCheckIsBattleArea(x, y)
}

func (W _game_BattleCtrl) DelLockMovePointFighter(point *ut.Vec2) bool {
	return W.WDelLockMovePointFighter(point)
}

func (W _game_BattleCtrl) DoAttackAfter(attacker g.IFighter, defender g.IFighter, data map[string]interface{}) int32 {
	return W.WDoAttackAfter(attacker, defender, data)
}

func (W _game_BattleCtrl) GetAreaSize() *ut.Vec2 {
	return W.WGetAreaSize()
}

func (W _game_BattleCtrl) GetAttackDamage(attacker g.IFighter, defender g.IFighter, data map[string]interface{}) (int32, int32, int32, bool) {
	return W.WGetAttackDamage(attacker, defender, data)
}

func (W _game_BattleCtrl) GetBowmanMovePaths(attacker g.IFighter) (*ut.Vec2, string) {
	return W.WGetBowmanMovePaths(attacker)
}

func (W _game_BattleCtrl) GetCurrentFrameIndex() int32 {
	return W.WGetCurrentFrameIndex()
}

func (W _game_BattleCtrl) GetFighter(uid string) g.IFighter {
	return W.WGetFighter(uid)
}

func (W _game_BattleCtrl) GetFighterCountByPoint(point *ut.Vec2) int32 {
	return W.WGetFighterCountByPoint(point)
}

func (W _game_BattleCtrl) GetFighterCountForCampByPoint(point *ut.Vec2, camp int32, reverse bool) int32 {
	return W.WGetFighterCountForCampByPoint(point, camp, reverse)
}

func (W _game_BattleCtrl) GetFighters() []g.IFighter {
	return W.WGetFighters()
}

func (W _game_BattleCtrl) GetFightersByPoint(x int32, y int32) []g.IFighter {
	return W.WGetFightersByPoint(x, y)
}

func (W _game_BattleCtrl) GetHeroFighters(skiilId int32, camp int32, ignoreUid string) []g.IFighter {
	return W.WGetHeroFighters(skiilId, camp, ignoreUid)
}

func (W _game_BattleCtrl) GetLockMovePointFighter(point *ut.Vec2) *g.AttackMovePointLockInfo {
	return W.WGetLockMovePointFighter(point)
}

func (W _game_BattleCtrl) GetRandom() *random.Random {
	return W.WGetRandom()
}

func (W _game_BattleCtrl) NormalizeVec2(point *ut.Vec2) *ut.Vec2 {
	return W.WNormalizeVec2(point)
}

func (W _game_BattleCtrl) RemoveDieFighters() string {
	return W.WRemoveDieFighters()
}

func (W _game_BattleCtrl) RemoveHasFighterPointByLast(paths []*ut.Vec2) []*ut.Vec2 {
	return W.WRemoveHasFighterPointByLast(paths)
}

func (W _game_BattleCtrl) RemoveNoncombat(uid string) {
	W.WRemoveNoncombat(uid)
}

func (W _game_BattleCtrl) SearchDiaupPoint(point *ut.Vec2, points []*ut.Vec2) (*ut.Vec2, bool) {
	return W.WSearchDiaupPoint(point, points)
}

func (W _game_BattleCtrl) SearchIdlePoint(point *ut.Vec2, rang int32) (*ut.Vec2, bool) {
	return W.WSearchIdlePoint(point, rang)
}

func (W _game_BattleCtrl) SetLockMovePointFighter(point *ut.Vec2, fighter g.IFighter, weight int64, movePoint *ut.Vec2) bool {
	return W.WSetLockMovePointFighter(point, fighter, weight, movePoint)
}

func (W _game_BattleCtrl) TriggerTaskAfterDamage(attacker g.IFighter, defender g.IFighter, damage int32, hpRatioBefore float64) {
	W.WTriggerTaskAfterDamage(attacker, defender, damage, hpRatioBefore)
}

func (W _game_BattleCtrl) UpdateFighterPointMap() {
	W.WUpdateFighterPointMap()
}

// _game_Bazaar is an interface wrapper for Bazaar type
type _game_Bazaar struct {
	IValue                interface{}
	WAddTradingRes        func(sell *g.TypeObj, buy *g.TypeObj, owner string, merchantCount int32, onlyAlli bool)
	WCleanPlayerBazaarRes func(uid string)
	WGetTradeCurPrice     func(sellType int32, buyType int32) float64
	WGetTradingResCount   func() int
	WModifyTradePrice     func(sellType int32, buyType int32, price float64)
	WRemoveTradingRes     func(uid string)
	WStrip                func() map[string]interface{}
	WToTradingRessPb      func(plyUid string) []*pb.TradingRessInfo
	WTradePriceToPb       func() []*pb.TradePriceInfo
}

func (W _game_Bazaar) AddTradingRes(sell *g.TypeObj, buy *g.TypeObj, owner string, merchantCount int32, onlyAlli bool) {
	W.WAddTradingRes(sell, buy, owner, merchantCount, onlyAlli)
}

func (W _game_Bazaar) CleanPlayerBazaarRes(uid string) {
	W.WCleanPlayerBazaarRes(uid)
}

func (W _game_Bazaar) GetTradeCurPrice(sellType int32, buyType int32) float64 {
	return W.WGetTradeCurPrice(sellType, buyType)
}

func (W _game_Bazaar) GetTradingResCount() int {
	return W.WGetTradingResCount()
}

func (W _game_Bazaar) ModifyTradePrice(sellType int32, buyType int32, price float64) {
	W.WModifyTradePrice(sellType, buyType, price)
}

func (W _game_Bazaar) RemoveTradingRes(uid string) {
	W.WRemoveTradingRes(uid)
}

func (W _game_Bazaar) Strip() map[string]interface{} {
	return W.WStrip()
}

func (W _game_Bazaar) ToTradingRessPb(plyUid string) []*pb.TradingRessInfo {
	return W.WToTradingRessPb(plyUid)
}

func (W _game_Bazaar) TradePriceToPb() []*pb.TradePriceInfo {
	return W.WTradePriceToPb()
}

// _game_Build is an interface wrapper for Build type
type _game_Build struct {
	IValue          interface{}
	WGetBuildPawnId func() int32
	WGetID          func() int32
	WGetLV          func() int32
	WGetUID         func() string
	WGetUpCosts     func() []*g.TypeObj
	WToShortData    func() map[string]interface{}
	WToShortDataPb  func() *pb.AreaBuildInfo
	WToShortInfoPb  func() *pb.AreaBuildInfo
}

func (W _game_Build) GetBuildPawnId() int32 {
	return W.WGetBuildPawnId()
}

func (W _game_Build) GetID() int32 {
	return W.WGetID()
}

func (W _game_Build) GetLV() int32 {
	return W.WGetLV()
}

func (W _game_Build) GetUID() string {
	return W.WGetUID()
}

func (W _game_Build) GetUpCosts() []*g.TypeObj {
	return W.WGetUpCosts()
}

func (W _game_Build) ToShortData() map[string]interface{} {
	return W.WToShortData()
}

func (W _game_Build) ToShortDataPb() *pb.AreaBuildInfo {
	return W.WToShortDataPb()
}

func (W _game_Build) ToShortInfoPb() *pb.AreaBuildInfo {
	return W.WToShortInfoPb()
}

// _game_IFighter is an interface wrapper for IFighter type
type _game_IFighter struct {
	IValue                           interface{}
	WAddAnger                        func(v int32)
	WAddAttackCount                  func(val int32)
	WAddBuff                         func(tp int32, provider string, lv int32) *g.BuffObj
	WAddBuffValue                    func(tp int32, provider string, value float64) *g.BuffObj
	WAddCDodgeCount                  func()
	WAddRoundCount                   func(val int32)
	WAddRoundEndDelayTime            func(val int32)
	WAmendAttack                     func(attack int32) int32
	WAmendRestrainValue              func(val float64, tp int32) float64
	WBeginAction                     func()
	WBehaviorTick                    func(dt int32)
	WChangeAttackTarget              func(val g.IFighter)
	WChangeState                     func(state int32)
	WCheckBattleBeginTrigger         func()
	WCheckPortrayalSkill             func(id int32) *g.PortrayalSkill
	WCheckTriggerBuff                func(tp int32) *g.BuffObj
	WCheckTriggerBuffOr              func(types ...int32) *g.BuffObj
	WCleanBlackboard                 func(retainKeys ...string)
	WCleanCanAttackTargets           func()
	WDeductAnger                     func(v int32)
	WEndAction                       func()
	WGetActAttack                    func() int32
	WGetActAttackForRestrain         func(attack int32, target g.IFighter) int32
	WGetActiveSkill                  func() g.PawnSkill
	WGetAngerRatio                   func() float64
	WGetAstar                        func() *astar.AStarRange
	WGetAttack                       func() int32
	WGetAttackAnimTimes              func() [][]float64
	WGetAttackCount                  func() int32
	WGetAttackIndex                  func() int32
	WGetAttackRange                  func() int32
	WGetAttackSpeed                  func() int32
	WGetAttackTarget                 func() g.IFighter
	WGetAttrJson                     func() map[string]interface{}
	WGetBaseJson                     func() map[string]interface{}
	WGetBlackboardData               func(id int32, key string) interface{}
	WGetBuff                         func(tp int32) *g.BuffObj
	WGetBuffOrAdd                    func(tp int32, provider string) *g.BuffObj
	WGetBuffValue                    func(tp int32) float64
	WGetBuffs                        func() []*g.BuffObj
	WGetCDodgeCount                  func() int32
	WGetCamp                         func() int32
	WGetCanAttackFighterByRange      func(fighters []g.IFighter, rang int32, cnt int32, ignoreUid string) []g.IFighter
	WGetCanAttackFighters            func() []g.IFighter
	WGetCanAttackPawnByRange         func(fighters []g.IFighter, rang int32, cnt int32, ignoreUid string) []g.IFighter
	WGetCanAttackRangeFighter        func(fighters []g.IFighter, rang int32, cnt int32, ignoreUid string, cb func(m g.IFighter) bool) []g.IFighter
	WGetCanAttackRangeFighterByPoint func(point *ut.Vec2, fighters []g.IFighter, rang int32, cnt int32, cb func(m g.IFighter) bool) []g.IFighter
	WGetCanAttackRangeTargets        func(point *ut.Vec2, fighters []g.IFighter, rang int32, ignoreUid string, cb func(m g.IFighter) bool) ([]g.IFighter, []g.IFighter)
	WGetCanAttackTargets             func() []g.CanAttackTargetInfo
	WGetCanByReqelPoint              func(point *ut.Vec2, rang int32) *ut.Vec2
	WGetCtrl                         func() g.BattleCtrl
	WGetCurAnger                     func() int32
	WGetCurHp                        func() int32
	WGetEnterDir                     func() int32
	WGetEntity                       func() g.Pawn
	WGetEquipEffectByType            func(tp int32) *g.EquipEffectObj
	WGetEquipEffects                 func() []*g.EquipEffectObj
	WGetFightPower                   func(target g.IFighter) int32
	WGetHPRatio                      func() float64
	WGetID                           func() int32
	WGetIgnoreBuffAttack             func(tp int32) int32
	WGetInstabilityMaxAttack         func() int32
	WGetInstabilityRandomAttack      func(index int32) int32
	WGetLV                           func() int32
	WGetLastPoint                    func() *ut.Vec2
	WGetMaxHp                        func() int32
	WGetOwner                        func() string
	WGetPawnType                     func() int32
	WGetPoint                        func() *ut.Vec2
	WGetPortrayalSkill               func() *g.PortrayalSkill
	WGetPortrayalSkillID             func() int32
	WGetRoundCount                   func() int32
	WGetSearchDir                    func(attackTarget g.IFighter) int32
	WGetSearchRange                  func() *astar.SearchRange
	WGetShieldVal                    func() int32
	WGetSkillByType                  func(tp int32) g.PawnSkill
	WGetStrategyBuff                 func(tp int32) *g.StrategyObj
	WGetStrategyParams               func(tp int32) int32
	WGetStrategyValue                func(tp int32) int32
	WGetSuckBloodValue               func() float64
	WGetTargetCanAttackPoints        func(point *ut.Vec2) []*ut.Vec2
	WGetTempAttackIndex              func() int32
	WGetTempRandomVal                func() int32
	WGetUID                          func() string
	WGetWaitRound                    func() int32
	WHitPrepDamageHandle             func(damage int32, trueDamage int32) (int32, int32)
	WInitSkillAttackAnimTime         func()
	WIsBuild                         func() bool
	WIsCanUseSkill                   func() bool
	WIsDie                           func() bool
	WIsFlag                          func() bool
	WIsFullHP                        func() bool
	WIsHasAnger                      func() bool
	WIsHasBuff                       func(tp int32) bool
	WIsHasBuffs                      func(types ...int32) bool
	WIsHasNegativeBuff               func() bool
	WIsHasStrategys                  func(tps ...int32) bool
	WIsHero                          func() bool
	WIsNoncombat                     func() bool
	WIsNotAddAngerByBuff             func() bool
	WIsPawn                          func() bool
	WIsRoundEnd                      func() bool
	WIsTower                         func() bool
	WMD5                             func() string
	WOnHeal                          func(val int32, suckbloodShield bool) int32
	WOnHit                           func(damage int32, baseDamage int32) (int32, int32, int32)
	WRemoveAllDebuff                 func()
	WRemoveBuff                      func(tp int32) bool
	WRemoveBuffByProvider            func(tp int32, provider string) bool
	WRemoveMultiBuff                 func(types ...int32)
	WRemoveShieldBuffs               func()
	WResetCDodgeCount                func()
	WSetAnger                        func(val int32)
	WSetAttackCount                  func(val int32)
	WSetAttackIndex                  func(val int32)
	WSetAttackTarget                 func(val g.IFighter)
	WSetBlackboard                   func(id int32, key string, val interface{})
	WSetBlackboardMap                func(val map[int32]map[string]interface{})
	WSetCamp                         func(camp int32)
	WSetFullAnger                    func(ratio float64, check bool)
	WSetLV                           func(val int32)
	WSetPoint                        func(point *ut.Vec2)
	WSetRoundEnd                     func()
	WSetTempRandomVal                func(val int32)
	WSetWaitRound                    func(val int32)
	WStrip                           func() map[string]interface{}
	WToBlackboardMap                 func() map[int32]map[string]interface{}
	WToBlackboardMapPb               func() map[int32]*pb.BlackboardInfo
	WToDB                            func() map[string]interface{}
	WToPb                            func() *pb.FighterInfo
	WUpdateAttrByBattle              func(id int32, lv int32)
	WUpdateBuff                      func()
	WUpdateLv                        func(val int32)
	WUpdateMaxHpRecord               func(maxHp int32)
	WUpdateStrategyEffect            func()
}

func (W _game_IFighter) AddAnger(v int32) {
	W.WAddAnger(v)
}

func (W _game_IFighter) AddAttackCount(val int32) {
	W.WAddAttackCount(val)
}

func (W _game_IFighter) AddBuff(tp int32, provider string, lv int32) *g.BuffObj {
	return W.WAddBuff(tp, provider, lv)
}

func (W _game_IFighter) AddBuffValue(tp int32, provider string, value float64) *g.BuffObj {
	return W.WAddBuffValue(tp, provider, value)
}

func (W _game_IFighter) AddCDodgeCount() {
	W.WAddCDodgeCount()
}

func (W _game_IFighter) AddRoundCount(val int32) {
	W.WAddRoundCount(val)
}

func (W _game_IFighter) AddRoundEndDelayTime(val int32) {
	W.WAddRoundEndDelayTime(val)
}

func (W _game_IFighter) AmendAttack(attack int32) int32 {
	return W.WAmendAttack(attack)
}

func (W _game_IFighter) AmendRestrainValue(val float64, tp int32) float64 {
	return W.WAmendRestrainValue(val, tp)
}

func (W _game_IFighter) BeginAction() {
	W.WBeginAction()
}

func (W _game_IFighter) BehaviorTick(dt int32) {
	W.WBehaviorTick(dt)
}

func (W _game_IFighter) ChangeAttackTarget(val g.IFighter) {
	W.WChangeAttackTarget(val)
}

func (W _game_IFighter) ChangeState(state int32) {
	W.WChangeState(state)
}

func (W _game_IFighter) CheckBattleBeginTrigger() {
	W.WCheckBattleBeginTrigger()
}

func (W _game_IFighter) CheckPortrayalSkill(id int32) *g.PortrayalSkill {
	return W.WCheckPortrayalSkill(id)
}

func (W _game_IFighter) CheckTriggerBuff(tp int32) *g.BuffObj {
	return W.WCheckTriggerBuff(tp)
}

func (W _game_IFighter) CheckTriggerBuffOr(types ...int32) *g.BuffObj {
	return W.WCheckTriggerBuffOr(types...)
}

func (W _game_IFighter) CleanBlackboard(retainKeys ...string) {
	W.WCleanBlackboard(retainKeys...)
}

func (W _game_IFighter) CleanCanAttackTargets() {
	W.WCleanCanAttackTargets()
}

func (W _game_IFighter) DeductAnger(v int32) {
	W.WDeductAnger(v)
}

func (W _game_IFighter) EndAction() {
	W.WEndAction()
}

func (W _game_IFighter) GetActAttack() int32 {
	return W.WGetActAttack()
}

func (W _game_IFighter) GetActiveSkill() g.PawnSkill {
	return W.WGetActiveSkill()
}

func (W _game_IFighter) GetAngerRatio() float64 {
	return W.WGetAngerRatio()
}

func (W _game_IFighter) GetAstar() *astar.AStarRange {
	return W.WGetAstar()
}

func (W _game_IFighter) GetAttack() int32 {
	return W.WGetAttack()
}

func (W _game_IFighter) GetAttackAnimTimes() [][]float64 {
	return W.WGetAttackAnimTimes()
}

func (W _game_IFighter) GetAttackCount() int32 {
	return W.WGetAttackCount()
}

func (W _game_IFighter) GetAttackIndex() int32 {
	return W.WGetAttackIndex()
}

func (W _game_IFighter) GetAttackRange() int32 {
	return W.WGetAttackRange()
}

func (W _game_IFighter) GetAttackSpeed() int32 {
	return W.WGetAttackSpeed()
}

func (W _game_IFighter) GetAttackTarget() g.IFighter {
	return W.WGetAttackTarget()
}

func (W _game_IFighter) GetAttrJson() map[string]interface{} {
	return W.WGetAttrJson()
}

func (W _game_IFighter) GetBaseJson() map[string]interface{} {
	return W.WGetBaseJson()
}

func (W _game_IFighter) GetBlackboardData(id int32, key string) interface{} {
	return W.WGetBlackboardData(id, key)
}

func (W _game_IFighter) GetBuff(tp int32) *g.BuffObj {
	return W.WGetBuff(tp)
}

func (W _game_IFighter) GetBuffOrAdd(tp int32, provider string) *g.BuffObj {
	return W.WGetBuffOrAdd(tp, provider)
}

func (W _game_IFighter) GetBuffValue(tp int32) float64 {
	return W.WGetBuffValue(tp)
}

func (W _game_IFighter) GetBuffs() []*g.BuffObj {
	return W.WGetBuffs()
}

func (W _game_IFighter) GetCDodgeCount() int32 {
	return W.WGetCDodgeCount()
}

func (W _game_IFighter) GetCamp() int32 {
	return W.WGetCamp()
}

func (W _game_IFighter) GetCanAttackFighterByRange(fighters []g.IFighter, rang int32, cnt int32, ignoreUid string) []g.IFighter {
	return W.WGetCanAttackFighterByRange(fighters, rang, cnt, ignoreUid)
}

func (W _game_IFighter) GetCanAttackFighters() []g.IFighter {
	return W.WGetCanAttackFighters()
}

func (W _game_IFighter) GetCanAttackPawnByRange(fighters []g.IFighter, rang int32, cnt int32, ignoreUid string) []g.IFighter {
	return W.WGetCanAttackPawnByRange(fighters, rang, cnt, ignoreUid)
}

func (W _game_IFighter) GetCanAttackRangeFighter(fighters []g.IFighter, rang int32, cnt int32, ignoreUid string, cb func(m g.IFighter) bool) []g.IFighter {
	return W.WGetCanAttackRangeFighter(fighters, rang, cnt, ignoreUid, cb)
}

func (W _game_IFighter) GetCanAttackRangeFighterByPoint(point *ut.Vec2, fighters []g.IFighter, rang int32, cnt int32, cb func(m g.IFighter) bool) []g.IFighter {
	return W.WGetCanAttackRangeFighterByPoint(point, fighters, rang, cnt, cb)
}

func (W _game_IFighter) GetCanAttackRangeTargets(point *ut.Vec2, fighters []g.IFighter, rang int32, ignoreUid string, cb func(m g.IFighter) bool) ([]g.IFighter, []g.IFighter) {
	return W.WGetCanAttackRangeTargets(point, fighters, rang, ignoreUid, cb)
}

func (W _game_IFighter) GetCanAttackTargets() []g.CanAttackTargetInfo {
	return W.WGetCanAttackTargets()
}

func (W _game_IFighter) GetCanByReqelPoint(point *ut.Vec2, rang int32) *ut.Vec2 {
	return W.WGetCanByReqelPoint(point, rang)
}

func (W _game_IFighter) GetCtrl() g.BattleCtrl {
	return W.WGetCtrl()
}

func (W _game_IFighter) GetCurAnger() int32 {
	return W.WGetCurAnger()
}

func (W _game_IFighter) GetCurHp() int32 {
	return W.WGetCurHp()
}

func (W _game_IFighter) GetEnterDir() int32 {
	return W.WGetEnterDir()
}

func (W _game_IFighter) GetEntity() g.Pawn {
	return W.WGetEntity()
}

func (W _game_IFighter) GetEquipEffectByType(tp int32) *g.EquipEffectObj {
	return W.WGetEquipEffectByType(tp)
}

func (W _game_IFighter) GetEquipEffects() []*g.EquipEffectObj {
	return W.WGetEquipEffects()
}

func (W _game_IFighter) GetFightPower(target g.IFighter) int32 {
	return W.WGetFightPower(target)
}

func (W _game_IFighter) GetHPRatio() float64 {
	return W.WGetHPRatio()
}

func (W _game_IFighter) GetID() int32 {
	return W.WGetID()
}

func (W _game_IFighter) GetIgnoreBuffAttack(tp int32) int32 {
	return W.WGetIgnoreBuffAttack(tp)
}

func (W _game_IFighter) GetInstabilityMaxAttack() int32 {
	return W.WGetInstabilityMaxAttack()
}

func (W _game_IFighter) GetInstabilityRandomAttack(index int32) int32 {
	return W.WGetInstabilityRandomAttack(index)
}

func (W _game_IFighter) GetLV() int32 {
	return W.WGetLV()
}

func (W _game_IFighter) GetLastPoint() *ut.Vec2 {
	return W.WGetLastPoint()
}

func (W _game_IFighter) GetMaxHp() int32 {
	return W.WGetMaxHp()
}

func (W _game_IFighter) GetOwner() string {
	return W.WGetOwner()
}

func (W _game_IFighter) GetPawnType() int32 {
	return W.WGetPawnType()
}

func (W _game_IFighter) GetPoint() *ut.Vec2 {
	return W.WGetPoint()
}

func (W _game_IFighter) GetPortrayalSkill() *g.PortrayalSkill {
	return W.WGetPortrayalSkill()
}

func (W _game_IFighter) GetPortrayalSkillID() int32 {
	return W.WGetPortrayalSkillID()
}

func (W _game_IFighter) GetRoundCount() int32 {
	return W.WGetRoundCount()
}

func (W _game_IFighter) GetSearchDir(attackTarget g.IFighter) int32 {
	return W.WGetSearchDir(attackTarget)
}

func (W _game_IFighter) GetSearchRange() *astar.SearchRange {
	return W.WGetSearchRange()
}

func (W _game_IFighter) GetShieldVal() int32 {
	return W.WGetShieldVal()
}

func (W _game_IFighter) GetSkillByType(tp int32) g.PawnSkill {
	return W.WGetSkillByType(tp)
}

func (W _game_IFighter) GetStrategyBuff(tp int32) *g.StrategyObj {
	return W.WGetStrategyBuff(tp)
}

func (W _game_IFighter) GetStrategyParams(tp int32) int32 {
	return W.WGetStrategyParams(tp)
}

func (W _game_IFighter) GetStrategyValue(tp int32) int32 {
	return W.WGetStrategyValue(tp)
}

func (W _game_IFighter) GetSuckBloodValue() float64 {
	return W.WGetSuckBloodValue()
}

func (W _game_IFighter) GetTargetCanAttackPoints(point *ut.Vec2) []*ut.Vec2 {
	return W.WGetTargetCanAttackPoints(point)
}

func (W _game_IFighter) GetTempAttackIndex() int32 {
	return W.WGetTempAttackIndex()
}

func (W _game_IFighter) GetTempRandomVal() int32 {
	return W.WGetTempRandomVal()
}

func (W _game_IFighter) GetUID() string {
	return W.WGetUID()
}

func (W _game_IFighter) GetWaitRound() int32 {
	return W.WGetWaitRound()
}

func (W _game_IFighter) HitPrepDamageHandle(damage int32, trueDamage int32) (int32, int32) {
	return W.WHitPrepDamageHandle(damage, trueDamage)
}

func (W _game_IFighter) InitSkillAttackAnimTime() {
	W.WInitSkillAttackAnimTime()
}

func (W _game_IFighter) IsBuild() bool {
	return W.WIsBuild()
}

func (W _game_IFighter) IsCanUseSkill() bool {
	return W.WIsCanUseSkill()
}

func (W _game_IFighter) IsDie() bool {
	return W.WIsDie()
}

func (W _game_IFighter) IsFlag() bool {
	return W.WIsFlag()
}

func (W _game_IFighter) IsFullHP() bool {
	return W.WIsFullHP()
}

func (W _game_IFighter) IsHasAnger() bool {
	return W.WIsHasAnger()
}

func (W _game_IFighter) IsHasBuff(tp int32) bool {
	return W.WIsHasBuff(tp)
}

func (W _game_IFighter) IsHasBuffs(types ...int32) bool {
	return W.WIsHasBuffs(types...)
}

func (W _game_IFighter) IsHasNegativeBuff() bool {
	return W.WIsHasNegativeBuff()
}

func (W _game_IFighter) IsHasStrategys(tps ...int32) bool {
	return W.WIsHasStrategys(tps...)
}

func (W _game_IFighter) IsHero() bool {
	return W.WIsHero()
}

func (W _game_IFighter) IsNoncombat() bool {
	return W.WIsNoncombat()
}

func (W _game_IFighter) IsNotAddAngerByBuff() bool {
	return W.WIsNotAddAngerByBuff()
}

func (W _game_IFighter) IsPawn() bool {
	return W.WIsPawn()
}

func (W _game_IFighter) IsRoundEnd() bool {
	return W.WIsRoundEnd()
}

func (W _game_IFighter) IsTower() bool {
	return W.WIsTower()
}

func (W _game_IFighter) MD5() string {
	return W.WMD5()
}

func (W _game_IFighter) OnHeal(val int32, suckbloodShield bool) int32 {
	return W.WOnHeal(val, suckbloodShield)
}

func (W _game_IFighter) OnHit(damage int32, baseDamage int32) (int32, int32, int32) {
	return W.WOnHit(damage, baseDamage)
}

func (W _game_IFighter) RemoveAllDebuff() {
	W.WRemoveAllDebuff()
}

func (W _game_IFighter) RemoveBuff(tp int32) bool {
	return W.WRemoveBuff(tp)
}

func (W _game_IFighter) RemoveBuffByProvider(tp int32, provider string) bool {
	return W.WRemoveBuffByProvider(tp, provider)
}

func (W _game_IFighter) RemoveMultiBuff(types ...int32) {
	W.WRemoveMultiBuff(types...)
}

func (W _game_IFighter) RemoveShieldBuffs() {
	W.WRemoveShieldBuffs()
}

func (W _game_IFighter) ResetCDodgeCount() {
	W.WResetCDodgeCount()
}

func (W _game_IFighter) SetAnger(val int32) {
	W.WSetAnger(val)
}

func (W _game_IFighter) SetAttackCount(val int32) {
	W.WSetAttackCount(val)
}

func (W _game_IFighter) SetAttackIndex(val int32) {
	W.WSetAttackIndex(val)
}

func (W _game_IFighter) SetAttackTarget(val g.IFighter) {
	W.WSetAttackTarget(val)
}

func (W _game_IFighter) SetBlackboard(id int32, key string, val interface{}) {
	W.WSetBlackboard(id, key, val)
}

func (W _game_IFighter) SetBlackboardMap(val map[int32]map[string]interface{}) {
	W.WSetBlackboardMap(val)
}

func (W _game_IFighter) SetCamp(camp int32) {
	W.WSetCamp(camp)
}

func (W _game_IFighter) SetFullAnger(ratio float64, check bool) {
	W.WSetFullAnger(ratio, check)
}

func (W _game_IFighter) SetLV(val int32) {
	W.WSetLV(val)
}

func (W _game_IFighter) SetPoint(point *ut.Vec2) {
	W.WSetPoint(point)
}

func (W _game_IFighter) SetRoundEnd() {
	W.WSetRoundEnd()
}

func (W _game_IFighter) SetTempRandomVal(val int32) {
	W.WSetTempRandomVal(val)
}

func (W _game_IFighter) SetWaitRound(val int32) {
	W.WSetWaitRound(val)
}

func (W _game_IFighter) Strip() map[string]interface{} {
	return W.WStrip()
}

func (W _game_IFighter) ToBlackboardMap() map[int32]map[string]interface{} {
	return W.WToBlackboardMap()
}

func (W _game_IFighter) ToBlackboardMapPb() map[int32]*pb.BlackboardInfo {
	return W.WToBlackboardMapPb()
}

func (W _game_IFighter) ToDB() map[string]interface{} {
	return W.WToDB()
}

func (W _game_IFighter) ToPb() *pb.FighterInfo {
	return W.WToPb()
}

func (W _game_IFighter) UpdateAttrByBattle(id int32, lv int32) {
	W.WUpdateAttrByBattle(id, lv)
}

func (W _game_IFighter) UpdateBuff() {
	W.WUpdateBuff()
}

func (W _game_IFighter) UpdateLv(val int32) {
	W.WUpdateLv(val)
}

func (W _game_IFighter) UpdateMaxHpRecord(maxHp int32) {
	W.WUpdateMaxHpRecord(maxHp)
}

func (W _game_IFighter) UpdateStrategyEffect() {
	W.WUpdateStrategyEffect()
}

// _game_Pawn is an interface wrapper for Pawn type
type _game_Pawn struct {
	IValue                   interface{}
	WAddBuff                 func(buff *g.BuffObj)
	WAddStrategyBuff         func(strategy *g.StrategyObj)
	WAmendAttack             func(attack int32, ignoreBuffType int32) int32
	WBuffsLock               func()
	WBuffsRLock              func()
	WBuffsRUnlock            func()
	WBuffsUnlock             func()
	WCleanAllBuffs           func()
	WCleanStrategyBuffs      func()
	WGetActiveSkill          func() g.PawnSkill
	WGetArmyUid              func() string
	WGetAttack               func() int32
	WGetAttackIndex          func() int32
	WGetAttackRange          func() int32
	WGetAttackSpeed          func() int32
	WGetAttrJson             func() map[string]interface{}
	WGetBaseJson             func() map[string]interface{}
	WGetBehaviorId           func() int32
	WGetBuffs                func() []*g.BuffObj
	WGetCurAnger             func() int32
	WGetCurHP                func() int32
	WGetEnterDir             func() int32
	WGetEquip                func() *g.EquipInfo
	WGetEquipEffectByType    func(tp int32) *g.EquipEffectObj
	WGetEquipEffects         func() []*g.EquipEffectObj
	WGetID                   func() int32
	WGetInstabilityMaxAttack func() int32
	WGetLV                   func() int32
	WGetMaxAnger             func() int32
	WGetMaxHP                func() int32
	WGetMoveRange            func() int32
	WGetMoveVelocity         func() int32
	WGetOwner                func() string
	WGetPawnType             func() int32
	WGetPetId                func() int32
	WGetPoint                func() *ut.Vec2
	WGetPortrayal            func() *g.PortrayalInfo
	WGetPortrayalID          func() int32
	WGetPortrayalSkill       func() *g.PortrayalSkill
	WGetRodeleroCadetLv      func() int32
	WGetSkillByType          func(tp int32) g.PawnSkill
	WGetSkinID               func() int32
	WGetStrategyBuff         func(tp int32) *g.StrategyObj
	WGetStrategyValue        func(tp int32) int32
	WGetUID                  func() string
	WGetViewID               func() int32
	WInitAnger               func()
	WIsDie                   func() bool
	WIsHasAnger              func() bool
	WIsHasStrategys          func(tps ...int32) bool
	WIsHero                  func() bool
	WIsMaxLv                 func() bool
	WRemoveBuffByIndex       func(index int32)
	WRemoveBuffByIndexNoLock func(index int32)
	WSetCurAnger             func(val int32)
	WSetCurHP                func(val int32)
	WSetLv                   func(lv int32)
	WSetMaxHP                func(val int32)
	WSetPoint                func(point *ut.Vec2)
	WToNotifyPb              func() *pb.AreaPawnInfo
	WUpdateAttrByBattle      func(id int32, lv int32)
	WUpdateLv                func(lv int32)
}

func (W _game_Pawn) AddBuff(buff *g.BuffObj) {
	W.WAddBuff(buff)
}

func (W _game_Pawn) AddStrategyBuff(strategy *g.StrategyObj) {
	W.WAddStrategyBuff(strategy)
}

func (W _game_Pawn) AmendAttack(attack int32, ignoreBuffType int32) int32 {
	return W.WAmendAttack(attack, ignoreBuffType)
}

func (W _game_Pawn) BuffsLock() {
	W.WBuffsLock()
}

func (W _game_Pawn) BuffsRLock() {
	W.WBuffsRLock()
}

func (W _game_Pawn) BuffsRUnlock() {
	W.WBuffsRUnlock()
}

func (W _game_Pawn) BuffsUnlock() {
	W.WBuffsUnlock()
}

func (W _game_Pawn) CleanAllBuffs() {
	W.WCleanAllBuffs()
}

func (W _game_Pawn) CleanStrategyBuffs() {
	W.WCleanStrategyBuffs()
}

func (W _game_Pawn) GetActiveSkill() g.PawnSkill {
	return W.WGetActiveSkill()
}

func (W _game_Pawn) GetArmyUid() string {
	return W.WGetArmyUid()
}

func (W _game_Pawn) GetAttack() int32 {
	return W.WGetAttack()
}

func (W _game_Pawn) GetAttackIndex() int32 {
	return W.WGetAttackIndex()
}

func (W _game_Pawn) GetAttackRange() int32 {
	return W.WGetAttackRange()
}

func (W _game_Pawn) GetAttackSpeed() int32 {
	return W.WGetAttackSpeed()
}

func (W _game_Pawn) GetAttrJson() map[string]interface{} {
	return W.WGetAttrJson()
}

func (W _game_Pawn) GetBaseJson() map[string]interface{} {
	return W.WGetBaseJson()
}

func (W _game_Pawn) GetBehaviorId() int32 {
	return W.WGetBehaviorId()
}

func (W _game_Pawn) GetBuffs() []*g.BuffObj {
	return W.WGetBuffs()
}

func (W _game_Pawn) GetCurAnger() int32 {
	return W.WGetCurAnger()
}

func (W _game_Pawn) GetCurHP() int32 {
	return W.WGetCurHP()
}

func (W _game_Pawn) GetEnterDir() int32 {
	return W.WGetEnterDir()
}

func (W _game_Pawn) GetEquip() *g.EquipInfo {
	return W.WGetEquip()
}

func (W _game_Pawn) GetEquipEffectByType(tp int32) *g.EquipEffectObj {
	return W.WGetEquipEffectByType(tp)
}

func (W _game_Pawn) GetEquipEffects() []*g.EquipEffectObj {
	return W.WGetEquipEffects()
}

func (W _game_Pawn) GetID() int32 {
	return W.WGetID()
}

func (W _game_Pawn) GetInstabilityMaxAttack() int32 {
	return W.WGetInstabilityMaxAttack()
}

func (W _game_Pawn) GetLV() int32 {
	return W.WGetLV()
}

func (W _game_Pawn) GetMaxAnger() int32 {
	return W.WGetMaxAnger()
}

func (W _game_Pawn) GetMaxHP() int32 {
	return W.WGetMaxHP()
}

func (W _game_Pawn) GetMoveRange() int32 {
	return W.WGetMoveRange()
}

func (W _game_Pawn) GetMoveVelocity() int32 {
	return W.WGetMoveVelocity()
}

func (W _game_Pawn) GetOwner() string {
	return W.WGetOwner()
}

func (W _game_Pawn) GetPawnType() int32 {
	return W.WGetPawnType()
}

func (W _game_Pawn) GetPetId() int32 {
	return W.WGetPetId()
}

func (W _game_Pawn) GetPoint() *ut.Vec2 {
	return W.WGetPoint()
}

func (W _game_Pawn) GetPortrayal() *g.PortrayalInfo {
	return W.WGetPortrayal()
}

func (W _game_Pawn) GetPortrayalID() int32 {
	return W.WGetPortrayalID()
}

func (W _game_Pawn) GetPortrayalSkill() *g.PortrayalSkill {
	return W.WGetPortrayalSkill()
}

func (W _game_Pawn) GetRodeleroCadetLv() int32 {
	return W.WGetRodeleroCadetLv()
}

func (W _game_Pawn) GetSkillByType(tp int32) g.PawnSkill {
	return W.WGetSkillByType(tp)
}

func (W _game_Pawn) GetSkinID() int32 {
	return W.WGetSkinID()
}

func (W _game_Pawn) GetStrategyBuff(tp int32) *g.StrategyObj {
	return W.WGetStrategyBuff(tp)
}

func (W _game_Pawn) GetStrategyValue(tp int32) int32 {
	return W.WGetStrategyValue(tp)
}

func (W _game_Pawn) GetUID() string {
	return W.WGetUID()
}

func (W _game_Pawn) GetViewID() int32 {
	return W.WGetViewID()
}

func (W _game_Pawn) InitAnger() {
	W.WInitAnger()
}

func (W _game_Pawn) IsDie() bool {
	return W.WIsDie()
}

func (W _game_Pawn) IsHasAnger() bool {
	return W.WIsHasAnger()
}

func (W _game_Pawn) IsHasStrategys(tps ...int32) bool {
	return W.WIsHasStrategys(tps...)
}

func (W _game_Pawn) IsHero() bool {
	return W.WIsHero()
}

func (W _game_Pawn) IsMaxLv() bool {
	return W.WIsMaxLv()
}

func (W _game_Pawn) RemoveBuffByIndex(index int32) {
	W.WRemoveBuffByIndex(index)
}

func (W _game_Pawn) RemoveBuffByIndexNoLock(index int32) {
	W.WRemoveBuffByIndexNoLock(index)
}

func (W _game_Pawn) SetCurAnger(val int32) {
	W.WSetCurAnger(val)
}

func (W _game_Pawn) SetCurHP(val int32) {
	W.WSetCurHP(val)
}

func (W _game_Pawn) SetLv(lv int32) {
	W.WSetLv(lv)
}

func (W _game_Pawn) SetMaxHP(val int32) {
	W.WSetMaxHP(val)
}

func (W _game_Pawn) SetPoint(point *ut.Vec2) {
	W.WSetPoint(point)
}

func (W _game_Pawn) ToNotifyPb() *pb.AreaPawnInfo {
	return W.WToNotifyPb()
}

func (W _game_Pawn) UpdateAttrByBattle(id int32, lv int32) {
	W.WUpdateAttrByBattle(id, lv)
}

func (W _game_Pawn) UpdateLv(lv int32) {
	W.WUpdateLv(lv)
}

// _game_PawnSkill is an interface wrapper for PawnSkill type
type _game_PawnSkill struct {
	IValue              interface{}
	WGetBaseId          func() int32
	WGetBulletId        func() int32
	WGetId              func() int32
	WGetIntensifyType   func() int32
	WGetLv              func() int32
	WGetNeedAttackTime  func() int32
	WGetNeedHitTime     func() int32
	WGetParamsFloat64   func() float64
	WGetParamsFloats    func() []float64
	WGetParamsInt       func() int32
	WGetParamsInts      func() []int32
	WGetParamsString    func() string
	WGetTarget          func() int32
	WGetType            func() int32
	WGetUseType         func() int32
	WGetValue           func() float64
	WInitAttackAnimTime func(pawn g.Pawn)
}

func (W _game_PawnSkill) GetBaseId() int32 {
	return W.WGetBaseId()
}

func (W _game_PawnSkill) GetBulletId() int32 {
	return W.WGetBulletId()
}

func (W _game_PawnSkill) GetId() int32 {
	return W.WGetId()
}

func (W _game_PawnSkill) GetIntensifyType() int32 {
	return W.WGetIntensifyType()
}

func (W _game_PawnSkill) GetLv() int32 {
	return W.WGetLv()
}

func (W _game_PawnSkill) GetNeedAttackTime() int32 {
	return W.WGetNeedAttackTime()
}

func (W _game_PawnSkill) GetNeedHitTime() int32 {
	return W.WGetNeedHitTime()
}

func (W _game_PawnSkill) GetParamsFloat64() float64 {
	return W.WGetParamsFloat64()
}

func (W _game_PawnSkill) GetParamsFloats() []float64 {
	return W.WGetParamsFloats()
}

func (W _game_PawnSkill) GetParamsInt() int32 {
	return W.WGetParamsInt()
}

func (W _game_PawnSkill) GetParamsInts() []int32 {
	return W.WGetParamsInts()
}

func (W _game_PawnSkill) GetParamsString() string {
	return W.WGetParamsString()
}

func (W _game_PawnSkill) GetTarget() int32 {
	return W.WGetTarget()
}

func (W _game_PawnSkill) GetType() int32 {
	return W.WGetType()
}

func (W _game_PawnSkill) GetUseType() int32 {
	return W.WGetUseType()
}

func (W _game_PawnSkill) GetValue() float64 {
	return W.WGetValue()
}

func (W _game_PawnSkill) InitAttackAnimTime(pawn g.Pawn) {
	W.WInitAttackAnimTime(pawn)
}

// _game_Player is an interface wrapper for Player type
type _game_Player struct {
	IValue                          interface{}
	WAddAntiCheatScore              func(score int32)
	WChangeCostByTypeObjOne         func(m *g.TypeObj, change int32) (val int32, add int32)
	WChangeMerchantState            func(c int32, t int32, count int32)
	WCheckAndDeductCostByTypeObjOne func(to *g.TypeObj) (b bool)
	WGetOccupyLandCount             func(lv int32) int32
	WGetTempAreaInfo                func(index int32, land int32) map[string]interface{}
	WGetUID                         func() string
	WIsOnline                       func() bool
	WKick                           func(tpe int)
	WNotifyHasTreasure              func(val bool)
	WPutNotifyQueue                 func(nType int, data *pb.OnUpdatePlayerInfoNotify)
	WSessionSendNR                  func(topic string, body []byte)
	WSetAlliUid                     func(uid string)
	WToItemByTypeObjsPb             func(tos []*g.TypeObj) *pb.UpdateOutPut
	WToMerchants                    func() []map[string]interface{}
	WToMerchantsPb                  func() []*pb.MerchantInfo
	WUpdateOpSec                    func(isNotify bool)
	WUpdatePolicyEffect             func(effectMap map[int32]bool)
}

func (W _game_Player) AddAntiCheatScore(score int32) {
	W.WAddAntiCheatScore(score)
}

func (W _game_Player) ChangeCostByTypeObjOne(m *g.TypeObj, change int32) (val int32, add int32) {
	return W.WChangeCostByTypeObjOne(m, change)
}

func (W _game_Player) ChangeMerchantState(c int32, t int32, count int32) {
	W.WChangeMerchantState(c, t, count)
}

func (W _game_Player) CheckAndDeductCostByTypeObjOne(to *g.TypeObj) (b bool) {
	return W.WCheckAndDeductCostByTypeObjOne(to)
}

func (W _game_Player) GetOccupyLandCount(lv int32) int32 {
	return W.WGetOccupyLandCount(lv)
}

func (W _game_Player) GetTempAreaInfo(index int32, land int32) map[string]interface{} {
	return W.WGetTempAreaInfo(index, land)
}

func (W _game_Player) GetUID() string {
	return W.WGetUID()
}

func (W _game_Player) IsOnline() bool {
	return W.WIsOnline()
}

func (W _game_Player) Kick(tpe int) {
	W.WKick(tpe)
}

func (W _game_Player) NotifyHasTreasure(val bool) {
	W.WNotifyHasTreasure(val)
}

func (W _game_Player) PutNotifyQueue(nType int, data *pb.OnUpdatePlayerInfoNotify) {
	W.WPutNotifyQueue(nType, data)
}

func (W _game_Player) SessionSendNR(topic string, body []byte) {
	W.WSessionSendNR(topic, body)
}

func (W _game_Player) SetAlliUid(uid string) {
	W.WSetAlliUid(uid)
}

func (W _game_Player) ToItemByTypeObjsPb(tos []*g.TypeObj) *pb.UpdateOutPut {
	return W.WToItemByTypeObjsPb(tos)
}

func (W _game_Player) ToMerchants() []map[string]interface{} {
	return W.WToMerchants()
}

func (W _game_Player) ToMerchantsPb() []*pb.MerchantInfo {
	return W.WToMerchantsPb()
}

func (W _game_Player) UpdateOpSec(isNotify bool) {
	W.WUpdateOpSec(isNotify)
}

func (W _game_Player) UpdatePolicyEffect(effectMap map[int32]bool) {
	W.WUpdatePolicyEffect(effectMap)
}

// _game_Record is an interface wrapper for Record type
type _game_Record struct {
	IValue                                  interface{}
	WAddArmyBattleRecords                   func(battleUid string, armyIdnex int32, datas []map[string]interface{})
	WAddArmyMarchRecord                     func(tp int32, owner string, armyUid string, armyName string, armyIndex int32, targetIndex int32)
	WAddBattleRecord                        func(uid string, index int32, beginTime int64, endTime int64, datas []map[string]interface{})
	WAddBattleScoreRecords                  func(recordUid string, startTime int64, endTime int64, alliUidMap map[string]string, deadInfoMap map[string][]*g.PawmDeadRecord, validUserInfoMap map[string]map[int32]int32, invalidUserInfoMap map[string]map[int32]int32, scoreMap map[string]float64, playerHasArmyMap map[string]bool)
	WAddBazaarRecord                        func(tp int8, owner string, target string, res map[string]interface{}, names ...string)
	WAddPlayerBattleScoreRecord             func(data *g.BattleScoreRecordData)
	WBattleScoreRecordToPb                  func(record g.BattleScoreRecordData) *pb.BattleScoreRecordByDate
	WGetAlliPlayersBattleScoreRecordsByDate func(alliUid string, date string) []*pb.BattleScoreRecordByDate
	WGetArmyRecords                         func(uid string, isBattle bool) []*pb.ArmyRecordInfo
	WGetBattleRecord                        func(uid string) *pb.BattleRecordInfo
	WGetBattleScoreRecordsByDate            func(uid string, date string, alli string) (*g.BattleScoreRecordData, bool)
	WGetBazaarRecords                       func(uid string) []*pb.BazaarRecordInfo
	WGetPlayerBattleScoreRecords            func(uid string, date string, alli string) (*g.BattleScoreRecordData, bool)
	WGetPlayerBattleScoreRecordsByDate      func(uid string, date string) *g.BattleScoreRecordData
	WRemoveAllRecordByUID                   func(uid string)
}

func (W _game_Record) AddArmyBattleRecords(battleUid string, armyIdnex int32, datas []map[string]interface{}) {
	W.WAddArmyBattleRecords(battleUid, armyIdnex, datas)
}

func (W _game_Record) AddArmyMarchRecord(tp int32, owner string, armyUid string, armyName string, armyIndex int32, targetIndex int32) {
	W.WAddArmyMarchRecord(tp, owner, armyUid, armyName, armyIndex, targetIndex)
}

func (W _game_Record) AddBattleRecord(uid string, index int32, beginTime int64, endTime int64, datas []map[string]interface{}) {
	W.WAddBattleRecord(uid, index, beginTime, endTime, datas)
}

func (W _game_Record) AddBattleScoreRecords(recordUid string, startTime int64, endTime int64, alliUidMap map[string]string, deadInfoMap map[string][]*g.PawmDeadRecord, validUserInfoMap map[string]map[int32]int32, invalidUserInfoMap map[string]map[int32]int32, scoreMap map[string]float64, playerHasArmyMap map[string]bool) {
	W.WAddBattleScoreRecords(recordUid, startTime, endTime, alliUidMap, deadInfoMap, validUserInfoMap, invalidUserInfoMap, scoreMap, playerHasArmyMap)
}

func (W _game_Record) AddBazaarRecord(tp int8, owner string, target string, res map[string]interface{}, names ...string) {
	W.WAddBazaarRecord(tp, owner, target, res, names...)
}

func (W _game_Record) AddPlayerBattleScoreRecord(data *g.BattleScoreRecordData) {
	W.WAddPlayerBattleScoreRecord(data)
}

func (W _game_Record) BattleScoreRecordToPb(record g.BattleScoreRecordData) *pb.BattleScoreRecordByDate {
	return W.WBattleScoreRecordToPb(record)
}

func (W _game_Record) GetAlliPlayersBattleScoreRecordsByDate(alliUid string, date string) []*pb.BattleScoreRecordByDate {
	return W.WGetAlliPlayersBattleScoreRecordsByDate(alliUid, date)
}

func (W _game_Record) GetArmyRecords(uid string, isBattle bool) []*pb.ArmyRecordInfo {
	return W.WGetArmyRecords(uid, isBattle)
}

func (W _game_Record) GetBattleRecord(uid string) *pb.BattleRecordInfo {
	return W.WGetBattleRecord(uid)
}

func (W _game_Record) GetBattleScoreRecordsByDate(uid string, date string, alli string) (*g.BattleScoreRecordData, bool) {
	return W.WGetBattleScoreRecordsByDate(uid, date, alli)
}

func (W _game_Record) GetBazaarRecords(uid string) []*pb.BazaarRecordInfo {
	return W.WGetBazaarRecords(uid)
}

func (W _game_Record) GetPlayerBattleScoreRecords(uid string, date string, alli string) (*g.BattleScoreRecordData, bool) {
	return W.WGetPlayerBattleScoreRecords(uid, date, alli)
}

func (W _game_Record) GetPlayerBattleScoreRecordsByDate(uid string, date string) *g.BattleScoreRecordData {
	return W.WGetPlayerBattleScoreRecordsByDate(uid, date)
}

func (W _game_Record) RemoveAllRecordByUID(uid string) {
	W.WRemoveAllRecordByUID(uid)
}

// _game_Room is an interface wrapper for Room type
type _game_Room struct {
	IValue                      interface{}
	WAddMainCityIndex           func(index int32)
	WCheckRemoveOfflinePlayer   func(plr g.Player)
	WDeletePlayer               func(uid string)
	WGameOver                   func(winType int32, winUid string, winName string, winCellCount int32, winners [][]string, ancientInfo []int32)
	WGetBazaar                  func() g.Bazaar
	WGetCreateTime              func() int64
	WGetGameWiner               func() (winType int32, winUID string)
	WGetMapId                   func() int32
	WGetMapSize                 func() *ut.Vec2
	WGetOnlinePlayerOrDB        func(uid string) g.Player
	WGetPlayer                  func(uid string) g.Player
	WGetRecord                  func() g.Record
	WGetRunDay                  func() int32
	WGetRunTime                 func() int32
	WGetSID                     func() int32
	WGetSeason                  func() g.Season
	WGetStaminas                func() []int32
	WGetSubType                 func() uint8
	WGetType                    func() uint8
	WGetWinCond                 func() []int32
	WGetWorld                   func() g.World
	WHasPlayer                  func(uid string) bool
	WInvoke                     func(moduleType string, _func string, params ...interface{}) (result interface{}, err string)
	WInvokeLobbyRpc             func(id string, _func string, params ...interface{}) (result interface{}, err string)
	WInvokeLobbyRpcNR           func(id string, _func string, params ...interface{})
	WInvokeNR                   func(moduleType string, _func string, params ...interface{})
	WIsGameOver                 func() bool
	WIsPlayerOnline             func(uid string) bool
	WNotifyAll                  func(topic string, msg *pb.GAME_ONNOTICE_NOTIFY, ignores ...string)
	WNotifyAllByBytes           func(topic string, body []byte, ignores ...string)
	WNotifyAllPlayerUpdateOpSec func()
	WPutNotifyAllPlayersQueue   func(topic string, msg []byte, uids []string)
	WPutNotifyPlayerHasTreasure func(uid string)
	WPutNotifyQueueAllPlayer    func(t int, data *pb.OnUpdatePlayerInfoNotify, ignores ...string)
	WPutPlayerNotifyQueue       func(nType int, uid string, msg *pb.OnUpdatePlayerInfoNotify)
	WPutPlayerUpdateOpSec       func(uid string)
	WRecordDeletePlayer         func(uid string, index int32, cellCount int32, offlineTime int64)
	WSendMailItemOne            func(contentId int, title string, content string, sender string, receiver string, items []*g.TypeObj)
	WSendMailOne                func(contentId int, title string, content string, sender string, receiver string)
	WSetCreateTime              func(val int64)
	WSetWinCond                 func(val []int32)
	WUpdatePlayerDB             func(plr g.Player)
}

func (W _game_Room) AddMainCityIndex(index int32) {
	W.WAddMainCityIndex(index)
}

func (W _game_Room) CheckRemoveOfflinePlayer(plr g.Player) {
	W.WCheckRemoveOfflinePlayer(plr)
}

func (W _game_Room) DeletePlayer(uid string) {
	W.WDeletePlayer(uid)
}

func (W _game_Room) GameOver(winType int32, winUid string, winName string, winCellCount int32, winners [][]string, ancientInfo []int32) {
	W.WGameOver(winType, winUid, winName, winCellCount, winners, ancientInfo)
}

func (W _game_Room) GetBazaar() g.Bazaar {
	return W.WGetBazaar()
}

func (W _game_Room) GetCreateTime() int64 {
	return W.WGetCreateTime()
}

func (W _game_Room) GetGameWiner() (winType int32, winUID string) {
	return W.WGetGameWiner()
}

func (W _game_Room) GetMapId() int32 {
	return W.WGetMapId()
}

func (W _game_Room) GetMapSize() *ut.Vec2 {
	return W.WGetMapSize()
}

func (W _game_Room) GetOnlinePlayerOrDB(uid string) g.Player {
	return W.WGetOnlinePlayerOrDB(uid)
}

func (W _game_Room) GetPlayer(uid string) g.Player {
	return W.WGetPlayer(uid)
}

func (W _game_Room) GetRecord() g.Record {
	return W.WGetRecord()
}

func (W _game_Room) GetRunDay() int32 {
	return W.WGetRunDay()
}

func (W _game_Room) GetRunTime() int32 {
	return W.WGetRunTime()
}

func (W _game_Room) GetSID() int32 {
	return W.WGetSID()
}

func (W _game_Room) GetSeason() g.Season {
	return W.WGetSeason()
}

func (W _game_Room) GetStaminas() []int32 {
	return W.WGetStaminas()
}

func (W _game_Room) GetSubType() uint8 {
	return W.WGetSubType()
}

func (W _game_Room) GetType() uint8 {
	return W.WGetType()
}

func (W _game_Room) GetWinCond() []int32 {
	return W.WGetWinCond()
}

func (W _game_Room) GetWorld() g.World {
	return W.WGetWorld()
}

func (W _game_Room) HasPlayer(uid string) bool {
	return W.WHasPlayer(uid)
}

func (W _game_Room) Invoke(moduleType string, _func string, params ...interface{}) (result interface{}, err string) {
	return W.WInvoke(moduleType, _func, params...)
}

func (W _game_Room) InvokeLobbyRpc(id string, _func string, params ...interface{}) (result interface{}, err string) {
	return W.WInvokeLobbyRpc(id, _func, params...)
}

func (W _game_Room) InvokeLobbyRpcNR(id string, _func string, params ...interface{}) {
	W.WInvokeLobbyRpcNR(id, _func, params...)
}

func (W _game_Room) InvokeNR(moduleType string, _func string, params ...interface{}) {
	W.WInvokeNR(moduleType, _func, params...)
}

func (W _game_Room) IsGameOver() bool {
	return W.WIsGameOver()
}

func (W _game_Room) IsPlayerOnline(uid string) bool {
	return W.WIsPlayerOnline(uid)
}

func (W _game_Room) NotifyAll(topic string, msg *pb.GAME_ONNOTICE_NOTIFY, ignores ...string) {
	W.WNotifyAll(topic, msg, ignores...)
}

func (W _game_Room) NotifyAllByBytes(topic string, body []byte, ignores ...string) {
	W.WNotifyAllByBytes(topic, body, ignores...)
}

func (W _game_Room) NotifyAllPlayerUpdateOpSec() {
	W.WNotifyAllPlayerUpdateOpSec()
}

func (W _game_Room) PutNotifyAllPlayersQueue(topic string, msg []byte, uids []string) {
	W.WPutNotifyAllPlayersQueue(topic, msg, uids)
}

func (W _game_Room) PutNotifyPlayerHasTreasure(uid string) {
	W.WPutNotifyPlayerHasTreasure(uid)
}

func (W _game_Room) PutNotifyQueueAllPlayer(t int, data *pb.OnUpdatePlayerInfoNotify, ignores ...string) {
	W.WPutNotifyQueueAllPlayer(t, data, ignores...)
}

func (W _game_Room) PutPlayerNotifyQueue(nType int, uid string, msg *pb.OnUpdatePlayerInfoNotify) {
	W.WPutPlayerNotifyQueue(nType, uid, msg)
}

func (W _game_Room) PutPlayerUpdateOpSec(uid string) {
	W.WPutPlayerUpdateOpSec(uid)
}

func (W _game_Room) RecordDeletePlayer(uid string, index int32, cellCount int32, offlineTime int64) {
	W.WRecordDeletePlayer(uid, index, cellCount, offlineTime)
}

func (W _game_Room) SendMailItemOne(contentId int, title string, content string, sender string, receiver string, items []*g.TypeObj) {
	W.WSendMailItemOne(contentId, title, content, sender, receiver, items)
}

func (W _game_Room) SendMailOne(contentId int, title string, content string, sender string, receiver string) {
	W.WSendMailOne(contentId, title, content, sender, receiver)
}

func (W _game_Room) SetCreateTime(val int64) {
	W.WSetCreateTime(val)
}

func (W _game_Room) SetWinCond(val []int32) {
	W.WSetWinCond(val)
}

func (W _game_Room) UpdatePlayerDB(plr g.Player) {
	W.WUpdatePlayerDB(plr)
}

// _game_Season is an interface wrapper for Season type
type _game_Season struct {
	IValue             interface{}
	WChangeBaseResCost func(list []*g.TypeObj, tp int) []*g.TypeObj
	WGetEffectFloat    func(tp int) float64
	WGetType           func() int
	WToPb              func() *pb.SeasonInfo
}

func (W _game_Season) ChangeBaseResCost(list []*g.TypeObj, tp int) []*g.TypeObj {
	return W.WChangeBaseResCost(list, tp)
}

func (W _game_Season) GetEffectFloat(tp int) float64 {
	return W.WGetEffectFloat(tp)
}

func (W _game_Season) GetType() int {
	return W.WGetType()
}

func (W _game_Season) ToPb() *pb.SeasonInfo {
	return W.WToPb()
}

// _game_World is an interface wrapper for World type
type _game_World struct {
	IValue                           interface{}
	WAddBattlePassScore              func(uid string, score int32)
	WAddCheckPlayerOfflineMsg        func(uid string, msgType int32, endTime int64, params ...string)
	WAddPlayerRodeleroCadet          func(uid string, val int32, maxLv int32)
	WAncientAvoidWarCheck            func()
	WAreaBattleEnd                   func(index int32, attacker string, fighters []string, atkReinforceCount int32, defReinforceCount int32, datas []map[string]interface{}, battleInfo map[string]map[int32]int32, battlePlayerUids []string, treasuresMap map[string][]*g.TreasureInfo, deadInfoMap map[string][]*g.PawmDeadRecord, alliUidMap map[string]string, lostTressureMap map[string]map[int32]int32)
	WAreaBuildBTComplete             func(index int32, uid string, lv int32) g.Build
	WChangeArmyDistIndex             func(owner string, uid string, index int32)
	WChangeCheckPlayerOfflineMsgTime func(uid string, msgType int32, add int32, params ...string)
	WChangePlayerStaminaAndAddScore  func(uid string, landLv int32, uiLv int32, val int32) int32
	WCheckArmyMarchingByUID          func(uid string) bool
	WCheckIsOneAlliance              func(a string, b string) bool
	WCheckPlayerHasMaxAttrEquip      func(uid string) bool
	WCheckPlayerHasTreasure          func(uid string) bool
	WCleanGeneralAvoidWarArea        func()
	WCompatiBuildLv0                 func(index int32, uids []string)
	WCreateAncientCities             func()
	WDelPlayerAvoidWar               func(uid string)
	WDirectOccupyCell                func(index int32, uid string, isAddScore bool)
	WFindPlayerMainIndex             func(uid string) int32
	WForgePlayerEquip                func(uid string, id int32, lockEffect int32) *g.EquipInfo
	WGetAllianceNameByUID            func(uid string) string
	WGetAncientEffectFloatByPlayer   func(uid string, tp int32) float64
	WGetAncientHighestLv             func() int32
	WGetAreaAllBuildEffect           func(index int32) map[int32]*g.EffectObj
	WGetAreaBuildEffect              func(index int32, uid string) map[int32]*g.EffectObj
	WGetAreaBuildsByID               func(index int32, id int32) []g.Build
	WGetFortAutoSupports             func(uid string) map[int32]bool
	WGetHasCreateAncient             func() bool
	WGetLandLv                       func(index int32) int32
	WGetLandType                     func(index int32) int32
	WGetMaxPawnCountInArmy           func(uid string) int32
	WGetPlayerAccTotalPawnCount      func(uid string) int32
	WGetPlayerAlliUid                func(uid string) string
	WGetPlayerArmyDistArrayPb        func(uid string, withPawnsInfo bool) []*pb.AreaDistInfo
	WGetPlayerArmyHero               func(uid string, armyUid string) *g.PortrayalInfo
	WGetPlayerAvoidWarReduce         func(uid string) int32
	WGetPlayerBuildLvById            func(uid string, id int32) int32
	WGetPlayerBuildsSumLvById        func(uid string, id int32) int32
	WGetPlayerCellOutput             func(uid string) (cereal float64, timber float64, stone float64)
	WGetPlayerCerealConsume          func(uid string) float64
	WGetPlayerDataToDB               func(uid string) (string, string, string, int32)
	WGetPlayerDistinctId             func(uid string) string
	WGetPlayerEquips                 func(uid string) []*g.EquipInfo
	WGetPlayerFCMToken               func(uid string) string
	WGetPlayerHasResBuild            func(uid string) bool
	WGetPlayerHeroSlots              func(uid string) []*g.HeroSlotInfo
	WGetPlayerIsGiveupGame           func(uid string) bool
	WGetPlayerLandCount              func(uid string, notHasMainCityCount bool) int32
	WGetPlayerLandCountByLv          func(uid string, lv int32) int32
	WGetPlayerLandCountByType        func(uid string, tp int32) int32
	WGetPlayerLandScoreAndAlliScore  func(uid string) (int32, int32, int32, int32)
	WGetPlayerLanguage               func(uid string) string
	WGetPlayerMainBuildLv            func(uid string) int32
	WGetPlayerMainBuildsPb           func(index int32) []*pb.AreaBuildInfo
	WGetPlayerMainIndex              func(uid string) int32
	WGetPlayerMaxLandCount           func(uid string) int32
	WGetPlayerMaxLvPawnCount         func(uid string) int32
	WGetPlayerNickname               func(uid string) string
	WGetPlayerNoAvoidWar             func(uid string) bool
	WGetPlayerOfflineOpt             func(uid string) []int32
	WGetPlayerOwnCells               func(uid string) []int32
	WGetPlayerOwnedCityCountByID     func(uid string, id int32) int32
	WGetPlayerPawnTrackInfo          func(uid string) []map[string]interface{}
	WGetPlayerPersonalDesc           func(uid string) string
	WGetPlayerPolicyEffectFloatByUid func(uid string, tp int32) float64
	WGetPlayerPolicyEffectIntByUid   func(uid string, tp int32) int32
	WGetPlayerRodeleroCadetLv        func(uid string) int32
	WGetPlayerTitle                  func(uid string) int32
	WGetPlayerTotalEquipRecastCount  func(uid string) int32
	WGetPlayerTowerLvByPawn          func(owner string, pawnId int32) int32
	WGetPlayerTreasureAwardMul       func(uid string) float64
	WGetPlayerTreasureLostCount      func(uid string) int32
	WGetSeason                       func() g.Season
	WGetServerRunDay                 func() int32
	WGetToMapCellDis                 func(sindex int32, tindex int32) int32
	WGetWorldPbCells                 func() map[string]*pb.PlayerCellBytesInfo
	WGetWorldPbCellsByUserId         func(uid string) *pb.PlayerCellBytesInfo
	WHasPlayer                       func(uid string) bool
	WNewTempPawn                     func(index int32, uid string, point *ut.Vec2, owner string, armyUid string, id int32, lv int32) g.Pawn
	WNotifyAllAlliBaseInfo           func()
	WNotifyAreaCheckFrame            func(index int32, msg *pb.GAME_ONFSPCHECKFRAME_NOTIFY)
	WNotifyAreaUpdateInfo            func(index int32, ntype int32, data *pb.GAME_ONUPDATEAREAINFO_NOTIFY, ignores ...string)
	WNotifyPlayerArmyDistInfo        func(uid string)
	WOfflineNotify                   func(uid string, msgType int32, params ...string)
	WPutNotifyQueue                  func(t int32, msg *pb.OnUpdateWorldInfoNotify)
	WRecordPlayerDistinctId          func(uid string, distinctId string)
	WRecordPlayerKillCount           func(uid string, id int32, count int32)
	WRemoveAvoidWarArea              func(index int32)
	WRemoveCheckPlayerOfflineMsg     func(uid string, msgType int32, params ...string)
	WRemovePawnByDeserter            func(uid string, count int)
	WRemovePawnLvingPawn             func(index int32, puid string)
	WSendPlayerHasTreasure           func(uid string)
	WSetAncientLv                    func(id int32, lv int32)
	WSetBuildLv                      func(index int32, id int32, lv int32) g.Build
	WSetPlayerFCMToken               func(uid string, token string)
	WSetPlayerLanguage               func(uid string, lang string)
	WSetPlayerNoAvoidWar             func(uid string, noAvoidWar bool)
	WSetPlayerOfflineOpt             func(uid string, opt []int32)
	WSmeltPlayerEquip                func(uid string, mainId int32, viceId int32, mainAttrs [][]int32, viceAttrs [][]int32) *g.EquipInfo
	WTaTrack                         func(uid string, reCreateCount int32, eventName string, properties map[string]interface{})
	WTaUserSet                       func(uid string, reCreateCount int32, properties map[string]interface{})
	WTagBattleSettleFinish           func()
	WTagBattleSettling               func()
	WTagUpdateDBByIndex              func(index int32)
	WToAreaBuildShortInfo            func(index int32, uid string) map[string]interface{}
	WToDrillPawnQueuePb              func(index int32) map[string]*pb.DrillPawnInfoList
	WToHttpTempPlayerStrip           func(uid string) map[string]interface{}
	WToPawnLvingQueuePb              func(index int32) []*pb.PawnLvingInfo
	WToPb                            func(isReconnect bool) *pb.WorldInfo
	WToPlayerBattleRecordInfo        func(uid string) map[int32]int32
	WToPlayerCityOutputDB            func(uid string) map[int32][]*g.TypeObj
	WToPlayerConfigPawnMap           func(uid string) map[int32]*g.PawnConfigInfo
	WToPlayerConfigPawnMapPb         func(uid string) map[int32]*pb.PawnConfigInfo
	WToPlayerEquipUseMap             func(uid string) map[int32]bool
	WToPlayerKillRecordMap           func(uid string) map[int32]int32
	WToPlayerPawnDrillMap            func(uid string) map[int32]int32
	WToPlayerPolicyUseMap            func(uid string) map[int32]bool
	WToPolicyMap                     func(uid string) map[int32]*g.PolicyInfo
	WToPolicys                       func(uid string) map[int32]int32
	WToPolicysPb                     func(uid string) map[int32]int32
	WTriggerTask                     func(uid string, condType int32, count int32, param int32)
	WUpdatePlayerAlliMemberEmbassyLv func(alliUid string, uid string, lv int32)
	WUpdatePlayerCityOutput          func(uid string, count int32, init bool)
	WUpdatePlayerHeroArmy            func(uid string, id int32, armyUid string)
	WUpdatePlayerHeroAttr            func(uid string, id int32, attrs [][]int32) string
	WUpdatePlayerHeroDie             func(uid string, armyUid string, id int32)
	WUpdatePlayerOnlineState         func(uid string, time int64)
	WUpdatePlayerOutput              func(uid string)
	WUpdatePlayerPawnEquipInfo       func(uid string, euid string, attrs [][]int32)
	WUpdatePlayerPawnHeroInfo        func(uid string, id int32, attrs [][]int32)
	WUpdateSepctatorMainIndex        func(uid string, mainIndex int32)
	WWorldPbRLock                    func()
	WWorldPbRUnLock                  func()
}

func (W _game_World) AddBattlePassScore(uid string, score int32) {
	W.WAddBattlePassScore(uid, score)
}

func (W _game_World) AddCheckPlayerOfflineMsg(uid string, msgType int32, endTime int64, params ...string) {
	W.WAddCheckPlayerOfflineMsg(uid, msgType, endTime, params...)
}

func (W _game_World) AddPlayerRodeleroCadet(uid string, val int32, maxLv int32) {
	W.WAddPlayerRodeleroCadet(uid, val, maxLv)
}

func (W _game_World) AncientAvoidWarCheck() {
	W.WAncientAvoidWarCheck()
}

func (W _game_World) AreaBattleEnd(index int32, attacker string, fighters []string, atkReinforceCount int32, defReinforceCount int32, datas []map[string]interface{}, battleInfo map[string]map[int32]int32, battlePlayerUids []string, treasuresMap map[string][]*g.TreasureInfo, deadInfoMap map[string][]*g.PawmDeadRecord, alliUidMap map[string]string, lostTressureMap map[string]map[int32]int32) {
	W.WAreaBattleEnd(index, attacker, fighters, atkReinforceCount, defReinforceCount, datas, battleInfo, battlePlayerUids, treasuresMap, deadInfoMap, alliUidMap, lostTressureMap)
}

func (W _game_World) AreaBuildBTComplete(index int32, uid string, lv int32) g.Build {
	return W.WAreaBuildBTComplete(index, uid, lv)
}

func (W _game_World) ChangeArmyDistIndex(owner string, uid string, index int32) {
	W.WChangeArmyDistIndex(owner, uid, index)
}

func (W _game_World) ChangeCheckPlayerOfflineMsgTime(uid string, msgType int32, add int32, params ...string) {
	W.WChangeCheckPlayerOfflineMsgTime(uid, msgType, add, params...)
}

func (W _game_World) ChangePlayerStaminaAndAddScore(uid string, landLv int32, uiLv int32, val int32) int32 {
	return W.WChangePlayerStaminaAndAddScore(uid, landLv, uiLv, val)
}

func (W _game_World) CheckArmyMarchingByUID(uid string) bool {
	return W.WCheckArmyMarchingByUID(uid)
}

func (W _game_World) CheckIsOneAlliance(a string, b string) bool {
	return W.WCheckIsOneAlliance(a, b)
}

func (W _game_World) CheckPlayerHasMaxAttrEquip(uid string) bool {
	return W.WCheckPlayerHasMaxAttrEquip(uid)
}

func (W _game_World) CheckPlayerHasTreasure(uid string) bool {
	return W.WCheckPlayerHasTreasure(uid)
}

func (W _game_World) CleanGeneralAvoidWarArea() {
	W.WCleanGeneralAvoidWarArea()
}

func (W _game_World) CompatiBuildLv0(index int32, uids []string) {
	W.WCompatiBuildLv0(index, uids)
}

func (W _game_World) CreateAncientCities() {
	W.WCreateAncientCities()
}

func (W _game_World) DelPlayerAvoidWar(uid string) {
	W.WDelPlayerAvoidWar(uid)
}

func (W _game_World) DirectOccupyCell(index int32, uid string, isAddScore bool) {
	W.WDirectOccupyCell(index, uid, isAddScore)
}

func (W _game_World) FindPlayerMainIndex(uid string) int32 {
	return W.WFindPlayerMainIndex(uid)
}

func (W _game_World) ForgePlayerEquip(uid string, id int32, lockEffect int32) *g.EquipInfo {
	return W.WForgePlayerEquip(uid, id, lockEffect)
}

func (W _game_World) GetAllianceNameByUID(uid string) string {
	return W.WGetAllianceNameByUID(uid)
}

func (W _game_World) GetAncientEffectFloatByPlayer(uid string, tp int32) float64 {
	return W.WGetAncientEffectFloatByPlayer(uid, tp)
}

func (W _game_World) GetAncientHighestLv() int32 {
	return W.WGetAncientHighestLv()
}

func (W _game_World) GetAreaAllBuildEffect(index int32) map[int32]*g.EffectObj {
	return W.WGetAreaAllBuildEffect(index)
}

func (W _game_World) GetAreaBuildEffect(index int32, uid string) map[int32]*g.EffectObj {
	return W.WGetAreaBuildEffect(index, uid)
}

func (W _game_World) GetAreaBuildsByID(index int32, id int32) []g.Build {
	return W.WGetAreaBuildsByID(index, id)
}

func (W _game_World) GetFortAutoSupports(uid string) map[int32]bool {
	return W.WGetFortAutoSupports(uid)
}

func (W _game_World) GetHasCreateAncient() bool {
	return W.WGetHasCreateAncient()
}

func (W _game_World) GetLandLv(index int32) int32 {
	return W.WGetLandLv(index)
}

func (W _game_World) GetLandType(index int32) int32 {
	return W.WGetLandType(index)
}

func (W _game_World) GetMaxPawnCountInArmy(uid string) int32 {
	return W.WGetMaxPawnCountInArmy(uid)
}

func (W _game_World) GetPlayerAccTotalPawnCount(uid string) int32 {
	return W.WGetPlayerAccTotalPawnCount(uid)
}

func (W _game_World) GetPlayerAlliUid(uid string) string {
	return W.WGetPlayerAlliUid(uid)
}

func (W _game_World) GetPlayerArmyDistArrayPb(uid string, withPawnsInfo bool) []*pb.AreaDistInfo {
	return W.WGetPlayerArmyDistArrayPb(uid, withPawnsInfo)
}

func (W _game_World) GetPlayerArmyHero(uid string, armyUid string) *g.PortrayalInfo {
	return W.WGetPlayerArmyHero(uid, armyUid)
}

func (W _game_World) GetPlayerAvoidWarReduce(uid string) int32 {
	return W.WGetPlayerAvoidWarReduce(uid)
}

func (W _game_World) GetPlayerBuildLvById(uid string, id int32) int32 {
	return W.WGetPlayerBuildLvById(uid, id)
}

func (W _game_World) GetPlayerBuildsSumLvById(uid string, id int32) int32 {
	return W.WGetPlayerBuildsSumLvById(uid, id)
}

func (W _game_World) GetPlayerCellOutput(uid string) (cereal float64, timber float64, stone float64) {
	return W.WGetPlayerCellOutput(uid)
}

func (W _game_World) GetPlayerCerealConsume(uid string) float64 {
	return W.WGetPlayerCerealConsume(uid)
}

func (W _game_World) GetPlayerDataToDB(uid string) (string, string, string, int32) {
	return W.WGetPlayerDataToDB(uid)
}

func (W _game_World) GetPlayerDistinctId(uid string) string {
	return W.WGetPlayerDistinctId(uid)
}

func (W _game_World) GetPlayerEquips(uid string) []*g.EquipInfo {
	return W.WGetPlayerEquips(uid)
}

func (W _game_World) GetPlayerFCMToken(uid string) string {
	return W.WGetPlayerFCMToken(uid)
}

func (W _game_World) GetPlayerHasResBuild(uid string) bool {
	return W.WGetPlayerHasResBuild(uid)
}

func (W _game_World) GetPlayerHeroSlots(uid string) []*g.HeroSlotInfo {
	return W.WGetPlayerHeroSlots(uid)
}

func (W _game_World) GetPlayerIsGiveupGame(uid string) bool {
	return W.WGetPlayerIsGiveupGame(uid)
}

func (W _game_World) GetPlayerLandCount(uid string, notHasMainCityCount bool) int32 {
	return W.WGetPlayerLandCount(uid, notHasMainCityCount)
}

func (W _game_World) GetPlayerLandCountByLv(uid string, lv int32) int32 {
	return W.WGetPlayerLandCountByLv(uid, lv)
}

func (W _game_World) GetPlayerLandCountByType(uid string, tp int32) int32 {
	return W.WGetPlayerLandCountByType(uid, tp)
}

func (W _game_World) GetPlayerLandScoreAndAlliScore(uid string) (int32, int32, int32, int32) {
	return W.WGetPlayerLandScoreAndAlliScore(uid)
}

func (W _game_World) GetPlayerLanguage(uid string) string {
	return W.WGetPlayerLanguage(uid)
}

func (W _game_World) GetPlayerMainBuildLv(uid string) int32 {
	return W.WGetPlayerMainBuildLv(uid)
}

func (W _game_World) GetPlayerMainBuildsPb(index int32) []*pb.AreaBuildInfo {
	return W.WGetPlayerMainBuildsPb(index)
}

func (W _game_World) GetPlayerMainIndex(uid string) int32 {
	return W.WGetPlayerMainIndex(uid)
}

func (W _game_World) GetPlayerMaxLandCount(uid string) int32 {
	return W.WGetPlayerMaxLandCount(uid)
}

func (W _game_World) GetPlayerMaxLvPawnCount(uid string) int32 {
	return W.WGetPlayerMaxLvPawnCount(uid)
}

func (W _game_World) GetPlayerNickname(uid string) string {
	return W.WGetPlayerNickname(uid)
}

func (W _game_World) GetPlayerNoAvoidWar(uid string) bool {
	return W.WGetPlayerNoAvoidWar(uid)
}

func (W _game_World) GetPlayerOfflineOpt(uid string) []int32 {
	return W.WGetPlayerOfflineOpt(uid)
}

func (W _game_World) GetPlayerOwnCells(uid string) []int32 {
	return W.WGetPlayerOwnCells(uid)
}

func (W _game_World) GetPlayerOwnedCityCountByID(uid string, id int32) int32 {
	return W.WGetPlayerOwnedCityCountByID(uid, id)
}

func (W _game_World) GetPlayerPawnTrackInfo(uid string) []map[string]interface{} {
	return W.WGetPlayerPawnTrackInfo(uid)
}

func (W _game_World) GetPlayerPersonalDesc(uid string) string {
	return W.WGetPlayerPersonalDesc(uid)
}

func (W _game_World) GetPlayerPolicyEffectFloatByUid(uid string, tp int32) float64 {
	return W.WGetPlayerPolicyEffectFloatByUid(uid, tp)
}

func (W _game_World) GetPlayerPolicyEffectIntByUid(uid string, tp int32) int32 {
	return W.WGetPlayerPolicyEffectIntByUid(uid, tp)
}

func (W _game_World) GetPlayerRodeleroCadetLv(uid string) int32 {
	return W.WGetPlayerRodeleroCadetLv(uid)
}

func (W _game_World) GetPlayerTitle(uid string) int32 {
	return W.WGetPlayerTitle(uid)
}

func (W _game_World) GetPlayerTotalEquipRecastCount(uid string) int32 {
	return W.WGetPlayerTotalEquipRecastCount(uid)
}

func (W _game_World) GetPlayerTowerLvByPawn(owner string, pawnId int32) int32 {
	return W.WGetPlayerTowerLvByPawn(owner, pawnId)
}

func (W _game_World) GetPlayerTreasureAwardMul(uid string) float64 {
	return W.WGetPlayerTreasureAwardMul(uid)
}

func (W _game_World) GetPlayerTreasureLostCount(uid string) int32 {
	return W.WGetPlayerTreasureLostCount(uid)
}

func (W _game_World) GetSeason() g.Season {
	return W.WGetSeason()
}

func (W _game_World) GetServerRunDay() int32 {
	return W.WGetServerRunDay()
}

func (W _game_World) GetToMapCellDis(sindex int32, tindex int32) int32 {
	return W.WGetToMapCellDis(sindex, tindex)
}

func (W _game_World) GetWorldPbCells() map[string]*pb.PlayerCellBytesInfo {
	return W.WGetWorldPbCells()
}

func (W _game_World) GetWorldPbCellsByUserId(uid string) *pb.PlayerCellBytesInfo {
	return W.WGetWorldPbCellsByUserId(uid)
}

func (W _game_World) HasPlayer(uid string) bool {
	return W.WHasPlayer(uid)
}

func (W _game_World) NewTempPawn(index int32, uid string, point *ut.Vec2, owner string, armyUid string, id int32, lv int32) g.Pawn {
	return W.WNewTempPawn(index, uid, point, owner, armyUid, id, lv)
}

func (W _game_World) NotifyAllAlliBaseInfo() {
	W.WNotifyAllAlliBaseInfo()
}

func (W _game_World) NotifyAreaCheckFrame(index int32, msg *pb.GAME_ONFSPCHECKFRAME_NOTIFY) {
	W.WNotifyAreaCheckFrame(index, msg)
}

func (W _game_World) NotifyAreaUpdateInfo(index int32, ntype int32, data *pb.GAME_ONUPDATEAREAINFO_NOTIFY, ignores ...string) {
	W.WNotifyAreaUpdateInfo(index, ntype, data, ignores...)
}

func (W _game_World) NotifyPlayerArmyDistInfo(uid string) {
	W.WNotifyPlayerArmyDistInfo(uid)
}

func (W _game_World) OfflineNotify(uid string, msgType int32, params ...string) {
	W.WOfflineNotify(uid, msgType, params...)
}

func (W _game_World) PutNotifyQueue(t int32, msg *pb.OnUpdateWorldInfoNotify) {
	W.WPutNotifyQueue(t, msg)
}

func (W _game_World) RecordPlayerDistinctId(uid string, distinctId string) {
	W.WRecordPlayerDistinctId(uid, distinctId)
}

func (W _game_World) RecordPlayerKillCount(uid string, id int32, count int32) {
	W.WRecordPlayerKillCount(uid, id, count)
}

func (W _game_World) RemoveAvoidWarArea(index int32) {
	W.WRemoveAvoidWarArea(index)
}

func (W _game_World) RemoveCheckPlayerOfflineMsg(uid string, msgType int32, params ...string) {
	W.WRemoveCheckPlayerOfflineMsg(uid, msgType, params...)
}

func (W _game_World) RemovePawnByDeserter(uid string, count int) {
	W.WRemovePawnByDeserter(uid, count)
}

func (W _game_World) RemovePawnLvingPawn(index int32, puid string) {
	W.WRemovePawnLvingPawn(index, puid)
}

func (W _game_World) SendPlayerHasTreasure(uid string) {
	W.WSendPlayerHasTreasure(uid)
}

func (W _game_World) SetAncientLv(id int32, lv int32) {
	W.WSetAncientLv(id, lv)
}

func (W _game_World) SetBuildLv(index int32, id int32, lv int32) g.Build {
	return W.WSetBuildLv(index, id, lv)
}

func (W _game_World) SetPlayerFCMToken(uid string, token string) {
	W.WSetPlayerFCMToken(uid, token)
}

func (W _game_World) SetPlayerLanguage(uid string, lang string) {
	W.WSetPlayerLanguage(uid, lang)
}

func (W _game_World) SetPlayerNoAvoidWar(uid string, noAvoidWar bool) {
	W.WSetPlayerNoAvoidWar(uid, noAvoidWar)
}

func (W _game_World) SetPlayerOfflineOpt(uid string, opt []int32) {
	W.WSetPlayerOfflineOpt(uid, opt)
}

func (W _game_World) SmeltPlayerEquip(uid string, mainId int32, viceId int32, mainAttrs [][]int32, viceAttrs [][]int32) *g.EquipInfo {
	return W.WSmeltPlayerEquip(uid, mainId, viceId, mainAttrs, viceAttrs)
}

func (W _game_World) TaTrack(uid string, reCreateCount int32, eventName string, properties map[string]interface{}) {
	W.WTaTrack(uid, reCreateCount, eventName, properties)
}

func (W _game_World) TaUserSet(uid string, reCreateCount int32, properties map[string]interface{}) {
	W.WTaUserSet(uid, reCreateCount, properties)
}

func (W _game_World) TagBattleSettleFinish() {
	W.WTagBattleSettleFinish()
}

func (W _game_World) TagBattleSettling() {
	W.WTagBattleSettling()
}

func (W _game_World) TagUpdateDBByIndex(index int32) {
	W.WTagUpdateDBByIndex(index)
}

func (W _game_World) ToAreaBuildShortInfo(index int32, uid string) map[string]interface{} {
	return W.WToAreaBuildShortInfo(index, uid)
}

func (W _game_World) ToDrillPawnQueuePb(index int32) map[string]*pb.DrillPawnInfoList {
	return W.WToDrillPawnQueuePb(index)
}

func (W _game_World) ToHttpTempPlayerStrip(uid string) map[string]interface{} {
	return W.WToHttpTempPlayerStrip(uid)
}

func (W _game_World) ToPawnLvingQueuePb(index int32) []*pb.PawnLvingInfo {
	return W.WToPawnLvingQueuePb(index)
}

func (W _game_World) ToPb(isReconnect bool) *pb.WorldInfo {
	return W.WToPb(isReconnect)
}

func (W _game_World) ToPlayerBattleRecordInfo(uid string) map[int32]int32 {
	return W.WToPlayerBattleRecordInfo(uid)
}

func (W _game_World) ToPlayerCityOutputDB(uid string) map[int32][]*g.TypeObj {
	return W.WToPlayerCityOutputDB(uid)
}

func (W _game_World) ToPlayerConfigPawnMap(uid string) map[int32]*g.PawnConfigInfo {
	return W.WToPlayerConfigPawnMap(uid)
}

func (W _game_World) ToPlayerConfigPawnMapPb(uid string) map[int32]*pb.PawnConfigInfo {
	return W.WToPlayerConfigPawnMapPb(uid)
}

func (W _game_World) ToPlayerEquipUseMap(uid string) map[int32]bool {
	return W.WToPlayerEquipUseMap(uid)
}

func (W _game_World) ToPlayerKillRecordMap(uid string) map[int32]int32 {
	return W.WToPlayerKillRecordMap(uid)
}

func (W _game_World) ToPlayerPawnDrillMap(uid string) map[int32]int32 {
	return W.WToPlayerPawnDrillMap(uid)
}

func (W _game_World) ToPlayerPolicyUseMap(uid string) map[int32]bool {
	return W.WToPlayerPolicyUseMap(uid)
}

func (W _game_World) ToPolicyMap(uid string) map[int32]*g.PolicyInfo {
	return W.WToPolicyMap(uid)
}

func (W _game_World) ToPolicys(uid string) map[int32]int32 {
	return W.WToPolicys(uid)
}

func (W _game_World) ToPolicysPb(uid string) map[int32]int32 {
	return W.WToPolicysPb(uid)
}

func (W _game_World) TriggerTask(uid string, condType int32, count int32, param int32) {
	W.WTriggerTask(uid, condType, count, param)
}

func (W _game_World) UpdatePlayerAlliMemberEmbassyLv(alliUid string, uid string, lv int32) {
	W.WUpdatePlayerAlliMemberEmbassyLv(alliUid, uid, lv)
}

func (W _game_World) UpdatePlayerCityOutput(uid string, count int32, init bool) {
	W.WUpdatePlayerCityOutput(uid, count, init)
}

func (W _game_World) UpdatePlayerHeroArmy(uid string, id int32, armyUid string) {
	W.WUpdatePlayerHeroArmy(uid, id, armyUid)
}

func (W _game_World) UpdatePlayerHeroAttr(uid string, id int32, attrs [][]int32) string {
	return W.WUpdatePlayerHeroAttr(uid, id, attrs)
}

func (W _game_World) UpdatePlayerHeroDie(uid string, armyUid string, id int32) {
	W.WUpdatePlayerHeroDie(uid, armyUid, id)
}

func (W _game_World) UpdatePlayerOnlineState(uid string, time int64) {
	W.WUpdatePlayerOnlineState(uid, time)
}

func (W _game_World) UpdatePlayerOutput(uid string) {
	W.WUpdatePlayerOutput(uid)
}

func (W _game_World) UpdatePlayerPawnEquipInfo(uid string, euid string, attrs [][]int32) {
	W.WUpdatePlayerPawnEquipInfo(uid, euid, attrs)
}

func (W _game_World) UpdatePlayerPawnHeroInfo(uid string, id int32, attrs [][]int32) {
	W.WUpdatePlayerPawnHeroInfo(uid, id, attrs)
}

func (W _game_World) UpdateSepctatorMainIndex(uid string, mainIndex int32) {
	W.WUpdateSepctatorMainIndex(uid, mainIndex)
}

func (W _game_World) WorldPbRLock() {
	W.WWorldPbRLock()
}

func (W _game_World) WorldPbRUnLock() {
	W.WWorldPbRUnLock()
}
