package slg

const (
	ERROR   = -1
	SUCCESS = iota
)

func HttpResponseConstruct(status int, data interface{}, desc string) map[string]interface{} {
	return map[string]interface{}{
		"status": status,
		"data":   data,
		"desc":   desc,
	}
}

// HttpResponseSuccessNoDataNoDesc  http成功 但无数据 无提示返回
func HttpResponseSuccessNoDataNoDesc() map[string]interface{} {
	return HttpResponseConstruct(SUCCESS, "", "")
}

// HttpResponseSuccessNoDataWithDesc  http成功 无数据 有提示返回
func HttpResponseSuccessNoDataWithDesc(desc string) map[string]interface{} {
	return HttpResponseConstruct(SUCCESS, "", desc)
}

// HttpResponseSuccessWithDataWithDesc http成功 有数据 有提示返回
func HttpResponseSuccessWithDataWithDesc(data interface{}, desc string) map[string]interface{} {
	return HttpResponseConstruct(SUCCESS, data, desc)
}

// HttpResponseSuccessWithDataNoDesc http成功 有数据 无提示返回
func HttpResponseSuccessWithDataNoDesc(data interface{}) map[string]interface{} {
	return HttpResponseConstruct(SUCCESS, data, "")
}

// HttpResponseErrorNoDataNoDesc http失败 无数据 无提示返回
func HttpResponseErrorNoDataNoDesc() map[string]interface{} {
	return HttpResponseConstruct(ERROR, "", "")
}

// HttpResponseErrorNoDataWithDesc http失败 无数据 有提示返回
func HttpResponseErrorNoDataWithDesc(desc string) map[string]interface{} {
	return HttpResponseConstruct(ERROR, "", desc)
}

// HttpResponseErrorWithDataNoDesc http失败 有数据 无提示返回
func HttpResponseErrorWithDataNoDesc(data interface{}) map[string]interface{} {
	return HttpResponseConstruct(ERROR, data, "")
}

// HttpResponseErrorWithDataWithDesc http失败 有数据 有提示返回
func HttpResponseErrorWithDataWithDesc(data interface{}, desc string) map[string]interface{} {
	return HttpResponseConstruct(ERROR, data, desc)
}
