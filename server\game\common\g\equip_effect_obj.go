package g

import (
	"slgsrv/server/game/common/config"
	ut "slgsrv/utils"
)

// 装备效果对象
type EquipEffectObj struct {
	Value float64
	Type  int32
	Odds  int32
}

func NewEquipEffectObj(t int32, value float64, odds int32) *EquipEffectObj {
	if json := config.GetJsonData("equipEffect", t); json != nil {
		if valueStr := ut.String(json["value"]); valueStr != "" {
			if arr := ut.StringToInt32s(valueStr, ","); len(arr) >= 2 {
				value = float64(ut.ClampInt32(int32(value), arr[0], arr[1]))
			}
		}
		if oddsStr := ut.String(json["odds"]); oddsStr != "" {
			if arr := ut.StringToInt32s(oddsStr, ","); len(arr) >= 2 {
				odds = ut.ClampInt32(odds, arr[0], arr[1])
			}
		}
	}
	return &EquipEffectObj{
		Type:  t,
		Value: value,
		Odds:  odds,
	}
}

func (this *EquipEffectObj) String() string {
	return "(" + ut.Itoa(this.Type) + "," + ut.Itoa(this.Value) + "," + ut.Itoa(this.Odds) + ")"
}

func (this *EquipEffectObj) Clone() *EquipEffectObj {
	return NewEquipEffectObj(this.Type, this.Value, this.Odds)
}
