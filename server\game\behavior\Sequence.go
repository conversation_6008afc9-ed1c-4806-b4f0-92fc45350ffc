package behavior

import ut "slgsrv/utils"

// 序列执行，如果一个节点执行失败就返回
type Sequence struct {
	BaseComposite
}

func (this *Sequence) OnOpen() {
	this.SetBlackboardData("startIndex", 0)
}

func (this *Sequence) OnTick(dt int32) Status {
	startIndex := ut.Int(this.GetBlackboardData("startIndex"))
	for i, l := startIndex, this.GetChildrenCount(); i < l; i++ {
		state := this.children[i].Execute(dt)
		if state == SUCCESS {
			continue
		} else if state == RUNNING {
			this.SetBlackboardData("startIndex", i) // 这里记录运行中的 下次继续从这里执行
		}
		return state
	}
	return SUCCESS
}
