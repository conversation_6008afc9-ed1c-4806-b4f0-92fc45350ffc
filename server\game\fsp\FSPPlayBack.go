package fsp

import (
	"time"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
)

type FSPPlayBack struct {
	FSPModel
	room          g.Room
	UserId        string // 调起回放的玩家uid
	FrameMap      map[int32]*pb.FrameInfo
	FramePanwsMap map[int32][]g.Pawn
}

func NewFSPPlayBack() *FSPPlayBack {
	return &FSPPlayBack{
		FSPModel: *NewFSPModel(),
	}
}

func (this *FSPPlayBack) Init(area Area, data FSPParam, userId string, room g.Room, frameMap map[int32]*pb.FrameInfo, framePanwsMap map[int32][]g.Pawn, towerId, towerLv int32, randSeed int, fightersCampMap map[string]int32) *FSPPlayBack {
	this.UserId = userId
	this.room = room
	this.FrameMap = frameMap
	this.FramePanwsMap = framePanwsMap
	this.mul = ut.MaxInt32(1, data.FspMul)
	this.currentFrameIndex = 0
	this.attacker = data.Attacker
	uids := map[string]bool{}
	for _, p := range data.Pawns {
		owner := p.GetOwner()
		if !uids[owner] {
			uids[owner] = true
			this.battlePlayerUids = append(this.battlePlayerUids, owner)
		}
	}
	// 初始化战斗逻辑控制
	this.battleController = new(FSPBattleController).Init(area, data)
	this.battleController.SetPlayBackFighters(towerId, towerLv, randSeed, fightersCampMap)
	this.battleController.SetPlayBackFspModel(this)
	frame := this.battleController.GetInitData()
	frame.Fps = this.fps
	frame.Owner = area.GetOwner()
	frame.CityId = area.GetCityID()
	frame.Hp = []int32{area.GetCurHP(), area.GetMaxHP()}
	frame.Builds = area.ToBuildsStrip()
	frame.Armys = area.ToArmysStrip2()
	this.recordDatas = []*g.BattleRecordData{frame}
	this.battleController.Run()
	return this
}

func (this *FSPPlayBack) Run() {
	this.isRunning = true
	go func() {
		tiker := time.NewTicker(time.Millisecond * time.Duration(this.dt))
		defer tiker.Stop()
		for this.isRunning {
			<-tiker.C
			this.update()
		}
	}()
}

func (this *FSPPlayBack) Stop() {
	this.isRunning = false
	this.battleController = nil
}

func (this *FSPPlayBack) update() {
	for i := int32(0); i < this.mul; i++ {
		this.tick()
	}
	// 加速
	if this.currentFrameIndex >= this.currUpSpeedFrame && this.mul < 3 {
		this.mul += 1
		this.currUpSpeedFrame += this.upSpeedFrame * this.mul
	}
}

// 每帧执行
func (this *FSPPlayBack) tick() {
	if this.battleController == nil {
		return
	}
	this.currentFrameIndex += 1
	// 根据战斗记录帧数据更新
	curFrameInfo, ok := this.FrameMap[this.currentFrameIndex]
	if ok {
		if curFrameInfo.GetType() == constant.FSP_NOTIFY_TYPE_ADD_ARMY {
			if pawns, ok := this.FramePanwsMap[this.currentFrameIndex]; ok && len(pawns) > 0 {
				owner := pawns[0].GetOwner()
				camp := curFrameInfo.Fighters[0].GetCamp()
				this.battleController.PlayBackAddFighters(pawns, owner, camp)
			}
		} else if curFrameInfo.GetType() == constant.FSP_NOTIFY_TYPE_ADD_PAWN {
			if pawns, ok := this.FramePanwsMap[this.currentFrameIndex]; ok && len(pawns) > 0 {
				owner := pawns[0].GetOwner()
				camp := curFrameInfo.Fighters[0].GetCamp()
				this.battleController.PlayBackAddFighters(pawns, owner, camp)
			}
		} else if curFrameInfo.GetType() == constant.FSP_NOTIFY_TYPE_UPDATE_HP {
			this.battleController.area.SetMaxHP(curFrameInfo.Hp[1])
			this.battleController.area.SetCurHP(curFrameInfo.Hp[0])
			this.battleController.UpdateBuildInfo(curFrameInfo.BuildInfo[0], curFrameInfo.BuildInfo[1])
		}
	}
	isEnd := this.battleController.UpdateFrame(this.dt)
	// 同步帧信息
	if !isEnd && this.currentFrameIndex%this.checkFrameCount == 0 {
		md5 := this.battleController.getSnapshootMD5()
		msg := &pb.GAME_ONFSPCHECKFRAME_NOTIFY{Data: &pb.FrameInfo{
			CurrentFrameIndex: int32(this.currentFrameIndex),
			SnapshootMD5:      md5,
		}}
		msgBytes, _ := pb.ProtoMarshal(msg)
		this.room.PutNotifyAllPlayersQueue("game/OnFSPCheckFrame", msgBytes, []string{this.UserId})
	}
}
