package lobby

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
)

type NotifyData struct {
	sid int32
	msg *pb.OnUpdateLobbyNotify
}

// 定时通知
func (this *Lobby) RunNotifyTick() {
	go func() {
		tiker := time.NewTicker(time.Millisecond * 25)
		defer tiker.Stop()
		for isRunning {
			this.UpdateNotify()
			<-tiker.C
		}
	}()
}

// 定时通知
func (this *Lobby) UpdateNotify() {
	if len(this.notifyQueue) == 0 {
		return
	}
	notifyMap := map[int32]*pb.LOBBY_ONNOTIFY_NOTIFY{}
	for len(this.notifyQueue) > 0 {
		data := <-this.notifyQueue
		info := notifyMap[data.sid]
		if info == nil {
			info = &pb.LOBBY_ONNOTIFY_NOTIFY{List: []*pb.OnUpdateLobbyNotify{}}
			notifyMap[data.sid] = info
		}
		info.List = append(info.List, data.msg)
		if len(info.List) >= 1000 {
			log.Info("UpdateNotify len(list) >= 1000, notifyQueue: %v", len(this.notifyQueue))
			break
		}
	}
	msg := map[int32][]byte{}
	for k, v := range notifyMap {
		if body, err := pb.ProtoMarshal(v); err == "" {
			msg[k] = body
		}
	}
	if len(msg) > 0 {
		this.NotifyAllLobbyUser(msg)
	}
}

// 添加到通知队列
func (this *Lobby) PutNotifyQueue(sid int32, msg *pb.OnUpdateLobbyNotify) {
	if msg == nil || this.notifyQueue == nil {
		return
	}
	this.notifyQueue <- NotifyData{sid: sid, msg: msg}
}

// 通知所有大厅服
func (this *Lobby) NotifyAllLobbyUser(msgMap map[int32][]byte) {
	if !isRunning {
		return
	}
	serverId := this.GetServerID()
	serverSessions := this.GetApp().GetServersByType("lobby")
	msgMapBytes := ut.Bytes(msgMap)
	for _, m := range serverSessions {
		if m.GetID() == serverId {
			this.NotifyAllUser(msgMap)
		} else {
			m.CallNR(slg.RPC_LOBYY_NOTIFY_ALL_USER, msgMapBytes)
		}
	}
}

// 通知所有用户 sid=-1 表示全服 包含大厅服和游戏服
func (this *Lobby) NotifyAllUser(msgMap map[int32][]byte) {
	if !isRunning {
		return
	}
	topic := "lobby/OnNotify"
	users.RLock()
	for _, m := range users.Map {
		if !m.IsOnline() {
			continue
		}
		for sid, body := range msgMap {
			if sid == -1 || sid == m.SID {
				m.SessionSendNR(topic, body)
			}
		}
	}
	users.RUnlock()
}

// 通知大转盘倍数消息 这里等4秒再发
func (this *Lobby) NotifyWheelMulMsg(sid int32, nickname string, mul int32) {
	ut.SetTimeout(4*time.Second, func() {
		this.PutNotifyQueue(sid, &pb.OnUpdateLobbyNotify{
			Type: NQ_SYS_NOTICE,
			Data_2: &pb.SysNoticeInfo{
				Id:      slg.WHEEL_MUL_NOTICE_ID,
				Parames: []string{nickname, ut.Itoa(mul)},
			},
		})
	})
}

// 通知天选画像 这里等1秒
func (this *Lobby) NotifyCompOpcPortrayal(nickname string, msgId int32, heroId int32) {
	ut.SetTimeout(1*time.Second, func() {
		this.PutNotifyQueue(-1, &pb.OnUpdateLobbyNotify{
			Type: NQ_SYS_NOTICE,
			Data_2: &pb.SysNoticeInfo{
				Id:      msgId,
				Parames: []string{nickname, "portrayalText.name_" + ut.Itoa(heroId)},
			},
		})
	})
}

// 通知点将多倍残卷
func (this *Lobby) NotifyMulPortrayal(nickname string, mul, heroId int32, index int) {
	var msgId int32
	switch mul {
	case 27:
		msgId = slg.POINT_SETS_HERO_MUL_27
	case 81:
		msgId = slg.POINT_SETS_HERO_MUL_81
	default:
		return
	}
	delayTime := time.Duration(0)
	// 每一个位置延迟1秒
	if index >= 0 {
		delayTime = time.Duration(index+1) * time.Second
	}

	ut.SetTimeout(delayTime, func() {
		this.PutNotifyQueue(-1, &pb.OnUpdateLobbyNotify{
			Type: NQ_SYS_NOTICE,
			Data_2: &pb.SysNoticeInfo{
				Id:      msgId,
				Parames: []string{nickname, ut.Itoa(mul), "portrayalText.name_" + ut.Itoa(heroId)},
			},
		})
	})
}

// 通知隐藏皮肤 这里等1秒
func (this *Lobby) NotifyRareSkin(nickname string, boxId int32) {
	ut.SetTimeout(1*time.Second, func() {
		this.PutNotifyQueue(-1, &pb.OnUpdateLobbyNotify{
			Type: NQ_SYS_NOTICE,
			Data_2: &pb.SysNoticeInfo{
				Id:      slg.RARE_SKIN_NOTICE_ID,
				Parames: []string{nickname, "ui.title_mysterybox_" + ut.Itoa(boxId)},
			},
		})
	})
}
