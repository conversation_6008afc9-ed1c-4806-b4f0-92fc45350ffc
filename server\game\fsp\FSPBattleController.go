package fsp

import (
	"math"
	"sort"
	"strings"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/astar"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bdtype"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/eeffect"
	"slgsrv/server/game/common/enums/hero"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"slgsrv/utils/random"

	"github.com/sasha-s/go-deadlock"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BaseFighter struct {
	UID      string
	ID       int32
	Camp     int32
	PawnType int32
}

type LianPoEffect struct {
	lp       g.IFighter
	targets  []g.<PERSON>ighter
	overlaps []g.IFighter
	pointMap map[string]bool
}

// 战斗控制中心
type FSPBattleController struct {
	area        Area
	random      *random.Random
	searchPoint *astar.SearchPoint

	campMap            map[int32]string                              // 阵营map
	environmentBuffs   []*g.BuffObj                                  //环境buff
	mainDoors          []g.IFighter                                  // 城门列表
	fighters           []g.IFighter                                  // 战斗者列表
	currentFighter     g.IFighter                                    // 当前回合出战者
	lockedMovePointMap map[string]*g.AttackMovePointLockInfo         // 士兵移动锁定点位Map
	campStrategyMap    *ut.MapLock[int32, map[string]*g.StrategyObj] // 阵营韬略列表
	lastAttackMainCamp int32                                         // 最后一次攻击中心的阵营
	accAttackIndex     int32                                         // 当前的累计攻击下标

	plyCampMap           deadlock.Map                   // 玩家阵营map
	camp                 int32                          // 当前场景所属阵营
	accCamp              int32                          // 当前阵营累加值
	fighterPointMap      map[string][]BaseFighter       // 当前所有士兵的位置分布
	fighterBattleInfoMap map[string]map[int32]int32     // 士兵战斗数据 key=>士兵uid
	figherDeadMap        map[string][]*g.PawmDeadRecord // 士兵阵亡数据 key=>玩家uid
	// fighterOwnerMap      *ut.MapLock[string, string]    //士兵所属玩家map

	pawnStatistics     map[string]map[int32]map[int32]int32 // 士兵数据统计 仅限有效战斗
	alliUidMap         map[string]string                    // 玩家首次进入该战斗时所属联盟map
	lostTreasureResMap *ut.MapLock[string, map[int32]int32] // 阵亡遗失的宝箱资源map
	isValidBattle      bool                                 // 是否有效战斗
	isEnd              bool                                 // 是否结束

	fspPlayBack *FSPPlayBack

	frameMutex             *deadlock.RWMutex
	cmapMutex              *deadlock.RWMutex
	fighterBattleDataMutex *deadlock.RWMutex
	lockedMovePointMutex   *deadlock.RWMutex
	fighterDeadInfoMutex   *deadlock.RWMutex
	battleScoreInfoMutex   *deadlock.RWMutex
	alliUidMapMutex        *deadlock.RWMutex
	fightersMutex          *deadlock.RWMutex
}

func (this *FSPBattleController) InitCampMap(obj map[int32]string) {
	this.campMap = map[int32]string{}
	if obj != nil {
		this.cmapMutex.Lock()
		for k, v := range obj {
			this.campMap[k] = v
		}
		this.cmapMutex.Unlock()
	}
}

func (this *FSPBattleController) InitAlliUidMap(obj map[string]string) {
	this.alliUidMap = map[string]string{}
	if obj != nil {
		this.alliUidMapMutex.Lock()
		for k, v := range obj {
			this.alliUidMap[k] = v
		}
		this.alliUidMapMutex.Unlock()
	}
}

func (this *FSPBattleController) ToCampMap() map[int32]string {
	obj := map[int32]string{}
	if this.campMap != nil {
		this.cmapMutex.RLock()
		for k, v := range this.campMap {
			obj[k] = v
		}
		this.cmapMutex.RUnlock()
	} else {
		obj[this.camp] = this.area.GetOwner()
		log.Error("ToCampMap error, owner=" + this.area.GetOwner())
	}
	return obj
}

func (this *FSPBattleController) ToLockPointMapDb() map[string]map[string]interface{} {
	obj := map[string]map[string]interface{}{}
	this.lockedMovePointMutex.RLock()
	defer this.lockedMovePointMutex.RUnlock()
	for k, v := range this.lockedMovePointMap {
		obj[k] = map[string]interface{}{
			"fighterUid": v.Fighter.GetUID(),
			"weight":     v.Weight,
			"movePoint":  v.MovePoint.ID(),
		}
	}
	return obj
}

func (this *FSPBattleController) ToPb() *pb.BattleInfo {
	msg := &pb.BattleInfo{
		Camp:             this.camp,
		RandSeed:         int32(this.random.GetSeed()),
		AccAttackIndex:   this.accAttackIndex,
		EnvironmentBuffs: array.Map(this.environmentBuffs, func(m *g.BuffObj, _ int) *pb.BuffInfo { return m.ToPb() }),
	}
	this.fightersMutex.RLock()
	for _, m := range this.fighters {
		msg.Fighters = append(msg.Fighters, m.ToPb())
	}
	this.fightersMutex.RUnlock()
	if this.currentFighter != nil {
		msg.CurrentFighter = &pb.CurFighterInfo{
			Uid:        this.currentFighter.GetUID(),
			Blackboard: this.currentFighter.ToBlackboardMapPb(),
		}
	}
	this.lockedMovePointMutex.RLock()
	msg.LockedMovePointMap = map[string]*pb.LockedMovePointInfo{}
	for k, v := range this.lockedMovePointMap {
		msg.LockedMovePointMap[k] = &pb.LockedMovePointInfo{
			FighterUid: v.Fighter.GetUID(),
			Weight:     v.Weight,
			MovePoint:  pb.NewVec2(v.MovePoint),
		}
	}
	this.lockedMovePointMutex.RUnlock()
	return msg
}

func (this *FSPBattleController) GetInitData() *g.BattleRecordData {
	fighters := []*g.FigherStrip{}
	this.fightersMutex.RLock()
	for _, m := range this.fighters {
		fighters = append(fighters, m.Strip())
	}
	this.fightersMutex.RUnlock()
	return &g.BattleRecordData{
		Type:             constant.FSP_NOTIFY_TYPE_NONE,
		Camp:             this.camp,
		RandSeed:         this.random.GetSeed(),
		Fighters:         fighters,
		EnvironmentBuffs: array.Map(this.environmentBuffs, func(m *g.BuffObj, _ int) *g.BuffObj { return m.Clone() }),
	}
}

func (this *FSPBattleController) ToDB() map[string]interface{} {
	this.frameMutex.RLock()
	msg := map[string]interface{}{
		"randSeed":           this.random.GetSeed(),
		"lastAttackMainCamp": this.lastAttackMainCamp,
		"accAttackIndex":     this.accAttackIndex,
		"environmentBuffs":   array.Map(this.environmentBuffs, func(m *g.BuffObj, _ int) map[string]interface{} { return m.ToJson() }),
	}
	if this.currentFighter != nil {
		msg["currentFighter"] = map[string]interface{}{
			"uid":        this.currentFighter.GetUID(),
			"blackboard": this.currentFighter.ToBlackboardMap(),
		}
	}
	this.frameMutex.RUnlock()
	msg["campMap"] = this.ToCampMap()
	fighters := []*g.FigherStrip{}
	this.fightersMutex.RLock()
	for _, m := range this.fighters {
		fighters = append(fighters, m.ToDB())
	}
	this.fightersMutex.RUnlock()
	msg["fighters"] = fighters
	msg["fighterBattleInfo"] = this.ToFighterBattleInfoMap()
	msg["fighterDeadInfo"] = this.ToFighterDeadInfoMap()
	msg["lockedMovePointMap"] = this.ToLockPointMapDb()
	msg["alliUidMap"] = this.ToAlliUidMap()
	msg["plyCampMap"] = this.ToPlyCampMap()
	msg["lostTreasureResMap"] = this.ToLostTreasureResMap()
	return msg
}

func (this *FSPBattleController) FromDB(area Area, data map[string]interface{}, pawns map[string]g.Pawn) *FSPBattleController {
	this.frameMutex = new(deadlock.RWMutex)
	this.cmapMutex = new(deadlock.RWMutex)
	this.fighterBattleDataMutex = new(deadlock.RWMutex)
	this.lockedMovePointMutex = new(deadlock.RWMutex)
	this.fighterDeadInfoMutex = new(deadlock.RWMutex)
	this.battleScoreInfoMutex = new(deadlock.RWMutex)
	this.alliUidMapMutex = new(deadlock.RWMutex)
	this.fightersMutex = new(deadlock.RWMutex)
	this.area = area
	this.lastAttackMainCamp = ut.Int32(data["lastAttackMainCamp"])
	this.accAttackIndex = ut.Int32(data["accAttackIndex"])
	this.environmentBuffs = []*g.BuffObj{}
	if buffs, ok := data["environmentBuffs"].(primitive.A); ok {
		for _, val := range buffs {
			this.environmentBuffs = append(this.environmentBuffs, g.NewBuffByJson(val.(map[string]interface{})))
		}
	}
	this.campMap = map[int32]string{}
	campMap := data["campMap"].(map[string]interface{})
	for key, val := range campMap {
		this.campMap[ut.Int32(key)] = ut.String(val)
	}
	this.accCamp = this.initAccCmap()
	this.camp = this.GetCampByUID(area.GetOwner())
	this.searchPoint = new(astar.SearchPoint).Init(this.CheckHasPass)
	// 初始化记录
	this.fighterBattleInfoMap = map[string]map[int32]int32{}
	if fighterBattleInfo, ok := data["fighterBattleInfo"].(map[string]interface{}); ok && fighterBattleInfo != nil {
		for uid, obj := range fighterBattleInfo {
			temp, d := map[int32]int32{}, obj.(map[string]interface{})
			for k, v := range d {
				temp[ut.Int32(k)] = ut.Int32(v)
			}
			this.fighterBattleInfoMap[uid] = temp
		}
	}
	// 初始化士兵阵亡记录
	this.figherDeadMap = map[string][]*g.PawmDeadRecord{}
	if figherDeadInfo, ok := data["figherDeadInfo"].(map[string]interface{}); ok && figherDeadInfo != nil {
		for uid, obj := range figherDeadInfo {
			deadArr := obj.([]*g.PawmDeadRecord)
			this.figherDeadMap[uid] = deadArr
		}
	}
	// 初始化联盟map
	this.alliUidMap = map[string]string{}
	if alliMap, ok := data["alliUidMap"].(map[string]interface{}); ok && alliMap != nil {
		for uid, alliUid := range alliMap {
			this.alliUidMap[uid] = ut.String(alliUid)
		}
	}
	// 初始化玩家阵营map
	this.plyCampMap = deadlock.Map{}
	if plyCampMap, ok := data["plyCampMap"].(map[string]interface{}); ok && plyCampMap != nil {
		for uid, camp := range plyCampMap {
			this.plyCampMap.Store(uid, ut.Int32(camp))
		}
	}
	// 初始化士兵
	buildPawnId, _ := area.GetBuildPawnInfo()
	tempAttackTargetMap := map[string]string{}
	this.campStrategyMap = ut.NewMapLock[int32, map[string]*g.StrategyObj]()
	this.fighters = []g.IFighter{}
	pawnMap := map[string]bool{}
	if fighters, ok := data["fighters"].(primitive.A); ok {
		for _, f := range fighters {
			m := f.(map[string]interface{})
			uid := ut.String(m["uid"])
			if pawnMap[uid] {
				continue // 这里兼容一下重复的士兵
			}
			pawnMap[uid] = true
			var fighter g.IFighter = nil
			if towerLv := ut.Int32(m["towerLv"]); towerLv > 0 {
				towerId := ut.Int32(m["towerId"])
				fighter = new(Tower).Init(uid, ut.If(towerId == 0, buildPawnId, towerId), towerLv, ut.NewVec2ByObj(m["point"]), area, this.camp, this)
			} else if ut.Bool(m["isFalg"]) {
				flagPawn := area.CreateBuildFlagPawn(uid, ut.Int32Array(m["hp"]), ut.NewVec2ByObj(m["point"]), ut.String(m["owner"]))
				fighter = new(Building).Init(flagPawn, ut.Int32(m["camp"]), ut.Int32(m["roundCount"]), this)
			} else if ut.Bool(m["isPet"]) {
				petPawn := area.CreatePetPawn(uid, ut.Int32(m["id"]), ut.Int32(m["lv"]), ut.NewVec2ByObj(m["point"]), ut.String(m["owner"]))
				petPawn.SetCurHP(ut.Int32Array(m["hp"])[0])
				petPawn.SetBuffsByDB(m["buffs"])
				fighter = new(Pet).Init(petPawn, ut.Int32(m["camp"]), ut.Int32(m["roundCount"]), this)
			} else if ut.Bool(m["isNoncombat"]) {
				noncombatPawn := area.CreateNoncombatPawn(uid, ut.Int32(m["id"]), ut.Int32(m["lv"]), ut.NewVec2ByObj(m["point"]), ut.String(m["owner"]), ut.Int32(m["enterDir"]))
				if hp := ut.Int32Array(m["hp"]); len(hp) == 2 {
					noncombatPawn.SetCurHP(hp[0])
					noncombatPawn.SetMaxHP(hp[1])
				}
				fighter = new(Building).Init(noncombatPawn, ut.Int32(m["camp"]), ut.Int32(m["roundCount"]), this)
			} else if pawn := pawns[uid]; pawn != nil {
				// pawn.SetBuffsByDB(m["buffs"])
				fighter = new(Fighter).Init(pawn, ut.Int32(m["camp"]), ut.Int32(m["roundCount"]), this)
				this.AddCampStrategys(fighter)
			} else {
				continue
			}
			fighter.SetAttackCount(ut.Int32(m["attackCount"]))
			fighter.SetAttackIndex(ut.Int32(m["attackIndex"]))
			fighter.SetWaitRound(ut.Int32(m["waitRound"]))
			if !fighter.IsNoncombat() {
				tempAttackTargetMap[uid] = ut.String(m["attackTarget"])
			}
			this.fighters = append(this.fighters, fighter)
		}
	}
	// 初始化城门
	this.mainDoors = []g.IFighter{}
	if !area.IsBoss() {
		mainPoints := area.GetMainPoints()
		for _, p := range mainPoints {
			this.mainDoors = append(this.mainDoors, new(MainDoor).Init(area, this.camp, p))
		}
	}
	// 根据出手顺序排序
	sort.SliceStable(this.fighters, func(i, j int) bool {
		return this.fighters[i].GetAttackIndex() < this.fighters[j].GetAttackIndex()
	})
	// 初始化随机种子
	this.random = random.NewRandom(ut.Int(data["randSeed"]))
	// 初始化每个士兵的攻击目标
	for _, m := range this.fighters {
		m.SetAttackTarget(this.GetFighter(tempAttackTargetMap[m.GetUID()]))
		m.CheckBattleBeginTrigger()
		m.UpdateStrategyEffect()
	}
	// slg.Log("--------------------------------------" + ut.Itoa(area.GetIndex()) + ", owner=" + area.GetOwner())
	// for _, m := range this.fighters {
	// 	slg.Log(m.MD5())
	// }
	// 刷新一下位置
	this.UpdateFighterPointMap()
	// 当前出手的士兵
	if currentFighterInfo, _ := data["currentFighter"].(map[string]interface{}); currentFighterInfo != nil {
		ok := false
		this.currentFighter, ok = this.GetFighter(ut.String(currentFighterInfo["uid"])).(*Fighter)
		if this.currentFighter != nil && ok {
			blackboard, _ := currentFighterInfo["blackboard"].(map[int32]map[string]interface{})
			if blackboard == nil {
				blackboard = map[int32]map[string]interface{}{}
			}
			this.currentFighter.SetBlackboardMap(blackboard)
		} else {
			this.currentFighter = nil
		}
	}
	if this.currentFighter == nil && len(this.fighters) > 0 {
		this.currentFighter = this.fighters[0]
	}
	// 初始化士兵移动目标Map
	this.lockedMovePointMap = map[string]*g.AttackMovePointLockInfo{}
	lockedMovePointMap := data["lockedMovePointMap"].(map[string]interface{})
	for k, v := range lockedMovePointMap {
		lockPointData := ut.MapInterface(v)
		if findFighter := this.GetFighter(ut.String(lockPointData["fighterUid"])); findFighter != nil {
			lockPointInfo := &g.AttackMovePointLockInfo{
				Fighter:   findFighter,
				Weight:    int64(ut.Int64(lockPointData["weight"])),
				MovePoint: ut.NewVec2ByString(ut.String(lockPointData["movePoint"]), "_"),
			}
			this.lockedMovePointMap[k] = lockPointInfo
		}
	}
	// 初始化阵亡遗失的宝箱资源map
	this.lostTreasureResMap = ut.NewMapLock[string, map[int32]int32]()
	if lostTreasureResMap, ok := data["lostTreasureResMap"].(map[string]interface{}); ok {
		for k, v := range lostTreasureResMap {
			resMapData := ut.MapInt32Int32(v)
			this.lostTreasureResMap.Set(k, resMapData)
		}
	}
	return this
}

func (this *FSPBattleController) Init(area Area, data FSPParam) *FSPBattleController {
	this.frameMutex = new(deadlock.RWMutex)
	this.cmapMutex = new(deadlock.RWMutex)
	this.fighterBattleDataMutex = new(deadlock.RWMutex)
	this.lockedMovePointMutex = new(deadlock.RWMutex)
	this.fighterDeadInfoMutex = new(deadlock.RWMutex)
	this.battleScoreInfoMutex = new(deadlock.RWMutex)
	this.alliUidMapMutex = new(deadlock.RWMutex)
	this.fightersMutex = new(deadlock.RWMutex)
	this.fighterBattleInfoMap = map[string]map[int32]int32{}
	this.figherDeadMap = map[string][]*g.PawmDeadRecord{}
	this.pawnStatistics = map[string]map[int32]map[int32]int32{}
	this.alliUidMap = map[string]string{}
	this.area = area
	this.environmentBuffs = data.EnvironmentBuffs
	this.InitCampMap(data.CampMap)
	this.InitAlliUidMap(data.AlliUidMap)
	this.plyCampMap = deadlock.Map{}
	this.accAttackIndex = 0
	this.accCamp = this.initAccCmap()
	this.camp = this.GetCampByUID(area.GetOwner())
	this.searchPoint = new(astar.SearchPoint).Init(this.CheckHasPass)
	this.lockedMovePointMap = map[string]*g.AttackMovePointLockInfo{}
	this.campStrategyMap = ut.NewMapLock[int32, map[string]*g.StrategyObj]()
	this.lostTreasureResMap = ut.NewMapLock[string, map[int32]int32]()
	// 战斗者列表
	pawnMap := map[string]bool{}
	this.fighters = []g.IFighter{}
	for _, m := range data.Pawns {
		if !pawnMap[m.GetUID()] {
			pawnMap[m.GetUID()] = true
			fighter := new(Fighter).Init(m, this.GetCampByUID(m.GetOwner()), 0, this)
			this.AddCampStrategys(fighter)
			this.fighters = append(this.fighters, fighter)
		}
	}
	// 箭塔建筑
	this.mainDoors = []g.IFighter{}
	if !area.IsBoss() {
		buildPawnId, buildPawnLv := area.GetBuildPawnInfo()
		mainPoints := area.GetMainPoints()
		for _, p := range mainPoints {
			this.mainDoors = append(this.mainDoors, new(MainDoor).Init(area, this.camp, p))
			// 箭塔
			if buildPawnId > 0 {
				this.fighters = append(this.fighters, new(Tower).Init(ut.ID(), buildPawnId, buildPawnLv, p, area, this.camp, this))
			}
		}
	}
	// 根据出手顺序排序
	sort.SliceStable(this.fighters, func(i, j int) bool {
		return this.fighters[i].GetTempAttackIndex() > this.fighters[j].GetTempAttackIndex()
	})
	for _, m := range this.fighters {
		m.SetAttackIndex(this.getAccAttackIndex())
	}
	// 初始化随机种子
	// this.random = random.NewRandom(ut.Random(100000, 999999))
	this.random = random.NewRandom(ut.Atoi(data.Attacker)/100 + int(area.GetIndex()))
	return this
}

func (this *FSPBattleController) Run() {
	randomSeed := this.random.GetSeed()
	for _, m := range this.fighters {
		m.CheckBattleBeginTrigger()
		m.UpdateStrategyEffect()
		// slg.Log(m.MD5())
	}
	// 刷新一下位置
	this.UpdateFighterPointMap()
	// 默认第一个开始
	this.currentFighter = this.fighters[0]
	slg.Log("battleStart randSeed: " + ut.Itoa(randomSeed) + ", index: " + ut.Itoa(this.area.GetIndex()))
	slg.Log("0 >>>>>>>>> " + ut.Itoa(this.currentFighter.GetID()) + "(" + this.currentFighter.GetPoint().Join(",") + ") [" + this.currentFighter.GetUID() + "] " + ut.Itoa(this.currentFighter.GetAttackIndex()) + " " + ut.Itoa(this.currentFighter.GetCamp()))
	// 有效战斗检测
	this.checkValidBattle()
}

func (this *FSPBattleController) GetCurrentFrameIndex() int32 {
	if this.fspPlayBack != nil {
		return this.fspPlayBack.GetCurrentFrameIndex()
	}
	return this.area.GetBattleCurrentFrameIndex()
}

// 结束
func (this *FSPBattleController) End() bool {
	this.currentFighter = nil
	if this.fspPlayBack != nil {
		this.fspPlayBack.Stop()
		this.fspPlayBack = nil
		return true
	}
	attackers := []string{}
	if this.IsAreaDie() {
		attacker := ""
		this.cmapMutex.RLock()
		if this.campMap != nil {
			if attacker = this.campMap[this.lastAttackMainCamp]; attacker == "" {
				attacker = this.campMap[2] // 2默认是攻击方
			}
		}
		this.cmapMutex.RUnlock()
		if attacker != "" {
			attackers = append(attackers, attacker)
		}
		this.plyCampMap.Range(func(key, value any) bool {
			uid := ut.String(key)
			if ut.Int32(value) == this.lastAttackMainCamp && uid != attacker {
				attackers = append(attackers, uid)
			}
			return true
		})
	}
	this.area.BattleEnd(attackers)
	return true
}

func (this *FSPBattleController) IsAreaDie() bool {
	if this.area.IsBoss() {
		return !array.Some(this.fighters, func(m g.IFighter) bool { return m.GetCamp() == this.camp && !m.IsDie() })
	}
	return len(this.mainDoors) == 0 || array.Some(this.mainDoors, func(m g.IFighter) bool { return m.IsDie() })
}

// 初始化阵营累加值
func (this *FSPBattleController) initAccCmap() int32 {
	var acc int32
	this.cmapMutex.RLock()
	for camp := range this.campMap {
		if camp > acc {
			acc = camp
		}
	}
	this.cmapMutex.RUnlock()
	return acc
}

// 获取阵营
func (this *FSPBattleController) GetCampByUID(uid string) int32 {
	if c, ok := this.plyCampMap.Load(uid); ok {
		return c.(int32)
	}
	wld := this.area.GetWorld()
	campMap := this.ToCampMap()
	for camp, owner := range campMap {
		if wld.CheckIsOneAlliance(owner, uid) {
			this.plyCampMap.Store(uid, camp)
			return camp
		}
	}
	var alliCamp int32
	this.plyCampMap.Range(func(key, value any) bool {
		if wld.CheckIsOneAlliance(ut.String(key), uid) {
			alliCamp = ut.Int32(value)
			return false
		}
		return true
	})
	if alliCamp > 0 {
		this.plyCampMap.Store(uid, alliCamp)
		return alliCamp
	}
	this.accCamp += 1
	this.cmapMutex.Lock()
	this.campMap[this.accCamp] = uid
	this.cmapMutex.Unlock()
	this.plyCampMap.Store(uid, this.accCamp)
	return this.accCamp
}

func (this *FSPBattleController) getAccAttackIndex() int32 {
	this.accAttackIndex += 1
	return this.accAttackIndex
}

// 添加士兵
func (this *FSPBattleController) AddFighters(pawns []g.Pawn, owner string, isLock bool) ([]*g.FigherStrip, []g.IFighter) {
	if isLock {
		this.frameMutex.Lock()
		defer this.frameMutex.Unlock()
	}
	// 根据出手顺序排序
	sort.SliceStable(pawns, func(i, j int) bool {
		return pawns[i].GetAttackIndex() > pawns[j].GetAttackIndex()
	})
	// 获取阵营
	camp := this.GetCampByUID(owner)
	// 获取玩家联盟uid
	this.alliUidMapMutex.Lock()
	if _, ok := this.alliUidMap[owner]; !ok {
		this.alliUidMap[owner] = this.area.GetWorld().GetPlayerAlliUid(owner)
	}
	this.alliUidMapMutex.Unlock()
	// slg.Log(this.campMap)
	// slg.Log(this.tempCampMap)
	// 添加到战斗者列表
	pawnMap := map[string]bool{}
	for _, m := range this.fighters {
		pawnMap[m.GetUID()] = true
	}
	addFighters := []g.IFighter{}
	checkUpdateStrategyCampMap := map[int32]bool{}
	for _, pawn := range pawns {
		uid := pawn.GetUID()
		if pawnMap[uid] {
			continue
		}
		pawnMap[uid] = true
		var m g.IFighter = nil
		if strings.Contains(uid, "build_") {
			m = new(Building).Init(pawn, camp, 0, this)
		} else if strings.Contains(uid, "pet_") {
			m = new(Pet).Init(pawn, camp, 0, this)
		} else {
			m = new(Fighter).Init(pawn, camp, 0, this)
		}
		m.SetAttackIndex(this.getAccAttackIndex())
		addFighters = append(addFighters, m)
		// 添加韬略
		if this.AddCampStrategys(m) {
			checkUpdateStrategyCampMap[camp] = true
		}
		// slg.Log(m.MD5())
	}
	// 刷新韬略
	if len(checkUpdateStrategyCampMap) > 0 {
		for _, m := range this.fighters {
			if checkUpdateStrategyCampMap[m.GetCamp()] {
				m.UpdateStrategyEffect()
			}
		}
	}
	// 获取当前出手战斗者下标
	curFighterIndex := this.getCurFighterIndex()
	// 将新加入的战斗者放到当前出手的战斗者之前
	fightersList := append(this.fighters[:curFighterIndex], append(addFighters, this.fighters[curFighterIndex:]...)...)
	// 设置添加后的出手顺序
	for i := curFighterIndex + len(addFighters); i < len(fightersList); i++ {
		f := fightersList[i]
		f.SetAttackIndex(this.getAccAttackIndex())
	}
	this.fightersMutex.Lock()
	this.fighters = fightersList
	this.fightersMutex.Unlock()
	fighters := []*g.FigherStrip{}
	for _, m := range addFighters {
		m.CheckBattleBeginTrigger()
		m.UpdateStrategyEffect()
		fighters = append(fighters, m.Strip())
	}
	this.UpdateFighterPointMap()
	this.checkValidBattle()
	// fmt.Println(array.Map(this.fighters, func(m g.IFighter, _ int) string { return m.GetPoint().ID() + "_" + ut.Itoa(m.GetAttackIndex()) }))
	return fighters, addFighters
}

// 删除士兵
func (this *FSPBattleController) RemoveFightersByArmyUid(armyUid string) {
	this.frameMutex.Lock()
	defer this.frameMutex.Unlock()
	checkUpdateStrategyCampMap := map[int32]bool{}
	this.fightersMutex.Lock()
	delPetUidMap := map[string]bool{}
	for i := len(this.fighters) - 1; i >= 0; i-- {
		f := this.fighters[i]
		pawn := f.GetEntity()
		if pawn == nil {
			continue
		} else if pawn.GetArmyUid() == armyUid {
			slg.Log("RemoveFighterByArmyUid " + ut.Itoa(pawn.GetID()))
			if this.currentFighter != nil && f.GetUID() == this.currentFighter.GetUID() {
				this.currentFighter.SetRoundEnd()
			}
			this.fighters = append(this.fighters[:i], this.fighters[i+1:]...)
			// 删除韬略
			if this.RemoveCampStrategy(f) {
				checkUpdateStrategyCampMap[f.GetCamp()] = true
			}
			if f.CheckPortrayalSkill(hero.YANG_YOUJI) != nil {
				petUid := "pet_" + f.GetUID()
				delPetUidMap[petUid] = true
			}
		}
	}
	// 检测删除召唤物
	for i := len(this.fighters) - 1; i >= 0; i-- {
		f := this.fighters[i]
		if delPetUidMap[f.GetUID()] {
			this.fighters = append(this.fighters[:i], this.fighters[i+1:]...)
		}
	}
	this.fightersMutex.Unlock()
	this.UpdateFighterPointMap()
	// 重新刷新下韬略
	if len(checkUpdateStrategyCampMap) > 0 {
		for _, m := range this.fighters {
			if checkUpdateStrategyCampMap[m.GetCamp()] {
				m.UpdateStrategyEffect()
			}
		}
	}
}

// 刷新建筑信息
func (this *FSPBattleController) UpdateBuildInfo(buildPawnId, buildPawnLv int32) {
	if buildPawnId == 0 {
		return
	}
	this.frameMutex.Lock()
	defer this.frameMutex.Unlock()
	this.fightersMutex.RLock()
	for _, m := range this.fighters {
		if m.IsTower() {
			m.UpdateAttrByBattle(buildPawnId, buildPawnLv)
		}
	}
	this.fightersMutex.RUnlock()
}

// 同步帧信息
func (this *FSPBattleController) NotifyCheckFrame(currentFrameIndex int32) {
	md5 := this.getSnapshootMD5()
	msg := &pb.GAME_ONFSPCHECKFRAME_NOTIFY{Data: &pb.FrameInfo{
		CurrentFrameIndex: currentFrameIndex,
		SnapshootMD5:      md5,
	}}
	this.area.NotifyCheckFrame(msg)
	// slg.Log(currentFrameIndex, md5)
}

// 记录最后一次攻击中心的玩家
func (this *FSPBattleController) RecordLastAttackMainPlayer() {
	if this.currentFighter != nil {
		this.lastAttackMainCamp = this.currentFighter.GetCamp()
	}
}

func (this *FSPBattleController) ToFighterBattleInfoMap() map[string]map[int32]int32 {
	this.fighterBattleDataMutex.RLock()
	defer this.fighterBattleDataMutex.RUnlock()
	fighterBattleInfo := map[string]map[int32]int32{}
	for uid, data := range this.fighterBattleInfoMap {
		temp := map[int32]int32{}
		for k, v := range data {
			temp[k] = v
		}
		fighterBattleInfo[uid] = temp
	}
	return fighterBattleInfo
}

func (this *FSPBattleController) ToFighterDeadInfoMap() map[string][]*g.PawmDeadRecord {
	this.fighterDeadInfoMutex.RLock()
	defer this.fighterDeadInfoMutex.RUnlock()
	fighterDeadInfo := map[string][]*g.PawmDeadRecord{}
	for uid, data := range this.figherDeadMap {
		arr := []*g.PawmDeadRecord{}
		for _, v := range data {
			arr = append(arr, v)
		}
		fighterDeadInfo[uid] = arr
	}
	return fighterDeadInfo
}

// 获取玩家所属联盟map
func (this *FSPBattleController) ToAlliUidMap() map[string]string {
	this.alliUidMapMutex.RLock()
	defer this.alliUidMapMutex.RUnlock()
	alliMap := map[string]string{}
	for k, v := range this.alliUidMap {
		alliMap[k] = v
	}
	return alliMap
}

// 获取玩家阵营map
func (this *FSPBattleController) ToPlyCampMap() map[string]int32 {
	ret := map[string]int32{}
	this.plyCampMap.Range(func(key, value any) bool {
		ret[ut.String(key)] = ut.Int32(value)
		return true
	})
	return ret
}

// 获取阵亡遗失的宝箱资源map
func (this *FSPBattleController) ToLostTreasureResMap() map[string]map[int32]int32 {
	ret := this.lostTreasureResMap.Clone()
	return ret
}

// 添加记录
func (this *FSPBattleController) AddFighterBattleInfo(uid, owner string, tp, val int32) {
	// 战斗数据记录
	if val <= 0 {
		return
	}
	this.fighterBattleDataMutex.Lock()
	defer this.fighterBattleDataMutex.Unlock()
	data := this.fighterBattleInfoMap[uid]
	if data == nil {
		data = map[int32]int32{}
		this.fighterBattleInfoMap[uid] = data
	}
	data[tp] += val
	// 积分相关战斗数据记录
	this.battleScoreInfoMutex.Lock()
	defer this.battleScoreInfoMutex.Unlock()
}

// 添加伤害记录
func (this *FSPBattleController) AddFighterBattleDamageInfo(attackerUID string, defender g.IFighter, damage int32) {
	if damage <= 0 {
		return
	} else if attackerUID == defender.GetUID() {
		return
	}
	var attId int32
	var attOwner string
	attacker := this.GetFighter(attackerUID)
	if attacker != nil {
		// 是否有混乱 并且打的是自己人
		if buff := attacker.GetBuff(bufftype.CHAOS); buff != nil && attacker.GetCamp() == defender.GetCamp() {
			this.AddFighterBattleDamageInfo(buff.Provider, defender, damage)
			return
		} else if attacker.GetEntity() != nil {
			attId = attacker.GetEntity().GetID()
		}
		attOwner = attacker.GetOwner()
	}
	var defId int32
	if defender.GetEntity() != nil {
		defId = defender.GetEntity().GetID()
	}
	defOwner := defender.GetOwner()
	isPlayer := defOwner != ""
	this.AddFighterBattleInfo(attackerUID, attOwner, bdtype.SUM_DAMAGE, damage) // 输出伤害
	if attacker != nil && attacker.IsTower() && !defender.IsFlag() {
		this.AddFighterBattleInfo(attackerUID, attOwner, bdtype.BUILD_DAMAGE, damage) // 建筑造成的伤害
	}
	if isPlayer && attacker != nil && attacker.IsPawn() {
		if defender.IsFlag() {
		} else if defender.IsBuild() {
			this.AddFighterBattleInfo(attackerUID, attOwner, bdtype.DAMAGE_TO_BUILD, damage) // 对建筑造成的伤害
		} else if defender.IsPawn() {
			this.AddFighterBattleInfo(attackerUID, attOwner, bdtype.TO_PAWN_DAMGE, damage) // 对士兵造成的伤害
		}
	} else if !isPlayer {
		this.AddFighterBattleInfo(attackerUID, attOwner, bdtype.DAMAGE_TO_MONSTER, damage) // 对野怪造成的伤害
	}
	if defender.IsDie() && !defender.IsFlag() && !defender.IsBuild() {
		this.AddFighterBattleInfo(attackerUID, attOwner, bdtype.SUM_KILL, 1) // 击杀
		if !isPlayer {
			if defender.IsPawn() {
				this.AddFighterBattleInfo(attackerUID, attOwner, bdtype.KILL_MONSTER, 1) // 击杀野怪
				if attOwner != "" {
					this.area.GetWorld().TriggerTask(attOwner, tctype.KILL_MONSTER_COUNT, 1, 0)
					this.area.GetWorld().AddBattlePassScore(attOwner, slg.BATTLE_PASS_BUY_SCORE_KILL_MONSTER)
				}
			}
		} else if defender.IsPawn() {
			this.AddFighterBattleInfo(attackerUID, attOwner, bdtype.KILL_PAWN, 1)       // 击杀士兵
			this.AddFighterDeadInfo(defId, defender.GetLV(), attId, defOwner, attOwner) // 士兵阵亡
			if attOwner != "" {
				this.area.GetWorld().AddBattlePassScore(attOwner, slg.BATTLE_PASS_BUY_SCORE_KILL_PAWN)
			}
		}
		// 记录击杀数量
		if attOwner != "" && defId > 0 {
			this.area.GetWorld().RecordPlayerKillCount(attOwner, defId, 1)
		}
	}
}

// 添加承受记录
func (this *FSPBattleController) AddFighterBattleHitDamage(uid, owner string, damage, baseDamage int32) {
	this.AddFighterBattleInfo(uid, owner, bdtype.HIT_DAMAGE_MITIGATED, damage)
	this.AddFighterBattleInfo(uid, owner, bdtype.HIT_DAMAGE_TAKEN, baseDamage)
}

// 添加阵亡记录
func (this *FSPBattleController) AddFighterDeadInfo(id, lv, kId int32, owner, kOwner string) {
	this.fighterDeadInfoMutex.Lock()
	defer this.fighterDeadInfoMutex.Unlock()
	info := &g.PawmDeadRecord{
		Id:          id,
		Lv:          lv,
		KillerId:    kId,
		KillerOwner: kOwner,
	}
	_, ok := this.figherDeadMap[owner]
	if !ok {
		this.figherDeadMap[owner] = []*g.PawmDeadRecord{info}
	} else {
		this.figherDeadMap[owner] = append(this.figherDeadMap[owner], info)
	}
}

// 每一个逻辑帧
func (this *FSPBattleController) UpdateFrame(dt int32) bool {
	this.frameMutex.Lock()
	if this.currentFighter == nil || len(this.fighters) == 0 || this.currentFighter.IsBattleOver() {
		this.isEnd = true
		this.frameMutex.Unlock()
		return this.End()
	} else if this.currentFighter.UpdateBattleOver(dt) {
		this.frameMutex.Unlock()
		return false
	} else if this.currentFighter.IsRoundEnd() {
		// 删除死亡的士兵
		removeCurrUid := this.RemoveDieFighters()
		// 检测战斗是否结束
		endDelayTime := this.checkBattleOver()
		if endDelayTime > 0 {
			this.currentFighter.SetBattleOverDelay(endDelayTime)
			this.frameMutex.Unlock()
			return false
		}
		// 刷新位置分布
		this.UpdateFighterPointMap()
		// 上一个结束
		this.currentFighter.UpdateBuff()
		this.currentFighter.EndAction()
		// 下一个出手
		this.currentFighter = this.getNextFighter()
		this.currentFighter.BeginAction()
		if removeCurrUid != "" {
			this.fightersMutex.Lock()
			this.fighters = array.RemoveItem(this.fighters, func(m g.IFighter) bool { return m.GetUID() == removeCurrUid })
			this.fightersMutex.Unlock()
		}
		// fmt.Println("-------------------------------[" + ut.Itoa(this.currentFighter.entity.GetID()) + "](" + this.currentFighter.GetPoint().Join(",") + ")")
	}
	this.currentFighter.BehaviorTick(dt) // 执行士兵的逻辑
	this.frameMutex.Unlock()
	return false
}

// 刷新士兵死亡情况
func (this *FSPBattleController) RemoveDieFighters() string {
	removeCurrUid, checkUpdateStrategyCampMap, noncombats := "", map[int32]bool{}, []string{}
	for i := len(this.fighters) - 1; i >= 0; i-- {
		if f := this.fighters[i]; f.IsDie() {
			// slg.Log("removeDieFighter " + ut.Itoa(f.entity.GetID()))
			if f.GetUID() == this.currentFighter.GetUID() {
				// 当前出手的士兵死亡 等到获取下一个出手士兵后再删除
				removeCurrUid = f.GetUID()
			} else {
				this.fightersMutex.Lock()
				this.fighters = append(this.fighters[:i], this.fighters[i+1:]...)
				this.fightersMutex.Unlock()
			}
			// 删除实际军队里面的
			if f.IsPawn() {
				_, resMap := this.area.RemoveArmyPawn(f.GetEntity().GetArmyUid(), f.GetUID(), true)
				if resMap != nil {
					lostResMap := this.lostTreasureResMap.Get(f.GetOwner())
					if lostResMap == nil {
						lostResMap = map[int32]int32{}
					}
					for tp, count := range resMap {
						lostResMap[tp] += count
					}
					this.lostTreasureResMap.Set(f.GetOwner(), lostResMap)
				}
			}
			// 删除韬略
			if this.RemoveCampStrategy(f) {
				checkUpdateStrategyCampMap[f.GetCamp()] = true
			}
			// 如果是秦良玉还要删除矛
			if f.CheckPortrayalSkill(hero.QIN_LIANGYU) != nil {
				noncombats = append(noncombats, "build_"+f.GetUID())
			}
			// 清理搜索范围
			if searchRange := f.GetSearchRange(); searchRange != nil {
				searchRange.Clean()
			}
		}
	}
	// 重新刷新下韬略
	if len(checkUpdateStrategyCampMap) > 0 {
		for _, m := range this.fighters {
			if checkUpdateStrategyCampMap[m.GetCamp()] {
				m.UpdateStrategyEffect()
			}
		}
	}
	// 删除非战斗单位
	for _, m := range noncombats {
		this.RemoveNoncombat(m)
	}
	return removeCurrUid
}

// 战斗是否结束
func (this *FSPBattleController) checkBattleOver() int32 {
	if len(this.fighters) == 0 {
		return 1 // 没有士兵肯定就结束了啊
	}
	isBoss := this.area.IsBoss()
	if !isBoss && (len(this.mainDoors) == 0 || array.Some(this.mainDoors, func(m g.IFighter) bool { return m.IsDie() })) {
		return 1 // 血量小于0 直接结束
	}
	campMap := map[int32]bool{}
	if !isBoss {
		campMap[this.camp] = true
	}
	this.fightersMutex.RLock()
	for _, m := range this.fighters {
		if !m.IsDie() && !m.IsNoncombat() {
			campMap[m.GetCamp()] = true
		}
	}
	this.fightersMutex.RUnlock()
	// fmt.Println("checkBattleOver campMap", campMap)
	if len(campMap) <= 1 {
		return 1000 // 只要还只剩下一个阵营 就算结束
	} else if this.area.CheckBattleEndTime() {
		return 1 // 最后看是否时间到了
	}
	return 0
}

// 刷新所有士兵的位置分布
func (this *FSPBattleController) UpdateFighterPointMap() {
	this.fighterPointMap = map[string][]BaseFighter{}
	flagPointMap := map[string][]int32{}
	sr := new(astar.SearchRange).Init(this.CheckIsBattleArea)
	lianPoList := []*LianPoEffect{}
	this.fightersMutex.RLock()
	for _, m := range this.fighters {
		id, camp := m.GetPoint().ID(), m.GetCamp()
		arr := this.fighterPointMap[id]
		if arr == nil {
			arr = []BaseFighter{}
		}
		this.fighterPointMap[id] = append(arr, BaseFighter{UID: m.GetUID(), ID: m.GetID(), Camp: camp, PawnType: m.GetPawnType()})
		if m.IsFlag() {
			points := sr.Search(m.GetPoint(), 2, true)
			for _, p := range points {
				pid := p.ID()
				camps := flagPointMap[pid]
				if camps == nil {
					flagPointMap[pid] = []int32{camp}
				} else if !array.Has(camps, camp) {
					flagPointMap[pid] = append(camps, camp)
				}
			}
		} else if m.CheckPortrayalSkill(hero.LIAN_PO) != nil && m.GetHPRatio() != 0.5 { // 是否廉颇
			lianPoList = append(lianPoList, &LianPoEffect{
				lp:       m,
				targets:  []g.IFighter{},
				overlaps: []g.IFighter{},
				pointMap: map[string]bool{},
			})
		}
	}
	for _, m := range this.fighters {
		if !m.IsPawn() {
			continue
		}
		point := m.GetPoint()
		pointId := point.ID()
		// 检测军旗
		camps := flagPointMap[pointId]
		if len(camps) == 0 { // 删除
			m.RemoveMultiBuff(bufftype.DAMAGE_INCREASE, bufftype.DAMAGE_REDUCE)
		} else {
			has := array.Has(camps, m.GetCamp())
			if has { // 添加增伤
				m.AddBuff(bufftype.DAMAGE_INCREASE, m.GetEntity().GetOwner(), 1)
			}
			if !has || len(camps) >= 2 { // 添加减伤
				m.AddBuff(bufftype.DAMAGE_REDUCE, m.GetEntity().GetOwner(), 1)
			}
		}
		// 检测廉颇
		for _, v := range lianPoList {
			if v.lp.GetUID() != m.GetUID() && v.lp.GetCamp() == m.GetCamp() {
				dis := helper.GetPointToPointDis(v.lp.GetPoint(), point)
				if dis > 3 {
				} else if !v.pointMap[pointId] {
					v.pointMap[pointId] = true
					v.targets = append(v.targets, m) // 优先选择没有重叠的
				} else {
					v.overlaps = append(v.overlaps, m) // 这里就是重叠一起的
				}
			}
		}
		m.RemoveMultiBuff(bufftype.LIAN_PO_ATTACK, bufftype.LIAN_PO_DEFEND)
	}
	this.fightersMutex.RUnlock()
	// 廉颇添加buff
	for _, v := range lianPoList {
		heroSkill := v.lp.GetPortrayalSkill()
		lpUid, isAttack, valueRatio := v.lp.GetUID(), v.lp.GetHPRatio() > 0.5, 1+heroSkill.GetValue()*0.01
		lpBuffMaxCnt, value := int(heroSkill.Target), heroSkill.GetParamsFloat64()
		tLen, oLen := len(v.targets), len(v.overlaps)
		if tLen < lpBuffMaxCnt && oLen > 0 {
			if isAttack {
				sort.Slice(v.overlaps, func(i, j int) bool {
					a, b := v.overlaps[i], v.overlaps[j]
					aw, bw := a.GetActAttack(), b.GetActAttack()
					if aw == bw {
						aw -= a.GetAttackIndex()
						bw -= b.GetAttackIndex()
					}
					return aw > bw
				})
			} else {
				sort.Slice(v.overlaps, func(i, j int) bool {
					a, b := v.overlaps[i], v.overlaps[j]
					aw, bw := 100-a.GetHPRatio()*100, 100-b.GetHPRatio()*100
					if aw == bw {
						aw -= float64(a.GetAttackIndex())
						bw -= float64(b.GetAttackIndex())
					}
					return aw > bw
				})
			}
			v.targets = append(v.targets, v.overlaps[:ut.Min(oLen, lpBuffMaxCnt-tLen)]...)
		} else {
			v.targets = v.targets[:ut.Min(tLen, lpBuffMaxCnt)]
		}
		buffId := int32(ut.If(isAttack, bufftype.LIAN_PO_ATTACK, bufftype.LIAN_PO_DEFEND))
		for _, m := range v.targets {
			m.AddBuffValue(buffId, lpUid, value)
		}
		v.lp.AddBuffValue(buffId, lpUid, math.Round(value*valueRatio))
	}
}

// 获取下一个战斗者
func (this *FSPBattleController) getNextFighter() g.IFighter {
	if this.currentFighter.IsHasBuff(bufftype.CONTINUE_ACTION) {
		return this.currentFighter // 继续行动
	}
	uid := this.currentFighter.GetUID()
	i, cnt := 0, len(this.fighters)
	for i = 0; i < cnt; i++ {
		if this.fighters[i].GetUID() == uid {
			break
		}
	}
	i = ut.LoopValue(i+1, cnt)
	return this.fighters[i]
}

// 获取当前战斗中下标
func (this *FSPBattleController) getCurFighterIndex() int {
	uid := this.currentFighter.GetUID()
	i, cnt := 0, len(this.fighters)
	for i = 0; i < cnt; i++ {
		if this.fighters[i].GetUID() == uid {
			return i
		}
	}
	return i
}

// 获取当前状态快照md5
func (this *FSPBattleController) getSnapshootMD5() string {
	md5 := ut.Itoa(this.area.GetIndex()) + "_" + ut.Itoa(this.area.GetCurHP()) + "_" + ut.Itoa(this.area.GetMaxHP())
	this.fightersMutex.RLock()
	for _, m := range this.fighters {
		md5 += "_" + m.MD5()
	}
	this.fightersMutex.RUnlock()
	return md5
}

// 添加韬略
func (this *FSPBattleController) AddCampStrategys(f g.IFighter) bool {
	if !f.IsHero() {
		return false
	}
	this.campStrategyMap.Lock()
	camp := f.GetCamp()
	strategyMap := this.campStrategyMap.Map[camp]
	if strategyMap == nil {
		strategyMap = map[string]*g.StrategyObj{}
	}
	strategys := f.GetEntity().GetPortrayal().GetStrategys()
	for _, strategy := range strategys {
		if strategy.Type == 50029 && this.camp == camp {
			continue // 这个韬略只能处于进攻方增加
		}
		targetType, targetValue := strategy.TargetType, strategy.TargetValue
		if targetType == 10 {
			targetType = 1
			targetValue = f.GetID()
		}
		key := ut.Itoa(strategy.Type) + "_" + ut.Itoa(targetType) + "_" + ut.Itoa(targetValue)
		if v := strategyMap[key]; v != nil {
			v.AddFighter(f)
		} else {
			strategyMap[key] = strategy.Clone(targetType, targetValue, f)
		}
	}
	this.campStrategyMap.Map[camp] = strategyMap
	this.campStrategyMap.Unlock()
	return true
}

// 删除韬略
func (this *FSPBattleController) RemoveCampStrategy(f g.IFighter) bool {
	if !f.IsHero() {
		return false
	}
	camp := f.GetCamp()
	strategyMap := this.campStrategyMap.Get(camp)
	if len(strategyMap) == 0 {
		return false
	}
	uid, ok := f.GetUID(), false
	for k, m := range strategyMap {
		if m.RemoveFighter(uid) {
			ok = true
			if len(m.GetFighters()) == 0 {
				delete(strategyMap, k)
			}
		}
	}
	return ok
}

func (this *FSPBattleController) GetCampStrategyMap(camp int32) map[string]*g.StrategyObj {
	return this.campStrategyMap.Get(camp)
}

func (this *FSPBattleController) GetHeroFighters(skiilId, camp int32, ignoreUid string) []g.IFighter {
	obj, arr := this.campStrategyMap.Get(camp), []g.IFighter{}
	for _, o := range obj {
		for _, m := range o.Fighters {
			if m.GetPortrayalSkillID() == skiilId && m.GetUID() != ignoreUid {
				arr = append(arr, m)
			}
		}
	}
	return arr
}

func (this *FSPBattleController) GetMainDoors() []g.IFighter    { return this.mainDoors }
func (this *FSPBattleController) GetMainCamp() int32            { return this.camp }
func (this *FSPBattleController) GetFighters() []g.IFighter     { return this.fighters }
func (this *FSPBattleController) GetRandom() *random.Random     { return this.random }
func (this *FSPBattleController) GetAreaSize() *ut.Vec2         { return this.area.GetAreaSize() }
func (this *FSPBattleController) GetAreaMainPoints() []*ut.Vec2 { return this.area.GetMainPoints() }

// 获取士兵
func (this *FSPBattleController) GetFighter(uid string) g.IFighter {
	if uid == "" {
		return nil
	}
	this.fightersMutex.RLock()
	defer this.fightersMutex.RUnlock()
	for _, m := range this.fighters {
		if m.GetUID() == uid && !m.IsDie() {
			return m
		}
	}
	for _, m := range this.mainDoors {
		if m.GetUID() == uid && !m.IsDie() {
			return m
		}
	}
	return nil
}

// 获取某个阵营的士兵数量
func (this *FSPBattleController) GetFighterCountByCamp(camp int32) int32 {
	this.fightersMutex.RLock()
	defer this.fightersMutex.RUnlock()
	return int32(len(array.Filter(this.fighters, func(m g.IFighter, _ int) bool { return m.IsPawn() && m.GetCamp() == camp })))
}

// 获取某个点的所有士兵
func (this *FSPBattleController) GetFightersByPoint(x, y int32) []g.IFighter {
	this.fightersMutex.RLock()
	defer this.fightersMutex.RUnlock()
	return array.Filter(this.fighters, func(m g.IFighter, _ int) bool { return m.GetPoint().Equal2(x, y) })
}

func (this *FSPBattleController) GetFighterPointInfos(key string) []BaseFighter {
	if arr := this.fighterPointMap[key]; arr != nil {
		return array.Filter(arr, func(m BaseFighter, _ int) bool { return m.PawnType != constant.PAWN_TYPE_NONCOMBAT })
	}
	return nil
}

func (this *FSPBattleController) GetFighterCountByPoint(point *ut.Vec2) int32 {
	if arr := this.GetFighterPointInfos(point.ID()); arr != nil {
		return int32(len(arr))
	}
	return 0
}

// 获取某个点位的士兵数量
func (this *FSPBattleController) GetFighterCountForCampByPoint(point *ut.Vec2, camp int32, reverse bool) int32 {
	if arr := this.fighterPointMap[point.ID()]; arr != nil {
		return int32(len(array.Filter(arr, func(m BaseFighter, _ int) bool {
			return ut.If(reverse, m.Camp != camp, m.Camp == camp) && m.PawnType != constant.PAWN_TYPE_NONCOMBAT
		})))
	}
	return 0
}

// 检测是否在战斗区域内
func (this *FSPBattleController) CheckIsBattleArea(x, y int32) bool {
	return this.area.CheckIsBattleArea(x, y)
}

// 检测是否有士兵
func (this *FSPBattleController) CheckHasFighter(x, y int32) bool {
	arr := this.GetFighterPointInfos(ut.Itoa(x) + "_" + ut.Itoa(y))
	return len(arr) > 0
}

// 检测是否有某个士兵 根据id
func (this *FSPBattleController) CheckHasFighterById(x, y, id int32) bool {
	arr := this.fighterPointMap[ut.Itoa(x)+"_"+ut.Itoa(y)]
	return arr != nil && array.Some(arr, func(m BaseFighter) bool { return m.ID == id })
}

// 检测是否可经过
func (this *FSPBattleController) CheckHasPass(x, y, camp int32) bool {
	if !this.CheckIsBattleArea(x, y) {
		return false
	}
	arr := this.GetFighterPointInfos(ut.Itoa(x) + "_" + ut.Itoa(y))
	if len(arr) == 0 {
		return true
	} else if camp != 0 {
		return !array.Some(arr, func(m BaseFighter) bool { return m.Camp != camp })
	}
	return false
}

func (this *FSPBattleController) CheckHasPassToState(x, y, camp int32) int32 {
	if !this.CheckIsBattleArea(x, y) {
		return 0
	}
	arr := this.GetFighterPointInfos(ut.Itoa(x) + "_" + ut.Itoa(y))
	if len(arr) == 0 {
		return 1
	} else if camp != 0 {
		return ut.If(!array.Some(arr, func(m BaseFighter) bool { return m.Camp != camp }), int32(2), 0)
	}
	return 0
}

// 是否孤立
func (this *FSPBattleController) IsIsolate(f g.IFighter) bool {
	points, camp, uid := f.GetSearchRange().Search(f.GetPoint(), 1, true), f.GetCamp(), f.GetUID()
	return !array.Some(points, func(point *ut.Vec2) bool {
		arr := this.GetFighterPointInfos(point.ID())
		if len(arr) == 0 {
			return false
		}
		return array.Some(arr, func(m BaseFighter) bool { return m.UID != uid && m.Camp == camp })
	})
}

// 搜索击飞点
func (this *FSPBattleController) SearchDiaupPoint(point *ut.Vec2, points []*ut.Vec2) (*ut.Vec2, bool) {
	return this.searchPoint.Search(point, points, 0)
}

func (this *FSPBattleController) SearchIdlePoint(point *ut.Vec2, rang int32) (*ut.Vec2, bool) {
	return this.searchPoint.Search(point, []*ut.Vec2{}, rang)
}

// 检测冲撞点
func (this *FSPBattleController) CheckDashPoint(point *ut.Vec2, camp int32) bool {
	if this.area.CheckIsBuildArea(point.X, point.Y) {
		return false // 建筑区域不可停留
	}
	arr := this.GetFighterPointInfos(point.ID())
	if len(arr) == 0 {
		return true // 表示没有人 可以停留
	}
	data := arr[0]
	return data.Camp != camp && data.PawnType < constant.PAWN_TYPE_BUILD
}

// 删除有位置的点 从后面开始删
func (this *FSPBattleController) RemoveHasFighterPointByLast(paths []*ut.Vec2) []*ut.Vec2 {
	for i := len(paths) - 1; i >= 0; i-- {
		p := paths[i]
		if this.CheckHasFighter(p.X, p.Y) {
			paths = append(paths[:i], paths[i+1:]...) // 从后往后看是否有人 如果有删除
		} else {
			break
		}
	}
	return paths
}

// 通过buff修正攻击力
func (this *FSPBattleController) amendAttackByBuff(fighter g.IFighter, tp, damage int32, reverse bool, addType int32, ignoreReduction float64) int32 {
	if damage < 0 {
		return damage
	}
	if buff := fighter.CheckTriggerBuff(tp); buff != nil {
		if addType == 1 {
			damage += buff.GetValueInt()
		} else {
			bv := buff.Value
			if ignoreReduction != 0 {
				bv *= ignoreReduction
			}
			val := ut.If(reverse, 1-bv, bv)
			damage = ut.RoundInt32(float64(damage) * val)
		}
	}
	return damage
}

// 检测buff闪避
func (this *FSPBattleController) checkBuffDodge(dodgeOdds int32, attacker, defender g.IFighter, ignoreDodge float64) bool {
	addOdds := defender.GetStrategyValue(50002)
	if defender.GetPortrayalSkillID() == hero.ZHAO_YUN {
		addOdds += 10 // 赵云初始10
	}
	defenderEntity := defender.GetEntity()
	if defenderEntity == nil {
		return false
	}
	buffs, oddsArr := defenderEntity.GetBuffs(), []int32{dodgeOdds}
	defenderEntity.BuffsRLock()
	for _, m := range buffs {
		if m.Type == bufftype.DODGE { // 闪避
			oddsArr = append(oddsArr, int32(m.Value))
		} else if m.Type == bufftype.VALOR { // 李嗣业 勇猛
			if skill := defender.CheckPortrayalSkill(hero.LI_SIYE); skill != nil {
				oddsArr = append(oddsArr, int32(m.Value*skill.GetParamsFloat64()))
			}
		} else if m.Type == bufftype.ROYAL_BLUE_DODGE { // 赵云 龙胆 闪避
			addOdds += int32(m.Value)
		}
	}
	defenderEntity.BuffsRUnlock()
	// 被动技能 躲闪
	if evade := defender.GetSkillByType(constant.PAWN_SKILL_TYPE_EVADE); evade != nil {
		oddsArr = append(oddsArr, int32(evade.GetValue()))
	}
	if attacker.IsTower() {
		// 先登 闪避
		if defender.IsHasBuff(bufftype.PRESTAGE) {
			oddsArr = append(oddsArr, 80)
		}
		// 韬略
		if v := defender.GetStrategyValue(50023); v > 0 {
			oddsArr = append(oddsArr, v)
		}
	}
	// 防守方为连弩兵且有专属装备 则有闪避效果
	if skill := defender.GetSkillByType(207); skill != nil && skill.GetIntensifyType() == 1 {
		// 受到攻击伤害时，有30%几率闪避该次伤害，与攻击者的距离每远一格，几率就减少10%
		dis := helper.GetPointToPointDis(attacker.GetPoint(), defender.GetPoint())
		v := ut.MaxInt32(0, 30-ut.MaxInt32(0, dis-1)*10)
		if v > 0 {
			oddsArr = append(oddsArr, v)
		}
	}
	for i, l := 0, len(oddsArr); i < l; i++ {
		odds := ut.RoundInt32(float64(oddsArr[i]+addOdds) * ignoreDodge)
		if this.random.ChanceInt32(odds) {
			return true
		}
	}
	return false
}

// 受击基础攻击真实伤害
func (this *FSPBattleController) OnHitBaseTrueDamage(attacker g.IFighter, defender g.IFighter, data map[string]interface{}) (int32, int32, int32) {
	trueDamage := this.GetBaseAttackDamage(attacker, defender, data)
	_, damage := defender.HitPrepDamageHandle(0, trueDamage)
	v, heal, hitShield := defender.OnHit(damage, damage)
	// 是否处理后续
	if data != nil && ut.Bool(data["doAfter"]) {
		this.DoAttackAfter(attacker, defender, map[string]interface{}{
			"attackSegments": 1,
			"sumDamage":      damage,
			"actDamage":      v,
			"trueDamage":     trueDamage,
			"hitShield":      hitShield,
		})
	}
	// 记录数据
	this.AddFighterBattleDamageInfo(attacker.GetUID(), defender, v)
	return v, heal, hitShield
}

// 获取基础的攻击伤害
func (this *FSPBattleController) GetBaseAttackDamage(attacker g.IFighter, defender g.IFighter, data map[string]interface{}) int32 {
	damage := attacker.GetAttack()
	defenderPawnType := defender.GetPawnType()
	isBuild := defenderPawnType == constant.PAWN_TYPE_BUILD
	if data == nil {
		data = map[string]interface{}{}
	}
	// 如果是建筑 伤害默认为1
	if isBuild {
		damage = 1
	} else if instabilityAttackIndex := ut.Int32(data["instabilityAttackIndex"]); instabilityAttackIndex > 0 && attacker.GetID() == 3104 {
		damage = attacker.GetInstabilityRandomAttack(instabilityAttackIndex) // 不稳定攻击
	} else if initDamage := ut.Int32(data["initDamage"]); initDamage > 0 {
		damage = initDamage // 强制初始伤害
	} else {
		damage = attacker.AmendAttack(damage)
	}
	// 攻击方是否有克制技能
	if ar := attacker.GetSkillByType(constant.PAWN_SKILL_TYPE_ATTACK_RESTRAIN); ar != nil && ar.GetTarget() == defenderPawnType {
		damage = ut.RoundInt32(float64(damage) * attacker.AmendRestrainValue(ar.GetValue(), constant.PAWN_SKILL_TYPE_ATTACK_RESTRAIN))
	}
	// 狩猎
	if ar := attacker.GetSkillByType(constant.PAWN_SKILL_TYPE_RESTRAIN_BEAST); ar != nil && ar.GetTarget() == defenderPawnType {
		damage = ut.RoundInt32(float64(damage) * ar.GetValue())
	}
	// 防守方是否有克制技能
	if dr := defender.GetSkillByType(constant.PAWN_SKILL_TYPE_DEFENSE_RESTRAIN); dr != nil && dr.GetTarget() == attacker.GetPawnType() {
		damage = ut.RoundInt32(float64(damage) * defender.AmendRestrainValue(dr.GetValue(), constant.PAWN_SKILL_TYPE_DEFENSE_RESTRAIN))
	}
	if !isBuild {
		// 攻击力修正
		if attackAmend := ut.Float64(data["attackAmend"]); attackAmend > 0 {
			damage = ut.RoundInt32(float64(damage) * attackAmend)
		}
		// 长剑兵攻击流血目标 攻击力提高30%
		if attacker.GetEntity().GetID() == 3105 {
			if s := attacker.GetSkillByType(216); s != nil && s.GetIntensifyType() == 1 && defender.IsHasBuff(bufftype.BLEED) {
				damage = ut.RoundInt32(float64(damage) * 1.3)
			}
		}
		// 弓骑兵攻击近战时 攻击力提高30%
		if attacker.GetEntity().GetID() == 3405 && defender.GetAttackRange() <= 1 {
			if s := attacker.GetSkillByType(217); s != nil && s.GetIntensifyType() == 1 {
				damage = ut.RoundInt32(float64(damage) * 1.3)
			}
		}
		// 韬略 猎人攻击山贼
		if sv := attacker.GetStrategyValue(31301); sv > 0 && defenderPawnType == constant.PAWN_TYPE_CATERAN {
			damage += ut.RoundInt32(float64(damage) * float64(sv) * 0.01)
		}
	}
	return damage
}

// 获取攻击伤害
func (this *FSPBattleController) GetAttackDamage(attacker g.IFighter, defender g.IFighter, data map[string]interface{}) (int32, int32, int32, bool) {
	damage, trueDamage, baseDamage, isCrit := attacker.GetAttack(), int32(0), int32(0), false
	defenderPawnType := defender.GetPawnType()
	isBuild := defenderPawnType == constant.PAWN_TYPE_BUILD
	// 最终修正伤害
	attackLastDamageAmend, attackLastDamageLow, ignoreReduction := 1.0, 0.0, 0.0
	if data == nil {
		data = map[string]interface{}{}
	}
	isBaseAttack := ut.String(data["attackType"]) == "attack"
	if lda := ut.Float64(data["lastDamageAmend"]); lda > 0 {
		attackLastDamageAmend = lda
	}
	// ---------------------------------处理一级攻击力---------------------------------------- 克制
	// 如果是建筑 伤害默认为1
	if isBuild {
		damage = 1
	} else if instabilityAttackIndex := ut.Int32(data["instabilityAttackIndex"]); instabilityAttackIndex > 0 && attacker.GetID() == 3104 {
		damage = attacker.GetInstabilityRandomAttack(instabilityAttackIndex) // 不稳定攻击
	} else if initDamage := ut.Int32(data["initDamage"]); initDamage > 0 {
		damage = initDamage // 强制初始伤害
	} else {
		damage = attacker.AmendAttack(damage)
	}
	// 攻击方是否有克制技能
	if ar := attacker.GetSkillByType(constant.PAWN_SKILL_TYPE_ATTACK_RESTRAIN); ar != nil && ar.GetTarget() == defenderPawnType {
		damage = ut.RoundInt32(float64(damage) * attacker.AmendRestrainValue(ar.GetValue(), constant.PAWN_SKILL_TYPE_ATTACK_RESTRAIN))
		ignoreReduction += ar.GetParamsFloat64()
	}
	// 狩猎
	if ar := attacker.GetSkillByType(constant.PAWN_SKILL_TYPE_RESTRAIN_BEAST); ar != nil && ar.GetTarget() == defenderPawnType {
		damage = ut.RoundInt32(float64(damage) * ar.GetValue())
	}
	// 防守方是否有克制技能
	if dr := defender.GetSkillByType(constant.PAWN_SKILL_TYPE_DEFENSE_RESTRAIN); dr != nil && dr.GetTarget() == attacker.GetPawnType() {
		damage = ut.RoundInt32(float64(damage) * defender.AmendRestrainValue(dr.GetValue(), constant.PAWN_SKILL_TYPE_DEFENSE_RESTRAIN))
	}
	if !isBuild {
		// 攻击力修正 ----------------------------------------
		if attackAmend := ut.Float64(data["attackAmend"]); attackAmend > 0 {
			damage = ut.RoundInt32(float64(damage) * attackAmend)
		}
		// 长剑兵攻击流血目标 攻击力提高30%
		if attacker.GetID() == 3105 {
			if s := attacker.GetSkillByType(216); s != nil && s.GetIntensifyType() == 1 && defender.IsHasBuff(bufftype.BLEED) {
				damage = ut.RoundInt32(float64(damage) * 1.3)
			}
		}
		// 弓骑兵攻击近战时 攻击力提高30%
		if attacker.GetID() == 3405 && defender.GetAttackRange() <= 1 {
			if s := attacker.GetSkillByType(217); s != nil && s.GetIntensifyType() == 1 {
				damage = ut.RoundInt32(float64(damage) * 1.3)
			}
		}
		// 韬略 猎人攻击山贼
		if sv := attacker.GetStrategyValue(31301); sv > 0 && defenderPawnType == constant.PAWN_TYPE_CATERAN {
			damage += ut.RoundInt32(float64(damage) * float64(sv) * 0.01)
		}
		// 李牧 蓄势 攻击附带真实伤害
		if ANTICIPATION_ATTACK := attacker.GetBuff(bufftype.ANTICIPATION_ATTACK); ANTICIPATION_ATTACK != nil {
			trueDamage += ut.RoundInt32(float64(damage) * ANTICIPATION_ATTACK.Value * 0.01)
		}
		// 额外伤害 ------------------------------------------
		if getExtraDamgeFunc, ok := data["getExtraDamge"].(func() int32); ok {
			damage += getExtraDamgeFunc()
		}
		// 猩猩 狂怒
		if attacker.GetID() == 4114 && attacker.IsHasBuff(bufftype.RAGE) {
			damage = ut.RoundInt32(float64(damage) * 3)
		}
		// 韬略 当前生命值的伤害
		if sv := attacker.GetStrategyValue(40406); sv > 0 {
			damage += ut.RoundInt32(float64(defender.GetCurHp()) * float64(sv) * 0.01)
		}
		// 陈到 盾兵总攻击
		if heroSkill := attacker.CheckPortrayalSkill(hero.CHEN_DAO); heroSkill != nil { // 陈到额外伤害
			extraDamge, armyUid := 0.0, attacker.GetArmyUID()
			for _, m := range this.fighters {
				if m.GetArmyUID() != armyUid || m.GetPawnType() != constant.PAWN_TYPE_PELTAST || m.IsDie() {
					continue
				}
				extraDamge += float64(m.GetActAttack())
			}
			damage += ut.RoundInt32(extraDamge * heroSkill.GetParamsFloat64())
		}
		// 秦琼 斩将 造成额外伤害
		if isBaseAttack {
			if BEHEADED_GENERAL := attacker.CheckTriggerBuff(bufftype.BEHEADED_GENERAL); BEHEADED_GENERAL != nil {
				damage += ut.RoundInt32(float64(defender.GetMaxHp()) * BEHEADED_GENERAL.Value * 0.01)
			}
		}
		// 乐进 先登
		if heroSkill := attacker.CheckPortrayalSkill(hero.YUE_JIN); heroSkill != nil {
			damage += ut.RoundInt32(attacker.GetBuffValue(bufftype.PRESTAGE) * heroSkill.GetValue() * 0.01)
		}
		// 大黄蜂 额外造成当前生命
		if TELSON := attacker.GetSkillByType(constant.PAWN_SKILL_TYPE_TELSON); TELSON != nil {
			damage += ut.RoundInt32(float64(defender.GetCurHp()) * TELSON.GetValue())
		}
		// 连弩兵每第5次射击额外造成目标最大生命值2%的伤害
		if strategyBuff := attacker.GetStrategyBuff(31102); strategyBuff != nil {
			buff := attacker.GetBuffOrAdd(bufftype.ARBATOR_ATTACK_COUNT, attacker.GetUID())
			if buff.Value+1 >= strategyBuff.GetParams() {
				buff.Value = 0
				damage += ut.RoundInt32(float64(defender.GetMaxHp()) * strategyBuff.GetValue() * 0.01)
			} else {
				buff.Value += 1
			}
		}
		// 韬略 最大生命值的真实伤害
		if sv := attacker.GetStrategyValue(50030); sv > 0 {
			trueDamage += ut.RoundInt32(float64(defender.GetMaxHp()*sv) * 0.01)
		}
		// 政策 野怪克星
		attackLastDamageAmend += (attacker.GetBuffValue(bufftype.ADD_DMG_TO_MONSTER) * 0.01)
		// 韬略 加伤害
		attackLastDamageAmend += float64(attacker.GetStrategyValue(10001)) * 0.01
		attackLastDamageAmend += float64(attacker.GetStrategyValue(20005)) * 0.01
		// 韬略 对方是否有缴械
		if sv := attacker.GetStrategyValue(30301); sv > 0 && defender.IsHasBuff(bufftype.DESTROY_WEAPONS) {
			attackLastDamageAmend += float64(sv) * 0.01
		}
		// 韬略 生命值高于多少加伤害
		if sv := attacker.GetStrategyValue(50011); sv > 0 && defender.GetHPRatio() > 0.5 {
			attackLastDamageAmend += float64(sv) * 0.01
		}
		// 韬略 对方是否有中毒
		if sv := attacker.GetStrategyValue(31201); sv > 0 && defender.IsHasBuffs(bufftype.POISONING_MAX_HP, bufftype.POISONING_CUR_HP) {
			attackLastDamageAmend += float64(sv) * 0.01
		}
		// 韬略 对方是否有眩晕
		if sv := attacker.GetStrategyValue(31801); sv > 0 && defender.IsHasBuff(bufftype.DIZZINESS) {
			attackLastDamageAmend += float64(sv) * 0.01
		}
		// 韬略 攻击力比自身低
		if sv := attacker.GetStrategyValue(50025); sv > 0 && defender.GetActAttack() < attacker.GetActAttack() {
			attackLastDamageAmend += float64(sv) * 0.01
		}
		// 韬略 血越少伤害越高
		if sv := attacker.GetStrategyValue(40105); sv > 0 {
			attackLastDamageAmend += math.Floor((1.0-attacker.GetHPRatio())/0.1) * float64(sv) * 0.01
		}
		// 韬略 对方是否孤立
		if sv := attacker.GetStrategyValue(50016); sv > 0 && this.IsIsolate(defender) {
			attackLastDamageAmend += float64(sv) * 0.01
		}
		// 韬略 交战30分钟 伤害提高
		if sv := attacker.GetStrategyValue(50019); sv > 0 && this.area.GetBattleTime() >= ut.TIME_MINUTE*30 {
			attackLastDamageAmend += float64(sv) * 0.01
		}
		// 韬略 对英雄造成的伤害提高
		if sv := attacker.GetStrategyValue(50021); sv > 0 && defender.IsHero() {
			attackLastDamageAmend += float64(sv) * 0.01
		}
		// 韬略 敌方士兵数量大于我方时，我方全体士兵造成的伤害提高
		if sv := attacker.GetStrategyValue(50024); sv > 0 {
			if cnt := this.GetFighterCountByCamp(attacker.GetCamp()); cnt < int32(len(this.fighters))-cnt {
				attackLastDamageAmend += float64(sv) * 0.01
			}
		}
		// 韬略 四周没有友方增伤
		if sv := attacker.GetStrategyValue(50026); sv > 0 && this.IsIsolate(attacker) {
			attackLastDamageAmend += float64(sv) * 0.01
		}
		// 韬略 全体前20回合造成的伤害提高10%
		if buff := attacker.GetBuff(bufftype.RECORD_ROUND_ADD_DAMAGE); buff != nil && buff.Value > 0 {
			buff.Value -= 1
			attackLastDamageAmend += float64(attacker.GetStrategyValue(50032)) * 0.01
		}
		// 剑盾兵专属被动 伤害提高
		if RODELERO_ADD_ATTACK := attacker.CheckTriggerBuff(bufftype.RODELERO_ADD_ATTACK); RODELERO_ADD_ATTACK != nil {
			attackLastDamageAmend += RODELERO_ADD_ATTACK.Value * 0.01
		}
		// 斧骑兵 盾甲粉碎
		if SHIELD_CRUSHING := attacker.GetSkillByType(constant.PAWN_SKILL_TYPE_SHIELD_CRUSHING); SHIELD_CRUSHING != nil {
			// 有护盾伤害提高
			if defender.GetShieldVal() > 0 {
				attackLastDamageAmend += SHIELD_CRUSHING.GetValue()
			}
			// 专属的重击 伤害提高30%
			instabilityAttackIndex := ut.Int32(data["instabilityAttackIndex"])
			if instabilityAttackIndex%2 == 1 {
				attackLastDamageAmend += 0.3
			}
			// 程咬金
			if heroSkill := attacker.CheckPortrayalSkill(hero.CHENG_YAOJIN); heroSkill != nil {
				ii := instabilityAttackIndex / 2
				if ii == 1 { // 第1斧 加伤害
					attackLastDamageAmend += 0.5
				} else if ii == 2 { // 第3斧 加真实伤害
					trueDamage += ut.RoundInt32(float64(defender.GetMaxHp()-defender.GetCurHp()) * heroSkill.GetValue() * 0.01)
				}
			}
		}
		// 夏侯渊 奔袭
		if heroSkill := attacker.CheckPortrayalSkill(hero.XIA_HOUYUAN); heroSkill != nil {
			v := attacker.GetBuffValue(bufftype.LONG_RANGE_RAID) * heroSkill.GetParamsFloat64() * 0.01
			attackLastDamageAmend += v
		}
		// 赵云 龙胆 伤害
		if heroSkill := attacker.CheckPortrayalSkill(hero.ZHAO_YUN); heroSkill != nil {
			attackLastDamageAmend += attacker.GetBuffValue(bufftype.ROYAL_BLUE_DAMAGE) * 0.01
		}
		// 许褚 裸衣
		if heroSkill := attacker.CheckPortrayalSkill(hero.XU_CHU); heroSkill != nil {
			attackLastDamageAmend += attacker.GetBuffValue(bufftype.NAKED_CLOTHES)
		}
		// 吕蒙 三日后伤害提高
		if heroSkill := attacker.CheckPortrayalSkill(hero.LV_MENG); heroSkill != nil && int32(attacker.GetBuffValue(bufftype.CHECK_ABNEGATION)) >= heroSkill.Target {
			attackLastDamageAmend += heroSkill.GetValue() * 0.01
		}
		// 于禁 毅重
		attackLastDamageAmend += attacker.GetBuffValue(bufftype.RESOLUTE) * 0.01
		// 邓艾 屯垦令结束加伤害
		if attacker.IsHasBuff(bufftype.TONDEN_RECOVER) {
			attackLastDamageAmend += 0.1
		}
		// 廉颇 攻
		attackLastDamageAmend += attacker.GetBuffValue(bufftype.LIAN_PO_ATTACK) * 0.01
		// 霍去病 跳斩伤害
		if !isBaseAttack {
			if JUMPSLASH_DAMAGE := attacker.CheckTriggerBuff(bufftype.JUMPSLASH_DAMAGE); JUMPSLASH_DAMAGE != nil {
				attackLastDamageAmend += JUMPSLASH_DAMAGE.Value * 0.01
			}
		}
		// 高顺 陷阵 每损失1%生命，造成的伤害就提高2%
		if attacker.IsHasBuff(bufftype.BREAK_ENEMY_RANKS) {
			maxHp := attacker.GetMaxHp()
			v := float64(maxHp-attacker.GetCurHp()) / float64(maxHp)
			attackLastDamageAmend += v
		}
		// 孟获 再起 提高伤害
		if heroSkill := attacker.CheckPortrayalSkill(hero.MENG_HUO); heroSkill != nil {
			v := attacker.GetBuffValue(bufftype.RECURRENCE) * heroSkill.GetValue() * 0.01
			attackLastDamageAmend += v
		}
		// 攻击方被动暴击 目前暂时是野怪技能
		if critSkill := attacker.GetSkillByType(constant.PAWN_SKILL_TYPE_CRIT); critSkill != nil {
			if this.random.ChanceInt32(critSkill.GetTarget()) {
				isCrit = true
				damage = ut.RoundInt32(float64(damage) * critSkill.GetValue())
			} else if critSkill.GetTarget() == 99 {
				// 暴击概率为99%未暴击时 被攻击方触发成就
				this.area.GetWorld().TriggerTask(defender.GetEntity().GetOwner(), tctype.NOT_GET_CRIT, 1, 0)
			}
		}
	} else {
		// 政策 摧坚巧工
		v := attacker.GetBuffValue(bufftype.ADD_DMG_TO_BUILD)
		if v > 0 && this.random.ChanceInt32(int32(v)) {
			damage += 1
		}
		// 环境 建筑受到的伤害提高
		if ENVIRONMENT_BUILD_DMG := array.Find(this.environmentBuffs, func(m *g.BuffObj) bool {
			return m.Type == bufftype.ENVIRONMENT_BUILD_DMG
		}); ENVIRONMENT_BUILD_DMG != nil {
			damage += ENVIRONMENT_BUILD_DMG.GetValueInt()
		}
	}
	// ---------------------------------处理二级攻击力---------------------------------------- 装备效果
	attackerAttackRange := attacker.GetEntity().GetAttackRange()
	effects1, effects2 := attacker.GetEquipEffects(), defender.GetEquipEffects()
	executeRatio, infallibleRatio, ignoreDodge := 0.0, 0.0, 0.0
	// 最后结算伤害
	if !isBuild {
		// 攻击方装备效果
		for _, m := range effects1 {
			if m.Type == eeffect.BLACK_IRON_STAFF { // 玄铁杖
				// 目标身上是否有无法再触发的buff
				if !defender.IsHasBuff(bufftype.BLACK_IRON_STAFF_MARK) {
					defender.AddBuff(bufftype.BLACK_IRON_STAFF_MARK, attacker.GetUID(), 1) // 标记
					val := float64(defender.GetMaxHp()) * m.Value * 0.01
					trueDamage += ut.RoundInt32(val)
					// 添加护盾
					attacker.AddBuffValue(bufftype.BLACK_IRON_STAFF_SHIELD, attacker.GetUID(), math.Round(val*0.3))
				}
			} else if m.Type == eeffect.CUR_HP_DAMAGE { // 当前生命值的伤害
				damage += ut.RoundInt32(float64(defender.GetEntity().GetCurHP()) * m.Value * 0.01)
			} else if m.Type == eeffect.CRIT { // 暴击
				if this.random.ChanceInt32(m.Odds) {
					isCrit = true
					damage = ut.RoundInt32(float64(damage) * m.Value * 0.01)
				}
			} else if m.Type == eeffect.MAX_HP_TRUE_DMG { // 百分比真实伤害
				if this.random.ChanceInt32(m.Odds) {
					base := ut.If(attackerAttackRange > 1, int32(1), 2) // 近战+2 远程+1
					val := float64(base + attacker.GetLV())
					trueDamage += ut.RoundInt32(float64(defender.GetMaxHp()) * val * 0.01)
				}
			} else if m.Type == eeffect.BILLHOOK { // 钩镰
				// 对生命和攻击力都比自身高的目标 额外造成真实伤害
				attack := defender.GetActAttack()
				if defender.GetCurHp() > attacker.GetCurHp() && attack > attacker.GetActAttack() {
					trueDamage += ut.RoundInt32(float64(attack) * (m.Value * float64(attacker.GetLV())) * 0.01)
				}
			} else if m.Type == eeffect.SILVER_SNAKE_WHIP { // 银蛇鞭
				// 切换目标加伤害
				if !isBaseAttack {
				} else if buff := attacker.GetBuffOrAdd(bufftype.CHECK_SILVER_SNAKE_WHIP, ""); buff.Provider != defender.GetUID() {
					buff.Provider = defender.GetUID()
					attackLastDamageAmend += (m.Value * 0.01)
				}
			} else if m.Type == eeffect.DOUBLE_EDGED_SWORD { // 月牙刀
				attackLastDamageAmend += (m.Value * 0.01)
				ignoreReduction += 0.2 // 无视20%减伤
			} else if m.Type == eeffect.INFALLIBLE { // 转伤
				infallibleRatio = m.Value * 0.01
			} else if m.Type == eeffect.NOT_DODGE { // 无视闪避
				ignoreDodge += m.Value * 0.01
			} else if m.Type == eeffect.EXECUTE { // 处决
				executeRatio = m.Value * 0.01
			} else if m.Type == eeffect.HIT_GET_SUCK_BLOOD { // 受击获得吸血
				if buff := attacker.GetBuff(bufftype.HIT_SUCK_BLOOD); buff != nil && buff.Value >= 30 {
					attackLastDamageAmend += 0.3 // 达到30%吸血时下次攻击提高30%伤害
				}
			}
		}
		// 军旗伤害提升和降低
		DAMAGE_INCREASE, DAMAGE_REDUCE := attacker.GetBuff(bufftype.DAMAGE_INCREASE), attacker.GetBuff(bufftype.DAMAGE_REDUCE)
		if DAMAGE_INCREASE != nil && DAMAGE_REDUCE != nil {
			// 如果有2个 就抵消了
		} else if DAMAGE_INCREASE != nil {
			attackLastDamageAmend += DAMAGE_INCREASE.Value
		} else if DAMAGE_REDUCE != nil {
			attackLastDamageLow += DAMAGE_REDUCE.Value
		}
		// 不动如山
		if STILL_MOUNTAIN := defender.GetSkillByType(constant.PAWN_SKILL_TYPE_STILL_MOUNTAIN); STILL_MOUNTAIN != nil {
			attackLastDamageLow += STILL_MOUNTAIN.GetValue() // 0.7
		}
		if STILL_MOUNTAIN := attacker.GetSkillByType(constant.PAWN_SKILL_TYPE_STILL_MOUNTAIN); STILL_MOUNTAIN != nil {
			ignoreReduction += STILL_MOUNTAIN.GetValue() // 无视减伤
		}
		// 屯垦令开始时降低伤害
		if attacker.IsHasBuff(bufftype.TONDEN_BEGIN) {
			attackLastDamageLow += 0.2
		}
		attackLastDamageLow = math.Max(0, 1-attackLastDamageLow)
		// 最终伤害修正
		damage = ut.RoundInt32(float64(damage) * attackLastDamageAmend * attackLastDamageLow)
		// 获取真实伤害
		if getTrueDamageFunc, ok := data["getTrueDamage"].(func() int32); ok {
			trueDamage += getTrueDamageFunc()
		}
		// 真实伤害修正
		trueDamage = ut.RoundInt32(float64(trueDamage) * attackLastDamageAmend * attackLastDamageLow)
		// 杨妙真 识破 这个需要放到最后特殊处理 因为算的额外真实伤害
		if heroSkill := attacker.CheckPortrayalSkill(hero.YANG_MIAOZHEN); heroSkill != nil {
			if buff := defender.GetBuff(bufftype.PENETRATE); buff != nil {
				td := math.Round(float64(defender.GetMaxHp()) * heroSkill.GetParamsFloat64() * attackLastDamageLow)
				trueDamage += int32(td)
				buff.Value = math.Round(td * heroSkill.GetValue() * 0.01)
			}
		}
		// 转伤
		if infallibleRatio > 0 {
			infallibleTrueDamage := ut.RoundInt32(float64(damage) * infallibleRatio)
			trueDamage += infallibleTrueDamage
			damage -= infallibleTrueDamage
		}
		// 韬略 无视闪避
		ignoreDodge += float64(attacker.GetStrategyValue(40306)) * 0.01
	}
	baseDamage = damage + trueDamage
	// 格挡
	if buff := defender.CheckTriggerBuffOr(bufftype.PARRY, bufftype.PARRY_102, bufftype.PARRY_001); buff != nil {
		defender.ResetCDodgeCount() // 严格来说 不算连续闪避了
		return -2, 0, baseDamage, false
	} else if isBaseAttack { // 普通攻击
		if buff := defender.GetBuff(bufftype.TURNTHEBLADE); buff != nil && this.random.ChanceInt32(buff.GetValueInt()) {
			// // 典韦招架则反击 可以放到攻击后 并且需要判断多段攻击的时候处理
			// if skill := defender.CheckPortrayalSkill(hero.DIAN_WEI); skill != nil {
			// 	// 获取目标范围2格内的敌方目标
			// 	if arr := defender.GetCanAttackPawnByRange(defender.GetCanAttackFighters(), 2, 8, ""); len(arr) > 0 {
			// 		val := ut.RoundInt32(float64(defender.GetActAttack()) * (skill.GetValue() * 0.01))
			// 		for _, f := range arr {
			// 			_, val = f.HitPrepDamageHandle(0, val)
			// 			v, _, _ := f.OnHit(val, val)
			// 			// 记录数据
			// 			this.AddFighterBattleDamageInfo(defender.GetUID(), f, v)
			// 		}
			// 	}
			// }
			return -3, 0, baseDamage, false // 招架
		}
	} else { // 技能攻击
		if defender.CheckTriggerBuff(bufftype.WITHSTAND) != nil {
			return -4, 0, baseDamage, false // 抵挡技能攻击
		}
	}
	// ---------------------------------处理防守方减伤----------------------------------------
	if s := attacker.GetSkillByType(constant.PAWN_SKILL_TYPE_INSTABILITY_ATTACK); s != nil {
		ignoreReduction += s.GetParamsFloat64()
	}
	ignoreReduction = math.Max(0, 1-ignoreReduction)
	// 韬略 减伤 先算
	if damage > 0 && !isBuild {
		v := math.Round(float64(defender.GetStrategyValue(10001))*ignoreReduction) * 0.01
		damage = ut.RoundInt32(float64(damage) * (1 - v))
		v = math.Round(float64(defender.GetStrategyValue(20006))*ignoreReduction) * 0.01
		damage = ut.RoundInt32(float64(damage) * (1 - v))
		v = math.Round(float64(defender.GetStrategyValue(30401))*ignoreReduction) * 0.01
		if v > 0 && attacker.GetActAttack() > defender.GetActAttack() {
			damage = ut.RoundInt32(float64(damage) * (1 - v))
		}
		v1 := ut.RoundInt32(float64(defender.GetStrategyValue(40206)) * ignoreReduction)
		damage = ut.MaxInt32(0, damage-v1)
		// 韬略 四周没有友方减伤
		v = math.Round(float64(defender.GetStrategyValue(50020))*ignoreReduction) * 0.01
		if v > 0 && this.IsIsolate(defender) {
			damage = ut.RoundInt32(float64(damage) * (1 - v))
		}
		v = math.Round(float64(defender.GetStrategyValue(50033))*ignoreReduction) * 0.01
		damage = ut.RoundInt32(float64(damage) * (1 - v))
	}
	var FIXED_DAMAGE *g.EquipEffectObj = nil
	var SAME_CORPS_REDUCE_DMG *g.EquipEffectObj = nil
	var dodgeOdds int32
	for _, m := range effects2 {
		if m.Type == eeffect.REDUCTION_INJURY { // 减固伤
			value := 5 + defender.GetBuffValue(bufftype.HAUBERK_DEFENSE)
			v := ut.RoundInt32(value * ignoreReduction)
			damage = ut.MaxInt32(0, damage-v)
		} else if m.Type == eeffect.REDUCTION_RANGED { // 减少远程伤害
			if attackerAttackRange > 1 && damage > 0 {
				v := math.Round(m.Value*ignoreReduction) * 0.01
				if defender.GetHPRatio() < 0.5 { // 血量低于50 效果减半
					damage = ut.RoundInt32(float64(damage) * (1 - v*0.5))
				} else {
					damage = ut.RoundInt32(float64(damage) * (1 - v))
				}
			}
		} else if m.Type == eeffect.REDUCTION_MELEE { // 减少近战伤害
			if attackerAttackRange <= 1 && damage > 0 {
				v := math.Round(m.Value*ignoreReduction) * 0.01
				if defender.GetHPRatio() < 0.5 { // 血量低于50 效果翻倍
					damage = ut.RoundInt32(float64(damage) * (1 - v*2))
				} else {
					damage = ut.RoundInt32(float64(damage) * (1 - v))
				}
			}
		} else if m.Type == eeffect.THOUSAND_UMBRELLA { // 减伤伤害 千机伞
			val := ut.If(defender.GetBuffValue(bufftype.THOUSAND_UMBRELLA) == 1, m.Odds*2, m.Odds)
			v := math.Round(float64(val)*ignoreReduction) * 0.01
			damage = ut.RoundInt32(float64(damage) * (1 - v))
		} else if m.Type == eeffect.OBSIDIAN_ARMOR { // 黑曜铠
			if defender.IsHasBuff(bufftype.OBSIDIAN_ARMOR_DEFENSE) {
				v := math.Round(m.Value*ignoreReduction) * 0.01
				damage = ut.RoundInt32(float64(damage) * (1 - v))
			}
		} else if m.Type == eeffect.SAME_CORPS_REDUCE_DMG { // 受到同兵种的伤害递减
			SAME_CORPS_REDUCE_DMG = m
		} else if m.Type == eeffect.FIXED_DAMAGE { // 固定伤害
			FIXED_DAMAGE = m
		} else if m.Type == eeffect.DODGE { // 闪避
			dodgeOdds = m.Odds
		}
	}
	if !isBuild {
		// 是否闪避
		if this.checkBuffDodge(dodgeOdds, attacker, defender, 1-ignoreDodge) {
			// 赵云 龙胆 伤害
			if skill := defender.CheckPortrayalSkill(hero.ZHAO_YUN); skill != nil {
				buff := defender.GetBuffOrAdd(bufftype.ROYAL_BLUE_DAMAGE, defender.GetUID())
				buff.Value += float64(skill.Value)
			}
			if attacker.IsTower() {
				// 乐进 先登 闪避后加伤害
				if skill := defender.CheckPortrayalSkill(hero.YUE_JIN); skill != nil {
					if buff := defender.GetBuff(bufftype.PRESTAGE); buff != nil {
						buff.Value += float64(damage)
					}
				}
			}
			if owner := defender.GetEntity().GetOwner(); owner != "" {
				defender.AddCDodgeCount()
				if defender.GetCDodgeCount() == 5 { // 触发任务
					this.area.GetWorld().TriggerTask(owner, tctype.C_DODGE_FIVE_TIMES, 1, 0)
				}
			}
			return -1, 0, baseDamage, false
		} else {
			defender.ResetCDodgeCount()
		}
		if !defender.IsDie() {
			// 处决
			if executeRatio > 0 {
				// 先计算本次伤害是否能破护盾
				curActHp := defender.GetEntity().GetCurHP() + defender.GetShieldVal()
				if float64(curActHp)/float64(defender.GetEntity().GetMaxHP()) < executeRatio {
					damage = 0
					trueDamage = curActHp
					baseDamage = curActHp
					// 被处决加攻击力
					buff := attacker.GetBuffOrAdd(bufftype.ADD_EXECUTE_ATTACK, attacker.GetUID())
					buff.ChangeValue(1)
					return damage, trueDamage, baseDamage, false
				}
			}
			// 黄忠 处决
			if skill := attacker.CheckPortrayalSkill(hero.HUANG_ZHONG); skill != nil {
				// 先计算本次伤害是否能破护盾
				curActHp := defender.GetEntity().GetCurHP() + defender.GetShieldVal()
				ratio := float64(curActHp) / float64(defender.GetEntity().GetMaxHP())
				odds, executeRatio := int32(0), skill.GetParamsFloat64()
				if ratio < executeRatio*0.5 {
					odds = skill.Value * 2
				} else if ratio < executeRatio {
					odds = skill.Value
				}
				if this.random.ChanceInt32(odds) {
					damage = 0
					trueDamage = curActHp
					baseDamage = curActHp
					return damage, trueDamage, baseDamage, false
				}
			}
		}
	}
	// 普通伤害修正
	if damage > 0 && !isBuild {
		// 护心镜
		if SAME_CORPS_REDUCE_DMG != nil {
			buff := defender.GetBuff(bufftype.DAMAGE_DECREASE)
			if buff != nil {
				v := math.Round(buff.Value*ignoreReduction) * 0.01
				damage = ut.RoundInt32(float64(damage) * (1 - v))
			} else {
				buff = defender.AddBuff(bufftype.DAMAGE_DECREASE, "", 1)
			}
			if !strings.Contains(buff.Provider, attacker.GetUID()) {
				if buff.Provider != "" {
					buff.Provider += "|"
				}
				buff.Provider += attacker.GetUID()
				buff.SetValue(math.Min(SAME_CORPS_REDUCE_DMG.Value*3, buff.Value+3))
			}
		}
		// 防守方增益buff和技能 需要放到最后计算
		if attackerAttackRange > 1 {
			if dr := defender.GetSkillByType(constant.PAWN_SKILL_TYPE_REDUCTION_RANGED); dr != nil {
				damage = ut.RoundInt32(float64(damage) * (1 - dr.GetValue()*ignoreReduction)) // 被动技能 减少远程伤害
			}
		}
		// 鳞甲
		if dr := defender.GetSkillByType(constant.PAWN_SKILL_TYPE_REDUCTION); dr != nil {
			damage = ut.RoundInt32(float64(damage) * (1 - dr.GetValue()*ignoreReduction)) // 被动技能 减少伤害
		}
		// 立盾
		damage = this.amendAttackByBuff(defender, bufftype.STAND_SHIELD, damage, true, 0, ignoreReduction)
		// 姜维 智勇
		if skill := defender.CheckPortrayalSkill(hero.JIANG_WEI); skill != nil {
			if buff := defender.GetBuff(bufftype.WISDOM_COURAGE); buff != nil {
				v := buff.Value * skill.GetParamsFloat64() * 0.01
				damage = ut.RoundInt32(float64(damage) * (1 - v*ignoreReduction))
			}
		}
		// 张辽 突袭
		if skill := defender.CheckPortrayalSkill(hero.ZHANG_LIAO); skill != nil {
			if buff := defender.GetBuff(bufftype.ASSAULT); buff != nil {
				v := buff.Value * 0.01
				damage = ut.RoundInt32(float64(damage) * (1 - v*ignoreReduction))
			}
		}
		// 廉颇 守
		buffValue := defender.GetBuffValue(bufftype.LIAN_PO_DEFEND)
		if buffValue > 0 {
			v := buffValue * 0.01
			damage = ut.RoundInt32(float64(damage) * (1 - v*ignoreReduction))
		}
		// 梁红玉 擂鼓防护
		buffValue = defender.GetBuffValue(bufftype.THUNDERS_DEFENSE)
		if buffValue > 0 {
			v := buffValue * 0.01
			damage = ut.RoundInt32(float64(damage) * (1 - v*ignoreReduction))
		}
		// 辛弃疾 金戈
		if defender.IsHasBuff(bufftype.KERIAN) {
			damage = ut.RoundInt32(float64(damage) * (1 - 0.3*ignoreReduction))
		}
		// 李牧 蓄势防护
		if defender.IsHasBuff(bufftype.ANTICIPATION_DEFENSE) {
			damage = ut.RoundInt32(float64(damage) * (1 - 0.5*ignoreReduction))
		}
		// 周泰 为3格范围内承担伤害
		zts := this.GetHeroFighters(hero.ZHOU_TAI, defender.GetCamp(), defender.GetUID())
		for _, m := range zts {
			skill := m.GetPortrayalSkill()
			if helper.GetPointToPointDis(m.GetPoint(), defender.GetPoint()) <= skill.Target {
				r := skill.GetValue()
				transferDamage := ut.RoundInt32(float64(damage) * r * 0.01)
				transferTrueDamage := ut.RoundInt32(float64(trueDamage) * r * 0.01)
				damage = ut.MaxInt32(damage-transferDamage, 0)
				trueDamage = ut.MaxInt32(trueDamage-transferTrueDamage, 0)
				val := transferDamage + transferTrueDamage
				// 扣除伤害 不触发破甲
				v, _, _ := m.OnHit(val, val)
				// 记录数据
				this.AddFighterBattleDamageInfo(attacker.GetUID(), m, v)
			}
		}
		// 陈到 伤害转移
		if skill := defender.CheckPortrayalSkill(hero.CHEN_DAO); skill != nil {
			var peltast g.IFighter = nil
			var maxRatio float64 = 0
			arr, armyUid := this.GetFighters(), defender.GetEntity().GetArmyUid()
			for _, m := range arr {
				if m.GetEntity().GetArmyUid() != armyUid || m.GetPawnType() != constant.PAWN_TYPE_PELTAST || m.IsDie() {
					continue
				} else if ratio := m.GetHPRatio(); ratio > maxRatio {
					peltast = m
					maxRatio = ratio
				}
			}
			if peltast != nil {
				sv := skill.GetValue()
				transferDamage := ut.RoundInt32(float64(damage) * sv * 0.01)
				transferTrueDamage := ut.RoundInt32(float64(trueDamage) * sv * 0.01)
				damage = ut.MaxInt32(damage-transferDamage, 0)
				trueDamage = ut.MaxInt32(trueDamage-transferTrueDamage, 0)
				val := transferDamage + transferTrueDamage
				// 扣除伤害 不触发破甲
				v, _, _ := peltast.OnHit(val, val)
				// 记录数据
				this.AddFighterBattleDamageInfo(attacker.GetUID(), peltast, v)
			}
		}
		// 金甲 这里单独将 固定伤害放到最后
		if FIXED_DAMAGE != nil {
			if fd := int32(FIXED_DAMAGE.Value); damage > fd {
				s := damage - fd // 多余伤害
				damage = fd
				buff := defender.GetBuff(bufftype.DELAY_DEDUCT_HP)
				if buff == nil {
					buff = defender.AddBuff(bufftype.DELAY_DEDUCT_HP, defender.GetUID(), 1)
				}
				buff.ChangeValue(float64(s))
			}
		}
	}
	return damage, trueDamage, baseDamage, isCrit
}

// 造成伤害后
func (this *FSPBattleController) DoAttackAfter(attacker g.IFighter, defender g.IFighter, data map[string]interface{}) int32 {
	attackRange, camp := attacker.GetAttackRange(), attacker.GetCamp()
	attackerHeroSkill, defenderHeroSkill := attacker.GetPortrayalSkill(), defender.GetPortrayalSkill()
	isDie := defender.IsDie()
	actDamage, sumDamage, heal, lastDamageAmend := ut.Int32(data["actDamage"]), ut.Int32(data["sumDamage"]), int32(0), ut.Float64(data["lastDamageAmend"])
	trueDamage := ut.Int32(data["trueDamage"])
	var attackerHeal int32
	effects1, effects2 := attacker.GetEquipEffects(), defender.GetEquipEffects()
	attackHeal, damageHealRatio := ut.Int32(data["healVal"]), 0.0
	isBaseAttack := ut.String(data["attackType"]) == "attack"
	attackSegments := ut.Int32(data["attackSegments"])
	suckbloodShield := false
	if lastDamageAmend <= 0 {
		lastDamageAmend = 1
	}
	if defender.IsPawn() {
		// 攻击方装备效果
		for _, m := range effects1 {
			if m.Type == eeffect.LONGYUAN_SWORD { // 龙渊剑
				if this.random.ChanceInt32(25) {
					buff := attacker.GetBuffOrAdd(bufftype.LONGYUAN_SWORD_ATTACK, attacker.GetUID())
					buff.Value += 1
				}
			} else if m.Type == eeffect.BLOOD_RETURN { // 回血
				if actDamage > 0 && attackSegments == 0 {
					value := int32(m.Value)
					if attackRange > 2 {
						value = ut.RoundInt32(m.Value * 0.5)
					}
					attackHeal += value
					// 有几率给血量百分比最低的回血
					if this.random.ChanceInt32(40) {
						uid, point := attacker.GetUID(), attacker.GetPoint()
						var f g.IFighter = nil
						r := 0.0
						this.fightersMutex.RLock()
						for _, m := range this.fighters {
							if m.GetCamp() == camp && m.GetUID() != uid && !m.IsDie() && m.GetPawnType() < constant.PAWN_TYPE_MACHINE && helper.GetPointToPointDis(m.GetPoint(), point) <= 5 {
								if hr := m.GetHPRatio(); r == 0 || hr < r {
									r = hr
									f = m
								}
							}
						}
						this.fightersMutex.RUnlock()
						if f != nil {
							f.OnHeal(value, false)
							f.ChangeState(constant.PAWN_STATE_HEAL)
						}
					}
				}
			} else if m.Type == eeffect.SUCK_BLOOD { // 吸血
				damageHealRatio += m.Value
				suckbloodShield = true
			} else if m.Type == eeffect.ATTACK_GET_SHIELD { // 攻击获得护盾
				if actDamage > 0 {
					attacker.AddBuffValue(bufftype.ATTACK_SHIELD, attacker.GetUID(), math.Max(1, math.Round(float64(defender.GetEntity().GetMaxHP())*m.Value*0.01)))
				}
			} else if m.Type == eeffect.RANGE_ATTACK { // 范围攻击 乾坤刀
				if actDamage > 0 && this.random.ChanceInt32(m.Odds) {
					// 获取目标范围1格内的敌方目标
					arr := defender.GetCanAttackPawnByRange(attacker.GetCanAttackFighters(), 1, 4, defender.GetUID())
					if cnt := len(arr); cnt > 0 {
						for _, f := range arr {
							this.OnHitBaseTrueDamage(attacker, f, map[string]interface{}{
								"attackAmend":            m.Value * 0.01 * lastDamageAmend,
								"instabilityAttackIndex": data["instabilityAttackIndex"],
							})
						}
					}
				}
			} else if m.Type == eeffect.BURNING_HEART_RING && !isBaseAttack && !isDie { // 焚心戒
				if actDamage > 0 && this.random.ChanceInt32(m.Odds) {
					val, buff := 5*attacker.GetLV(), defender.GetBuff(bufftype.IGNITION)
					if buff != nil {
						val = ut.MaxInt32(val, buff.GetValueInt())
					}
					defender.AddBuffValue(bufftype.IGNITION, attacker.GetUID(), float64(val))
				}
			} else if m.Type == eeffect.SPIKY_BALL && isDie { // 尖刺球
				arr := defender.GetCanAttackPawnByRange(attacker.GetCanAttackFighters(), 2, 8, defender.GetUID())
				if cnt := len(arr); cnt > 0 {
					extraDamge := ut.MaxInt32(0, sumDamage-actDamage)
					for _, f := range arr {
						trueDamage := this.GetBaseAttackDamage(attacker, f, map[string]interface{}{"attackAmend": m.Value * 0.01})
						_, damage := f.HitPrepDamageHandle(0, trueDamage+extraDamge)
						v, _, _ := f.OnHit(damage, damage)
						// 记录数据
						this.AddFighterBattleDamageInfo(attacker.GetUID(), f, v)
					}
				}
			}
		}
		// 是否有 周瑜的 链索
		if WIRE_CHAIN := defender.GetBuff(bufftype.WIRE_CHAIN); WIRE_CHAIN != nil {
			// 找有链索的其他目标
			uid := defender.GetUID()
			arr := array.Filter(attacker.GetCanAttackFighters(), func(m g.IFighter, _ int) bool { return m.GetUID() != uid && m.IsHasBuff(bufftype.WIRE_CHAIN) })
			damageVal := ut.RoundInt32(float64(sumDamage) * WIRE_CHAIN.Value * 0.01)
			for _, m := range arr {
				_, val := m.HitPrepDamageHandle(0, damageVal)
				v, _, _ := m.OnHit(val, val)
				// 记录数据
				this.AddFighterBattleDamageInfo(WIRE_CHAIN.Provider, m, v)
			}
		}
		// 英雄攻击后
		if attackerHeroSkill != nil {
			// 关银屏 范围攻击
			if attackerHeroSkill.Id == hero.GUAN_YIN_PING && actDamage > 0 && !isBaseAttack {
				if attacker.IsHasBuff(bufftype.AROA) {
					if buff := attacker.GetBuff(bufftype.DAMAGE_SUPERPOSITION); buff != nil && buff.Value > 0 {
						attacker.RemoveBuff(bufftype.AROA)
						// 主要目标也要眩晕
						if !isDie {
							defender.AddBuff(bufftype.DIZZINESS, attacker.GetUID(), 1)
						}
						damageVal := ut.RoundInt32(buff.Value * (attackerHeroSkill.GetValue() * 0.01) * lastDamageAmend)
						arr := defender.GetCanAttackPawnByRange(attacker.GetCanAttackFighters(), 2, 4, defender.GetUID())
						for _, f := range arr {
							_, val := f.HitPrepDamageHandle(0, damageVal)
							v, _, _ := f.OnHit(val, val)
							// 添加buff
							f.AddBuff(bufftype.DIZZINESS, attacker.GetUID(), 1)
							// 记录数据
							this.AddFighterBattleDamageInfo(attacker.GetUID(), f, v)
						}
					}
				} else {
					attacker.AddBuff(bufftype.AROA, attacker.GetUID(), 1)
				}
			}
			// 杨妙真 识破恢复
			if attackerHeroSkill.Id == hero.YANG_MIAOZHEN {
				if buff := defender.GetBuff(bufftype.PENETRATE); buff != nil {
					attackHeal += buff.GetValueInt()
					buff.Value = 0
				}
			}
			// 辛弃疾 愁梦记录攻击次数
			if attackerHeroSkill.Id == hero.XIN_QIJI {
				buff := attacker.GetBuff(bufftype.SORROWFUL_DREAM)
				if buff == nil {
				} else if buff.Value >= float64(attackerHeroSkill.GetTarget())-1 {
					buff.Value = 0
					attacker.AddBuffValue(bufftype.KERIAN, attacker.GetUID(), attackerHeroSkill.GetValue())
				} else {
					buff.Value += 1
				}
			}
		}
		// 猩猩 狂怒
		if attacker.CheckTriggerBuff(bufftype.RAGE) != nil && sumDamage > 0 {
			arr := defender.GetCanAttackPawnByRange(attacker.GetCanAttackFighters(), 2, 8, defender.GetUID())
			if cnt := len(arr); cnt > 0 {
				extraDamge := ut.RoundInt32(float64(sumDamage) * 0.5)
				for _, f := range arr {
					_, damage := f.HitPrepDamageHandle(0, extraDamge)
					v, _, _ := f.OnHit(damage, damage)
					// 记录数据
					this.AddFighterBattleDamageInfo(attacker.GetUID(), f, v)
				}
			}
		}
		// 韬略 攻击回血
		attackHeal += attacker.GetStrategyValue(30102)
		// buff吸血
		if suckBloodBuff := attacker.GetBuff(bufftype.HIT_SUCK_BLOOD); suckBloodBuff != nil {
			damageHealRatio += suckBloodBuff.Value
		}
		// 战斧
		if LOW_HP_ADD_SUCKBLOOD := attacker.GetBuff(bufftype.LOW_HP_ADD_SUCKBLOOD); LOW_HP_ADD_SUCKBLOOD != nil {
			damageHealRatio += LOW_HP_ADD_SUCKBLOOD.Value
		}
		// 徐盛 庇护
		if PROTECTION_NIE := attacker.GetBuff(bufftype.PROTECTION_NIE); PROTECTION_NIE != nil {
			damageHealRatio += PROTECTION_NIE.Value
		}
		// 斧骑韬略
		if hitShield := ut.Int32(data["hitShield"]); hitShield > 0 {
			// 吸盾
			if v := math.Round(float64(hitShield) * float64(attacker.GetStrategyValue(32001)) * 0.01); v > 0 {
				buff := attacker.GetBuffOrAdd(bufftype.SUCK_SHIELD, attacker.GetUID())
				buff.Value = math.Round(v * (1 + float64(attacker.GetStrategyValue(30601))*0.01))
			}
			// 破盾 对周围目标造成伤害
			if defender.GetShieldVal() != 0 {
			} else if v := attacker.GetStrategyValue(32002); v > 0 {
				arr := defender.GetCanAttackPawnByRange(attacker.GetCanAttackFighters(), 1, 4, defender.GetUID())
				if cnt := len(arr); cnt > 0 {
					damageVal := ut.RoundInt32(float64(hitShield) * float64(v) * 0.01)
					for _, f := range arr {
						_, val := f.HitPrepDamageHandle(0, damageVal)
						v, _, _ := f.OnHit(val, val)
						// 记录数据
						this.AddFighterBattleDamageInfo(attacker.GetUID(), f, v)
					}
				}
			}
		}
	}
	// 攻击方回血
	if attacker.IsPawn() {
		if damageHealRatio > 0 && ut.Int32(data["noHeal"]) != 1 {
			attackHeal += ut.RoundInt32(float64(sumDamage) * damageHealRatio * 0.01)
		}
		if attackHeal > 0 {
			attackerHeal += attacker.OnHeal(attackHeal, suckbloodShield)
		}
	}
	// 剑盾加攻击力 击杀山贼头目
	if defender.IsDie() && defender.IsPawn() && defender.GetEntity().GetID() == 4205 {
		// 记录到玩家身上
		if skill := attacker.GetSkillByType(constant.PAWN_SKILL_TYPE_CADET); skill != nil {
			this.area.GetWorld().AddPlayerRodeleroCadet(attacker.GetEntity().GetOwner(), 1, int32(skill.GetValue()))
		}
	}
	// 曹操 给队友加士气
	if defender.IsPawn() && attackerHeroSkill != nil && attackerHeroSkill.Id == hero.CAO_CAO {
		uid := attacker.GetUID()
		arr := attacker.GetCanAttackRangeFighter(this.fighters, 3, 20, "", func(m g.IFighter) bool { return m.GetCamp() != camp || m.IsFlag() })
		for _, m := range arr {
			buff := m.GetBuffOrAdd(bufftype.MORALE, uid)
			if buff.Value < attackerHeroSkill.GetValue() {
				buff.Value += 1
				m.UpdateMaxHpRecord(m.GetMaxHp())
				v := m.OnHeal(attackerHeroSkill.GetParamsInt(), false) // 回复血量
				if m.GetUID() == uid {
					attackerHeal += v
				}
			}
		}
	}
	// 防守方装备效果
	if !defender.IsPawn() {
	} else if defender.IsDie() {
		// 陌刀专属：击杀下次最高值
		if INSTABILITY_ATTACK := attacker.GetSkillByType(constant.PAWN_SKILL_TYPE_INSTABILITY_ATTACK); INSTABILITY_ATTACK != nil && INSTABILITY_ATTACK.GetIntensifyType() == 1 && !attacker.IsHasBuff(bufftype.STEADY_ATTACK) {
			attacker.AddBuff(bufftype.STEADY_ATTACK, attacker.GetUID(), 1).Times = 1
		}
		// 韬略 击杀加攻击力
		if attacker.IsHasStrategys(40104, 40305, 40403) {
			buff := attacker.GetBuffOrAdd(bufftype.KILL_ADD_ATTACK, attacker.GetUID())
			buff.Value += 1
		}
		// 韬略 击杀后回怒
		if !isBaseAttack {
			attacker.AddAnger(attacker.GetStrategyValue(50022))
		}
	} else if actDamage > 0 { // 如果还活着
		// 徐晃 给目标添加破甲
		if attackerHeroSkill != nil && attackerHeroSkill.Id == hero.XU_HUANG {
			var lv int32
			buff := defender.GetBuff(bufftype.ARMOR_PENETRATION)
			if buff != nil {
				lv = buff.Lv
			}
			if lv < attackerHeroSkill.GetParamsInt() {
				buff = defender.AddBuff(bufftype.ARMOR_PENETRATION, attacker.GetUID(), lv+1)
				buff.Value = attackerHeroSkill.GetValue()
			}
		}
		// 检测技能受击效果
		// 剑盾 专属效果 每受到一次伤害增加3%伤害
		if actDamage <= 0 {
		} else if GRANAGE := defender.GetSkillByType(218); GRANAGE != nil && GRANAGE.GetIntensifyType() == 1 {
			buff := defender.GetBuff(bufftype.RODELERO_ADD_ATTACK)
			if buff == nil {
				buff = defender.AddBuff(bufftype.RODELERO_ADD_ATTACK, defender.GetUID(), 1)
			}
			buff.ChangeValue(3)
		}
		// 检测英雄技能受击效果
		if defenderHeroSkill == nil {
		} else if defenderHeroSkill.Id == hero.CAO_REN { // 曹仁 添加buff
			buff := defender.GetBuffOrAdd(bufftype.TOUGH, defender.GetUID())
			if buff.Value < defenderHeroSkill.GetValue() {
				buff.Value += 1
				heal += defender.OnHeal(defenderHeroSkill.Target, false)
			}
		} else if defenderHeroSkill.Id == hero.DIAN_WEI { // 典韦 添加buff
			buff := defender.GetBuffOrAdd(bufftype.COURAGEOUSLY, defender.GetUID())
			if buff.Value < defenderHeroSkill.GetValue() {
				buff.Value += 1
			}
		} else if defenderHeroSkill.Id == hero.ZHAO_YUN { // 赵云 龙胆 加闪避
			buff := defender.GetBuffOrAdd(bufftype.ROYAL_BLUE_DODGE, defender.GetUID())
			buff.Value += defenderHeroSkill.GetParamsFloat64()
		} else if defenderHeroSkill.Id == hero.MENG_HUO { // 孟获 再起 加伤害
			buff := defender.GetBuffOrAdd(bufftype.RECURRENCE, defender.GetUID())
			if buff.GetValueInt() < defenderHeroSkill.Target {
				buff.Value += 1
			} else {
				// 重新叠加
				buff.Value = 1
				// 回复生命
				v := ut.RoundInt32(float64(defender.GetMaxHp()-defender.GetCurHp()) * 0.1)
				heal += defender.OnHeal(v, false)
			}
		}
		// 韬略 受到真实伤害回血
		if trueDamage > 0 {
			v := ut.RoundInt32(float64(trueDamage) * float64(defender.GetStrategyValue(31601)) * 0.01)
			heal += defender.OnHeal(v, false) // 回复血量
		}
		// 检测装备受击效果
		for _, m := range effects2 {
			if m.Type == eeffect.REDUCTION_INJURY { // 锁子甲
				if this.random.ChanceInt32(m.Odds) {
					buff := defender.GetBuffOrAdd(bufftype.HAUBERK_DEFENSE, defender.GetUID())
					if buff.Value < math.Floor(float64(defender.GetMaxHp())*0.02) {
						buff.Value += 1
					}
				}
			} else if m.Type == eeffect.THE_INJURY { // 反伤
				if attacker.IsPawn() && this.random.ChanceInt32(m.Odds) {
					v := 5 + int32(math.Floor(float64(defender.GetMaxHp())*0.02))
					_, v = attacker.HitPrepDamageHandle(0, v)
					val, _, _ := attacker.OnHit(v, v)
					attacker.ChangeState(constant.PAWN_STATE_DEDUCT_HP)
					// 记录数据
					this.AddFighterBattleDamageInfo(defender.GetUID(), attacker, val)
				}
			} else if m.Type == eeffect.SAME_CORPS_REDUCE_DMG { // 受到不同目标的攻击时 伤害递减 达到30%回复10%血量
				if buff := defender.GetBuff(bufftype.DAMAGE_DECREASE); buff != nil && buff.Value >= 30 && !strings.Contains(buff.Provider, "x") {
					buff.Provider = "x|" + buff.Provider
					heal += defender.OnHeal(ut.RoundInt32(float64(defender.GetEntity().GetMaxHP())*0.1), false) // 回复血量
				}
			} else if m.Type == eeffect.HIT_GET_SUCK_BLOOD { // 受到获得吸血
				buff := defender.GetBuffOrAdd(bufftype.HIT_SUCK_BLOOD, defender.GetUID())
				if buff.Value < 60 {
					buff.Value = math.Min(buff.Value+m.Value, 60)
				}
			} else if m.Type == eeffect.BELT_BLOODRAGE { // 受击回怒
				if defender.IsHasAnger() && this.random.ChanceInt32(m.Odds) {
					defender.AddAnger(1)
				}
			}
		}
	}
	// 防守方反制效果
	if attacker.IsPawn() {
		// 反伤
		if THE_INJURY := defender.GetSkillByType(constant.PAWN_SKILL_TYPE_THE_INJURY); THE_INJURY != nil {
			if val := ut.RoundInt32(float64(actDamage) * THE_INJURY.GetValue()); val > 0 {
				_, val = attacker.HitPrepDamageHandle(0, val)
				val, _, _ = attacker.OnHit(val, val)
				attacker.ChangeState(constant.PAWN_STATE_DEDUCT_HP)
				// 记录数据
				this.AddFighterBattleDamageInfo(defender.GetUID(), attacker, val)
			}
		}
		// 受到有真实伤害 有几率使攻击者眩晕1回合
		if trueDamage > 0 {
			if DIZZY_HEART_DROP := defender.GetEquipEffectByType(eeffect.DIZZY_HEART_DROP); DIZZY_HEART_DROP != nil && this.random.ChanceInt32(DIZZY_HEART_DROP.Odds) {
				attacker.AddBuff(bufftype.DIZZINESS, defender.GetUID(), 1)
			}
		}
		// 瘟疫感染
		if INFECTION_PLAGUE := defender.GetSkillByType(constant.PAWN_SKILL_TYPE_INFECTION_PLAGUE); INFECTION_PLAGUE != nil {
			if defender.GetMinDis(attacker) <= INFECTION_PLAGUE.GetTarget() {
				buff := attacker.GetBuffOrAdd(bufftype.INFECTION_PLAGUE, defender.GetUID())
				buff.Value += INFECTION_PLAGUE.GetValue()
			}
		}
	}
	// 先登 归0
	if !defender.IsBuild() {
		if buff := attacker.GetBuff(bufftype.PRESTAGE); buff != nil {
			buff.SetValue(0)
		}
	}
	return heal
}

// 阵亡时处理
func (this *FSPBattleController) DoDieAfter(defender g.IFighter) {
	defenderUid, camp, point := defender.GetUID(), defender.GetCamp(), defender.GetPoint()
	// 阵亡时掉落军旗
	if GEN_FLAG_BY_DIE := defender.GetEquipEffectByType(eeffect.GEN_FLAG_BY_DIE); GEN_FLAG_BY_DIE != nil {
		point := defender.GetPoint()
		if !array.Some(this.GetFightersByPoint(point.X, point.Y), func(m g.IFighter) bool { return m.IsFlag() }) { // 该位置是否已经有军旗了
			val := int32(GEN_FLAG_BY_DIE.Value)
			pawn := this.area.CreateBuildFlagPawn("flag_"+defenderUid, []int32{val, val}, point, defender.GetEntity().GetOwner())
			fighter := new(Building).Init(pawn, defender.GetCamp(), 0, this)
			fighter.attackIndex = defender.GetAttackIndex()
			this.fightersMutex.Lock()
			this.fighters = append(this.fighters, fighter)
			// 根据出手顺序排序
			sort.SliceStable(this.fighters, func(i, j int) bool {
				return this.fighters[i].GetAttackIndex() < this.fighters[j].GetAttackIndex()
			})
			this.fightersMutex.Unlock()
			this.UpdateFighterPointMap()
		}
	}
	// 曹操
	isCaoCao := defender.CheckPortrayalSkill(hero.CAO_CAO) != nil
	// 高顺
	isGaoShun := defender.CheckPortrayalSkill(hero.GAO_SHUN) != nil
	// 于禁
	isYuJin := defender.CheckPortrayalSkill(hero.YU_JIN) != nil
	// 董卓
	isDongZhuo := defender.CheckPortrayalSkill(hero.DONG_ZHUO) != nil
	// 霍去病
	isHuoQuBing := defender.CheckPortrayalSkill(hero.HUO_QUBING) != nil
	// 宠物
	petMaster := defender.GetPetMaster()
	// 敌方阵亡加攻击 数量
	addDdieAttackFighters := []g.IFighter{}
	//
	for _, m := range this.fighters {
		uid := m.GetUID()
		if uid == defenderUid {
			continue
		} else if m.GetCamp() == camp {
			entity := m.GetEntity()
			// 死的是否曹操 删除士气
			if isCaoCao && m.RemoveBuffByProvider(bufftype.MORALE, defenderUid) {
				maxHp := entity.GetMaxHP()
				entity.SetCurHP(ut.MinInt32(entity.GetCurHP(), maxHp))
				m.UpdateMaxHpRecord(maxHp)
			}
			// 死的是否高顺 删除陷阵效果
			if isGaoShun && m.RemoveBuffByProvider(bufftype.BREAK_ENEMY_RANKS, defenderUid) {
				maxHp := entity.GetMaxHP()
				entity.SetCurHP(ut.MinInt32(entity.GetCurHP(), maxHp))
				m.UpdateMaxHpRecord(maxHp)
			}
			// 死的是于禁 删除毅重效果
			if isYuJin {
				m.RemoveBuffByProvider(bufftype.RESOLUTE, defenderUid)
			}
			// 死的是霍去病 删除回怒效果
			if isHuoQuBing {
				m.RemoveBuffByProvider(bufftype.CHECK_JUMPSLASH_ADD_ANGER, defenderUid)
			}
			// 死的是宠物 记录喂养次数
			if uid == petMaster {
				if val := defender.GetBuffValue(bufftype.FEED_INTENSIFY); val > 0 {
					if buff := m.GetBuffOrAdd(bufftype.FEED_INTENSIFY_RECORD, uid); val > buff.Value {
						buff.Value = val
					}
				}
			}
		} else {
			dis := helper.GetPointToPointDis(point, m.GetPoint())
			// 看周围是否有 李嗣业 ^^
			if heroSkill := m.CheckPortrayalSkill(hero.LI_SIYE); heroSkill != nil && dis <= m.GetAttackRange() {
				buff := m.GetBuffOrAdd(bufftype.VALOR, uid)
				buff.Value = math.Min(buff.Value+1, heroSkill.GetValue())
			}
			// 韬略 是否有加攻击
			if sb := m.GetStrategyBuff(50003); sb != nil && dis <= 1 && int32(m.GetBuffValue(bufftype.DDIE_ADD_ATTACK)) < sb.Params {
				addDdieAttackFighters = append(addDdieAttackFighters, m)
			}
			// 死的是董卓 删除暴虐效果
			if isDongZhuo {
				m.RemoveBuffByProvider(bufftype.TYRANNICAL, defenderUid)
			}
		}
	}
	// 鸩毒
	POISONED_WINE := defender.GetBuff(bufftype.POISONED_WINE)
	if POISONED_WINE != nil {
		camp := defender.GetCamp()
		arr := defender.GetCanAttackRangeFighter(this.GetFighters(), 2, 8, defenderUid, func(m g.IFighter) bool { return m.GetCamp() != camp || m.IsFlag() })
		if len(arr) > 0 {
			damage := ut.RoundInt32(POISONED_WINE.Value * float64(POISONED_WINE.Lv+9) * 0.01)
			for _, m := range arr {
				_, val := m.HitPrepDamageHandle(0, damage)
				v, _, _ := m.OnHit(val, val)
				// 记录数据
				this.AddFighterBattleDamageInfo(POISONED_WINE.Provider, m, v)
			}
			if this.currentFighter != nil {
				this.currentFighter.AddRoundEndDelayTime(500)
			}
		}
	}
	// 敌方阵亡 我方攻击力+1
	addDdieAttackFighterLen := len(addDdieAttackFighters)
	for addDdieAttackFighterLen > 4 {
		addDdieAttackFighterLen -= 1
		addDdieAttackFighters = array.RemoveByIndex(addDdieAttackFighters, this.random.Get(0, addDdieAttackFighterLen))
	}
	for _, m := range addDdieAttackFighters {
		m.GetBuffOrAdd(bufftype.DDIE_ADD_ATTACK, m.GetUID()).Value += 1
	}
}

// 造成伤害后触发任务
func (this *FSPBattleController) TriggerTaskAfterDamage(attacker g.IFighter, defender g.IFighter, damage int32, hpRatioBefore float64) {
	// 同等级秒杀秒杀任务触发
	if defender.IsDie() &&
		attacker.GetEntity() != nil &&
		defender.GetEntity() != nil &&
		defender.GetEntity().GetOwner() != "" &&
		hpRatioBefore >= 1 &&
		damage >= defender.GetEntity().GetMaxHP() &&
		attacker.GetLV() == defender.GetLV() {
		this.area.GetWorld().TriggerTask(attacker.GetEntity().GetOwner(), tctype.SAME_LEVEL_SECKILL, 1, 0)
	}
}

// 获取以指定点位为移动目标的fighter
func (this *FSPBattleController) GetLockMovePointFighter(point *ut.Vec2) *g.AttackMovePointLockInfo {
	lockInfo := this.lockedMovePointMap[point.ID()]
	// 锁定该点的单位不存在、死亡或者被强制位移后 移除锁定
	delLock := false
	if lockInfo != nil {
		if lockInfo.Fighter != nil {
			if lockInfo.Fighter.IsDie() || !lockInfo.MovePoint.Equals(lockInfo.Fighter.GetPoint()) {
				delLock = true
			} else if lockInfo.Fighter.GetAttackTarget() != nil && !lockInfo.Fighter.GetPoint().Equals(point) && lockInfo.Fighter.CheckInMyAttackRange(lockInfo.Fighter.GetAttackTarget()) {
				// 锁定该点的单位未移动到锁定点位且已有攻击目标 移除锁定
				delLock = true
			}
		} else {
			delLock = true
		}
	}
	if delLock {
		this.DelLockMovePointFighter(point)
		return nil
	}
	return lockInfo
}

// 设置以指定点位为移动目标的fighter
func (this *FSPBattleController) SetLockMovePointFighter(point *ut.Vec2, fighter g.IFighter, weight int64, movePoint *ut.Vec2) bool {
	this.lockedMovePointMutex.Lock()
	defer this.lockedMovePointMutex.Unlock()
	this.lockedMovePointMap[point.ID()] = &g.AttackMovePointLockInfo{
		Fighter:   fighter,
		Weight:    weight,
		MovePoint: movePoint,
	}
	// log.Info("setLockMovePointFighter fighterUid: %v, point: %v, weight: %v, movePoint: %v", fighter.GetUID(), point, weight, movePoint)
	return true
}

// 移除以指定点位为移动目标的fighter
func (this *FSPBattleController) DelLockMovePointFighter(point *ut.Vec2) bool {
	if point == nil {
		return false
	}
	this.lockedMovePointMutex.Lock()
	delete(this.lockedMovePointMap, point.ID())
	this.lockedMovePointMutex.Unlock()
	return true
}

// 获取弓骑兵骑射移动路径
func (this *FSPBattleController) GetBowmanMovePaths(attacker g.IFighter) (*ut.Vec2, string) {
	fighters := array.Filter(attacker.GetCanAttackFighters(), func(m g.IFighter, _ int) bool { return (m.IsPawn() || m.IsFlag()) && !m.IsDie() })
	if len(fighters) == 0 {
		return nil, ""
	}
	attackRange := attacker.GetAttackRange()
	// 获取待移动的点
	points := attacker.GetSearchRange().Search(attacker.GetPoint(), 1, false)
	target, weight := "", int32(0)
	var point *ut.Vec2 = nil
	for i, l := 0, len(points); i < l; i++ {
		np := points[i]
		if this.CheckHasFighter(np.X, np.Y) {
			continue
		}
		var minDis, tempW int32
		var tempTarget g.IFighter = nil
		for _, m := range fighters {
			dis := helper.GetPointToPointDis(m.GetPoint(), np)
			w := dis*10 + (9 - m.GetAttackSpeed())
			w = w*1000 + m.GetAttackIndex()
			if w < tempW || tempTarget == nil {
				tempW = w
				minDis = dis
				tempTarget = m
			}
		}
		if minDis > attackRange {
			continue
		}
		// let w = minDis <= attackRange ? 1 : 0
		// w = w * 1000 + minDis
		w := minDis
		if w > weight && tempTarget != nil {
			weight = w
			target = tempTarget.GetUID()
			point = np
		}
	}
	return point, target
}

// 添加宠物士兵
func (this *FSPBattleController) AddPetPawn(uid string, id, lv int32, target *ut.Vec2, camp int32, owner string) g.IFighter {
	point := target.Clone()
	pawn := this.area.CreatePetPawn(uid, id, lv, point, owner)
	_, fighters := this.AddFighters([]g.Pawn{pawn}, owner, false)
	return fighters[0]
}

// 添加非战斗单位
func (this *FSPBattleController) AddNoncombat(uid string, id, lv int32, target *ut.Vec2, camp int32, owner string, params map[string]interface{}) {
	point := target.Clone()
	pawn := this.area.CreateNoncombatPawn(uid, id, lv, point, owner, ut.Int32(params["dir"]))
	if hp := ut.Int32Array(params["hp"]); len(hp) == 2 {
		pawn.SetCurHP(hp[0])
		pawn.SetMaxHP(hp[1])
	}
	this.AddFighters([]g.Pawn{pawn}, owner, false)
}

// 删除非战斗单位
func (this *FSPBattleController) RemoveNoncombat(uid string) {
	for i := len(this.fighters) - 1; i >= 0; i-- {
		if f := this.fighters[i]; f.GetUID() == uid {
			this.fightersMutex.Lock()
			this.fighters = append(this.fighters[:i], this.fighters[i+1:]...)
			this.fightersMutex.Unlock()
			return
		}
	}
}

func (this *FSPBattleController) SetPlayBackFspModel(fsp *FSPPlayBack) {
	this.fspPlayBack = fsp
}

// 回放添加战斗者
func (this *FSPBattleController) PlayBackAddFighters(pawns []g.Pawn, owner string, camp int32) {
	this.frameMutex.Lock()
	defer this.frameMutex.Unlock()
	// 根据出手顺序排序
	sort.SliceStable(pawns, func(i, j int) bool {
		return pawns[i].GetAttackIndex() < pawns[j].GetAttackIndex()
	})
	// 添加到战斗者列表
	pawnMap := map[string]bool{}
	for _, m := range this.fighters {
		pawnMap[m.GetUID()] = true
	}
	fighters := []*g.FigherStrip{}
	// for _, pawn := range pawns {
	// 	if pawnMap[pawn.GetUID()] {
	// 		continue
	// 	}
	// 	pawnMap[pawn.GetUID()] = true
	// 	m := new(Fighter).Init(pawn, camp, 0, this)
	// 	m.attackIndex = this.getAccAttackIndex()
	// 	m.waitRound = 1 //都等待一回合
	// 	this.fighters = append(this.fighters, m)
	// 	fighters = append(fighters, m.Strip())
	// 	slg.Log(m.MD5())
	// }
	addFighters := []g.IFighter{}
	checkUpdateStrategyCampMap := map[int32]bool{}
	for _, pawn := range pawns {
		uid := pawn.GetUID()
		if pawnMap[uid] {
			continue
		}
		pawnMap[uid] = true
		var m g.IFighter = nil
		if strings.Contains(uid, "build_") {
			m = new(Building).Init(pawn, camp, 0, this)
		} else if strings.Contains(uid, "pet_") {
			m = new(Pet).Init(pawn, camp, 0, this)
		} else {
			m = new(Fighter).Init(pawn, camp, 0, this)
		}
		m.SetAttackIndex(this.getAccAttackIndex())
		addFighters = append(addFighters, m)
		fighters = append(fighters, m.Strip())
		// 添加韬略
		if this.AddCampStrategys(m) {
			checkUpdateStrategyCampMap[camp] = true
		}
		// slg.Log(m.MD5())
	}
	// 刷新韬略
	if len(checkUpdateStrategyCampMap) > 0 {
		for _, m := range this.fighters {
			if checkUpdateStrategyCampMap[m.GetCamp()] {
				m.UpdateStrategyEffect()
			}
		}
	}
	// 获取当前出手战斗者下标
	curFighterIndex := this.getCurFighterIndex()
	// 将新加入的战斗者放到当前出手的战斗者之前
	fightersList := append(this.fighters[:curFighterIndex], append(addFighters, this.fighters[curFighterIndex:]...)...)
	// 设置添加后的出手顺序
	for i := curFighterIndex + len(addFighters); i < len(fightersList); i++ {
		f := fightersList[i]
		f.SetAttackIndex(this.getAccAttackIndex())
	}
	this.fighters = fightersList
	for _, m := range this.fighters {
		m.CheckBattleBeginTrigger()
		m.UpdateStrategyEffect()
	}
	this.UpdateFighterPointMap()
}

// 回放设置战斗者
func (this *FSPBattleController) SetPlayBackFighters(buildPawnId, buildPawnLv int32, randSeed int, fightersCampMap map[string]int32) {
	// 删除正常Controller Init中添加的箭塔
	for i := len(this.fighters) - 1; i >= 0; i-- {
		f := this.fighters[i]
		if f.IsTower() {
			this.fighters = append(this.fighters[:i], this.fighters[i+1:]...)
		}
	}
	// 根据出手顺序排序
	sort.SliceStable(this.fighters, func(i, j int) bool {
		return this.fighters[i].GetTempAttackIndex() < this.fighters[j].GetTempAttackIndex()
	})
	this.accAttackIndex = 0
	for _, m := range this.fighters {
		m.SetAttackIndex(this.getAccAttackIndex())
		if camp, ok := fightersCampMap[m.GetUID()]; ok {
			m.SetCamp(camp)
		}
		// slg.Log(m.MD5())
	}
	this.mainDoors = []g.IFighter{}
	// 初始化城门
	if !this.area.IsBoss() {
		mainPoints := this.area.GetMainPoints()
		for _, p := range mainPoints {
			this.mainDoors = append(this.mainDoors, new(MainDoor).Init(this.area, this.camp, p))
			// 箭塔
			if buildPawnId > 0 {
				tower := new(Tower).Init(ut.ID(), buildPawnId, buildPawnLv, p, this.area, this.camp, this)
				this.fighters = append(this.fighters, tower)
				tower.SetAttackIndex(this.getAccAttackIndex())
			}
		}
	}
	// 初始化随机种子
	this.random = random.NewRandom(randSeed)
	// 刷新一下位置
	this.UpdateFighterPointMap()
	// 默认第一个开始
	this.currentFighter = this.fighters[0]
}

// 有效战斗检测
func (this *FSPBattleController) checkValidBattle() {
	if this.area.GetOwner() == "" {
		// 野地战斗不计入
		return
	}
	if this.isValidBattle {
		// 已经为有效战斗则不做判断
		return
	}
	this.isValidBattle = true
	// if len(this.fighters) < constant.VALID_BATTLE_PAWNS_MIN {
	// 	return
	// }
	// this.cmapMutex.RLock()
	// defer this.cmapMutex.RUnlock()
	// fighterCampMap := map[int]int{}
	// fighterCnt := 0
	// for _, fighter := range this.fighters {
	// 	if fighter.IsBuild() {
	// 		continue
	// 	}
	// 	if fighter.GetEntity().GetOwner() != "" {
	// 		fighterCnt++
	// 		if _, ok := fighterCampMap[fighter.GetCamp()]; !ok {
	// 			fighterCampMap[fighter.GetCamp()] = 1
	// 		} else {
	// 			fighterCampMap[fighter.GetCamp()]++
	// 		}
	// 		if len(fighterCampMap) >= 2 && fighterCnt >= constant.VALID_BATTLE_PAWNS_MIN {
	// 			// 双方至少1个士兵，以及总参战士兵数>=5
	// 			this.isValidBattle = true
	// 			return
	// 		}
	// 	}
	// }
}

// 归1
func (this *FSPBattleController) NormalizeVec2(point *ut.Vec2) *ut.Vec2 {
	// 如果是斜着的 随机一个方向
	if point.X == 0 && point.Y == 0 {
		if this.random.ChanceInt32(50) {
			point.X = ut.If(this.random.ChanceInt32(50), int32(1), -1)
		} else {
			point.Y = ut.If(this.random.ChanceInt32(50), int32(1), -1)
		}
	} else if point.X != 0 && point.Y != 0 {
		if this.random.ChanceInt32(50) {
			point.X = 0
		} else {
			point.Y = 0
		}
	}
	point.X = int32(ut.NormalizeNumber(int(point.X)))
	point.Y = int32(ut.NormalizeNumber(int(point.Y)))
	return point
}
