package world

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/bazaar"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/player"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/sasha-s/go-deadlock"
)

/**
运送相关
*/

type TransitList struct {
	deadlock.RWMutex
	List   []*Transit
	RbTree *ut.RbTreeContainer[*Transit]
}

func (this *Model) GetTransitsClone() []*Transit {
	this.Transits.RLock()
	defer this.Transits.RUnlock()
	return this.Transits.List[:]
}

func (this *Model) ToTransits(index int32) []map[string]interface{} {
	this.Transits.RLock()
	defer this.Transits.RUnlock()
	arr := []map[string]interface{}{}
	for _, m := range this.Transits.List {
		if m.StartIndex == index || m.TargetIndex == index {
			arr = append(arr, m.Strip())
		}
	}
	return arr
}

func (this *Model) ToTransitsPb(index int32) []*pb.TransitInfo {
	this.Transits.RLock()
	defer this.Transits.RUnlock()
	arr := []*pb.TransitInfo{}
	for _, m := range this.Transits.List {
		if m.StartIndex == index || m.TargetIndex == index {
			arr = append(arr, m.ToPb())
		}
	}
	return arr
}

// 添加商队
func (this *Model) AddTransitRes(tres *bazaar.TRes, target int32, tp int8) {
	sindex := this.GetPlayerMainIndex(tres.Owner)
	dis := this.GetToMapCellDis(sindex, target)
	// 获取时间
	time := float64(dis) * (float64(ut.TIME_HOUR) / constant.TRANSIT_TIME)
	// 是否加速
	time *= 1.0 - this.GetPlayerPolicyEffectFloatByUid(tres.Owner, effect.TRANSIT_CD)*0.01
	time /= float64(slg.GetTransitSpeedUp())
	transit := NewTransit(ut.ID()).Init(tres.Owner, tres.SellType, tres.SellCount, tres.BuyType, tres.BuyCount, sindex, target, int32(time))
	transit.Type = tp
	transit.MerchantCount = tres.MerchantCount
	// 加入列表
	this.Transits.Lock()
	this.Transits.List = append(this.Transits.List, transit)
	this.Transits.RbTree.AddElement(int(transit.StartTime+int64(transit.NeedTime)), transit)
	this.Transits.Unlock()
	// 通知运送
	this.PutNotifyQueue(constant.NQ_ADD_TRANSIT, &pb.OnUpdateWorldInfoNotify{Data_21: transit.ToPb()})
}

// 检测更新商人运送信息
func (this *Model) CheckUpdateTransit() {
	now := time.Now().UnixMilli()
	// 遍历之前先检测最快的运送是否结束
	this.Transits.RLock()
	minTreeNode := this.Transits.RbTree.FindMinElements()
	if minTreeNode == nil {
		this.Transits.RUnlock()
		return
	} else {
		minEndTime := ut.Int64(minTreeNode.Key)
		if minEndTime > now {
			// 结束时间最快的运送都没结束
			this.Transits.RUnlock()
			return
		}
	}
	this.Transits.RUnlock()

	this.Transits.Lock()
	for m := this.Transits.RbTree.FindMinElements(); m != nil; m = this.Transits.RbTree.FindMinElements() {
		endTime := ut.Int64(m.Key)
		if endTime > now { // 结束时间最快的运送都没完成
			break
		}
		this.Transits.RbTree.Remove(m.Key) // 先删除红黑树
		infoList := m.Value.([]*Transit)
		for _, info := range infoList {
			// 获取目的地玩家
			isUpdatePlayerDB := false
			i := array.FindIndex(this.Transits.List, func(m *Transit) bool { return m.Uid == info.Uid })
			tp := this.GetTempPlayerByIndex(info.TargetIndex)
			if tp == nil {
				this.Transits.List = append(this.Transits.List[:i], this.Transits.List[i+1:]...)
				// 目标位置的tempPlr不存在 移除运送信息并重置该商人状态
				ownerPlr := this.room.GetOnlinePlayerOrDB(info.Owner).(*player.Model)
				if ownerPlr != nil {
					if this.ResetMerchant(ownerPlr, info) {
						this.room.UpdatePlayerDB(ownerPlr)
					}
				}
				continue
			}
			plr, _ := this.room.GetOnlinePlayerOrDB(tp.Uid).(*player.Model)
			if plr == nil {
				this.Transits.List = append(this.Transits.List[:i], this.Transits.List[i+1:]...)
				// 目标位置的plr不存在 移除运送信息并重置该商人状态
				ownerPlr := this.room.GetOnlinePlayerOrDB(info.Owner).(*player.Model)
				if ownerPlr != nil {
					if this.ResetMerchant(ownerPlr, info) {
						this.room.UpdatePlayerDB(ownerPlr)
					}
				}
				continue
			}
			// 添加资源
			if info.GoodsCount > 0 {
				item := g.NewTypeObjNotId(info.GoodsType, info.GoodsCount)
				_, add := plr.ChangeCostByTypeObjOne(item, 1)
				if plr.IsOnline() {
					plr.PutNotifyQueue(constant.NQ_UPDATE_ITEMS, &pb.OnUpdatePlayerInfoNotify{Data_41: plr.ToItemByTypeObjsPb([]*g.TypeObj{item})})
				} else {
					isUpdatePlayerDB = true // 如果没在线 就保存到数据库
				}
				// 记录
				if info.Type == constant.BAZAAR_RECORD_TYPE_GIVE_TO || info.Type == constant.BAZAAR_RECORD_TYPE_BUY_TO || info.Type == constant.BAZAAR_RECORD_TYPE_BUY_BACK {
					var tplr *TempPlayer = nil
					if info.Type == constant.BAZAAR_RECORD_TYPE_BUY_BACK {
						tplr = this.GetTempPlayerByIndex(info.StartIndex)
					} else {
						tplr = this.GetTempPlayer(info.Owner)
					}
					if tplr != nil {
						this.room.GetRecord().AddBazaarRecord(info.Type, plr.Uid, tplr.Uid, map[string]interface{}{
							"resType":  info.GoodsType,
							"resCount": info.GoodsCount,
							"actCount": add,
						}, tplr.Nickname)
					}
				}
			}
			if plr.Uid == info.Owner {
				// 回来了 先删除
				this.Transits.List = append(this.Transits.List[:i], this.Transits.List[i+1:]...)
				isUpdatePlayerDB = this.ResetMerchant(plr, info)
			} else {
				// 否则就直接原路返回
				if info.Type == constant.BAZAAR_RECORD_TYPE_BUY_TO {
					info.Type = constant.BAZAAR_RECORD_TYPE_BUY_BACK
				}
				this.TransitBack(info, now)
			}
			if isUpdatePlayerDB {
				this.room.UpdatePlayerDB(plr)
			}
		}
	}
	this.Transits.Unlock()
}

// 返回商队
func (this *Model) TransitBack(m *Transit, time int64) {
	target := m.StartIndex
	m.StartIndex = m.TargetIndex
	m.TargetIndex = target
	m.StartTime = time
	// 带回
	m.GoodsType = m.BackGoodsType
	m.GoodsCount = m.BackGoodsCount
	m.BackGoodsCount = 0
	m.BackGoodsType = 0
	// 如果带回是空的 就加快3倍速度
	if m.GoodsCount == 0 {
		m.NeedTime = m.NeedTime / 3
	}
	this.Transits.RbTree.AddElement(int(m.StartTime+int64(m.NeedTime)), m)
	this.PutNotifyQueue(constant.NQ_ADD_TRANSIT, &pb.OnUpdateWorldInfoNotify{Data_21: m.ToPb()})
}

// 删除玩家所有商队
func (this *Model) RemovePlayerAllTransit(uid string) {
	this.Transits.Lock()
	defer this.Transits.Unlock()
	for i := len(this.Transits.List) - 1; i >= 0; i-- {
		if m := this.Transits.List[i]; m.Owner == uid {
			this.Transits.List = append(this.Transits.List[:i], this.Transits.List[i+1:]...)
			this.Transits.RbTree.RemoveElement(int(m.StartTime+int64(m.NeedTime)), m)
			this.PutNotifyQueue(constant.NQ_REMOVE_TRANSIT, &pb.OnUpdateWorldInfoNotify{Data_22: m.Uid})
		}
	}
}

// 获取玩家运输中商人数量
func (this *Model) GetPlayerTransitMerchantCount(uid string) int32 {
	var cnt int32
	this.Transits.RLock()
	defer this.Transits.RUnlock()
	for i := len(this.Transits.List) - 1; i >= 0; i-- {
		if m := this.Transits.List[i]; m.Owner == uid {
			cnt += m.MerchantCount
		}
	}
	return cnt
}

// 重置商人状态
func (this *Model) ResetMerchant(plr *player.Model, info *Transit) bool {
	isUpdatePlayerDB := false
	// 改变商人状态
	plr.ChangeMerchantState(constant.MS_TRANSIT, constant.MS_NONE, info.MerchantCount)
	if plr.IsOnline() {
		plr.PutNotifyQueue(constant.NQ_UPDATE_MERCHANT, &pb.OnUpdatePlayerInfoNotify{Data_20: plr.Merchants.ToPb()})
	} else {
		isUpdatePlayerDB = true // 如果没在线 需要保存到数据库
	}
	this.PutNotifyQueue(constant.NQ_REMOVE_TRANSIT, &pb.OnUpdateWorldInfoNotify{Data_22: info.Uid})
	return isUpdatePlayerDB
}
