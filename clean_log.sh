#!/bin/bash

# 检查传入的参数个数是否正确
if [ "$#" -ne 2 ]; then
  echo "Usage: $0 <disk_usage_threshold> <days_old>"
  exit 1
fi

# 获取传入的参数
disk_usage_threshold=$1
days_old=$2

# 指定要监控的目录
directory="/projects/slg-server/bin/logs"

# 指定要检查的文件系统
filesystem="/dev/vda1"

# 获取指定文件系统的磁盘使用百分比
disk_usage=$(df -h | grep -w "$filesystem" | awk '{print $5}' | sed 's/%//')

echo "Disk usage is  $disk_usage%."

# 检查磁盘使用是否超过阈值
if [ "$disk_usage" -gt "$disk_usage_threshold" ]; then
  echo "Disk usage is above $disk_usage_threshold%. Deleting files older than $days_old days in $directory."

  # 删除指定目录中比当前日期早指定天数的文件
  find "$directory" -type f -mtime +$days_old -exec rm -f {} \;

  echo "Files deleted."
else
  echo "Disk usage is below $disk_usage_threshold%. No files deleted."
fi