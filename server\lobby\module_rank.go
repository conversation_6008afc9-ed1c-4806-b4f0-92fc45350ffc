package lobby

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/common/config"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/gate"
)

func (this *Lobby) InitHDRank() {
	this.GetServer().RegisterGO("HD_GetUserRankScore", this.getUserRankScore)       // 获取用户评分
	this.GetServer().RegisterGO("HD_GetScoreRank", this.getScoreRank)               // 获取评分排行榜
	this.GetServer().RegisterGO("HD_RSExchangeCitySkin", this.rsExchangeCitySkin)   // 兑换城市皮肤
	this.GetServer().RegisterGO("HD_RSExchangePawnSkin", this.rsExchangePawnSkin)   // 兑换士兵皮肤
	this.GetServer().RegisterGO("HD_RSExchangeChatEmoji", this.rsExchangeChatEmoji) // 兑换聊天表情
	this.GetServer().RegisterGO("HD_RSExchangeHeadIcon", this.rsExchangeHeadIcon)   // 兑换头像
}

// 获取用户评分
func (this *Lobby) getUserRankScore(session gate.Session, msg *pb.LOBBY_HD_GETUSERRANKSCORE_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	uid := msg.GetUid()
	s2c := &pb.LOBBY_HD_GETUSERRANKSCORE_S2C{}
	if user.UID == uid {
		s2c.RankScore = user.RankScore
		s2c.AccTotalRankCount = user.AccTotalRankCounts[1]
	} else if other := GetUserByDBNotAdd(uid); other != nil {
		s2c.RankScore = other.RankScore
		s2c.AccTotalRankCount = other.AccTotalRankCounts[1]
	}
	return pb.ProtoMarshal(s2c)
}

// 获取评分排行榜
func (this *Lobby) getScoreRank(session gate.Session, msg *pb.LOBBY_HD_GETSCORERANK_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	list, no, me := GetScoreRanks(user.UID)
	return pb.ProtoMarshal(&pb.LOBBY_HD_GETSCORERANK_S2C{
		List: list,
		No:   int32(no),
		Me:   me,
	})
}

// 兑换城市皮肤
func (this *Lobby) rsExchangeCitySkin(session gate.Session, msg *pb.LOBBY_HD_RSEXCHANGECITYSKIN_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	json := config.GetJsonData("citySkin", id)
	if json == nil {
		return nil, ecode.SKIN_NOT_EXIST.String()
	} else if user.HasCitySkin(id) {
		return nil, ecode.PAWN_SKIN_EXIST.String()
	} else if ut.Int(json["cond"]) != 3 {
		return nil, ecode.NOT_EXCHANGE.String()
	}
	needCost := ut.Int32(json["rank_coin"])
	if user.ChangeRankCoin(-needCost) == -1 {
		return nil, ecode.RANK_COIN_NOT_ENOUGH.String()
	}
	// 添加到列表
	user.AddCitySkin(id)
	// 上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_rsExchangeCitySkin", map[string]interface{}{
		"city_skin_id":   id,
		"rank_coin_cost": needCost,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_RSEXCHANGECITYSKIN_S2C{
		UnlockCitySkinIds: user.ToUnlockCitySkinsToPb(),
		RankCoin:          user.RankCoin,
	})
}

// 兑换士兵皮肤
func (this *Lobby) rsExchangePawnSkin(session gate.Session, msg *pb.LOBBY_HD_RSEXCHANGEPAWNSKIN_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	json := config.GetJsonData("pawnSkin", id)
	if json == nil {
		return nil, ecode.SKIN_NOT_EXIST.String()
	} else if user.HasPawnSkin(id) {
		return nil, ecode.PAWN_SKIN_EXIST.String()
	} else if ut.Int(json["cond"]) != 3 {
		return nil, ecode.NOT_EXCHANGE.String()
	}
	needCost := ut.Int32(json["rank_coin"])
	if user.ChangeRankCoin(-needCost) == -1 {
		return nil, ecode.RANK_COIN_NOT_ENOUGH.String()
	}
	// 添加到列表
	user.AddPawnSkin(id)
	// 上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_rsExchangePawnSkin", map[string]interface{}{
		"pawn_skin_id":   id,
		"rank_coin_cost": needCost,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_RSEXCHANGEPAWNSKIN_S2C{
		UnlockPawnSkinIds: user.ToUnlockPawnSkinsPb(),
		RankCoin:          user.RankCoin,
	})
}

// 兑换聊天表情
func (this *Lobby) rsExchangeChatEmoji(session gate.Session, msg *pb.LOBBY_HD_RSEXCHANGECHATEMOJI_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	json := config.GetJsonData("chatEmoji", id)
	if json == nil {
		return nil, ecode.CHAT_EMOJI_NOT_EXIST.String()
	} else if user.CheckHasEmoji(id) {
		return nil, ecode.CHAT_EMOJI_EXIST.String()
	} else if ut.Int(json["cond"]) != 3 {
		return nil, ecode.NOT_EXCHANGE.String()
	}
	needCost := ut.Int32(json["rank_coin"])
	if user.ChangeRankCoin(-needCost) == -1 {
		return nil, ecode.RANK_COIN_NOT_ENOUGH.String()
	}
	// 添加到列表
	user.AddEmoji(id)
	// 上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_rsExchangeChatEmoji", map[string]interface{}{
		"chat_emoji_id":  id,
		"rank_coin_cost": needCost,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_RSEXCHANGECHATEMOJI_S2C{
		UnlockChatEmojiIds: user.EmojisToPb(),
		RankCoin:           user.RankCoin,
	})
}

// 兑换头像
func (this *Lobby) rsExchangeHeadIcon(session gate.Session, msg *pb.LOBBY_HD_RSEXCHANGEHEADICON_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	json := config.GetJsonData("headIcon", id)
	if json == nil {
		return nil, ecode.HEAD_ICON_NOT_EXIST.String()
	}
	needCost := ut.Int32(json["rank_coin"])
	icon := ut.String(json["icon"])
	if user.HasHeadIcon(icon) {
		return nil, ecode.HEAD_ICON_EXIST.String()
	} else if ut.Int(json["cond"]) != 3 {
		return nil, ecode.NOT_EXCHANGE.String()
	} else if user.ChangeRankCoin(-needCost) == -1 {
		return nil, ecode.RANK_COIN_NOT_ENOUGH.String()
	}
	// 添加到列表
	user.AddHeadIcon(icon)
	// 上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_rsExchangeHeadIcon", map[string]interface{}{
		"head_id":        id,
		"rank_coin_cost": needCost,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_RSEXCHANGEHEADICON_S2C{
		UnlockHeadIcons: user.UnlockHeadIcons,
		RankCoin:        user.RankCoin,
	})
}
