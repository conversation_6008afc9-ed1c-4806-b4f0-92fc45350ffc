package lobby

import (
	"encoding/json"
	"errors"
	"io"
	"net/http"

	"github.com/huyangv/vmqant/log"
)

const (
	LINE_URL = "https://api.line.me/v2/profile"
)

type LineGetProfileRet struct {
	UserId        string `json:"userId"`
	DisplayName   string `json:"displayName"`
	PictureUrl    string `json:"pictureUrl"`
	StatusMessage int    `json:"statusMessage"`
}

// Line登录获取用户信息
func LineLoginCheck(token string) (res LineGetProfileRet, err error) {
	client := http.Client{}
	req, err := http.NewRequest("GET", LINE_URL, nil)
	if err != nil {
		log.Error("LineLoginCheck http req err: %v", err)
		return
	}
	req.Header.Add("Authorization", "Bearer "+token)
	resp, err := client.Do(req)
	defer func() {
		if resp != nil && resp.Body != nil {
			resp.Body.Close()
		}
	}()
	if err != nil {
		log.Error("LineLoginCheck http err:%v", err)
		return
	}
	if resp.StatusCode != http.StatusOK {
		err = errors.New("LineLoginCheck http resp status err")
		log.Error("LineLoginCheck http resp status err, status: %v", resp.Status)
		return
	}
	body, _ := io.ReadAll(resp.Body)
	err = json.Unmarshal(body, &res)
	if err != nil {
		log.Error("LineLoginCheck ret err:%v", err)
		return
	}
	return
}
