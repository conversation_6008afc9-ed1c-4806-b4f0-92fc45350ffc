package behavior

import (
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/hero"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
)

// 结束回合
type EndRound struct {
	BaseAction

	initDelay     int32
	isChangeState bool
}

func (this *EndRound) OnInit(conf map[string]interface{}) {
	this.initDelay = ut.Int32(conf["parameters"])
}

func (this *EndRound) OnOpen() {
	this.isChangeState = false
	this.SetBlackboardData("delay", this.initDelay+ut.Int32(this.GetTreeBlackboardData("addRoundEndDelayTime")))
}

func (this *EndRound) OnTick(dt int32) Status {
	if ut.Int(this.GetTreeBlackboardData("batterCount")) > 0 {
		return SUCCESS // 继续回合
	}
	currTime, delay := ut.Int32(this.GetBlackboardData("currTime")), ut.Int32(this.GetBlackboardData("delay"))
	this.SetBlackboardData("currTime", currTime+dt)
	actionTime := ut.Int32(this.GetTreeBlackboardData("addRoundEndActionHitTime"))
	// 动作时间
	if actionTime > 0 {
		ctrl := this.GetCtrl()
		heroSkill, attackTarget := this.target.GetPortrayalSkill(), this.target.GetAttackTarget()
		if heroSkill != nil {
			if heroSkill.Id == hero.ZHANG_LIAO && attackTarget != nil { //张辽
				if currTime < actionTime {
					return RUNNING
				} else if !ut.Bool(this.GetBlackboardData("isAction")) {
					this.SetBlackboardData("isAction", true)
					// 对范围敌人造成伤害
					arr := attackTarget.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), heroSkill.Target, heroSkill.GetParamsInt(), "")
					for _, m := range arr {
						ctrl.OnHitBaseTrueDamage(this.target, m, map[string]interface{}{"attackAmend": heroSkill.GetValue() * 0.01})
					}
					// 添加减伤buff
					this.target.AddBuff(bufftype.ASSAULT, this.target.GetUID(), 1)
				}
			}
		}
		if ut.Bool(this.GetTreeBlackboardData("isPlayJJShieldEnd")) { //机甲皮肤的收盾动作
			if currTime < actionTime {
				if currTime == 0 {
					this.target.RemoveBuff(bufftype.STAND_SHIELD)
				}
				return RUNNING
			}
			this.SetTreeBlackboardData("isPlayJJShieldEnd", false)
		} else if ut.Bool(this.GetTreeBlackboardData("isPoisonedWineEnd")) { //鸩毒 爆炸
			if currTime < actionTime {
				if currTime == 0 {
					POISONED_WINE := this.target.GetBuff(bufftype.POISONED_WINE)
					if POISONED_WINE == nil {
						this.SetTreeBlackboardData("isPoisonedWineEnd", false)
						return RUNNING
					}
					this.target.RemoveBuff(bufftype.POISONED_WINE)
					uid, camp := this.target.GetUID(), this.target.GetCamp()
					arr := this.target.GetCanAttackRangeFighter(ctrl.GetFighters(), 2, 7, uid, func(m g.IFighter) bool { return m.GetCamp() != camp || m.IsFlag() })
					arr = append(arr, this.target)
					damage := ut.RoundInt32(POISONED_WINE.Value * float64(POISONED_WINE.Lv+9) * 0.01)
					for _, m := range arr {
						_, val := m.HitPrepDamageHandle(0, damage)
						v, _, _ := m.OnHit(val, val)
						// 记录数据
						ctrl.AddFighterBattleDamageInfo(POISONED_WINE.Provider, m, v)
					}
				}
				return RUNNING
			}
			this.SetTreeBlackboardData("isPoisonedWineEnd", false)
		}
	}
	// 结束时间
	if currTime >= delay || !this.isActioning(delay > this.initDelay) {
		// 如果还没有攻击过 清理当前的目标 下次重新选
		if !ut.Bool(this.GetTreeBlackboardData("isAttack")) {
			this.target.ChangeAttackTarget(nil)
			// 夏侯渊 清理奔袭buff
			if this.target.CheckPortrayalSkill(hero.XIA_HOUYUAN) != nil {
				this.target.RemoveBuff(bufftype.LONG_RANGE_RAID)
			}
		}
		// 标记结束回合
		this.SetTreeBlackboardData("isRoundEnd", true)
		return SUCCESS
	} else if !this.isChangeState {
		this.isChangeState = true
		this.target.ChangeState(constant.PAWN_STATE_STAND)
	}
	return RUNNING
}

// 是否行动过
func (this *EndRound) isActioning(hasAddRoundEndDelayTime bool) bool {
	return hasAddRoundEndDelayTime || ut.Bool(this.GetTreeBlackboardData("isDeductHpAction")) || ut.Bool(this.GetTreeBlackboardData("isBloodAction")) || ut.Bool(this.GetTreeBlackboardData("isMove")) || ut.Bool(this.GetTreeBlackboardData("isAttack"))
}
