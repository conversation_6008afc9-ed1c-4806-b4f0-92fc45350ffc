package match

import (
	"context"
	"time"

	slg "slgsrv/server/common"
	r "slgsrv/server/game/room"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type RoomMongodb struct {
	table string
}

var (
	mgo_database   string
	mgo_client     *mongo.Client
	db_room        = &RoomMongodb{slg.DB_COLLECTION_NAME_ROOM}
	db_map_use     = &RoomMongodb{slg.DB_COLLECTION_NAME_MAP_USE}
	db_room_gen    = &RoomMongodb{slg.DB_COLLECTION_NAME_ROOM_GEN_INFO}
	db_game_machs  = &MachMongodb{slg.DB_COLLECTION_NAME_GAME_MACHS}
	db_lobby_machs = &MachMongodb{slg.DB_COLLECTION_NAME_LOBBY_MACHS}
	db_sup_machs   = &MachMongodb{slg.DB_COLLECTION_NAME_SUPPORT_MACHS}
)

// 初始化游戏服db
func InitRoomDB(url, dbname string) {
	mgo_database = dbname
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	opt := options.Client().ApplyURI(url)
	opt.SetLocalThreshold(3 * time.Second)  // 只使用与mongo操作耗时小于3秒的
	opt.SetMaxConnIdleTime(5 * time.Second) // 指定连接可以保持空闲的最大毫秒数
	opt.SetMaxPoolSize(20)                  // 使用最大的连接数
	var err error
	if mgo_client, err = mongo.Connect(ctx, opt); err == nil {
		log.Info("room_mongodb init success! " + url + "[" + mgo_database + "]")
	} else if err == mongo.ErrNoDocuments {
		log.Error("room_mongodb init error! ErrNoDocuments")
	} else {
		log.Error(err.Error())
	}
}

func (this *RoomMongodb) getCollection() *mongo.Collection {
	return mgo_client.Database(mgo_database).Collection(this.table)
}

// 获取所有房间数据
func (this *RoomMongodb) FindAll() (list []r.RoomTableData, err string) {
	// 已删除的房间不初始化
	filter := bson.M{
		"state": bson.M{"$ne": slg.SERVER_STATUS_DELETE},
	}
	cur, e := this.getCollection().Find(context.TODO(), filter)
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	if e != nil {
		return []r.RoomTableData{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []r.RoomTableData{}, e.Error()
	} else if e = cur.All(context.TODO(), &list); e != nil {
		err = e.Error()
	}
	return
}

// 获取指定房间数据
func (this *RoomMongodb) Find(sid int32) (roomData r.RoomTableData, err string) {
	filter := &bson.M{
		"id": sid,
	}
	e := this.getCollection().FindOne(context.TODO(), filter).Decode(&roomData)
	if e != nil {
		err = e.Error()
	}
	return
}

// 区服报名完成更新
func (this *RoomMongodb) UpdateRoomApplyFinish(room *Room) {
	update := &bson.M{
		"apply_finish_time": room.ApplyFinishTime,
		"open_room_time":    room.OpenRoomTime,
	}
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"id": room.Id}, bson.M{"$set": &update}); e != nil {
		log.Error("UpdateRoomApplyFinish sid: %v, err: %v", room.Id, e)
	}
}

// 玩家报名更新
func (this *RoomMongodb) UpdatePlayerApply(room *Room) {
	update := &bson.M{
		"apply_uids": room.ApplyUids,
	}
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"id": room.Id}, bson.M{"$set": &update}); e != nil {
		log.Error("UpdatePlayerApply sid: %v, err: %v", room.Id, e)
	}
}

// 更新主城位置列表
func (this *RoomMongodb) UpdateRoomMainCitys(room *Room) {
	update := &bson.M{
		"main_citys": room.mainCitys,
	}
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"id": room.Id}, bson.M{"$set": &update}); e != nil {
		log.Error("UpdateRoomMainCitys sid: %v, err: %v", room.Id, e)
	}
}

// 更新游戏服状态
func (this *RoomMongodb) UpdateRoomState(room *Room) {
	update := &bson.M{
		"state": room.state,
	}
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"id": room.Id}, bson.M{"$set": &update}); e != nil {
		log.Error("UpdateRoomState sid: %v, err: %v", room.Id, e)
	}
}

// 更新游戏服数据是否保留
func (this *RoomMongodb) UpdateRoomSave(room *Room) {
	update := &bson.M{
		"save": room.Save,
	}
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"id": room.Id}, bson.M{"$set": &update}); e != nil {
		log.Error("UpdateRoomSave sid: %v, err: %v", room.Id, e)
	}
}

// 区服生成相关信息
type RoomGenCol[V string | int | int32] struct {
	Key string `bson:"key"`
	Val V      `bson:"val"`
}

const (
	ROOM_GEN_COL_KEY_MAP_GEN_ID = "mapGenId"
	ROOM_GEN_COL_KEY_WIN_COND   = "win_cond"
)

// 获取区服生成相关信息string
func (this *RoomMongodb) GetRoomGenStringByKey(key string) (val string, err error) {
	filter := &bson.M{
		"key": key,
	}
	data := &RoomGenCol[string]{}
	err = this.getCollection().FindOne(context.TODO(), filter).Decode(&data)
	if err != nil {
		log.Error("GetRoomGenInfoByKey key: %v, err: %v", key, err)
		return
	}
	val = data.Val
	return
}

// 获取区服生成相关信息int
func (this *RoomMongodb) GetRoomGenIntByKey(key string) (val int32, err error) {
	filter := &bson.M{
		"key": key,
	}
	data := &RoomGenCol[int32]{}
	err = this.getCollection().FindOne(context.TODO(), filter).Decode(&data)
	if err != nil {
		log.Error("GetRoomGenIntByKey key: %v, err: %v", key, err)
		return
	}
	val = data.Val
	return
}

// 设置区服生成相关信息string
func (this *RoomMongodb) SetRoomGenInfoString(info *RoomGenCol[string]) (err error) {
	_, err = this.getCollection().UpdateOne(context.TODO(), bson.M{"key": info.Key}, bson.M{"$set": info}, options.Update().SetUpsert(true))
	if err != nil {
		log.Error("SetRoomGenInfoInt info: %v, err: %v", info, err)
		return
	}
	return
}

// 设置区服生成相关信息int
func (this *RoomMongodb) SetRoomGenInfoInt(info *RoomGenCol[int32]) (err error) {
	_, err = this.getCollection().UpdateOne(context.TODO(), bson.M{"key": info.Key}, bson.M{"$set": info}, options.Update().SetUpsert(true))
	if err != nil {
		log.Error("SetRoomGenInfoInt info: %v, err: %v", info, err)
		return
	}
	return
}

// 获取胜利条件
func (this *RoomMongodb) GetWinCond() string {
	filter := &bson.M{
		"key": "win_cond",
	}
	data := &RoomGenCol[string]{}
	err := this.getCollection().FindOne(context.TODO(), filter).Decode(&data)
	if err != nil {
		log.Error("GetWinCond err: %v", err)
		return ""
	}
	return data.Val
}

// 获取所有地图使用情况
func (this *RoomMongodb) FindAllMapUseInfo() (list []MapUseInfo, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.D{})
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	if err != nil {
		log.Error("FindAllMapUseInfo err: %v", err)
		return
	} else if err = cur.Err(); err != nil {
		return
	} else if err = cur.All(context.TODO(), &list); err != nil {
		return
	}
	return
}

// 更新地图使用情况
func (this *RoomMongodb) UpdateMapUse(info *MapUseInfo) (err error) {
	_, err = this.getCollection().UpdateOne(context.TODO(), bson.M{"id": info.Id}, bson.M{"$set": info}, options.Update().SetUpsert(true))
	if err != nil {
		log.Error("UpdateMapUse info: %v, err: %v", info, err)
		return
	}
	return
}

type MachMongodb struct {
	table string
}

func (this *MachMongodb) getCollection() *mongo.Collection {
	return mgo_client.Database(mgo_database).Collection(this.table)
}

// 获取所有游戏服物理机数据
func (this *MachMongodb) FindAllGameMachs() (list []Mach, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.D{})
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	if err != nil {
		log.Error("FindAllGameMachs err: %v", err)
		return
	} else if err = cur.Err(); err != nil {
		return
	} else if err = cur.All(context.TODO(), &list); err != nil {
		return
	}
	return
}

// 添加物理机
func (this *MachMongodb) AddGameMach(mach *Mach) error {
	_, err := this.getCollection().InsertOne(context.TODO(), mach)
	return err
}

// 删除物理机
func (this *MachMongodb) DelMach(mach *Mach) error {
	_, err := this.getCollection().DeleteOne(context.TODO(), bson.M{"ip": mach.Addr})
	return err
}

// 更新物理机信息
func (this *MachMongodb) UpdateGameMach(mach *Mach) error {
	_, err := this.getCollection().UpdateOne(context.TODO(),
		bson.D{{Key: "ip", Value: mach.Addr}},
		bson.D{{Key: "$set", Value: bson.D{
			{Key: "sid_list", Value: mach.SidList},
			{Key: "server_types", Value: mach.ServerTypes},
			{Key: "max_cap", Value: mach.MaxCap},
			{Key: "name", Value: mach.Name},
			{Key: "status", Value: mach.Status},
			{Key: "update_status", Value: mach.UpdateStatus},
		}}},
		options.Update().SetUpsert(true))
	return err
}

// 获取所有大厅服物理机数据
func (this *MachMongodb) FindAllLobbyMachs() (list []LobbyMach, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.D{})
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	if err != nil {
		return
	} else if err = cur.Err(); err != nil {
		return
	} else if err = cur.All(context.TODO(), &list); err != nil {
		return
	}
	return
}

// 更新大厅服物理机信息
func (this *MachMongodb) UpdateLobbyMachDb(mach *LobbyMach) error {
	_, err := this.getCollection().UpdateOne(context.TODO(), bson.M{"ip": mach.Addr}, bson.M{"$set": mach}, options.Update().SetUpsert(true))
	return err
}

// 添加大厅服物理机
func (this *MachMongodb) AddLobbyMach(mach *LobbyMach) error {
	_, err := this.getCollection().InsertOne(context.TODO(), mach)
	return err
}

// 更新辅助服物理机信息
func (this *MachMongodb) UpdateSupMachDb(mach *SupMach) error {
	_, err := this.getCollection().UpdateOne(context.TODO(), bson.M{"ip": mach.Addr}, bson.M{"$set": mach}, options.Update().SetUpsert(true))
	return err
}

// 添加辅助服物理机
func (this *MachMongodb) AddSupMach(mach *SupMach) error {
	_, err := this.getCollection().InsertOne(context.TODO(), mach)
	return err
}

// 获取所有辅助功能物理机数据
func (this *MachMongodb) FindAllSupMachs() (list []SupMach, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.D{})
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	if err != nil {
		return
	} else if err = cur.Err(); err != nil {
		return
	} else if err = cur.All(context.TODO(), &list); err != nil {
		return
	}
	return
}

// 删除游戏服指定表
func DelGameCol(col string) error {
	err := mgo_client.Database(mgo_database).Collection(col).Drop(context.TODO())
	if err != nil {
		log.Error("DelGameCol col: %v, err: %v", col, err)
	}
	return err
}

// 删除游戏服指定表指定sid的数据
func DelGameColDataBySid(col string, sid int32) error {
	_, err := mgo_client.Database(mgo_database).Collection(col).DeleteMany(context.TODO(), &bson.M{"sid": sid})
	if err != nil {
		log.Error("DelGameColDataBySid col: %v, sid: %v, err: %v", col, sid, err)
	}
	return err
}
