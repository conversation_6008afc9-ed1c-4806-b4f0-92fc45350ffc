package ctype

// 通用类型对象的类型
const (
	NONE              = iota
	CEREAL            // 粮食 1
	TIMBER            // 木头 2
	STONE             // 石头 3
	BUILD_LV          // 建筑等级 4 id,lv
	GOLD              // 金币 5
	TREASURE          // 宝箱 6
	EXP_BOOK          // 经验书 7
	CEREAL_C          // 粮耗 8
	IRON              // 铁 9
	TITLE             // 称号 10
	PAWN_SKIN         // 士兵皮肤 11
	CELL_COUNT        // 领地数 12
	UP_SCROLL         // 升级卷轴 13
	FIXATOR           // 固定器 14
	EQUIP             // 装备 15
	PAWN              // 士兵 16
	POLICY            // 政策 17
	BASE_RES          // 基础资源 18
	BASE_RES_2        // 书铁 19
	COMPLETE_GUIDE    // 完成新手引导 20
	RANK_SCORE        // 胜点 21
	INGOT             // 元宝 22
	WAR_TOKEN         // 兵符 23
	HERO_DEBRIS       // 英雄残卷 24
	HERO_OPT          // 自选英雄包 25
	STAMINA           // 奖励点 26
	UP_RECRUIT        // 加速招募 27
	SKIN_ITEM         // 皮肤物品 28
	CITY_SKIN         // 城市皮肤 29
	RANK_COIN         // 段位积分 30
	HEAD_ICON         // 头像 31
	CHAT_EMOJI        // 聊天表情 32
	BOTANY            // 植物 33
	BATTLE_PASS       // 战令 34
	BATTLE_PASS_SCORE // 战令积分 35
	FREE_RECRUIT      // 免费招募 36
	FREE_LEVING       // 免费训练 37
	FREE_CURE         // 免费治疗 38
	FREE_FORGE        // 免费打造 39
	FREE_AD           // 免广告 40
)

// 活动类型
const (
	ACTIVITY_NONE      = iota
	ACTIVITY_DC_FOLLOW // DC关注有礼
)
