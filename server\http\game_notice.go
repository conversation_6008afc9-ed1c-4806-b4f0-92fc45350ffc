package http

import (
	"context"
	"encoding/json"
	slg "slgsrv/server/common"
	"slgsrv/server/http/db"
	ut "slgsrv/utils"
	mgo "slgsrv/utils/mgodb"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// 游戏公告
type GameNotice struct {
	Type     int32    `bson:"type"`     // 公告类型 0公告
	Title    string   `bson:"title"`    // 公告标题
	Contents []string `bson:"contents"` // 公告内容
	Time     int64    `bson:"time"`     // 公告时间
}

type LangGameNotice struct {
	Lang string        `bson:"lang"` // 语言
	List []*GameNotice `bson:"list"` // 公告列表
}

var (
	noticeVersion = &db.MapCol[string]{
		Val: "",
	}
	langGameNoticeMap = ut.NewMapLock[string, *LangGameNotice]()
)

// 获取公告
func (this *Http) httpGetNotice(lang string) (response map[string]interface{}, err error) {
	if lang == "all" {
		// 获取所有语言
		list := []map[string]interface{}{}
		langGameNoticeMap.ForEach(func(v *LangGameNotice, k string) bool {
			list = append(list, map[string]interface{}{
				"lang": k,
				"list": v.List,
			})
			return true
		})
		return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
			"noticeVersion": ut.Int(noticeVersion.Val),
			"list":          list,
		}), nil
	}
	data := langGameNoticeMap.Get(lang)
	if data == nil {
		data = langGameNoticeMap.Get("en")
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"noticeVersion": ut.Int(noticeVersion.Val),
		"list":          data.List,
	}), nil
}

// 设置公告版本
func (this *Http) httpSetNoticeVersion(version string) (response map[string]interface{}, err error) {
	if _, e := setGmConfig("noticeVersion", version); e != nil {
		return slg.HttpResponseErrorNoDataWithDesc("设置失败"), err
	}
	noticeVersion.Val = version
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功"), nil
}

// 设置公告
func (this *Http) httpSetNotice(lang, noticeListJson string) (response map[string]interface{}, err error) {
	data := &LangGameNotice{}
	err = json.Unmarshal([]byte(noticeListJson), &data)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc(lang + "设置失败"), err
	}
	if lang != "" {
		langGameNoticeMap.Set(lang, data)
		// 保存到数据库
		mgo.GetCollection(slg.DB_COLLECTION_NAME_GAME_NOTICE).UpdateOne(context.TODO(), bson.M{"lang": lang}, bson.M{"$set": data}, options.Update().SetUpsert(true))
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 初始化公告
func InitNotify() {
	if data, err := getGmConfig("noticeVersion"); err == nil {
		noticeVersion = data
	}
	// 从数据库读取公告
	cur, err := mgo.GetCollection(slg.DB_COLLECTION_NAME_GAME_NOTICE).Find(context.TODO(), bson.M{})
	if err != nil {
		log.Error("InitNotify db err: %v", err)
		return
	}
	defer cur.Close(context.TODO())
	retList := []LangGameNotice{}
	cur.All(context.TODO(), &retList)
	for _, v := range retList {
		langGameNoticeMap.Set(v.Lang, &v)
	}
}
