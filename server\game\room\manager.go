package r

import (
	"slgsrv/server/common/sdk"

	"github.com/huyangv/vmqant/log"
	basemodule "github.com/huyangv/vmqant/module/base"
	"github.com/sasha-s/go-deadlock"
)

type RoomMap struct {
	deadlock.RWMutex
	Map map[int32]*Model
}

var (
	serverModule *basemodule.BaseModule = nil
	rooms                               = &RoomMap{Map: map[int32]*Model{}} // 当前所有房间 一个房间就是一局游戏
)

// 初始化所有房间 目前一般是本地测试 会用到
func LoadAllRoom() {
	// 从数据库获取有多少个房间
	datas, err := getAllRooms()
	if err != "" {
		log.Error("getAllRooms error! err=%v", err)
		return
	}
	// 初始化各个房间
	// now := ut.Now()
	for _, data := range datas {
		rooms.Map[data.Id] = NewRoomByDB(data)
	}
	log.Info("load rooms done.")
}

// 加载指定房间
func LoadRoomById(id int) {
	data, err := getRoomById(id)
	if err != "" {
		log.Error("getRoomById error! err=%v, id: %v", err, id)
		return
	}
	rooms.Map[data.Id] = NewRoomByDB(data)
	log.Info("load room[%v] done.", id)
}

// 运行所有服务器
func RunAllRoom(module *basemodule.BaseModule) {
	serverModule = module
	// NotifyRoomsToLogin()
	sdk.FirebaseCloudMessageInit()
	for _, room := range rooms.Map {
		room.RoomStateChangeToOpen()
		room.NotifyRoomInfoToMatch()
		if !room.IsClose() {
			room.Run()
		}
	}
}

// 保存信息
func SaveAllRoom() {
	// rooms.RLock()
	// defer rooms.RUnlock()
	for _, room := range rooms.Map {
		if !room.IsClose() {
			room.Stop(1)
		}
	}
}

// 获取房间
func GetRoomById(id int32) *Model {
	// rooms.RLock()
	// defer rooms.RUnlock()
	if room := rooms.Map[id]; room != nil && room.isRunning {
		return room
	}
	return nil
}

// 获取所有房间基础信息
func GetBaseRooms(uid string) []map[string]interface{} {
	// rooms.RLock()
	// defer rooms.RUnlock()
	list := []map[string]interface{}{}
	for _, room := range rooms.Map {
		list = append(list, room.Strip())
	}
	return list
}

// 获取所有房间的id的列表
func GetRoomIds() []int32 {
	// rooms.RLock()
	// defer rooms.RUnlock()
	arr := []int32{}
	for _, m := range rooms.Map {
		if !m.IsClose() {
			arr = append(arr, m.Id)
		}
	}
	return arr
}

func NotifyRoomsToMatch() {
	for _, v := range rooms.Map {
		v.NotifyRoomInfoToMatch()
	}
}
