package game

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

func (this *Game) InitHDHero() {
	this.GetServer().RegisterGO("HD_WorshipHero", this.worshipHero)                 // 供奉英雄
	this.GetServer().RegisterGO("HD_ChangePawnPortrayal", this.changePawnPortrayal) // 改变士兵画像
}

// 供奉英雄
func (this *Game) worshipHero(session gate.Session, msg *pb.GAME_HD_WORSHIPHERO_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	tplr := wld.GetTempPlayer(plr.Uid)
	if tplr == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	index, id := msg.GetIndex(), msg.GetId()
	if index < 0 || index >= int32(len(tplr.HeroSlots)) {
		return nil, ecode.HERO_SLOT_NOT_EXIST.String()
	} else if tplr.HasWorshipHero(id) {
		return nil, ecode.HERO_YET_WORSHIP.String()
	}
	slot := tplr.HeroSlots[index]
	if slot.Hero != nil {
		return nil, ecode.HERO_SLOT_YET_WORSHIP.String()
	}
	// 获取英雄
	bytes, err := ut.RpcBytes(room.InvokeLobbyRpc(rds.GetUserLid(plr.Uid), slg.RPC_GET_USER_HERO_INFO, plr.Uid, id))
	if err != "" {
		return nil, err
	}
	info := &pb.PortrayalInfo{}
	if pb.ProtoUnMarshal(bytes, info) != nil {
		return nil, ecode.UNKNOWN.String()
	}
	heroInfo := g.NewPortrayalByPb(info)
	if !tplr.HasWorshipHeroPawn(heroInfo.GetAvatarPawn()) {
		// 未供奉过该士兵id的英雄 解锁额外士兵槽位
		plr.WorshipHeroUnlockPawn(heroInfo.GetAvatarPawn(), index)
	}
	slot.Hero = heroInfo
	slot.DieTime = 0
	slot.AvatarArmyUID = ""
	tplr.IsNeedUpdateDB = true
	// 上报
	wld.TaTrack(plr.Uid, 0, "ta_choosePortrayal", map[string]interface{}{
		"portrayal_id":  id,
		"choose_num":    len(array.Filter(tplr.HeroSlots, func(m *g.HeroSlotInfo, _ int) bool { return m.Hero != nil })),
		"herohalllevel": wld.GetPlayerBuildLvById(plr.Uid, constant.HEROHALL_BUILD_ID),
		"maincitylevel": wld.GetPlayerBuildLvById(plr.Uid, constant.MAIN_BUILD_ID),
	})
	return pb.ProtoMarshal(&pb.GAME_HD_WORSHIPHERO_S2C{
		Slot:          slot.ToPb(wld.GetServerRunDay()),
		UnlockPawnIds: plr.UnlockPawnIds,
		PawnSlots:     plr.PawnCeriSlots.ToCerisPb(),
	})
}

// 改变士兵画像
func (this *Game) changePawnPortrayal(session gate.Session, msg *pb.GAME_HD_CHANGEPAWNPORTRAYAL_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, armyUid, uid, portrayalId := msg.GetIndex(), msg.GetArmyUid(), msg.GetUid(), msg.GetPortrayalId()
	area, army := wld.GetAreaAndArmy(index, armyUid)
	if area == nil || army.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if area.IsBattle() {
		log.Error("changePawnPortrayal error? BATTLEING, portrayalId=" + ut.Itoa(portrayalId))
		return nil, ecode.BATTLEING.String() // 当前战斗中
	} else if army.HasHero() {
		return nil, ecode.ARMY_ONLY_AVATAR_ONE.String()
	}
	pawn := army.GetPawnByUID(uid)
	if pawn == nil {
		log.Error("changePawnPortrayal error? index="+ut.Itoa(index)+", armyUid="+armyUid+", uid="+uid, area == nil, pawn == nil)
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if pawn.IsHero() {
		return nil, ecode.UNKNOWN.String()
	}
	tplr := wld.GetTempPlayer(plr.Uid)
	if tplr == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	slot := tplr.GetWorshipHeroSlotInfo(portrayalId)
	if slot == nil {
		return nil, ecode.HERO_NOT_EXIST.String()
	} else if slot.IsDie(wld.GetServerRunDay()) {
		return nil, ecode.HERO_YET_DIE.String()
	} else if slot.AvatarArmyUID != "" {
		return nil, ecode.HERO_YET_AVATAR.String()
	}
	json := config.GetJsonData("portrayalBase", portrayalId)
	if json == nil {
		return nil, ecode.HERO_NOT_EXIST.String()
	} else if ut.Int32(json["avatar_pawn"]) != pawn.Id {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	// 这里设置军队uid
	slot.AvatarArmyUID = armyUid
	slot.DieTime = 0
	tplr.IsNeedUpdateDB = true
	// 有画像就不会有皮肤
	pawn.SkinId = 0
	// 设置画像
	pawn.ChangePortrayal(portrayalId, slot.Hero.Attrs, area.IsRecoverPawnHP())
	// 标记
	wld.TagUpdateDBByIndex(index)
	// 通知
	wld.NotifyAreaUpdateInfo(index, constant.NQ_CHANGE_PAWN_PORTRAYAL, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
		Data_69: &pb.AreaPawnInfo{
			ArmyUid:   pawn.ArmyUid,
			Uid:       pawn.Uid,
			SkinId:    pawn.SkinId,
			Portrayal: pawn.ToPortrayalPb(),
		},
	})
	// 上报
	wld.TaTrack(plr.Uid, 0, "ta_changePawnPortrayal", map[string]interface{}{
		"role_id":      pawn.Id,
		"role_lv":      pawn.Lv,
		"portrayal_id": portrayalId,
	})
	return pb.ProtoMarshal(&pb.GAME_HD_CHANGEPAWNPORTRAYAL_S2C{
		Slot: slot.ToPb(wld.GetServerRunDay()),
	})
}
