package astar

import (
	"sort"

	ut "slgsrv/utils"
)

type TempRangePoint struct {
	point *ut.Vec2
	tag   int32
}

var DIR_POINTS_4_TO_SEARCH = [][]TempRangePoint{
	{ // 0.从上方进入
		TempRangePoint{point: ut.NewVec2(0, 1), tag: 1},  // 上
		TempRangePoint{point: ut.NewVec2(1, 0), tag: 1},  // 右
		TempRangePoint{point: ut.NewVec2(0, -1), tag: 1}, // 下
		TempRangePoint{point: ut.NewVec2(-1, 0), tag: 1}, // 左
	},
	{ // 1.从右方进入
		TempRangePoint{point: ut.NewVec2(1, 0), tag: 1},  // 右
		TempRangePoint{point: ut.NewVec2(0, -1), tag: 1}, // 下
		TempRangePoint{point: ut.NewVec2(-1, 0), tag: 1}, // 左
		TempRangePoint{point: ut.NewVec2(0, 1), tag: 1},  // 上
	},
	{ // 2.从下方进入
		TempRangePoint{point: ut.NewVec2(0, -1), tag: 1}, // 下
		TempRangePoint{point: ut.NewVec2(-1, 0), tag: 1}, // 左
		TempRangePoint{point: ut.NewVec2(0, 1), tag: 1},  // 上
		TempRangePoint{point: ut.NewVec2(1, 0), tag: 1},  // 右
	},
	{ // 3.从左方进入
		TempRangePoint{point: ut.NewVec2(-1, 0), tag: 1}, // 左
		TempRangePoint{point: ut.NewVec2(0, 1), tag: 1},  // 上
		TempRangePoint{point: ut.NewVec2(1, 0), tag: 1},  // 右
		TempRangePoint{point: ut.NewVec2(0, -1), tag: 1}, // 下
	},
}

// A星寻路 4方向
type AStar4 struct {
	opened       []*ANode
	closed       map[string]bool
	checkHasPass func(x, y int32, camp int32) bool // 检测方法
}

func (this *AStar4) Init(checkHasPass func(x, y int32, camp int32) bool) *AStar4 {
	this.checkHasPass = checkHasPass
	return this
}

// 新建一个节点
func (this *AStar4) newNode(x, y int32) *ANode {
	return new(ANode).Init(x, y)
}

// 寻路
func (this *AStar4) Search(start, end *ut.Vec2, maxSearchCount int32, dir int32, camp int32) []*ut.Vec2 {
	if start.Equals(end) {
		return []*ut.Vec2{}
	}
	dirPoints := DIR_POINTS_4_TO_SEARCH[2]
	dirCount := int32(len(dirPoints))
	if dir >= 0 && dir < dirCount {
		dirPoints = DIR_POINTS_4_TO_SEARCH[dir]
	}
	this.opened = []*ANode{}
	this.closed = map[string]bool{}
	// 把第一个点装进开起列表
	this.opened = append(this.opened, this.newNode(start.X, start.Y))
	// 开始搜索
	var node *ANode = nil
	for len(this.opened) > 0 {
		node = this.opened[0]
		this.opened = this.opened[1:]
		if node.G >= maxSearchCount || node.point.Equals(end) {
			break
		}
		this.closed[node.uid] = true
		// 找周围的是否可以移动
		for i := int32(0); i < dirCount; i++ {
			d := dirPoints[i]
			x := node.point.X + d.point.X
			y := node.point.Y + d.point.Y
			id := ut.Itoa(x) + "_" + ut.Itoa(y)
			if !end.Equal2(x, y) && (!this.checkHasPass(x, y, camp) || this.closed[id]) {
				continue
			}
			// 如果开启列表中已经有了 那么看现在这个节点到那的距离是否短一点
			it := this.findNode(x, y)
			if it == nil {
				temp := this.newNode(x, y)
				temp.H = int32(end.Sub(temp.point).Mag())
				temp.UpdateParent(node, d.tag)
				this.opened = append(this.opened, temp)
			} else if node.G+node.T+d.tag < it.G+it.T {
				it.UpdateParent(node, d.tag)
			}
		}
		// 排序
		sort.SliceStable(this.opened, func(i, j int) bool {
			return this.opened[i].F < this.opened[j].F
		})
	}
	return this.genPoints(node, start)
}

func (this *AStar4) findNode(x, y int32) *ANode {
	for _, m := range this.opened {
		if m.point.Equal2(x, y) {
			return m
		}
	}
	return nil
}

func (this *AStar4) genPoints(node *ANode, start *ut.Vec2) []*ut.Vec2 {
	points := []*ut.Vec2{}
	for node.parent != nil {
		points = append(points, node.point)
		node = node.parent
	}
	points = append(points, start)
	arr := []*ut.Vec2{}
	for i := len(points) - 1; i >= 0; i-- {
		arr = append(arr, points[i])
	}
	this.opened = []*ANode{}
	this.closed = map[string]bool{}
	return arr
}
