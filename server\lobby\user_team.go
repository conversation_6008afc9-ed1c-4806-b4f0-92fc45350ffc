package lobby

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
)

// 玩家加入队伍条件检测
func (this *User) UserJoinTeamCheck() string {
	if this.GetPlaySid() != 0 {
		return ecode.USER_ALREADY_IN_GAME.String()
	} else if this.TeamUid != "" {
		return ecode.USER_ALREADY_IN_TEAM.String()
	} else if this.IsNovicePlayer() {
		return ecode.NOVICE_PLAYER.String()
	}
	return ""
}

// 玩家收到的队伍邀请信息ToPb
func (this *User) TeamInvitesToPb() (arr []*pb.TeamInviteInfo) {
	this.TeamInviteListLock.RLock()
	for _, v := range this.TeamInviteList {
		arr = append(arr, v.ToPb())
	}
	this.TeamInviteListLock.RUnlock()
	return
}

// 被踢出队伍
func (this *User) KickByTeam(tp int) {
	this.SetTeamUid("")
	if this.FlagUpdateDB() {
		this.NotifyUser(slg.LOBBY_TEAM_DEL_TEAMMATE, &pb.LOBBY_ONTEAMDELTEAMMATE_NOTIFY{Type: int32(tp)})
	}
}

// 设置玩家队伍uid
func (this *User) SetTeamUid(teamUid string) {
	this.TeamUid = teamUid
	// if teamUid != "" {
	// 	this.AddSubChannels(rds.RDS_USER_SUB_CHANNEL_TEAM + teamUid)
	// } else if oldUid != "" {
	// 	this.UnSubChannels(rds.RDS_USER_SUB_CHANNEL_TEAM + oldUid)
	// }
}
