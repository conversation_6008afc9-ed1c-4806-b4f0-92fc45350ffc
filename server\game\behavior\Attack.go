package behavior

import (
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/eeffect"
	"slgsrv/server/game/common/enums/hero"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
)

type Attack struct {
	BaseAction

	attackAnimTimes [][]float64

	needAttackTime int32 // 需要攻击的时间
	needHitTime    int32 // 需要击中的时间
	bulletId       int32 // 子弹id

	isChangeFighterState bool // 是否改变士兵的状态了
	isChangeTargetState  bool // 是否改变目标士兵的状态了

	dashArray []*DashData // 冲撞时间记录
	isDashing bool
}

func (this *Attack) OnInit(conf map[string]interface{}) {
	this.attackAnimTimes = this.target.GetAttackAnimTimes()
	arr := this.attackAnimTimes[0]
	this.needAttackTime = int32(arr[0] * 1000.0)
	this.needHitTime = int32(arr[1] * 1000.0)
	this.bulletId = int32(arr[2])
}

func (this *Attack) OnOpen() {
	this.SetTreeBlackboardData("isAttack", true) // 记录已经攻击过了
	this.isChangeFighterState = false
	this.isChangeTargetState = false
	this.dashArray = []*DashData{}
	this.isDashing = false
	// 如果是陌刀兵 提前随机好攻击段
	if skill := this.target.GetSkillByType(constant.PAWN_SKILL_TYPE_INSTABILITY_ATTACK); skill != nil {
		index := ut.Int32(this.GetBlackboardData("instabilityAttackIndex"))
		if index == 0 {
			attackTarget := this.target.GetAttackTarget()
			if skill.GetIntensifyType() == 1 && this.target.CheckTriggerBuff(bufftype.STEADY_ATTACK) != nil {
				index = 4 // 专属前3次都造成最高值
			} else if attackTarget != nil && attackTarget.GetPawnType() == constant.PAWN_TYPE_SOWAR {
				index = 3 // 目标是骑兵时都会造成第3段
			} else if attackTarget != nil && attackTarget.IsBuild() {
				index = 1
			} else if this.target.CheckPortrayalSkill(hero.LI_SIYE) != nil {
				index = 3 // 李嗣业总是按人马俱碎的随即范围
			} else {
				index = this.GetCtrl().GetRandom().GetInt32(1, 3)
			}
			this.SetBlackboardData("instabilityAttackIndex", index)
		}
		arr := this.attackAnimTimes[ut.MinInt32(index-1, 2)]
		this.needAttackTime = int32(arr[0] * 1000.0)
		this.needHitTime = int32(arr[1] * 1000.0)
	}
	// 如果是斧骑兵 并且有专属 准备好攻击方式
	if skill := this.target.GetSkillByType(constant.PAWN_SKILL_TYPE_LONGITUDINAL_CLEFT); skill != nil && len(this.attackAnimTimes) >= 2 {
		index := ut.If(skill.GetIntensifyType() == 1, 1, 0)
		// 程咬金 0 2 4 | 1 3 5
		if this.target.GetPortrayalSkillID() == hero.CHENG_YAOJIN {
			axes := this.target.GetBuffValue(bufftype.THREE_AXES)
			if axes == 1 {
				index += 2 // 劈脑袋
			} else if axes == 3 {
				index += 4 // 掏耳朵
			}
		}
		this.SetBlackboardData("instabilityAttackIndex", index)
		arr := this.attackAnimTimes[index]
		this.needAttackTime = int32(arr[0] * 1000.0)
		this.needHitTime = int32(arr[1] * 1000.0)
	}
	// 如果是秦琼 并且有强化攻击 准备好攻击方式
	if this.target.GetPortrayalSkillID() == hero.QIN_QIONG {
		index := ut.If(this.target.IsHasBuff(bufftype.BEHEADED_GENERAL), 1, 0)
		this.SetBlackboardData("instabilityAttackIndex", index)
		arr := this.attackAnimTimes[index]
		this.needAttackTime = int32(arr[0] * 1000)
		this.needHitTime = int32(arr[1] * 1000)
	}
	// 猩猩 狂怒
	if this.target.GetID() == 4114 {
		index := ut.If(this.target.IsHasBuff(bufftype.RAGE), 1, 0)
		this.SetBlackboardData("instabilityAttackIndex", index)
		arr := this.attackAnimTimes[index]
		this.needAttackTime = int32(arr[0] * 1000)
		this.needHitTime = int32(arr[1] * 1000)
	}
}

func (this *Attack) OnTick(dt int32) Status {
	attackTarget := this.target.GetAttackTarget()
	if attackTarget == nil || attackTarget.IsDie() {
		return FAILURE
	}
	needAttackTime := this.getNeedAttackTime()
	currAttackTime := ut.Int32(this.GetBlackboardData("currAttackTime"))
	if currAttackTime >= needAttackTime && !this.isDashing {
		this.target.ChangeState(constant.PAWN_STATE_STAND)
		attackTarget.ChangeState(constant.PAWN_STATE_STAND)
		return SUCCESS
	}
	heroSkillId := this.target.GetPortrayalSkillID()
	// 增加时间
	this.SetBlackboardData("currAttackTime", currAttackTime+dt)
	// 是否到击中的时间点
	needHitTime := ut.If(this.bulletId > 0, needAttackTime-dt, this.needHitTime)
	if currAttackTime >= needHitTime {
		ctrl := this.GetCtrl()
		// 裴行俨 需要击飞
		if heroSkillId == hero.PEI_XINGYAN {
			if len(this.dashArray) == 0 {
				point := this.target.GetPoint()
				if p := attackTarget.GetCanByReqelPoint(point, 1); p != nil {
					attackTarget.SetPoint(p)
					ctrl.UpdateFighterPointMap()
					this.dashArray = []*DashData{{Point: point, Time: 0}}
				}
			}
			this.isDashing = true
			if len(this.dashArray) > 0 && !this.diaupOne(dt, 300) {
				return RUNNING
			}
			this.isDashing = false
		}
		// 击中
		if !ut.Bool(this.GetBlackboardData("isHit")) {
			hpRatioBefore := attackTarget.GetHPRatio()
			instabilityAttackIndex := ut.Int32(this.GetBlackboardData("instabilityAttackIndex"))
			damage, trueDamage, baseDamage, isCrit := ctrl.GetAttackDamage(this.target, attackTarget, map[string]interface{}{
				"instabilityAttackIndex": instabilityAttackIndex,
				"attackType":             "attack",
				"getExtraDamge": func() int32 {
					if heroSkillId == hero.PEI_XINGYAN {
						dis := helper.GetPointToPointDis(attackTarget.GetPoint(), attackTarget.GetLastPoint())
						v := ctrl.GetBaseAttackDamage(this.target, attackTarget, map[string]interface{}{
							"attackAmend": float64(this.target.GetPortrayalSkill().GetTarget()) * 0.01,
						})
						return v * dis
					}
					return 0
				},
				"getTrueDamage": func() int32 {
					if heroSkillId == hero.HAN_DANG {
						atk := ctrl.GetBaseAttackDamage(this.target, attackTarget, nil)
						return ut.RoundInt32(float64(atk*this.target.GetCurAnger()) * this.target.GetPortrayalSkill().GetValue() * 0.01)
					}
					return 0
				},
			})
			this.SetBlackboardData("isHit", true)
			this.SetBlackboardData("isCrit", isCrit)
			damage, trueDamage = attackTarget.HitPrepDamageHandle(damage, trueDamage)
			this.SetBlackboardData("damage", damage) // 这里记录供前端展示
			this.SetBlackboardData("trueDamage", trueDamage)
			if !attackTarget.IsDie() {
				sumDamage := ut.MaxInt32(damage, 0) + trueDamage
				val, heal, hitShield := attackTarget.OnHit(sumDamage, baseDamage)
				// 处理攻击之后
				heal += ctrl.DoAttackAfter(this.target, attackTarget, map[string]interface{}{
					"sumDamage":              sumDamage,
					"actDamage":              val,
					"trueDamage":             trueDamage,
					"hitShield":              hitShield,
					"instabilityAttackIndex": instabilityAttackIndex,
					"attackType":             "attack",
				})
				this.SetBlackboardData("heal", heal)
				// 增加攻击方怒气
				var anger int32 = 1
				if this.target.GetEquipEffectByType(eeffect.RECOVER_ANGER) != nil {
					anger += 1
				}
				if this.target.IsHasStrategys(40103, 40202, 40304, 40402) {
					anger += 1
				}
				this.target.AddAnger(anger)
				// 攻击次数
				this.target.AddAttackCount(1)
				// 增加防守方怒气
				if val > 0 || damage == -2 || (damage == -1 && attackTarget.IsHasStrategys(50002)) {
					attackTarget.AddAnger(1)
				}
				// 记录数据
				ctrl.AddFighterBattleDamageInfo(this.target.GetUID(), attackTarget, val)
				// 触发任务
				ctrl.TriggerTaskAfterDamage(this.target, attackTarget, val, hpRatioBefore)
			}
			// 太史慈 有概率直接释放骑射
			if heroSkillId == hero.TAI_SHICI && !ut.Bool(this.GetTreeBlackboardData("isBatter")) && ctrl.GetRandom().ChanceInt32(this.target.GetPortrayalSkill().Value) {
				this.target.SetFullAnger(1, false)
				this.target.CleanBlackboard("hitTargetAddAngerUids")
				this.SetTreeBlackboardData("isBatter", true) // 标记连击
			}
		}
		// 设置目标士兵状态
		if !this.isChangeTargetState {
			this.isChangeTargetState = true
			attackTarget.ChangeState(constant.PAWN_STATE_HIT)
		}
	}
	// 如果是强弩兵 这里归0
	if this.target.GetEntity().GetID() == constant.PAWN_CROSSBOW_ID && this.target.GetEntity().GetCurAnger() > 0 {
		this.target.SetAnger(0)
	}
	// 设置士兵状态 更新视图信息
	if !this.isChangeFighterState {
		this.isChangeFighterState = true
		this.target.ChangeState(constant.PAWN_STATE_ATTACK)
	}
	return RUNNING
}

// 获取攻击需要的时间
func (this *Attack) getNeedAttackTime() int32 {
	if this.bulletId == 0 {
		return this.needAttackTime
	}
	time := ut.Int32(this.GetBlackboardData("needAttackTime"))
	if time == 0 {
		point, target := this.target.GetPoint(), this.target.GetAttackTarget().GetPoint()
		// 计算距离
		mag := int32(helper.GetPixelByPoint(point).Sub(helper.GetPixelByPoint(target)).Mag() * 1000.0)
		// 计算飞行时间 加上等待发射的时间
		time = int32(float64(mag)/float64(this.needAttackTime)*1000.0) + this.needHitTime
		this.SetBlackboardData("needAttackTime", time)
	}
	return time
}

// 击飞
func (this *Attack) diaupOne(dt int32, needDiaupTime int32) bool {
	currDiaupTime := ut.Int32(this.GetBlackboardData("currDiaupTime"))
	if currDiaupTime >= needDiaupTime {
		return true
	}
	this.SetBlackboardData("currDiaupTime", currDiaupTime+dt)
	return false
}
