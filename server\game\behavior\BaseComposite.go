package behavior

type IBaseComposite interface {
	IBaseNode
	GetChildrenCount() int
	AddChild(child IBaseNode)
	GetChild(i int) IBaseNode
}

// 条件节点
type BaseComposite struct {
	BaseNode
	BaseWorker

	children []IBaseNode
}

func (this *BaseComposite) Ctor() {
	this.category = COMPOSITE
	this.children = make([]IBaseNode, 0)
}

func (this *BaseComposite) GetChildrenCount() int {
	return len(this.children)
}

// 添加子节点
func (this *BaseComposite) AddChild(node IBaseNode) {
	if node != nil {
		this.children = append(this.children, node)
	}
}

func (this *BaseComposite) GetChild(i int) IBaseNode {
	return this.children[i]
}
