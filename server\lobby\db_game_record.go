package lobby

import (
	"context"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	mgo "slgsrv/utils/mgodb"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type GameRecordDB struct {
	table string
}

// 用户对局记录
var gameRecordDB = &GameRecordDB{slg.DB_COLLECTION_NAME_GAME_RECORD}

type GameRecordData struct {
	UID string `bson:"uid"`

	Statistics     map[int32]int32           `bson:"statistics"`      // 统计
	PawnUse        map[int32]bool            `bson:"pawn_use"`        // 士兵使用
	EquipUse       map[int32]bool            `bson:"equip_use"`       // 装备使用
	PolicyUse      map[int32]bool            `bson:"policy_use"`      // 政策使用
	HeroUse        []int32                   `bson:"hero_use"`        // 英雄使用
	PawnStatistics map[int32]map[int32]int32 `bson:"pawn_statistics"` // 士兵数据统计

	AlliUid      string `bson:"alli_uid"`
	AlliName     string `bson:"alli_name"`
	StartTime    int64  `bson:"start_time"`
	EndTime      int64  `bson:"end_time"`
	AlliHeadicon int32  `bson:"alli_headicon"`
	SID          int32  `bson:"sid"`            // 服务器id
	Score        int32  `bson:"score"`          // 游戏积分
	Rank         int32  `bson:"rank"`           // 排名 -1表示没排名 -2表示放弃
	AddRankScore int32  `bson:"add_rank_score"` // 添加的段位分
	CurRankScore int32  `bson:"cur_rank_score"` // 当前的段位分
	AddWarToken  int32  `bson:"add_war_token"`  // 添加的兵符
	Win          bool   `bson:"win"`            // 是否胜利
}

func (this GameRecordData) ToPb() *pb.GameRecordInfo {
	statistics := map[int32]int32{}
	for k, v := range this.Statistics {
		statistics[k] = v
	}
	pawnStatistics := map[int32]*pb.Mapint32Info{}
	for pawnId, m := range this.PawnStatistics {
		temp := &pb.Mapint32Info{Data: map[int32]int32{}}
		for k, v := range m {
			temp.Data[k] = v
		}
		pawnStatistics[pawnId] = temp
	}
	return &pb.GameRecordInfo{
		Sid:            this.SID,
		StartTime:      this.StartTime,
		EndTime:        this.EndTime,
		Win:            this.Win,
		Score:          this.Score,
		Rank:           this.Rank,
		AddRankScore:   this.AddRankScore,
		CurRankScore:   this.CurRankScore,
		AddWarToken:    this.AddWarToken,
		Statistics:     statistics,
		UseHero:        this.HeroUse,
		PawnStatistics: pawnStatistics,
		AlliUid:        this.AlliUid,
		AlliName:       this.AlliName,
		AlliHeadicon:   this.AlliHeadicon,
	}
}

func (this *GameRecordDB) getCollection() *mongo.Collection {
	return mgo.GetCollection(this.table)
}

// 创建一条游戏记录
func CreateGameRecord(sid int32, time []int64, data map[string]interface{}, g, rankScore, addWarToken int32, pawnStatistic map[int32]map[int32]int32, alliInfo interface{}) *GameRecordData {
	rst := &GameRecordData{
		UID:            ut.String(data["uid"]),
		SID:            sid,
		StartTime:      time[0],
		EndTime:        time[1],
		Win:            ut.Bool(data["win"]),
		Score:          ut.Int32(data["score"]),
		Rank:           ut.Int32(data["rank"]),
		AddRankScore:   g,
		CurRankScore:   rankScore,
		AddWarToken:    addWarToken,
		Statistics:     ut.MapInt32Int32(data["statistics"]),
		PawnUse:        ut.MapInt32Bool(data["pawn_use"]),
		EquipUse:       ut.MapInt32Bool(data["equip_use"]),
		PolicyUse:      ut.MapInt32Bool(data["policy_use"]),
		HeroUse:        ut.Int32Array(data["hero_use"]),
		PawnStatistics: pawnStatistic,
	}
	if alli := ut.MapInterface(alliInfo); alli != nil {
		rst.AlliUid = ut.String(alli["uid"])
		rst.AlliName = ut.String(alli["name"])
		rst.AlliHeadicon = ut.Int32(alli["headicon"])
	}
	return rst
}

func (this *GameRecordDB) AddGameRecord(data *GameRecordData) {
	go this.InsertOne(data)
}

// 插入单个
func (this *GameRecordDB) InsertOne(data *GameRecordData) (err string) {
	if _, e := this.getCollection().InsertOne(context.TODO(), data); e != nil {
		err = e.Error()
	}
	return
}

// 插入多个
func (this *GameRecordDB) InsertMany(datas []interface{}) (err string) {
	if _, e := this.getCollection().InsertMany(context.TODO(), datas); e != nil {
		err = e.Error()
	}
	return
}

// 查询单个
func (this *GameRecordDB) FindOne(uid string, sid int) (data GameRecordData, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"uid": uid, "sid": sid}).Decode(&data); e != nil {
		err = e.Error()
		// log.Error("FindByUid", err, uid)
	}
	return
}

// 查询多个
func (this *GameRecordDB) FindAll(uid string, serverType, page int) (datas []GameRecordData, err string) {
	limit := int64(slg.SHOW_USER_SERVER_RECORD_PAGE_NUM)
	skip := int64((page - 1) * slg.SHOW_USER_SERVER_RECORD_PAGE_NUM)
	filter := bson.D{
		{Key: "uid", Value: uid},
		{Key: "$expr", Value: bson.D{
			{Key: "$eq", Value: bson.A{
				bson.D{{Key: "$floor", Value: bson.D{{Key: "$divide", Value: bson.A{"$sid", slg.ROOM_TYPE_FLAG}}}}},
				serverType,
			}},
		}},
	}
	if serverType == -1 {
		filter = bson.D{{Key: "uid", Value: uid}}
	}
	cursor, e := this.getCollection().Find(context.TODO(), filter, &options.FindOptions{
		Sort:  bson.M{"end_time": -1},
		Limit: &limit,
		Skip:  &skip,
	})
	if e != nil {
		err = e.Error()
	} else if e = cursor.All(context.TODO(), &datas); e != nil {
		err = e.Error()
	}
	cursor.Close(context.TODO())
	return
}

// 查询数量
func (this *GameRecordDB) FindCount(uid string, serverType int) (count int, err string) {
	filter := bson.D{
		{Key: "uid", Value: uid},
		{Key: "$expr", Value: bson.D{
			{Key: "$eq", Value: bson.A{
				bson.D{{Key: "$floor", Value: bson.D{{Key: "$divide", Value: bson.A{"$sid", slg.ROOM_TYPE_FLAG}}}}},
				serverType,
			}},
		}},
	}
	if serverType == -1 {
		filter = bson.D{{Key: "uid", Value: uid}}
	}
	cnt, e := this.getCollection().CountDocuments(context.TODO(), filter)
	if e != nil {
		err = e.Error()
	} else {
		count = ut.Int(cnt)
	}
	return
}
