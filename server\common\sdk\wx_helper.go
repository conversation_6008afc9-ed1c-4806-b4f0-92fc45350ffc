package sdk

import (
	"encoding/json"
	"io"
	"net/http"
)

const (
	APPID        = "wx8c0f94a9f9a66e0f"
	SECRET       = "feec0ca726310bbccb12945862a8a173"
	USERINFO_URL = "https://api.weixin.qq.com/sns/jscode2session?"
)

type Jscode2sessionRet struct {
	Openid     string `json:"openid"`
	SessionKey string `json:"session_key"`
	Unionid    string `json:"unionid"`
	Errcode    int    `json:"errcode"`
	Errmsg     string `json:"errmsg"`
}

// 获取微信openid根据code
func GetOpenIdByCode(code string) (e int, res Jscode2sessionRet) {
	url := USERINFO_URL + "appid=" + APPID + "&secret=" + SECRET + "&js_code=" + code + "&grant_type=authorization_code"
	resp, err := http.Get(url)
	defer func() {
		if resp != nil && resp.Body != nil {
			resp.Body.Close()
		}
	}()
	if err != nil {
		e = -1
		return
	}
	body, _ := io.ReadAll(resp.Body)
	// fmt.Println(string(body))
	_ = json.Unmarshal(body, &res)
	e = res.Errcode
	return
}
