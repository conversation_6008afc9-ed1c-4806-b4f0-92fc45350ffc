package chat

import (
	"strings"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sensitive"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/gate"
)

func (this *Chat) InitHDGallery() {
	this.GetServer().RegisterGO("HD_GetGalleryInfo", this.getGalleryInfo)             // 获取所有图鉴信息
	this.GetServer().RegisterGO("HD_GetGalleryComments", this.getGalleryComments)     // 获取指定图鉴评论信息
	this.GetServer().RegisterGO("HD_CommentGallery", this.commentGallery)             // 发表图鉴评论
	this.GetServer().RegisterGO("HD_GalleryCommentPraise", this.galleryCommentPraise) // 图鉴评论点赞
}

// 获取所有图鉴信息
func (this *Chat) getGalleryInfo(session gate.Session, msg *pb.CHAT_HD_GETGALLERYINFO_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	return pb.ProtoMarshal(&pb.CHAT_HD_GETGALLERYINFO_S2C{List: galleryModel.GetAllGalleryInfos()})
}

// 获取指定图鉴评论信息
func (this *Chat) getGalleryComments(session gate.Session, msg *pb.CHAT_HD_GETGALLERYCOMMENTS_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id, typeId := msg.GetId(), msg.GetType()
	galleryInfo := galleryModel.GetGalleryInfo(id, typeId)
	arr := []*pb.GalleryCommentInfo{}
	var info *pb.GalleryInfo = nil
	if galleryInfo != nil {
		info = galleryInfo.ToPb()
		arr = galleryInfo.GetComments(uid)
	}
	return pb.ProtoMarshal(&pb.CHAT_HD_GETGALLERYCOMMENTS_S2C{
		List:        arr,
		GalleryInfo: info,
	})
}

// 发表图鉴评论
func (this *Chat) commentGallery(session gate.Session, msg *pb.CHAT_HD_COMMENTGALLERY_C2S) (ret []byte, err string) {
	uid, lid := checkSession(session), session.Get("lid")
	if uid == "" || lid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id, typeId, star, content, version := msg.GetId(), msg.GetType(), msg.GetStar(), msg.GetContent(), msg.GetVersion()
	if ut.GetStringLen(content) > 200 {
		return nil, ecode.TEXT_LEN_LIMIT.String()
	} else if strings.Contains(content, "\n") || strings.Contains(content, "\t") {
		return nil, ecode.TEXT_HAS_SPECIAL_SYMBOL.String()
	} else if sensitive.Validate(content) {
		return nil, ecode.TEXT_HAS_SENSITIVE.String()
	}
	galleryInfo := galleryModel.GetGalleryInfoOrAdd(id, typeId)
	if galleryInfo == nil {
		return nil, ecode.UNKNOWN.String()
	}
	// rpc获取玩家信息
	if info, _ := ut.RpcInterfaceMap(this.InvokeLobbyRpc(lid, slg.RPC_GET_ONLINE_USER_INFO, uid)); info != nil {
		nickname := ut.String(info["nickname"])
		headIcon := ut.String(info["headIcon"])
		rankScore := ut.Int32(info["rankScore"])
		// 添加评价
		comment := galleryInfo.AddComment(uid, nickname, headIcon, star, rankScore, content, version)
		return pb.ProtoMarshal(&pb.CHAT_HD_COMMENTGALLERY_S2C{
			Comment:     comment.ToPb(uid),
			GalleryInfo: galleryInfo.ToPb(),
		})
	} else {
		return nil, ecode.NOT_BIND_UID.String()
	}
}

// 图鉴评论点赞
func (this *Chat) galleryCommentPraise(session gate.Session, msg *pb.CHAT_HD_GALLERYCOMMENTPRAISE_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id, typeId, praises := msg.GetId(), msg.GetType(), msg.GetPraises()
	galleryModel.GalleryCommentPraise(uid, id, typeId, praises)
	return pb.ProtoMarshal(&pb.CHAT_HD_GALLERYCOMMENTPRAISE_S2C{})
}
