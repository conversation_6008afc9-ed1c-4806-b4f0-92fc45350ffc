package match

import (
	"context"
	"errors"
	"math/rand"
	"strings"
	"time"

	"slgsrv/gener"
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	r "slgsrv/server/game/room"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

const (
	CLOSE_GAME_TIME         = ut.TIME_DAY * 3
	GAME_SERVER_UPDATE_TIME = 10 * 1000 // 游戏服信息更新时间间隔10s
)

type Room struct {
	clientVersion    string // 要求客户端版本
	clientBigVersion string // 客户端大版本
	Addr             string // 所在物理机地址

	mapSize      *ut.Vec2          // 地图大小
	gameOverInfo *slg.GameOverInfo // 游戏结束信息
	winCond      []int32           // 胜利条件 [type,value] 1.领地争夺 2.修建遗迹
	ApplyUids    [][]string        // 报名玩家uid列表 对应玩家选择的方位
	mainCitys    [][]int32         // 玩家剩余可创建的位置

	applyMutex *deadlock.RWMutex

	InitTime        int64 // 初始化时间
	createTime      int64 // 创建时间
	ApplyFinishTime int64 // 报名完成时间
	OpenRoomTime    int64 // 服务器开启时间

	Id         int32 // 房间唯一id
	pers       int32 // 人数
	PersCap    int32 // 剩余人数容量
	SurplusDay int32 // 剩余天数

	Type    uint8 // 对局类型 0.普通区 1.新手区 2.评分区
	SubType uint8 // 子类型
	state   int8  // 0关闭 1开放 2报名中

	isClose bool // 是否关闭
	Save    bool // 是否保留
}

var (
	rooms           = ut.NewMapLock[int32, *Room]() // 游戏服map
	createRoomChan  = make(chan int32, 100)
	openRoomChan    = make(chan int32, 100)
	loadRoomChan    = make(chan int32, 100)
	closeRoomChan   = make(chan *Room, 100)
	genSidMap       = ut.NewMapLock[int32, int32]()
	preGenSidMap    = ut.NewMapLock[int32, int32]()
	loadRoomsFinish = false
	createRoomLock  = new(deadlock.Mutex)

	allServerMaintainTime int64 = 0 // 维护后开启时间
)

// 是否已经关闭
func (this *Room) IsClose() bool {
	if this.isClose {
		return true
	}
	this.isClose = this.GetSurplusCloseTime() < 0
	return this.isClose
}

// 返回-1 表示已经关闭
func (this *Room) GetSurplusCloseTime() int64 {
	if this.gameOverInfo == nil {
		return 0
	}
	var closeTime int64 = CLOSE_GAME_TIME
	if len(this.winCond) >= 4 {
		closeTime = int64(this.winCond[3])
	}
	return ut.MaxInt64(-1, int64(closeTime)-(time.Now().UnixMilli()-this.gameOverInfo.EndTime))
}

// 是否结束
func (this *Room) IsGameOver() bool { return this.gameOverInfo != nil }

// 获取可以创建主城的区域数量
func (this *Room) GetCanCreateAreaCount() []int32 {
	arr := []int32{}
	for _, m := range this.mainCitys {
		arr = append(arr, int32(ut.Max(0, len(m)-constant.MAIN_CITY_PERS_MIN_COUNT)))
	}
	return arr
}

// 获取运行天数
func (this *Room) GetRunDay() int32 {
	if this.createTime == 0 {
		return 0
	}
	return int32(ut.Ceil(float64(time.Now().UnixMilli()-this.createTime) / float64(ut.TIME_DAY)))
}

// 加载游戏服物理机
func LoadGameMachs() {
	log.Info("LoadGameMachs start")
	machList, err := db_game_machs.FindAllGameMachs()
	if err == nil {
		for i, l := 0, len(machList); i < l; i++ {
			mach := &machList[i]
			if mach.SidList == nil {
				mach.SidList = []int32{}
			}
			if mach.MaxCap <= 0 {
				// 最大容量兼容
				mach.setMachMaxCap(ut.String(slg.MACH_MAX_SERVER_COUNT))
				updateMachInfo(mach)
			} else {
				machs.MachsMap.Set(mach.Addr, mach)
			}
			log.Info("LoadGameServer ip: %v", mach.Addr)
		}
	}
	log.Info("LoadGameMachs end")
}

// 初始化所有房间
func LoadAllRoom(app *Match) string {
	log.Info("load rooms start.")
	datas, err := db_room.FindAll()
	if err != "" {
		log.Error("LoadAllRoom db error", err)
		return err
	}
	rooms = ut.NewMapLock[int32, *Room]()
	for _, data := range datas {
		LoadRoomFromDbData(data)
		key := GetGenIdMapKey(data.Type, data.SubType)
		subType := uint8(key / (slg.ROOM_TYPE_FLAG / slg.ROOM_SUB_TYPE_FLAT))
		roomType := uint8(key % (slg.ROOM_TYPE_FLAG / slg.ROOM_SUB_TYPE_FLAT))
		if data.State != slg.SERVER_STATUS_PRE {
			genSid := genSidMap.Get(key)
			if genSid == 0 {
				// 自增sid兼容
				sid, err := gmMapDb.GetGenSidByRoomType(roomType, subType, false)
				if err == nil && sid > data.Id+1 {
					genSidMap.Set(key, sid)
				} else {
					genSidMap.Set(key, data.Id+1)
				}
			} else if data.Id+1 > genSid {
				genSidMap.Set(key, data.Id+1)
			}
		} else {
			preSid := preGenSidMap.Get(key)
			if preSid == 0 {
				// 提前创建自增id兼容
				sid, err := gmMapDb.GetGenSidByRoomType(roomType, subType, true)
				if err == nil && sid > data.Id+1 {
					preGenSidMap.Set(key, sid)
				} else {
					preGenSidMap.Set(key, data.Id+1)
				}
			} else if data.Id+1 > preSid {
				preGenSidMap.Set(key, data.Id+1)
			}
		}
	}
	loadRoomsFinish = true
	log.Info("load rooms done.")
	return ""
}

// 加载大厅服物理机
func LoadLobbyMachs() {
	log.Info("LoadLobbyMachs start")
	arr, err := db_lobby_machs.FindAllLobbyMachs()
	if err != nil {
		log.Error("LoadLobbyMachs err: %v", err)
		return
	}
	for i, l := 0, len(arr); i < l; i++ {
		mach := &arr[i]
		lobbyMachs.MachsMap.Set(mach.Addr, mach)
		log.Info("LoadLobbyMachs ip: %v", mach.Addr)
	}
	log.Info("LoadLobbyMachs end")
}

// 加载辅助功能物理机
func LoadSupMachs() {
	log.Info("LoadSupMachs start")
	arr, err := db_sup_machs.FindAllSupMachs()
	if err != nil {
		log.Error("LoadSupMachs err: %v", err)
		return
	}
	for i, l := 0, len(arr); i < l; i++ {
		mach := &arr[i]
		supMachs.MachsMap.Set(mach.Addr, mach)
		log.Info("LoadSupMachs ip: %v, type: %v", mach.Addr, mach.Type)
	}
	log.Info("LoadSupMachs end")
}

func LoadRoomFromDbData(data r.RoomTableData) {
	room := &Room{
		Id:               data.Id,
		Type:             data.Type,
		createTime:       data.CreateTime,
		mapSize:          data.MapSize,
		winCond:          data.WinCond,
		gameOverInfo:     data.GameOverInfo,
		clientVersion:    slg.CLIENT_VERSION,
		clientBigVersion: slg.CLIENT_BIG_VERSION,
		ApplyUids:        data.ApplyUids,
		ApplyFinishTime:  data.ApplyFinishTime,
		OpenRoomTime:     data.OpenRoomTime,
		state:            data.State,
		mainCitys:        data.MainCitys,
		SubType:          data.SubType,
		InitTime:         data.InitTime,
		Save:             data.Save,
	}
	if room.Type == slg.ROOKIE_SERVER_TYPE {
		// 新手区取出主城位置数组 用于计算剩余容量
		for _, v := range data.MainCitys {
			room.PersCap += int32(len(v))
		}
	}
	if room.state == slg.SERVER_STATUS_APPLYING {
		room.applyMutex = new(deadlock.RWMutex)
	}
	// 兼容10点统一开启
	if room.ApplyFinishTime > 0 && room.OpenRoomTime == 0 {
		room.OpenRoomTime = getOpenRoomTime(room.ApplyFinishTime)
	}
	if !room.IsClose() {
		rooms.Set(data.Id, room)
	}
}

// 从数据库加载房间
func LoadRoom(sid int32) {
	log.Info("LoadRoom sid: %v", sid)
	data, err := db_room.Find(sid)
	if err != "" {
		log.Error("LoadRoom db err: %v", err)
		return
	}
	LoadRoomFromDbData(data)
}

// 区服检测tick
func (this *Match) RoomCheck() {
	go func() {
		tiker := time.NewTicker(time.Minute * 1)
		defer tiker.Stop()
		for isRunning {
			this.UpdateRoomInfo()
			<-tiker.C
		}
	}()
}

func (this *Match) UpdateRoomInfo() {
	if !loadRoomsFinish {
		return
	}
	now := time.Now()
	nowTime := now.UnixMilli()
	rookieSerCount := 0 // 已开启的新手服
	rooms.ForEach(func(m *Room, k int32) bool {
		// 更新游戏服信息
		m.UpdateRoomInfo(this)
		if m.state == slg.SERVER_STATUS_CREATING {
			// 创建容错 防止Match服超时未返回 初始化5分钟后仍为创建中状态则从数据库读取
			if nowTime-m.InitTime >= 5*60*1000 {
				loadRoomChan <- m.Id
			}
		} else {
			machInfo := machs.MachsMap.Get(m.Addr)
			// 更新物理机中开启的游戏服
			if m.IsClose() {
				// 游戏服已关闭
				if machInfo != nil {
					delIndex := -1
					machInfo.lock.Lock()
					for i, sid := range machInfo.SidList {
						if sid == m.Id {
							delIndex = i
							machInfo.SidList = append(machInfo.SidList[0:i], machInfo.SidList[i+1:]...)
						}
					}
					machInfo.lock.Unlock()
					if delIndex >= 0 {
						updateMachInfo(machInfo)
					}
					if m.state == slg.SERVER_STATUS_OPEN {
						// 关闭到达关闭时间但未关闭的游戏服
						closeRoomChan <- m
						m.state = slg.SERVER_STATUS_STOP
					}
				}
			} else {
				// 游戏服未关闭
				if m.Type == slg.ROOKIE_SERVER_TYPE && !m.IsGameOver() &&
					m.PersCap > slg.ROOKIE_CAP_MIN && nowTime < slg.GetSummerStartTime(m.createTime) {
					// 未结束且有剩余容量的新手服计入有效数量 夏天之前计入有效数量
					rookieSerCount++
				}
				// 兼容开启中的物理机区服列表
				machChange := false
				if machInfo != nil {
					machInfo.lock.Lock()
					for _, sid := range machInfo.SidList {
						if sid == m.Id {
							machInfo.lock.Unlock()
							return true
						}
					}
					// machInfo.lock.RUnlock()
					// machInfo.lock.Lock()
					machInfo.SidList = append(machInfo.SidList, m.Id)
					machInfo.lock.Unlock()
					machChange = true
				} else if m.Addr != "" {
					machInfo = &Mach{
						Addr:        m.Addr,
						SidList:     []int32{m.Id},
						ServerTypes: []uint8{},
						MaxCap:      slg.MACH_MAX_SERVER_COUNT,
					}
					machChange = true
				}
				if machChange && machInfo.Addr != "" {
					updateMachInfo(machInfo)
				}
			}
		}
		return true
	})
	if !slg.AUTO_CREATE_SERVER {
		// 未自动开服
		return
	}
	rookieNeedCount := slg.ROOKIE_SERVER_COUNT - rookieSerCount
	log.Info("RoomCheck create Rooms rookieNeedCount: %v", rookieNeedCount)
	// 创建新手服
	for i := 0; i < rookieNeedCount; i++ {
		this.PreCreateRoom(slg.ROOKIE_SERVER_TYPE, slg.ROOKIE_SERVER_SUB_TYPE_STAND, 0, "")
	}

	// 开启自定义区服
	openCustomRooms := []*CustomRoom{}
	CustinRooms.ForEach(func(v *CustomRoom, k int32) bool {
		if v.OpenTime >= nowTime && !v.Opened {
			openCustomRooms = append(openCustomRooms, v)
		}
		return true
	})
	for _, v := range openCustomRooms {
		this.OpenCustomRoom(v)
	}
}

// 更新所有游戏服
func (this *Match) UpdateRooms() {
	rooms.ForEach(func(v *Room, k int32) bool {
		v.UpdateRoomInfo(this)
		return true
	})
}

// 更新游戏服
func (this *Room) UpdateRoomInfo(app *Match) {
	if this.state == slg.SERVER_STATUS_PRE || this.state == slg.SERVER_STATUS_APPLYING || this.state == slg.SERVER_STATUS_CREATING {
		// 预创建、创建中和报名中的服不主动更新
		return
	}
	if this.IsClose() {
		// 已结束关闭的服不主动更新
		return
	}
	msg, err := ut.RpcInterfaceMap(app.InvokeGameRpc(this.Id, slg.RPC_GET_ROOM_INFO, this.Id))
	if err == "" && msg != nil {
		this.UpdateRoomByGameMsg(msg)
		// log.Info("UpdateRoomInfo sid: %v, state: %v", this.Id, this.state)
	} else {
		this.state = slg.SERVER_STATUS_STOP
		// log.Info("UpdateRoomInfo sid: %v, err: %v", this.Id, err)
	}
}

func (this *Room) UpdateRoomByGameMsg(msg map[string]interface{}) {
	this.Type = uint8(ut.Int(msg["type"]))
	this.pers = ut.Int32(msg["pers"])
	this.PersCap = ut.Int32(msg["persCap"])
	this.SurplusDay = ut.Int32(msg["surplusDay"])
	this.clientVersion = ut.String(msg["clientVersion"])
	this.clientBigVersion = ut.String(msg["clientBigVersion"])
	this.createTime = ut.Int64(msg["createTime"])
	this.Addr = ut.String(msg["addr"])
	if winName := ut.String(msg["winName"]); winName != "" {
		if this.gameOverInfo == nil {
			this.gameOverInfo = &slg.GameOverInfo{}
		}
		this.gameOverInfo.WinName = winName
		this.gameOverInfo.WinType = ut.Int32(msg["winType"])
		this.gameOverInfo.WinCellCount = ut.Int32(msg["winCellCount"])
		this.gameOverInfo.EndTime = ut.Int64(msg["endTime"])
	}
	this.state = int8(ut.Int(msg["state"]))
}

func GetRoomById(sid int32) *Room {
	return rooms.Get(sid)
}

func OnRoomStateChange(msg map[string]interface{}) {
	sid := ut.Int32(msg["id"])
	room := rooms.Get(sid)
	if room != nil {
		room.UpdateRoomByGameMsg(msg)
	}
	log.Info("OnRoomStateChange sid=%v, state=%v", sid, room.state)
}

func (this *Room) ToPb(now int64) *pb.ServerInfo {
	roomInfo := &pb.ServerInfo{}
	if len(this.winCond) > 0 {
		roomInfo.WinCondType = int32(this.winCond[0])
	}
	roomInfo.Id = int32(this.Id)
	roomInfo.Type = int32(this.Type)
	roomInfo.CreateTime = int64(this.createTime)
	roomInfo.ClientVersion = this.clientVersion
	roomInfo.ClientBigVersion = this.clientBigVersion
	roomInfo.State = int32(this.state)
	surplusCloseTime := this.GetSurplusCloseTime()
	if surplusCloseTime < 0 { // 服务器已关闭
		roomInfo.IsClose = true
	} else {
		roomInfo.Pers = pb.Int32(this.pers)
		roomInfo.PersCap = pb.Int32(this.PersCap)
		roomInfo.SurplusDay = pb.Int32(this.SurplusDay)
	}
	if this.IsGameOver() {
		roomInfo.WinType = pb.Int32(this.gameOverInfo.WinType)
		roomInfo.WinName = pb.String(this.gameOverInfo.WinName)
		roomInfo.WinCellCount = pb.Int32(this.gameOverInfo.WinCellCount)
		roomInfo.EndTime = pb.Int64(this.gameOverInfo.EndTime)
	}
	return roomInfo
}

func ToPbRooms(serverType uint8) []*pb.ServerInfo {
	arr := []*pb.ServerInfo{}
	now := time.Now().UnixMilli()
	rooms.ForEach(func(v *Room, k int32) bool {
		if v.Type != serverType || v.state == slg.SERVER_STATUS_CREATING {
			return true
		}

		arr = append(arr, v.ToPb(now))
		return true
	})
	return arr
}

// 创建游戏服tick
func (this *Match) RunCreateRoom() {
	go func() {
		for isRunning {
			if sid, ok := <-createRoomChan; ok {
				roomType := sid / slg.ROOM_TYPE_FLAG
				subType := GetSubTypeBySid(sid)
				this.CreateGameRoom(uint8(roomType), uint8(subType), sid)
			}
		}
	}()
}

// 预创建游戏服
func (this *Match) PreCreateRoom(roomType, subType uint8, sid int32, addr string) int32 {
	if sid == 0 {
		sid = getGenSid(roomType, subType)
	}
	log.Info("PreCreateRoom roomType: %v, sid: %v", roomType, sid)
	if roomType == slg.ROOKIE_SERVER_TYPE {
		// 创建新手服需要选择物理机直接开启
		addr = AddServerToMach(sid, roomType)
		if addr == "" {
			return -1
		}
	}
	// 创建临时房间数据占位 等待Match服创建区服完成后再从数据库获取
	room := &Room{
		Id:       sid,
		Type:     roomType,
		state:    slg.SERVER_STATUS_CREATING,
		Addr:     addr,
		SubType:  subType,
		InitTime: time.Now().UnixMilli(),
	}
	rooms.Set(sid, room)
	if roomType == slg.CUSTOM_SERVER_TYPE {
		// 自定义房间已更新过自增id
	} else {
		// 更新自增id
		typeKey := GetGenIdMapKey(roomType, subType)
		genSid := genSidMap.Get(typeKey)
		setGenSid(typeKey, genSid)
	}
	createRoomChan <- sid
	return sid
}

// 开启游戏服tick
func (this *Match) RunOpenRoom() {
	go func() {
		for isRunning {
			if sid, ok := <-openRoomChan; ok {
				this.OpenGameRoom(sid)
			}
		}
	}()
}

// 创建游戏服
func (this *Match) CreateGameRoom(roomType, subType uint8, sid int32) bool {
	log.Info("CreateGameRoom roomType: %v, sid: %v", roomType, sid)
	addr := ""
	if roomType == slg.ROOKIE_SERVER_TYPE {
		// 从预创建中获取物理机地址
		room := GetRoomById(sid)
		if room == nil {
			log.Error("CreateGameRoom preCreate err sid: %v", sid)
			return false
		}
		addr = room.Addr
	}
	var state int8
	switch roomType {
	case slg.NORMAL_SERVER_TYPE, slg.RANK_SERVER_TYPE:
		// 普通服和排位服创建为报名中状态
		state = slg.SERVER_STATUS_APPLYING
	case slg.ROOKIE_SERVER_TYPE:
		// 新手服创建到开启之前为创建中状态
		state = slg.SERVER_STATUS_CREATING
	}
	err := CreateRoom(sid, roomType, subType, state, addr, false)
	if err != nil {
		log.Error("CreateGameRoom sid: %v, err: %v", sid, err)
	}
	// 从数据库中加载新创建的服
	loadRoomChan <- sid
	return true
}

// 开启游戏服
func (this *Match) OpenGameRoom(sid int32) bool {
	log.Info("OpenGameRoom sid: %v", sid)
	room := GetRoomById(sid)
	if room == nil {
		log.Error("OpenGameRoom is nil sid: %v", sid)
		return false
	}
	// 获取物理机
	addr := AddServerToMach(sid, room.Type)
	if addr == "" {
		return false
	}
	if !slg.IsLocal() {
		_, err := ut.SshExcuteShell(addr, slg.OPEN_GAME_SERVER_BASH, ut.String(sid))
		if err != nil {
			log.Error("OpenGameRoom sid: %v, err: %v", sid, err)
			return false
		}
	}
	return true
}

// // 玩家报名
// func (this *Match) UserApply(uid, name, headicon, lang string, sid, pos int) (posArr []int32, pers, surplusOpenTime int, err string) {
// 	room := GetRoomById(sid)
// 	if room == nil {
// 		// 服务器不存在
// 		err = ecode.ROOM_NOT_EXIST.String()
// 		return
// 	}
// 	if room.state != slg.SERVER_STATUS_APPLYING {
// 		// 服务器不在报名中
// 		err = ecode.ROOM_NOT_EXIST.String()
// 		return
// 	}
// 	if room.ApplyFinishTime > 0 {
// 		// 区服报名已完成不可报名
// 		err = ecode.SERVER_APPLY_FINISHED.String()
// 		return
// 	}
// 	room.applyMutex.Lock()
// 	defer room.applyMutex.Unlock()
// 	if room.Type == slg.RANK_SERVER_TYPE {
// 		pos = room.getRandMainCityPos()
// 		if pos < 0 {
// 			err = ecode.ROOM_FULL.String()
// 			return
// 		}
// 	} else if pos < 0 {
// 		pos = 0
// 	} else if pos >= len(room.mainCitys) {
// 		pos = len(room.mainCitys) - 1
// 	}
// 	cnt := len(room.mainCitys[pos])
// 	if room.ApplyUids[pos] != nil && len(room.ApplyUids[pos]) >= cnt {
// 		// 该方位主城位置已满
// 		err = ecode.NOT_CITY_INDEX.String()
// 		posArr = room.GetCanCreateAreaCount()
// 	}
// 	if room.ApplyUids[pos] == nil {
// 		room.ApplyUids[pos] = []string{}
// 	}
// 	room.ApplyUids[pos] = append(room.ApplyUids[pos], uid)
// 	db_room.UpdatePlayerApply(room)
// 	if room.ApplyFinishTime == 0 {
// 		// 报名还未完成
// 		plyCount := room.getServerApplyNum()
// 		if plyCount >= slg.SERVER_APPLY_FINISH_NUMBER {
// 			// 达到完成报名人数
// 			room.ApplyFinishTime = time.Now().UnixMilli()
// 			room.OpenRoomTime = getOpenRoomTime(room.ApplyFinishTime)
// 			db_room.UpdateRoomApplyFinish(room)
// 		}
// 	}
// 	pers = room.getServerApplyNum()
// 	surplusOpenTime = int(getSurplusOpenTime(room.ApplyFinishTime, room.OpenRoomTime))
// 	return
// }

// 玩家取消报名
// func (this *Match) UserCancelApply(uid string, sid int) (posArr []int32, pers, surplusOpenTime int, err string) {
// 	room := GetRoomById(sid)
// 	if room == nil {
// 		// 服务器不存在
// 		err = ecode.ROOM_NOT_EXIST.String()
// 		return
// 	}
// 	if room.state != slg.SERVER_STATUS_APPLYING {
// 		// 服务器不在报名中
// 		err = ecode.ROOM_NOT_EXIST.String()
// 		return
// 	}
// 	if room.ApplyFinishTime > 0 {
// 		// 区服报名已完成不可取消报名
// 		err = ecode.SERVER_APPLY_FINISHED.String()
// 		return
// 	}
// 	room.applyMutex.Lock()
// 	defer room.applyMutex.Unlock()
// 	pos, index := -1, -1
// 	for p, arr := range room.ApplyUids {
// 		for i, user := range arr {
// 			if user == uid {
// 				pos = p
// 				index = i
// 				break
// 			}
// 		}
// 	}
// 	if pos >= 0 && index >= 0 {
// 		room.ApplyUids[pos] = append(room.ApplyUids[pos][0:index], room.ApplyUids[pos][index+1:]...)
// 		db_room.UpdatePlayerApply(room)
// 	}
// 	pers = room.getServerApplyNum()
// 	surplusOpenTime = int(getSurplusOpenTime(room.ApplyFinishTime, room.OpenRoomTime))
// 	return
// }

// 获取自增sid
func getGenSid(roomType, subType uint8) int32 {
	key := GetGenIdMapKey(roomType, subType)
	sid := genSidMap.Get(key)
	if sid == 0 {
		sid, _ = gmMapDb.GetGenSidByRoomType(roomType, subType, false)
		genSidMap.Set(key, sid)
	}
	return sid
}

// 获取报名完成的区服剩余开启时间
func getSurplusOpenTime(finishTime, openRoomTime int64) int32 {
	if finishTime <= 0 {
		return 0
	}
	return int32(openRoomTime - time.Now().UnixMilli())
}

// 获取报名中的区服人数
func (this *Room) getServerApplyNum() int {
	plyCount := 0
	for _, arr := range this.ApplyUids {
		plyCount += len(arr)
	}
	return plyCount
}

// 获取随机主城区域
func (this *Room) getRandMainCityPos() int {
	arr := []int{}
	for pos, v := range this.mainCitys {
		if len(v) > 0 {
			arr = append(arr, pos)
		}
	}
	if len(arr) == 0 {
		return -1
	}
	return rand.Intn(len(arr))
}

// 根据sid获取子类型
func GetSubTypeBySid(sid int32) int32 {
	return sid / slg.ROOM_SUB_TYPE_FLAT % (slg.ROOM_TYPE_FLAG / slg.ROOM_SUB_TYPE_FLAT)
}

// 添加物理机
func (this *Match) AddGameMach(ip, serverTypes, maxCap, name string) (mach *Mach, err error) {
	mach = &Mach{
		Addr:    ip,
		SidList: []int32{},
		MaxCap:  slg.MACH_MAX_SERVER_COUNT,
		Name:    name,
	}
	mach.setMachServerTypes(serverTypes)
	mach.setMachMaxCap(maxCap)
	err = db_game_machs.AddGameMach(mach)
	if err == nil {
		machs.MachsMap.Set(ip, mach)
	}
	return
}

// 关闭游戏服tick
func (this *Match) RunCloseRoom() {
	go func() {
		tiker := time.NewTicker(time.Minute * 1)
		for isRunning {
			<-tiker.C
			if len(closeRoomChan) > 0 {
				if room, ok := <-closeRoomChan; ok {
					this.closeGameServer(room.Addr, room.Id)
				}
			}
		}
	}()
}

// 关闭游戏服
func (this *Match) closeGameServer(addr string, sid int32) {
	if slg.IsLocal() {
		return
	}
	go ut.SshExcuteShell(addr, slg.CLOSE_GAME_SERVER_BASH, ut.String(sid))
	// 通知邮件服删除邮件
	this.InvokeNR(slg.MACH_SERVER_TYPE_MAIL, slg.RPC_DEL_MAIL_BY_SID, ut.Bytes(sid))
}

// 加载游戏服
func (this *Match) RunLoadRoom() {
	go func() {
		for isRunning {
			if sid, ok := <-loadRoomChan; ok {
				LoadRoom(sid)
			}
		}
	}()
}

// 更新物理机信息
func updateMachInfo(mach *Mach) error {
	log.Info("updateMachInfo start: %v", mach.Addr)
	err := db_game_machs.UpdateGameMach(mach)
	if err != nil {
		log.Error("updateMachInfo mach: %v, err: %v", mach, err)
	} else {
		machs.MachsMap.Set(mach.Addr, mach)
	}
	log.Info("updateMachInfo end: %v", mach.Addr)
	return err
}

// 更新物理机容量
func (this *Mach) UpdateGameMachMaxCap(maxCap string) error {
	this.setMachMaxCap(maxCap)
	return updateMachInfo(this)
}

// 更新物理机可开启服务器类型
func (this *Mach) UpdateGameMachMachServerTypes(serverTypes string) error {
	this.setMachServerTypes(serverTypes)
	return updateMachInfo(this)
}

// 更新物理机名字
func (this *Mach) UpdateGameMachName(name string) error {
	this.Name = name
	return updateMachInfo(this)
}

// 更新物理机开启状态
func (this *Mach) UpdateGameMachStatus(status int8) error {
	this.Status = status
	return updateMachInfo(this)
}

// 更新物理机代码更新状态
func (this *Mach) UpdateGameMachUpdateStatus(status int8) error {
	this.UpdateStatus = status
	return updateMachInfo(this)
}

// 设置物理机可开启服务器类型
func (this *Mach) setMachServerTypes(serverTypes string) {
	this.ServerTypes = []uint8{}
	serverTypesArr := strings.Split(serverTypes, ",")
	if len(serverTypesArr) == 0 {
		this.ServerTypes = append(this.ServerTypes, uint8(ut.Int(serverTypes)))
	} else {
		for _, v := range serverTypesArr {
			this.ServerTypes = append(this.ServerTypes, uint8(ut.Int(v)))
		}
	}
}

// 设置物理机最大容量
func (this *Mach) setMachMaxCap(maxCap string) {
	_maxCap := ut.Int32(maxCap)
	if _maxCap > 0 {
		this.MaxCap = _maxCap
	}
}

// 删除物理机
func (this *Mach) delMach() error {
	if len(this.SidList) > 0 {
		// 有逻辑服在运行 物理机设置为禁用状态
		this.UpdateGameMachMachServerTypes("-1")
		return nil
	} else {
		machs.MachsMap.Del(this.Addr)
		return db_game_machs.DelMach(this)
	}
}

// 是否在开服时间段内
func checkInOpenRoomTimeRange(now time.Time) bool {
	nowHour := now.Hour()
	return nowHour >= slg.SERVER_APPLY_OPEN_TIME_START && nowHour < slg.SERVER_APPLY_OPEN_TIME_END
}

// 获取开服时间
func getOpenRoomTime(finishTime int64) int64 {
	date := time.Unix(int64(finishTime/1000), 0)
	year, month, day := date.Date()
	hour := date.Hour()
	addTime := time.Date(year, month, day, slg.SERVER_APPLY_OPEN_TIME, 0, 0, 0, date.Location())
	openTime := int64(addTime.UnixNano() / 1e6)
	if hour >= slg.SERVER_APPLY_OPEN_TIME {
		// 报名完成时间超过10点 在第二天10点开启
		return openTime + ut.TIME_DAY
	}
	return openTime
}

// 所有游戏服物理机更新代码
func updateAllGameCode() {
	machList := []*Mach{}
	machs.MachsMap.ForEach(func(mach *Mach, k string) bool {
		if mach.UpdateStatus != slg.MACH_UPDATE_STATUS_UPDATING {
			machList = append(machList, mach)
		}
		return true
	})
	for _, m := range machList {
		m.HandleCodeUpdate()
	}
}

// 查询所有游戏服物理机代码是否更新完成
func getUpdateAllGameCodeFinish() bool {
	rst := true
	machs.MachsMap.ForEach(func(mach *Mach, k string) bool {
		if mach.UpdateStatus == slg.MACH_UPDATE_STATUS_UPDATING {
			rst = false
			return false
		}
		return true
	})
	return rst
}

// 所有游戏服停机
func (this *Match) stopAllGameServers() (result map[string]interface{}, err string) {
	machList := []*Mach{}
	machs.MachsMap.ForEach(func(mach *Mach, k string) bool {
		if mach.Status != slg.MACH_STATUS_STOPPING {
			machList = append(machList, mach)
		}
		return true
	})
	for _, m := range machList {
		m.UpdateGameMachStatus(slg.MACH_STATUS_STOPPING)
		m.lock.RLock()
		sidList := array.Clone(m.SidList)
		m.lock.RUnlock()
		for _, sid := range sidList {
			this.InvokeNR("game@game"+ut.Itoa(sid), slg.RPC_STOP_SERVER, ut.Bytes(sid))
		}
	}
	return
}

// 开启自定义区服
func (this *Match) OpenCustomRoom(customRoom *CustomRoom) {
	customRoom.SetOpened(true)
	if rooms.Get(customRoom.Sid) != nil {
		// 区服已存在
		return
	}
	// 更新队伍到redis缓存
	rdsPipline := rds.RdsPipeline()
	for _, team := range customRoom.TeamList {
		rdsPipline.HSet(context.TODO(), rds.RDS_CREATE_ROOM_TEAM_MAP_KEY+ut.String(customRoom.Sid), team.Uid, true)
	}
	rdsPipline.Exec(context.TODO())
	this.PreCreateRoom(slg.CUSTOM_SERVER_TYPE, 0, customRoom.Sid, customRoom.MachAddr)
}

// 查询所有游戏服是否停机完成
func getAllGameServersStopFinish() bool {
	rst := true
	roomStateMap := map[int32]int8{}
	rooms.ForEach(func(v *Room, k int32) bool {
		if v.IsClose() {
			return true
		}
		roomStateMap[k] = v.state
		return true
	})
	machList := []*Mach{}
	machs.MachsMap.ForEach(func(mach *Mach, k string) bool {
		if mach.Status == slg.MACH_STATUS_STOPPED {
			return true
		} else if mach.Status != slg.MACH_STATUS_STOPPING {
			rst = false
			return false
		}
		stopFinish := true
		mach.lock.RLock()
		for _, sid := range mach.SidList {
			if roomStateMap[sid] == slg.SERVER_STATUS_OPEN {
				stopFinish = false
			}
		}
		mach.lock.RUnlock()
		if !stopFinish {
			rst = false
			return false
		} else if mach.Status != slg.MACH_STATUS_STOPPED {
			machList = append(machList, mach)
		}
		return true
	})
	// 该物理机上全部游戏服停机完成后修改状态
	for _, m := range machList {
		m.UpdateGameMachStatus(slg.MACH_STATUS_STOPPED)
	}
	return rst
}

// 关闭所有游戏服
func closeAllGameServers() {
	machList := []*Mach{}
	machs.MachsMap.ForEach(func(mach *Mach, k string) bool {
		if mach.Status != slg.MACH_STATUS_STOPPED {
			return true
		}
		_, e := ut.SshExcuteShell(mach.Addr, slg.STOP_SERVER_BASH)
		if e != nil {
			log.Error("closeAllGameServers ip: %v, name: %v, err: %v", mach.Addr, mach.Name, e)
		}
		machList = append(machList, mach)
		return true
	})
	for _, m := range machList {
		m.UpdateGameMachStatus(slg.MACH_STATUS_CLOSING)
	}
}

// 查询所有游戏服是否关闭完成
func getCloseAllGameServersFinish() bool {
	rst := true
	machList := []*Mach{}
	machs.MachsMap.ForEach(func(mach *Mach, k string) bool {
		if len(mach.SidList) == 0 {
			// 物理机中未开启游戏服则直接更新状态
			machList = append(machList, mach)
			return true
		}
		if mach.Status == slg.MACH_STATUS_CLOSE {
			return true
		} else if mach.Status != slg.MACH_STATUS_CLOSING {
			rst = false
			return false
		}
		ret, e := ut.SshExcuteShell(mach.Addr, slg.SHOW_PID_BASH)
		if e != nil {
			log.Error("getCloseAllGameServersFinish ip: %v, name: %v, err: %v", mach.Addr, mach.Name, e)
			rst = false
			return false
		} else if pInfos := strings.Split(ret, slg.PROCESS_DIR); len(pInfos) <= 1 {
			machList = append(machList, mach)
		} else {
			rst = false
			return false
		}
		return true
	})
	// 根据进程查询输出更新状态
	for _, m := range machList {
		m.UpdateGameMachStatus(slg.MACH_STATUS_CLOSE)
	}
	return rst
}

// 启动所有游戏服
func openAllGameServers() {
	machList := []*Mach{}
	machs.MachsMap.ForEach(func(mach *Mach, k string) bool {
		if len(mach.SidList) == 0 {
			// 物理机中未开启游戏服则直接更新状态
			machList = append(machList, mach)
			return true
		}
		if mach.Status != slg.MACH_STATUS_CLOSE {
			return true
		}
		_, e := ut.SshExcuteShell(mach.Addr, slg.START_SERVER_BASH)
		if e != nil {
			log.Error("startAllGameServers ip: %v, name: %v, err: %v", mach.Addr, mach.Name, e)
			return true
		}
		machList = append(machList, mach)
		return true
	})
	for _, m := range machList {
		m.UpdateGameMachStatus(slg.MACH_STATUS_OPENNING)
	}
}

// 查询所有游戏服是否开启完成
func getOpenAllGameServersFinish() bool {
	rst := true
	machList := []*Mach{}
	machs.MachsMap.ForEach(func(mach *Mach, k string) bool {
		if mach.Status == slg.MACH_STATUS_OPEN {
			return true
		}
		if mach.Status != slg.MACH_STATUS_OPENNING {
			rst = false
			return false
		}
		ret, e := ut.SshExcuteShell(mach.Addr, slg.SHOW_PID_BASH)
		if e != nil {
			log.Error("getCloseAllGameServersFinish ip: %v, name: %v, err: %v", mach.Addr, mach.Name, e)
			rst = false
			return false
		}
		// 根据进程查询输出更新状态
		if pInfos := strings.Split(ret, slg.PROCESS_DIR); len(pInfos) > 1 {
			machList = append(machList, mach)
		} else {
			rst = false
			return false
		}
		return true
	})
	for _, m := range machList {
		m.UpdateGameMachStatus(slg.MACH_STATUS_OPEN)
	}
	return rst
}

// 创建区服
func CreateRoom(sid int32, roomType, subType uint8, state int8, addr string, isPre bool) (err error) {
	needGen := true
	createRoomLock.Lock()
	data, e := db_room.Find(sid)
	if e == "" && data.Id == sid {
		// 数据库已有该区服数据 则不生产区服数据
		needGen = false
	}
	if needGen {
		GenRoomData(sid, roomType, subType, state)
	}
	createRoomLock.Unlock()
	if !slg.IsLocal() && roomType == slg.ROOKIE_SERVER_TYPE && !isPre {
		// 远程执行脚本修改配置并启动服务
		_, err = ut.SshExcuteShell(addr, slg.OPEN_GAME_SERVER_BASH, ut.String(sid))
	}
	return
}

// 生成区服数据
func GenRoomData(sid int32, roomType, subType uint8, state int8) (err error) {
	// 获取被使用次数最少的地图
	var mapId int32
	minUse := 1000
	mapUseInfo := getMapUseInfo()
	for k, v := range mapUseInfo {
		useCount := len(v.SidList)
		if useCount < minUse {
			mapId = k
			minUse = useCount
		}
	}
	if mapId == 0 {
		err = errors.New("CreateRoom get map err")
		return
	}
	// 创建区服并存入数据库
	winCond := GetWinCond(roomType)
	err = gener.GenerRoom(sid, roomType, subType, mapId, state, "/bin/maps/", winCond)
	if err != nil {
		return
	}
	// 更新地图使用情况
	setMapUseInfo(mapId, sid)
	return
}

// 获取胜利条件
func GetWinCond(roomType uint8) []int32 {
	switch roomType {
	case slg.ROOKIE_SERVER_TYPE: // 新手区 遗迹
		return slg.WIN_COND_ANCIENT
	case slg.NORMAL_SERVER_TYPE: // 自由区 遗迹
		return slg.WIN_COND_ANCIENT
	case slg.RANK_SERVER_TYPE: // 排位区 血战到底
		return slg.WIN_COND_CONQUER
	default:
		return nil
	}
}

// 设置区服自增sid
func setGenSid(keyType, sid int32) {
	sid++
	genSidMap.Set(keyType, sid)
	go gmMapDb.UpdateGenSid(keyType, sid, false)
}

// 设置提前创建区服自增sid
func setPreGenSid(keyType, sid int32) {
	sid++
	err := gmMapDb.UpdateGenSid(keyType, sid, true)
	if err != nil {
		log.Error("setPreGenSid err: %v", err)
	}
	preGenSidMap.Set(keyType, sid)
}

func ToHttpRooms() []map[string]interface{} {
	arr := []map[string]interface{}{}
	rooms.ForEach(func(m *Room, k int32) bool {
		surplusCloseTime := m.GetSurplusCloseTime()
		data := map[string]interface{}{
			"id":               m.Id,
			"createTime":       m.createTime,
			"surplusCloseTime": surplusCloseTime,
			"type":             m.Type,
			"winCond":          m.winCond,
			"state":            m.state,
			"save":             m.Save,
		}
		if surplusCloseTime < 0 { // 服务器已关闭
			data["winType"] = m.gameOverInfo.WinType
			data["winName"] = m.gameOverInfo.WinName
			data["winCellCount"] = m.gameOverInfo.WinCellCount
			data["endTime"] = m.gameOverInfo.EndTime
		}
		if m.state == slg.SERVER_STATUS_APPLYING { // 报名中
			data["applyFinishNum"] = slg.SERVER_APPLY_FINISH_NUMBER
			applyNum := 0
			for _, uidList := range m.ApplyUids {
				applyNum += len(uidList)
			}
			data["applyNum"] = applyNum
		}
		arr = append(arr, data)
		return true
	})
	return arr
}

// 清理room数据库数据
func (this *Room) DelDbData() string {
	if this.Save || this.state == slg.SERVER_STATUS_DELETE || !this.IsClose() {
		return ""
	}
	now := time.Now().UnixMilli()
	log.Info("DelDbData start id: %v", this.Id)
	this.state = slg.SERVER_STATUS_DELETE
	// 如果该区服在内存中 也更新状态
	if r := rooms.Get(this.Id); r != nil {
		if !r.IsClose() {
			// 内存中的区服未结束 则不删除
			log.Info("DelDbData room in cache not close id: %v", this.Id)
		}
		r.state = slg.SERVER_STATUS_DELETE
	}
	this.mainCitys = nil
	this.ApplyUids = nil
	db_room.UpdateRoomState(this)
	delColArr := []string{
		slg.DB_COLLECTION_NAME_WORLD,
		slg.DB_COLLECTION_NAME_GAME_PLAYER,
		slg.DB_COLLECTION_NAME_GAME_CHAT,
		slg.DB_COLLECTION_NAME_GAME_BAZAAR,
		slg.DB_COLLECTION_NAME_RECORD_ARMY,
		slg.DB_COLLECTION_NAME_RECORD_BATTLE,
		slg.DB_COLLECTION_NAME_RECORD_BAZAAR,
		slg.DB_COLLECTION_NAME_RECORD_BATTLE_SCORE,
		slg.DB_COLLECTION_NAME_RECORD_BATTLE_SCORE_PLAYER,
		slg.DB_COLLECTION_NAME_BAZAAR_RECORD,
	}
	// 删除room相关数据库表
	delFailColArr := []string{}
	for _, v := range delColArr {
		col := v + "_" + ut.String(this.Id)
		err := DelGameCol(col)
		if err != nil {
			delFailColArr = append(delFailColArr, col)
		}
	}
	delFailStr := ""
	if len(delFailColArr) > 0 {
		for i, v := range delFailColArr {
			delFailStr += v
			if i != len(delFailColArr)-1 {
				delFailStr += ", "
			}
		}
	}
	// 删除相关表中该区服的数据
	DelGameColDataBySid(slg.DB_COLLECTION_NAME_GAME, this.Id)     // 游戏相关
	DelGameColDataBySid(slg.DB_COLLECTION_NAME_ALLIANCE, this.Id) // 联盟
	DelGameColDataBySid(slg.DB_COLLECTION_NAME_MAIL, this.Id)     // 邮件
	log.Info("DelDbData finish delFailColArr: %v, costTime: %vms", delFailStr, time.Now().UnixMilli()-now)
	return delFailStr
}

// 定时清理结束过久的游戏服db
func RunDelRoomsDbTick() {
	go func() {
		tiker := time.NewTicker(time.Minute * 10)
		lastTime := time.Now().UnixMilli()
		for isRunning {
			<-tiker.C
			// 每天凌晨7点检查并清理 db每天6点过冷备完成 留点容错
			timeLast := ut.TodayHourTime(7)
			now := time.Now().UnixMilli()
			log.Info("RunDelRoomsDbTick findAll start")
			if lastTime < timeLast && now > timeLast {
				// 从数据库获取所有区服
				datas, err := db_room.FindAll()
				if err != "" {
					log.Error("RunDelRoomsDbTick LoadAllRoom db error", err)
					continue
				}
				for _, data := range datas {
					room := &Room{
						Id:               data.Id,
						Type:             data.Type,
						createTime:       data.CreateTime,
						mapSize:          data.MapSize,
						winCond:          data.WinCond,
						gameOverInfo:     data.GameOverInfo,
						clientVersion:    slg.CLIENT_VERSION,
						clientBigVersion: slg.CLIENT_BIG_VERSION,
						ApplyUids:        data.ApplyUids,
						ApplyFinishTime:  data.ApplyFinishTime,
						OpenRoomTime:     data.OpenRoomTime,
						state:            data.State,
						mainCitys:        data.MainCitys,
						SubType:          data.SubType,
						InitTime:         data.InitTime,
						Save:             data.Save,
					}
					if room.gameOverInfo == nil || room.Save || room.state == slg.SERVER_STATUS_DELETE || !room.IsClose() {
						continue
					}
					if now-room.gameOverInfo.EndTime > slg.AUTO_DELETE_CLOSE_ROOM_DB_DAYS*ut.TIME_DAY {
						room.DelDbData()
					}
				}
			}
			lastTime = now
		}
	}()
}

// 提前创建游戏服
func RunPreGenRoomTick() {
	if slg.IsDebug() {
		return
	}
	go func() {
		tiker := time.NewTicker(time.Second * 20)
		for isRunning {
			<-tiker.C
			if !loadRoomsFinish {
				continue
			}
			// 目前仅提前创建新手区
			typeKey := GetGenIdMapKey(slg.ROOKIE_SERVER_TYPE, 0)
			genId := genSidMap.Get(typeKey)
			preGenId := preGenSidMap.Get(typeKey)
			if preGenId == 0 {
				preGenId = genId + 1
			}
			if preGenId-genId < slg.ROOKIE_SERVER_PRE_NUM {
				// 提前创建区服数据
				setPreGenSid(typeKey, preGenId)
				CreateRoom(preGenId, slg.ROOKIE_SERVER_TYPE, 0, slg.SERVER_STATUS_PRE, "", true)
			}
		}
	}()
}

// 获取自增id的map主键
func GetGenIdMapKey(roomType, subType uint8) int32 {
	return int32(subType)*(slg.ROOM_TYPE_FLAG/slg.ROOM_SUB_TYPE_FLAT) + int32(roomType)
}
