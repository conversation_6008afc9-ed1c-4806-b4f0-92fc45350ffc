package lobby

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/sasha-s/go-deadlock"
)

type Friend struct {
	Uid      string `json:"uid" bson:"uid"`
	Nickname string `json:"nickname" bson:"nickname"`
	HeadIcon string `json:"headIcon" bson:"headIcon"`
	NoteName string `json:"note_name" bson:"note_name"` // 备注名

	FriendGifts  *FriendGiftList `json:"friend_gifts" bson:"friend_gifts"` // 好友礼物列表
	chats        *FriendChatList // 聊天
	LastChatInfo *FriendChatInfo `json:"last_chat_info" bson:"last_chat_info"` // 最后一条聊天记录

	LastOfflineTime int64 `json:"last_loginTime" bson:"last_loginTime"` // 上次离线时间
	Time            int64 `json:"time" bson:"time"`                     // 时间

	NotReadCount int32 `json:"not_read_count" bson:"not_read_count"` // 未读消息数量
	PlaySid      int32 `json:"play_sid" bson:"play_sid"`             // 当前游玩区服id
	Active       bool  `json:"active" bson:"active"`                 // 聊天记录存在主动添加好友的玩家身上
}

type ApplyFriend struct {
	Uid      string `json:"uid" bson:"uid"`
	Nickname string `json:"nickname" bson:"nickname"`
	HeadIcon string `json:"headIcon" bson:"headIcon"`
	Time     int64  `json:"time" bson:"time"`         // 时间
	PlaySid  int32  `json:"play_sid" bson:"play_sid"` // 当前游玩区服id
}

type FriendChatList struct {
	deadlock.RWMutex
	List         []*FriendChatInfo `json:"list" bson:"list"` // 聊天
	NeedUpdateDB bool              // 是否需要保存数据库
	Loaded       bool              // 是否已从数据库获取
}

type FriendChatInfo struct {
	UID     string `bson:"uid"`
	Sender  string `bson:"sender"`  // 发送者
	Content string `bson:"content"` // 内容
	Time    int64  `bson:"time"`    // 发送时间
	SID     int32  `bson:"sid"`
	Emoji   int32  `bson:"emoji"` // 表情
}

// 好友礼物
type FriendGift struct {
	UID      string `bson:"uid"`       // 物品uid
	Time     int64  `bson:"time"`      // 赠送时间
	Id       int32  `bson:"id"`        // 礼物id
	GiftType int32  `bson:"gift_type"` // 礼物类型
	BoxId    int32  `bson:"box_id"`    // 礼盒id
}

// 好友礼物列表
type FriendGiftList struct {
	deadlock.RWMutex
	List []*FriendGift `json:"list" bson:"list"` // 礼物列表
}

type FriendChatDb struct {
	Channel string            `json:"channel" bson:"channel"` // 频道
	List    []*FriendChatInfo `json:"list" bson:"list"`       // 聊天
}

// 添加好友
func (this *User) AddFriend(uid, nickName, headIcon string, playSid int32, lastOfflineTime int64) *Friend {
	now := time.Now().UnixMilli()
	// 好友列表添加数据
	friendInfo := &Friend{
		Uid:             uid,
		Nickname:        nickName,
		HeadIcon:        headIcon,
		PlaySid:         playSid,
		Time:            now,
		LastOfflineTime: lastOfflineTime,
		Active:          true,
		chats:           &FriendChatList{List: []*FriendChatInfo{}},
	}
	this.FriendsLock.Lock()
	this.FriendsList = append(this.FriendsList, friendInfo)
	this.FriendsLock.Unlock()
	if this.FlagUpdateDB() {
		this.NotifyUser(slg.LOBBY_FRIEND_ADD, friendInfo.ToAddFriendNotifyPb(now))
	}
	return friendInfo
}

// 删除好友
func (this *User) DelFriend(uid string) {
	// 检测是否是好友
	this.FriendsLock.RLock()
	friendInfo := array.Find(this.FriendsList, func(v *Friend) bool { return v.Uid == uid })
	this.FriendsLock.RUnlock()
	if friendInfo == nil {
		return
	}
	this.ReceiveFriendAllGift(friendInfo)
	this.FriendsLock.Lock()
	this.FriendsList = array.Delete(this.FriendsList, func(v *Friend) bool { return v.Uid == uid })
	this.FriendsLock.Unlock()
	// 在线则通知
	if this.FlagUpdateDB() {
		this.NotifyUser(slg.LOBBY_FRIEND_DEL, &pb.LOBBY_ONFRIENDDEL_NOTIFY{Uid: uid})
	}
}

// 获取好友信息
func (this *User) GetFriend(uid string) *Friend {
	this.FriendsLock.RLock()
	defer this.FriendsLock.RUnlock()
	if frined := array.Find(this.FriendsList, func(v *Friend) bool { return v.Uid == uid }); frined != nil {
		return frined
	}
	return nil
}

func (this *User) GetFriendCount() int {
	this.FriendsLock.RLock()
	defer this.FriendsLock.RUnlock()
	return len(this.FriendsList)
}

// 添加到申请列表
func (this *User) AddApplyFriend(uid, nickName, headIcon string, playSid int32) {
	applyFriend := &ApplyFriend{
		Uid:      uid,
		Nickname: nickName,
		HeadIcon: headIcon,
		PlaySid:  playSid,
		Time:     time.Now().UnixMilli(),
	}
	this.FriendsLock.Lock()
	this.FriendsApplyList = append(this.FriendsApplyList, applyFriend)
	this.FriendsLock.Unlock()
	// 在线则通知对方
	if this.FlagUpdateDB() {
		this.NotifyUser(slg.LOBBY_FRIEND_APPLY, applyFriend.ToNotifyPb())
	}
}

// 删除申请列表
func (this *User) DelApplyFriend(uid string) {
	this.FriendsLock.Lock()
	this.FriendsApplyList = array.Delete(this.FriendsApplyList, func(friend *ApplyFriend) bool { return friend.Uid == uid })
	this.FriendsLock.Unlock()
}

func (this *User) GetApplyFriendCount() int {
	this.FriendsLock.RLock()
	defer this.FriendsLock.RUnlock()
	return len(this.FriendsApplyList)
}

type TempFrinedInfo struct {
	user   *User
	friend *Friend
}

// 好友信息变动通知
func (this *User) FriendInfoNotify() {
	notifyFriendUpdateChan <- this
}

// 是否存在该好友
func (this *User) HasFriend(uid string) bool {
	this.FriendsLock.RLock()
	defer this.FriendsLock.RUnlock()
	return array.Some(this.FriendsList, func(v *Friend) bool { return v.Uid == uid })
}

// 是否申请过好友
func (this *User) HasApplyFriend(uid string) bool {
	this.FriendsLock.RLock()
	defer this.FriendsLock.RUnlock()
	return array.Some(this.FriendsApplyList, func(v *ApplyFriend) bool { return v.Uid == uid })
}

// 是否被添加黑名单
func (this *User) HasBlackList(uid string) bool {
	this.BlackListLock.RLock()
	defer this.BlackListLock.RUnlock()
	return array.Some(this.Blacklists, func(v *BlacklistInfo) bool { return v.UID == uid })
}

// 添加黑名单
func (this *User) AddBlackList(uid, nickName, headIcon string) {
	if uid == this.UID {
		return
	}
	this.BlackListLock.Lock()
	this.Blacklists = append(this.Blacklists, &BlacklistInfo{
		UID:      uid,
		Nickname: nickName,
		HeadIcon: headIcon,
		Time:     time.Now().UnixMilli(),
	})
	this.BlackListLock.Unlock()
}

// 移除黑名单
func (this *User) DelBlackList(index int) {
	this.BlackListLock.Lock()
	this.Blacklists = array.RemoveByIndex(this.Blacklists, index)
	this.BlackListLock.Unlock()
}

// 获取聊天数据
func (this *User) GetFriendChatInfo(friendInfo *Friend, start, count int) *Friend {
	if friendInfo.chats != nil && friendInfo.chats.Loaded {
		// 聊天数据在当前玩家的内存中 直接返回
		return friendInfo
	}
	friendUser := GetUserByRpc(friendInfo.Uid)
	if friendUser != nil {
		otherFriendInfo := friendUser.GetFriend(this.UID)
		if otherFriendInfo != nil && otherFriendInfo.chats != nil && otherFriendInfo.chats.Loaded {
			// 聊天数据在好友的内存中
			return otherFriendInfo
		}
	}
	// 聊天数据不在内存中 从数据库获取并放到玩家内存
	if friendInfo.chats == nil {
		friendInfo.chats = &FriendChatList{List: []*FriendChatInfo{}}
	}
	friendInfo.chats.Lock()
	channel := GetFriendChannel(this.UID, friendInfo.Uid)
	if chatData, e := friendChatDb.FindChat(channel); e == nil && chatData.List != nil {
		tail := start + count
		tail = ut.Min(tail, len(chatData.List))
		friendInfo.chats.List = chatData.List[start:tail]
	}
	friendInfo.chats.Unlock()
	friendInfo.chats.Loaded = true
	return friendInfo
}

// 被申请
func (this *User) ApplyFriendByOhter(uid, nickName, headIcon string, playSid int32, applied bool) (err string) {
	// 判断好友数量上限
	if this.GetFriendCount() >= slg.FRIENDS_MAX_NUM {
		return ecode.OTHER_FRIENDS_MAX_NUM.String()
	}
	// 判断是否添加黑名单
	if this.HasBlackList(uid) {
		return ecode.BLACKLIST_BLOCK_FRIEND.String()
	}
	// 判断是否已是对方好友
	if this.HasFriend(uid) {
		return ecode.ALREADY_FRIENDS.String()
	}
	// 已申请过
	if this.HasApplyFriend(uid) {
		return ecode.FRIEND_APPLY_REPEAT.String()
	}
	if applied {
		// 已申请过添加对方为好友 则直接添加好友
		this.AddFriend(uid, nickName, headIcon, playSid, 0)
		return ""
	}
	// 判断是否申请数量上限
	if this.GetApplyFriendCount() >= slg.FRIENDS_APPLY_MAX_NUM {
		return ecode.OTHER_APPLYS_MAX_NUM.String()
	}
	// 添加到申请列表
	this.AddApplyFriend(uid, nickName, headIcon, playSid)
	return ""
}

// 好友申请被回复
func (this *User) ApplyResponseByOhter(uid, nickName, headIcon string, playSid int32, agree bool) (err string) {
	// 判断好友数量上限
	if this.GetFriendCount() >= slg.FRIENDS_MAX_NUM {
		return ecode.OTHER_FRIENDS_MAX_NUM.String()
	}
	// 判断是否添加黑名单
	if this.HasBlackList(uid) {
		return ecode.BLACKLIST_BLOCK_FRIEND.String()
	}
	if agree {
		// 同意添加
		this.AddFriend(uid, nickName, headIcon, playSid, 0)
		return ""
	}
	return ""
}

// 领取好友礼物
func (this *User) ReceiveFriendGift(friendInfo *Friend, giftUid string, isBan bool) (friendGift *FriendGift, err string) {
	if friendInfo == nil {
		// 好友不存在
		return nil, ecode.NOT_FRIENDS.String()
	}
	if friendInfo.FriendGifts == nil || friendInfo.FriendGifts.List == nil {
		return nil, ecode.GIFT_NOT_EXIST.String()
	}
	// 领取礼物
	friendInfo.FriendGifts.Lock()
	for i := len(friendInfo.FriendGifts.List) - 1; i >= 0; i-- {
		v := friendInfo.FriendGifts.List[i]
		if v.UID == giftUid {
			friendGift = v
			friendInfo.FriendGifts.List = append(friendInfo.FriendGifts.List[:i], friendInfo.FriendGifts.List[i+1:]...)
			break
		}
	}
	friendInfo.FriendGifts.Unlock()
	if friendGift == nil {
		return nil, ecode.GIFT_NOT_EXIST.String()
	} else {
		// 添加皮肤物品
		this.AddSkinItem(friendGift.UID, friendGift.Id, true, isBan)
		// 更新溯源记录
		go skinItemTrackDb.UpdateSkinItemTrack(friendGift.UID, this.UID)
	}
	return
}

// 领取好友赠送的所有礼物
func (this *User) ReceiveFriendAllGift(friendInfo *Friend) {
	if friendInfo.FriendGifts != nil && len(friendInfo.FriendGifts.List) > 0 {
		// 有该好友赠送的礼物未领取 则直接领取
		giftList := []string{}
		friendInfo.FriendGifts.RLock()
		for _, v := range friendInfo.FriendGifts.List {
			giftList = append(giftList, v.UID)
		}
		friendInfo.FriendGifts.RUnlock()
		for _, giftUid := range giftList {
			isBan := false
			itemTrack, e1 := skinItemTrackDb.FindSkinItemTrack(giftUid)
			// 领取礼物前先获取初始拥有者是否被封号
			if e1 == nil && itemTrack.Owner != "" {
				rpcRst, e := ut.RpcInterfaceMap(lobbyModule.InvokeUserFunc(itemTrack.Owner, slg.RPC_USER_BAN_ACCCOUNT_CHECK))
				if e == "" && rpcRst != nil {
					isBan = ut.Bool(rpcRst["rst"])
				}
			}
			this.ReceiveFriendGift(friendInfo, giftUid, isBan)
		}
	}
}

func (this *User) FriendsListToPb() []*pb.FriendInfo {
	now := time.Now().UnixMilli()
	return array.Map(this.FriendsList, func(friend *Friend, i int) *pb.FriendInfo {
		return friend.ToPb(now)
	})
}

func (this *User) FriendsApplyListToPb() []*pb.FriendApplyInfo {
	return array.Map(this.FriendsApplyList, func(friend *ApplyFriend, i int) *pb.FriendApplyInfo {
		return &pb.FriendApplyInfo{
			Uid:      friend.Uid,
			Nickname: friend.Nickname,
			HeadIcon: friend.HeadIcon,
			Time:     friend.Time,
			PlaySid:  friend.PlaySid,
		}
	})
}

func (this *Friend) ToPb(now int64) *pb.FriendInfo {
	friendInfo := &pb.FriendInfo{
		Uid:          this.Uid,
		Nickname:     this.Nickname,
		HeadIcon:     this.HeadIcon,
		Time:         this.Time,
		PlaySid:      this.PlaySid,
		OfflineTime:  0,
		NoteName:     this.NoteName,
		NotReadCount: this.NotReadCount,
	}
	if this.LastChatInfo != nil {
		friendInfo.LastChatInfo = this.LastChatInfo.ToPb()
	}
	if this.LastOfflineTime > 0 {
		friendInfo.OfflineTime = ut.MaxInt64(1, now-this.LastOfflineTime)
	}
	if this.FriendGifts != nil {
		friendInfo.GiftList = []*pb.FriendGift{}
		this.FriendGifts.RLock()
		for _, v := range this.FriendGifts.List {
			friendInfo.GiftList = append(friendInfo.GiftList, v.ToPb())
		}
		this.FriendGifts.RUnlock()
	}
	return friendInfo
}

func (this *Friend) ToAddFriendNotifyPb(now int64) *pb.LOBBY_ONFRIENDADD_NOTIFY {
	notify := &pb.LOBBY_ONFRIENDADD_NOTIFY{
		Uid:         this.Uid,
		Nickname:    this.Nickname,
		HeadIcon:    this.HeadIcon,
		Time:        this.Time,
		PlaySid:     this.PlaySid,
		OfflineTime: 0,
	}
	if this.LastOfflineTime > 0 {
		notify.OfflineTime = ut.MaxInt64(1, now-this.LastOfflineTime)
	}
	return notify
}

func (this *Friend) ToUpdateNotifyPb(now int64) *pb.LOBBY_ONFRIENDUPDATE_NOTIFY {
	notify := &pb.LOBBY_ONFRIENDUPDATE_NOTIFY{
		Uid:         this.Uid,
		Nickname:    this.Nickname,
		HeadIcon:    this.HeadIcon,
		PlaySid:     this.PlaySid,
		OfflineTime: 0,
	}
	if this.LastOfflineTime > 0 {
		notify.OfflineTime = ut.MaxInt64(1, now-this.LastOfflineTime)
	}
	return notify
}

func (this *ApplyFriend) ToNotifyPb() *pb.LOBBY_ONFRIENDAPPLY_NOTIFY {
	return &pb.LOBBY_ONFRIENDAPPLY_NOTIFY{
		Uid:      this.Uid,
		Nickname: this.Nickname,
		HeadIcon: this.HeadIcon,
		PlaySid:  this.PlaySid,
		Time:     this.Time,
	}
}

func (this *FriendChatInfo) ToPb() *pb.ChatInfo {
	return &pb.ChatInfo{
		Uid:     this.UID,
		Sender:  this.Sender,
		Content: this.Content,
		Emoji:   this.Emoji,
		Time:    this.Time,
	}
}

func (this *FriendGift) ToPb() *pb.FriendGift {
	return &pb.FriendGift{
		Uid:      this.UID,
		Id:       this.Id,
		GiftType: this.GiftType,
		BoxId:    this.BoxId,
		Time:     this.Time,
	}
}

func FriendChatToNotifyPb(chatInfo *pb.ChatInfo, friendUid string) *pb.LOBBY_ONFRIENDCHAT_NOTIFY {
	return &pb.LOBBY_ONFRIENDCHAT_NOTIFY{
		Uid: friendUid,
		ChatInfo: &pb.ChatInfo{
			Uid:     chatInfo.Uid,
			Sender:  chatInfo.Sender,
			Content: chatInfo.Content,
			Emoji:   chatInfo.Emoji,
			Time:    chatInfo.Time,
		},
	}
}

func GetFriendChannel(uid, friendUid string) string {
	return ut.If(uid < friendUid, uid+"_"+friendUid, friendUid+"_"+uid)
}

func (this *Friend) FriendChatListToPb() []*pb.ChatInfo {
	arr := []*pb.ChatInfo{}
	if this.chats == nil || this.chats.List == nil {
		return arr
	}
	this.chats.RLock()
	defer this.chats.RUnlock()
	for _, v := range this.chats.List {
		arr = append(arr, v.ToPb())
	}
	return arr
}
