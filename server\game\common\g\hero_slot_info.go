package g

import (
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
)

var HERO_REVIVES_TIME = ut.TIME_HOUR * 5 // 英雄复活剩余时间

// 英雄一个槽位信息
type HeroSlotInfo struct {
	Hero          *PortrayalInfo // 英雄
	DieTime       int64          // 阵亡时间
	AvatarArmyUID string         // 化身的军队uid
	Lv            int32          // 需要等级
	DieCount      int32          // 阵亡次数
}

func (this *HeroSlotInfo) FromHeroJson(data interface{}) {
	if data == nil {
		return
	} else if json, ok := data.(map[string]interface{}); ok {
		this.Hero = NewPortrayalByJson(json)
	}
}

func (this *HeroSlotInfo) GetHeroJson() map[string]interface{} {
	if this.Hero == nil {
		return nil
	}
	return this.Hero.ToJson()
}

func (this *HeroSlotInfo) GetHeroPb() *pb.PortrayalInfo {
	if this.Hero == nil {
		return nil
	}
	return this.Hero.ToPbForPawn()
}

func (this *HeroSlotInfo) ToPb(runDay int32) *pb.HeroSlotInfo {
	return &pb.HeroSlotInfo{
		Lv:                this.Lv,
		Hero:              this.GetHeroPb(),
		ReviveSurplusTime: int64(this.GetReviveSurplusTime(runDay)),
		AvatarArmyUID:     this.AvatarArmyUID,
	}
}

func (this *HeroSlotInfo) ToJson() map[string]interface{} {
	return map[string]interface{}{
		"hero":          this.GetHeroJson(),
		"dieTime":       this.DieTime,
		"avatarArmyUID": this.AvatarArmyUID,
		"dieCount":      this.DieCount,
	}
}

// 重置
func (this *HeroSlotInfo) Reset() {
	this.Hero = nil
	this.AvatarArmyUID = ""
	this.DieTime = 0
}

// 获取英雄id
func (this *HeroSlotInfo) GetHeroID() int32 {
	if this.Hero != nil {
		return this.Hero.ID
	}
	return 0
}

// 获取复活剩余时间 计算公式：复活时间 = 5 + ((天数-1)*25) + ((复活次数-1)*15)
func (this *HeroSlotInfo) GetReviveSurplusTime(runDay int32) int64 {
	if this.DieTime > 0 {
		reviveTime := ut.TIME_MINUTE*5 + (ut.MaxInt32(runDay, 1)-1)*25*ut.TIME_MINUTE + (this.DieCount-1)*15*ut.TIME_MINUTE
		reviveTime = ut.MinInt32(reviveTime, int32(HERO_REVIVES_TIME))
		return ut.MaxInt64(0, int64(reviveTime)-(ut.Now()-this.DieTime))
	}
	return 0
}

// 是否阵亡
func (this *HeroSlotInfo) IsDie(runDay int32) bool {
	return this.GetReviveSurplusTime(runDay) > 0
}
