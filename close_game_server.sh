#!/bin/bash

# 配置文件路径
config_file="./bin/conf/server.json"

# 要关闭的sid
close_sid=$1

# 通过pm2关闭游戏服进程
./pm2_stop.sh game$close_sid

# pm2管理的进程中删除指定游戏服
pm2 delete game$close_sid

# 删除配置文件的该游戏服配置
jq --arg close_sid "$close_sid" 'del(.Module.game[] | select(.Id == "game" + $close_sid))' "$config_file" > temp_config.json
mv temp_config.json "$config_file"

# 在pm配置文件中删除指定sid的元素
cp ecosystem.config.js ecosystem.config.js.bak
name_to_delete="name: 'game$close_sid',"
awk -v sid_to_delete="$name_to_delete" '
  # 设置记录分隔符为 "},\n"
  BEGIN { 
    RS="},\n"
  }

  # 查找是否包含指定的sid
  # 如果不包含，则输出该元素
  $0 !~ sid_to_delete {
    if (inside_element) {
      print "    },"
      print $0
      inside_element=0
    } else {
      if (NR > 1) {
        print "    },"
      }
      print $0
    }
  }

  # 设置标志变量 inside_element
  index($0, sid_to_delete) {
    inside_element = 1
  }

  # 在整个文件的最后输出结束标记 "}"
  END {
    if (inside_element) {
      print "  }"
    }
  }
' ecosystem.config.js > temp.js && mv temp.js ecosystem.config.js
# 删除空行
awk 'NF' ecosystem.config.js > temp.js && mv temp.js ecosystem.config.js

# 避免分隔符的原因导致第一个元素删除错误
# 检查第一行是否包含 "},"
if [ "$(head -n 1 ecosystem.config.js)" == "    }," ]; then
  # 如果包含，删除并在开头添加 module.exports = { apps : [ 行
  sed -i '1s/    },/module.exports = { \n  apps : [/g' ecosystem.config.js
fi