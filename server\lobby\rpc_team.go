package lobby

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/log"
	"google.golang.org/protobuf/reflect/protoreflect"
)

func (this *Lobby) RpcTeamRegister(id string, f any) {
	this.GetServer().RegisterGO(id, f)
	this.RpcTeamFuncMap[id] = f
}

// 初始化组队相关rpc
func (this *Lobby) InitTeamRpc() {
	this.RpcTeamFuncMap = map[string]any{}
	this.RpcTeamRegister(slg.RPC_GET_TEAM_INFO, this.rpcGetTeamInfo)
	this.RpcTeamRegister(slg.RPC_TEAM_INVITE, this.rpcTeamInvite)
	this.RpcTeamRegister(slg.RPC_TEAM_INVITE_RESPONSE, this.rpcTeamInviteResponse)
	this.RpcTeamRegister(slg.RPC_TEAM_DEL_TEAMMATE, this.rpcTeamDelTeammate)
	this.RpcTeamRegister(slg.RPC_LEAVE_TEAM, this.rpcLeaveTeam)
	this.RpcTeamRegister(slg.RPC_USER_LEAVE_TEAM_FORCE, this.rpcLeaveTeamForce)
	this.RpcTeamRegister(slg.RPC_GAMEOVER_LEAVE_TEAM, this.rpcGameOverExistTeam)
	this.RpcTeamRegister(slg.RPC_DEL_TEAM, this.rpcDelTeam)
	this.RpcTeamRegister(slg.RPC_CHANGE_TEAM_MODE, this.rpcChangeTeamMode)
	this.RpcTeamRegister(slg.RPC_UPDATE_TEAM_USER_INFO, this.rpcUpdateTeamUserInfo)
	this.RpcTeamRegister(slg.RPC_TEAM_APPLY_SERVER, this.rpcTeamApplyServer)
	this.RpcTeamRegister(slg.RPC_CANCEL_APPLY_SERVER, this.rpcCancelApplyServer)
	this.RpcTeamRegister(slg.RPC_TEAM_START_MATCH, this.rpcTeamStartMatch)
	this.RpcTeamRegister(slg.RPC_TEAM_ENTER_GAME, this.rpcTeamEnterGame)
	this.RpcTeamRegister(slg.RPC_TEAM_DELAY_MATCH, this.rpcTeamDelayMatch)
	this.RpcTeamRegister(slg.RPC_TEAM_RESET_STATE, this.rpcResetTeamState)
	this.RpcTeamRegister(slg.RPC_TEAM_GM_DEL_TEAM, this.delTeam)
	this.RpcTeamRegister(slg.RPC_TEAM_GM_DEL_TEAM_USER, this.delTeamUser)
	this.RpcTeamRegister(slg.RPC_MATCH_CHECK_APPLY_TEAM, this.checkApplyTeam)
	this.RpcTeamRegister(slg.RPC_DISBAND_TEAM, this.rpcDisbandTeam)
	this.RpcTeamRegister(slg.RPC_TEAM_CHANGE_JOB, this.rpcTeamChangeJob)

	this.RpcTeamRegister(slg.RPC_CHECK_ENTER_CUSTOM_TEAM, this.rpcCheckEnterCustomTeam)
	this.RpcTeamRegister(slg.RPC_ENTER_CUSTOM_ROOM, this.rpcEnterCustomRoom)
	this.RpcTeamRegister(slg.RPC_EXIT_CUSTOM_ROOM, this.rpcExitCustomRoom)
}

// 处理team功能
func (this *Lobby) InvokeTeamFunc(uid string, _func string, params ...any) (result any, err string) {
	if uid == "" {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	lid := rds.MallocTeamLid(uid)
	if lid == "" {
		log.Error("InvokeTeamFuncNR rds get lid uid:%v, _func: %v, params: %v", uid)
		err = ecode.LOBBY_QUEUE_UP.String()
		return
	}
	params = append([]any{uid}, params...)
	if lid == this.GetLid() {
		function := this.RpcTeamFuncMap[_func]
		if function == nil {
			log.Error("InvokeTeamFunc not found %v", _func)
			return nil, "not found " + _func
		}
		result, err = RunLocalRpcFunc(function, params...)
	} else {
		result, err = this.InvokeLobbyRpc(lid, _func, params...)
	}
	return
}

// 处理team功能 不需要回复
func (this *Lobby) InvokeTeamFuncNR(uid string, _func string, params ...any) {
	if uid == "" {
		return
	}
	lid := rds.MallocTeamLid(uid)
	if lid == "" {
		log.Error("InvokeTeamFuncNR rds get lid uid:%v, _func: %v, params: %v", uid)
		return
	}
	params = append([]any{uid}, params...)
	if lid == this.GetLid() {
		function := this.RpcTeamFuncMap[_func]
		if function == nil {
			log.Error("InvokeTeamFuncNR not found %v", _func)
			return
		}
		RunLocalRpcFunc(function, params...)
	} else {
		this.InvokeLobbyRpcNR(lid, _func, params...)
	}
}

// 获取队伍信息
func (this *Lobby) rpcGetTeamInfo(teamUid string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	} else if team.IsEqmpty() { // 如果是空队伍 就清理
		return nil, ecode.TEAM_NOT_EXIST.String()
	} else if team.PlaySid > 0 {
		team.RoomType = team.PlaySid / slg.ROOM_TYPE_FLAG // 这里兼容一下
	}
	return pb.ProtoMarshal(team.ToPb())
}

// 组队邀请
func (this *Lobby) rpcTeamInvite(uid, userId, inviteUid, inviteNickname, inviteHeadicon, userTeamUid, leaderNickname, leaderHeadicon string, roomType, pos, farmType int32) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(uid)
	if team == nil {
		// 队伍不存在时创建队伍 队伍uid为队长uid
		team = CreateTeam(userId, leaderNickname, leaderHeadicon, this.GetLid(), roomType, pos, farmType)
	} else {
		team.RoomType = roomType
	}
	defer func() {
		if err != "" {
			// 邀请失败 检测清除队伍
			this.CheckClearTeam(team)
		}
	}()

	if team.PlaySid > 0 {
		return nil, ecode.IN_GAME.String() // 是否已经游戏了
	} else if team.ApplyTime > 0 {
		return nil, ecode.IN_APPLY.String()
	} else if team.GetUserCount() >= slg.TEAM_USER_NUM_MAX {
		return nil, ecode.TEAM_USER_NUM_LIMIT.String()
	}
	if userId != team.Uid {
		// 邀请人不是队长 判断是否有权限
		team.userListLock.RLock()
		hasAuth := array.Some(team.UserList, func(m *TeamUserInfo) bool { return m.Uid == userId && m.Job == 2 })
		team.userListLock.RUnlock()
		if !hasAuth {
			return nil, ecode.NOT_OPERATING_AUTH.String()
		}
	}

	// 判断是否已在邀请中
	team.inviteListLock.RLock()
	if array.Some(team.InviteList, func(m *TeamInviteUserInfo) bool { return m.Uid == inviteUid }) {
		team.inviteListLock.RUnlock()
		return nil, ecode.USER_ALREADY_IN_TEAM.String()
	}
	team.inviteListLock.RUnlock()

	// 判断是否已在队伍中
	team.userListLock.RLock()
	if array.Some(team.UserList, func(m *TeamUserInfo) bool { return m.Uid == inviteUid }) {
		team.userListLock.RUnlock()
		return nil, ecode.USER_ALREADY_IN_TEAM.String()
	}
	team.userListLock.RUnlock()

	// 给被邀请玩家添加邀请信息
	_, err = this.InvokeUserFunc(inviteUid, slg.RPC_ADD_TEAM_INVITE_INFO,
		team.Uid,
		uid,
		leaderNickname,
		leaderHeadicon)
	if err == "" {
		if team.CustomRoomId != 0 {
			// 邀请信息同步给匹配服
			_, err = this.InvokeMatchRpc(slg.RPC_TEAM_INVITE, team.CustomRoomId, uid, inviteUid, inviteNickname, inviteHeadicon)
			if err != "" {
				log.Warning("rpcTeamInvite invokeMatch teamUid: %v, userId: %v, sid: %v, err: %v", uid, inviteUid, team.CustomRoomId, err)
				return
			}
		}
		// 队长没有队伍uid则设置
		if userTeamUid == "" {
			this.InvokeUserFunc(uid, slg.RPC_SET_USER_TEAMID, team.Uid)
		}
		// 队伍中添加邀请信息
		this.addInviteUser(inviteUid, inviteNickname, inviteHeadicon, team)
		// 发送系统消息
		this.SendTeamSysMsg(team.Uid, slg.TEAM_MSG_TYPE_INVITE, leaderNickname, inviteNickname)
	}
	return
}

// 组队邀请回复
func (this *Lobby) rpcTeamInviteResponse(teamUid, uid string, agree bool, pos, farmType int32) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	teamPb, err := team.userResponseInvite(uid, agree, pos, farmType)
	if team.CustomRoomId != 0 {
		// 邀请信息同步给匹配服
		this.InvokeMatchRpcNR(slg.RPC_TEAM_INVITE_RESPONSE, team.CustomRoomId, teamUid, uid, agree && err == "")
	}
	// 检测是否空队伍
	if !this.CheckClearTeam(team) {
		if teamPb == nil {
			teamPb = team.ToPb()
		}
		this.RcpTeamNotify(team, slg.LOBBY_TEAM_UPDATE, &pb.LOBBY_ONTEAMUPDATE_NOTIFY{TeamInfo: teamPb})
	}
	if agree && err == "" && teamPb != nil {
		return pb.ProtoMarshal(teamPb)
	}
	return
}

// 队伍删除队员
func (this *Lobby) rpcTeamDelTeammate(teamUid, uid string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	} else if team.PlaySid > 0 {
		return nil, ecode.IN_GAME.String()
	} else if team.ApplyTime > 0 {
		return nil, ecode.IN_APPLY.String()
	}
	if team.CustomRoomId != 0 {
		// 在自定义房间则通知匹配服 需接收错误码
		if _, err = this.InvokeMatchRpc(slg.RPC_EXIT_CUSTOM_ROOM, teamUid, uid); err != "" {
			return
		}
	}
	var nickname string
	if err, nickname = team.delTeammate(uid); err == "" {
		// 通知该玩家
		this.InvokeUserFunc(uid, slg.RPC_USER_KICK_BY_TEAM, team.Uid, 0)
	} else if err, nickname = team.delInviteTeammate(uid); err == "" {
		// 没有就从 邀请里面删除
	}
	if err == "" && !this.CheckClearTeam(team) {
		// 通知队伍更新信息
		this.RcpTeamNotify(team, slg.LOBBY_TEAM_UPDATE, &pb.LOBBY_ONTEAMUPDATE_NOTIFY{TeamInfo: team.ToPb()})
		// 发送系统消息
		this.SendTeamSysMsg(team.Uid, slg.TEAM_MSG_TYPE_KICK, nickname)
	}
	return
}

// 退出队伍
func (this *Lobby) rpcLeaveTeam(teamUid, uid string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	} else if team.PlaySid > 0 {
		return nil, ecode.IN_GAME.String()
	} else if team.ApplyTime > 0 {
		return nil, ecode.IN_APPLY.String()
	}
	if team.CustomRoomId != 0 {
		// 在自定义房间则通知匹配服 需接收错误码
		if _, err = this.InvokeMatchRpc(slg.RPC_EXIT_CUSTOM_ROOM, teamUid, uid); err != "" {
			return
		}
	}
	_, nickname := team.delTeammate(uid)
	// 通知玩家
	this.InvokeUserFuncNR(uid, slg.RPC_SET_USER_TEAMID, "")
	// 通知队伍更新信息
	if !this.CheckClearTeam(team) {
		// 通知队伍更新信息
		this.RcpTeamNotify(team, slg.LOBBY_TEAM_UPDATE, &pb.LOBBY_ONTEAMUPDATE_NOTIFY{TeamInfo: team.ToPb()})
		// 发送系统消息
		this.SendTeamSysMsg(team.Uid, slg.TEAM_MSG_TYPE_EXIT, nickname)
	}
	return
}

// 强制退出队伍
func (this *Lobby) rpcLeaveTeamForce(teamUid, uid string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	if team.ApplyTime > 0 { // 报名中则取消报名
		this.InvokeMatchRpc(slg.RPC_MATCH_USER_CANCEL_APPLY_SERVER, team.RoomType, teamUid, uid)
	}
	if team.CustomRoomId != 0 {
		// 在自定义房间则通知匹配服
		this.InvokeMatchRpcNR(slg.RPC_EXIT_CUSTOM_ROOM, teamUid, uid)
	}
	_, nickname := team.delTeammate(uid)
	// 通知队伍更新信息
	if !this.CheckClearTeam(team) {
		// 通知队伍更新信息
		this.RcpTeamNotify(team, slg.LOBBY_TEAM_UPDATE, &pb.LOBBY_ONTEAMUPDATE_NOTIFY{TeamInfo: team.ToPb()})
		// 发送系统消息
		this.SendTeamSysMsg(team.Uid, slg.TEAM_MSG_TYPE_EXIT, nickname)
	}
	return
}

// 结算游戏退出队伍
func (this *Lobby) rpcGameOverExistTeam(teamUid, uid string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	_, nickname := team.delTeammate(uid)
	// 通知玩家
	this.InvokeUserFuncNR(uid, slg.RPC_SET_USER_TEAMID, "")
	if teamUid == uid {
		// 放弃游戏的是队长 则解散队伍
		this.HandleDisbandTeam(team)
	} else {
		// 通知队伍更新信息
		this.RcpTeamNotify(team, slg.LOBBY_TEAM_UPDATE, &pb.LOBBY_ONTEAMUPDATE_NOTIFY{TeamInfo: team.ToPb()})
		// 发送系统消息
		this.SendTeamSysMsg(team.Uid, slg.TEAM_MSG_TYPE_EXIT, nickname)
		if team.CustomRoomId != 0 { // 在自定义房间则通知匹配服
			this.InvokeMatchRpcNR(slg.RPC_EXIT_CUSTOM_ROOM, teamUid, uid)
		}
		// 检测删除队伍
		this.CheckClearTeam(team)
	}
	return
}

// 删除队伍
func (this *Lobby) rpcDelTeam(teamUid string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		err = ecode.TEAM_NOT_EXIST.String()
		return
	}
	DelTeam(team)
	return
}

// 切换队伍模式
func (this *Lobby) rpcChangeTeamMode(teamUid string, roomType int32) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	err = this.changeRoomType(roomType, team)
	if err == "" {
		param := slg.TEAM_MSG_CHANGE_MODE_PARAM + ut.String(roomType)
		this.SendTeamSysMsg(team.Uid, slg.TEAM_MSG_TYPE_CHANGE_MODE, param)
	}
	return
}

// 刷新队员信息
func (this *Lobby) rpcUpdateTeamUserInfo(teamUid string, uid, nickname, headicon string, pos, farmType int32) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	this.UpdateUserInfo(uid, nickname, headicon, team, pos, farmType)
	return
}

// 队伍报名对局
func (this *Lobby) rpcTeamApplyServer(uid, userTeamUid, nickname, headIcon string, roomType, pos, farmType int32) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(uid)
	if team == nil {
		// 不存在则创建队伍
		team = CreateTeam(uid, nickname, headIcon, this.GetLid(), roomType, pos, farmType)
	} else {
		team.RoomType = roomType
	}
	if team.RoomType == slg.ROOKIE_SERVER_TYPE {
		return nil, ecode.UNKNOWN.String() // 新手区 不可报名
	} else if team.PlaySid != 0 {
		return nil, ecode.IN_GAME.String() // 已经再游戏中
	} else if team.ApplyTime > 0 {
		return nil, ecode.SERVER_APPLY_REPEAT.String() // 重复报名
	} else if team.IsHasInvite() {
		return nil, ecode.HAS_NOT_READY_PLAYER.String() // 还有玩家没进入队伍
	} else if team.CustomRoomId != 0 {
		return nil, ecode.ALREADY_IN_CUSTOM_ROOM.String() // 已经在自定义房间中
	}

	// 提前设置报名时间 避免成员中途退出或重复报名
	team.ApplyTime = int64(ut.Now())
	defer func() {
		if err != "" {
			// 报名失败 重置报名时间
			team.ApplyTime = 0
		}
	}()

	uidList, delList, hasCaptain := []string{}, []string{}, false
	var rankLvMax, rankLvMin, rankScoreSum, gameTotalSum int32 = 0, 1000, 0, 0
	var createTimeSum int64
	// 检测玩家是否在队伍中
	team.userListLock.RLock()
	for i := len(team.UserList) - 1; i >= 0; i-- {
		v := team.UserList[i]
		if v.Uid == team.Uid {
			hasCaptain = true
		}
		if info, e := this.InvokeUserFunc(v.Uid, slg.RPC_CHECK_USER_TEAM, team.Uid); e != "" {
			delList = append(delList, v.Uid)
		} else {
			userInfo := ut.MapInterface(info)
			rankScore := ut.Int32(userInfo["rankScore"])
			gameTotal := ut.Int32(userInfo["gameTotal"])
			createTime := ut.Int64(userInfo["createTime"])
			rankLv, _ := resolutionRankScore(rankScore)
			uidList = append(uidList, v.Uid)
			if rankLv > rankLvMax {
				rankLvMax = rankLv
			}
			if rankLv < rankLvMin {
				rankLvMin = rankLv
			}
			rankScoreSum += rankScore
			gameTotalSum += gameTotal
			createTimeSum += createTime
		}
	}
	team.userListLock.RUnlock()
	// 兼容 如果没有队长 这里加进去
	if !hasCaptain {
		team.addUser(NewTeamUser(uid, nickname, headIcon, 1, pos, farmType))
		uidList = append(uidList, uid)
	}
	// TODO 下个版本再加判断
	// if rankLvMax-rankLvMin > slg.TEAM_RANK_LEVEL_MAX {
	// 	// 队伍中段位差超过上限
	// 	return nil, ecode.TEAM_RANK_LV_LIMIT.String()
	// }

	// 是否有删除的
	if len(delList) > 0 {
		team.userListLock.Lock()
		for _, delUid := range delList {
			team.UserList = array.Delete(team.UserList, func(m *TeamUserInfo) bool { return m.Uid == delUid })
		}
		team.userListLock.Unlock()
		if !this.CheckClearTeam(team) {
			this.RcpTeamNotify(team, slg.LOBBY_TEAM_UPDATE, &pb.LOBBY_ONTEAMUPDATE_NOTIFY{TeamInfo: team.ToPb()})
		}
		log.Error("rpcTeamApplyServer delList > 0, teamUid: %v, uids: %v", team.Uid, delList)
		return nil, ecode.UNKNOWN.String()
	}
	// 已在匹配中无法报名
	if team.GetMatchOpenTime() > 0 {
		return nil, ecode.SERVER_APPLY_REPEAT.String()
	}
	// 计算队伍平均数据
	userNum := float64(len(team.UserList))
	rankScoreAvg := float64(rankScoreSum) / userNum
	gameTotalAvg := float64(gameTotalSum) / userNum
	creatTimeAvg := float64(createTimeSum) / userNum
	// 发送到匹配服报名
	_, err = this.InvokeMatchRpc(slg.RPC_MATCH_APPLY_SERVER, team.RoomType, team.Uid, uidList, rankScoreAvg, gameTotalAvg, creatTimeAvg)
	if err == "" {
		team.ApplyTime = time.Now().UnixMilli()
		teamDbChan <- team.Uid
		// 队长没有队伍uid则设置
		if userTeamUid == "" {
			this.InvokeUserFunc(uid, slg.RPC_SET_USER_TEAMID, team.Uid)
		}
		// 通知
		this.RcpTeamNotify(team, slg.LOBBY_TEAM_UPDATE, &pb.LOBBY_ONTEAMUPDATE_NOTIFY{TeamInfo: team.ToPb()})
		// 发送消息
		this.SendTeamSysMsg(team.Uid, slg.TEAM_MSG_TYPE_APPLY, slg.TEAM_MSG_CHANGE_MODE_PARAM+ut.String(team.RoomType))
	} else {
		this.CheckClearTeam(team)
	}
	return
}

// 取消报名
func (this *Lobby) rpcCancelApplyServer(teamUid string, uid string, nickname string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	} else if team.PlaySid != 0 {
		return nil, ecode.IN_GAME.String() // 已经在游戏中
	} else if team.ApplyTime == 0 {
		return nil, ecode.SERVER_NOT_APPLY.String() // 未报名
	} else if time.Now().UnixMilli()-team.ApplyTime < slg.SERVER_APPLY_CANCEL_CD {
		return nil, ecode.NEED_CANCEL_APPLY_TIME.String() // 未报名
	}
	if uid == teamUid {
		// 是队长则取消整个队伍报名
		_, err = this.InvokeMatchRpc(slg.RPC_MATCH_CANCEL_APPLY_SERVER, team.RoomType, teamUid)
		if err == "" {
			team.ApplyTime = 0
			teamDbChan <- team.Uid
			// 通知
			this.RcpTeamNotify(team, slg.LOBBY_TEAM_CANCEL_APPLY, &pb.LOBBY_ONTEAMCANCELAPPLY_NOTIFY{Uid: uid, Nickname: nickname})
			// 取消成功 检测并删除空队伍
			if !this.CheckClearTeam(team) {
				// 发送消息
				this.SendTeamSysMsg(team.Uid, slg.TEAM_MSG_TYPE_CANCEL_APPLY, nickname)
			}
		}
	} else {
		// 是队员则取消自己报名再退出队伍
		_, err = this.InvokeMatchRpc(slg.RPC_MATCH_USER_CANCEL_APPLY_SERVER, team.RoomType, teamUid, uid)
		if err == "" {
			_, nickname := team.delTeammate(uid)
			// 通知玩家
			this.InvokeUserFuncNR(uid, slg.RPC_SET_USER_TEAMID, "")
			// 通知队伍更新信息
			this.RcpTeamNotify(team, slg.LOBBY_TEAM_UPDATE, &pb.LOBBY_ONTEAMUPDATE_NOTIFY{TeamInfo: team.ToPb()})
			// 发送系统消息
			this.SendTeamSysMsg(team.Uid, slg.TEAM_MSG_TYPE_EXIT, nickname)
		}
	}
	return
}

// 队伍开始匹配
func (this *Lobby) rpcTeamStartMatch(teamUid string, openTime int64) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	if team.ApplyTime == 0 {
		// 未报名
		return nil, ecode.SERVER_NOT_APPLY.String()
	}
	team.MatchOpenTime = openTime
	teamDbChan <- team.Uid
	return
}

// 队伍进入游戏
func (this *Lobby) rpcTeamEnterGame(teamUid string, sid int32) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	team.PlaySid = sid
	team.ApplyTime = 0
	team.CustomRoomId = 0
	teamDbChan <- team.Uid
	return
}

// 延迟匹配
func (this *Lobby) rpcTeamDelayMatch(teamUid string, nextMatchTime int64) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	uidList := []string{}
	team.userListLock.RLock()
	for _, v := range team.UserList {
		uidList = append(uidList, v.Uid)
	}
	team.userListLock.RUnlock()
	// 发送邮件
	this.InvokeMailRpcNR(slg.RPC_SEND_MAIL_MANY, 0, slg.MAIL_DELAY_MATCH_ID, "", "ui.title_server_name_"+ut.String(team.RoomType), "-1", uidList, []*g.TypeObj{g.NewTypeObj(ctype.WAR_TOKEN, 0, slg.MATCH_DELAY_WAR_TOKEN_NUM)})
	return
}

// 重置队伍状态
func (this *Lobby) rpcResetTeamState(teamUid string, roomType, playSid int32) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	if team.RoomType != roomType && roomType == -1 {
		// 重置报名状态
		team.RoomType = 0
		team.ApplyTime = 0
		team.MatchOpenTime = 0
	}
	team.PlaySid = playSid
	teamDbChan <- team.Uid
	return
}

// 解散队伍
func (this *Lobby) HandleDisbandTeam(team *TeamInfo) {
	if team.ApplyTime > 0 {
		// 报名中则取消报名
		this.InvokeMatchRpcNR(slg.RPC_MATCH_CANCEL_APPLY_SERVER, team.RoomType, team.Uid)
	} else if team.CustomRoomId != 0 { // 在自定义房间则通知匹配服
		this.InvokeMatchRpcNR(slg.RPC_EXIT_CUSTOM_ROOM, team.Uid, team.Uid)
	}
	team.userListLock.Lock()
	for _, teamUser := range team.UserList {
		// 通知该玩家
		this.InvokeUserFuncNR(teamUser.Uid, slg.RPC_USER_KICK_BY_TEAM, team.Uid, 1)
	}
	team.UserList = []*TeamUserInfo{}
	team.userListLock.Unlock()
	team.inviteListLock.Lock()
	team.InviteList = []*TeamInviteUserInfo{}
	team.inviteListLock.Unlock()
	// 通知聊天服删除队伍频道
	this.InvokeChatRpcNR(slg.RPC_DEL_TEAM, team.Uid)
	// 从数据库和内存中删除
	DelTeam(team)
}

// 删除队伍
func (this *Lobby) delTeam(teamUid string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	this.HandleDisbandTeam(team)
	return
}

// 删除队员
func (this *Lobby) delTeamUser(teamUid, uid string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	if team.ApplyTime > 0 {
		// 报名中则取消报名
		this.InvokeMatchRpcNR(slg.RPC_MATCH_CANCEL_APPLY_SERVER, team.RoomType, teamUid)
	} else if team.CustomRoomId != 0 { // 在自定义房间则通知匹配服
		this.InvokeMatchRpcNR(slg.RPC_EXIT_CUSTOM_ROOM, teamUid, uid)
	}
	err, _ = team.delTeammate(uid)
	// 通知该玩家清除队伍uid
	this.InvokeUserFuncNR(uid, slg.RPC_SET_USER_TEAMID, "")
	teamDbChan <- team.Uid
	this.CheckClearTeam(team)
	return
}

// 检测报名中的队伍
func (this *Lobby) checkApplyTeam(teamUid string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	if team.ApplyTime == 0 {
		// 队伍中的数据未报名
		return nil, ecode.SERVER_NOT_APPLY.String()
	}
	if team.PlaySid != 0 {
		room := GetRoomById(team.PlaySid)
		if room != nil && !IsGameOver(room) {
			return nil, ecode.IN_GAME.String()
		}
	}
	if team.GetMatchOpenTime() != 0 {
		return nil, ecode.IN_MATCH.String()
	}
	userList := []string{}
	team.userListLock.RLock()
	for _, teamUser := range team.UserList {
		userList = append(userList, teamUser.Uid)
	}
	team.userListLock.RUnlock()
	for _, userUid := range userList {
		_, err = this.InvokeUserFunc(userUid, slg.RPC_MATCH_CHECK_APPLY_USER, teamUid)
		if err != "" {
			return
		}
	}
	return
}

// 解散队伍
func (this *Lobby) rpcDisbandTeam(teamUid, userId string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	if team.ApplyTime > 0 {
		// 报名中
		return nil, ecode.IN_APPLY.String()
	} else if team.GetMatchOpenTime() != 0 {
		// 匹配中
		return nil, ecode.IN_MATCH.String()
	} else if team.PlaySid != 0 {
		room := GetRoomById(team.PlaySid)
		if room != nil && !IsGameOver(room) {
			// 对局中
			return nil, ecode.IN_GAME.String()
		}
	} else if team.CustomRoomId != 0 { // 在自定义房间中
		return nil, ecode.ALREADY_IN_CUSTOM_ROOM.String()
	}
	// 只有队长可解散
	var teamUser *TeamUserInfo
	team.userListLock.RLock()
	for _, v := range team.UserList {
		if v.Uid == userId {
			teamUser = v
		}
	}
	team.userListLock.RUnlock()
	if teamUser == nil || teamUser.Job != 1 {
		err = ecode.NOT_OPERATING_AUTH.String()
		return
	}
	this.HandleDisbandTeam(team)
	return
}

// 变更队员职位
func (this *Lobby) rpcTeamChangeJob(teamUid, userId string, job int) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	if team.ApplyTime > 0 {
		// 报名中
		return nil, ecode.IN_APPLY.String()
	}
	if team.GetMatchOpenTime() != 0 {
		// 匹配中
		return nil, ecode.IN_MATCH.String()
	}
	if team.PlaySid != 0 {
		room := GetRoomById(team.PlaySid)
		if room != nil && !IsGameOver(room) {
			// 对局中
			return nil, ecode.IN_GAME.String()
		}
	}
	if job == 1 {
		// 不能变更为队长
		return nil, ecode.NOT_OPERATING_AUTH.String()
	}
	var teamUser *TeamUserInfo
	team.userListLock.Lock()
	for _, v := range team.UserList {
		if v.Uid == userId {
			teamUser = v
			teamUser.Job = job
			break
		}
	}
	team.userListLock.Unlock()
	if teamUser == nil {
		err = ecode.USER_NOT_IN_TEAM.String()
	} else {
		// 通知
		this.RcpTeamNotify(team, slg.LOBBY_TEAMMATE_INFO_CHANGE, &pb.LOBBY_ONTEAMMATEINFOCHANGE_NOTIFY{Info: teamUser.ToPb()})
	}
	return
}

// 检测进入自定义房间的队伍
func (this *Lobby) rpcCheckEnterCustomTeam(teamUid string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	if team.ApplyTime > 0 {
		// 报名中
		return nil, ecode.IN_APPLY.String()
	}
	if team.GetMatchOpenTime() != 0 {
		// 匹配中
		return nil, ecode.IN_MATCH.String()
	}
	if team.PlaySid != 0 {
		room := GetRoomById(team.PlaySid)
		if room != nil && !IsGameOver(room) {
			// 对局中
			return nil, ecode.IN_GAME.String()
		}
	}
	return
}

// 进入自定义房间
func (this *Lobby) rpcEnterCustomRoom(teamUid, nickname, headIcon, password string, pos, farmType, sid int32) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		// 不存在则创建队伍
		team = CreateTeam(teamUid, nickname, headIcon, this.GetLid(), slg.CUSTOM_SERVER_TYPE, pos, farmType)
	}
	_, err = this.rpcCheckEnterCustomTeam(teamUid)
	if err != "" {
		log.Warning("rpcEnterCustomRoom teamUid: %v, err: %v", teamUid, err)
	}

	teamPb := team.ToPb()
	teamBytes, _ := pb.ProtoMarshal(teamPb)
	_, err = this.InvokeMatchRpc(slg.RPC_ENTER_CUSTOM_ROOM, password, teamBytes)
	if err == "" {
		team.CustomRoomId = sid
	}
	return
}

// 退出自定义房间
func (this *Lobby) rpcExitCustomRoom(teamUid, userId string) (result []byte, err string) {
	team := this.GetLocalTeamOrDb(teamUid)
	if team == nil {
		return nil, ecode.TEAM_NOT_EXIST.String()
	} else if team.ApplyTime > 0 {
		return nil, ecode.IN_APPLY.String()
	} else if team.MatchOpenTime > 0 {
		return nil, ecode.IN_MATCH.String()
	}

	_, err = this.InvokeMatchRpc(slg.RPC_EXIT_CUSTOM_ROOM, teamUid, userId)
	if err == "" {
		if teamUid == userId {
			// 队伍退出
			team.CustomRoomId = 0
			// 检测并删除空队伍
			if !this.CheckClearTeam(team) {
				// TODO 发送消息
				// this.SendTeamSysMsg(team.Uid, slg.TEAM_MSG_TYPE_CANCEL_APPLY, nickname)
			}
		} else {
			// 个人退出 并退出队伍
			_, nickname := team.delTeammate(userId)
			// 通知玩家
			this.InvokeUserFuncNR(userId, slg.RPC_SET_USER_TEAMID, "")
			// 通知队伍更新信息
			this.RcpTeamNotify(team, slg.LOBBY_TEAM_UPDATE, &pb.LOBBY_ONTEAMUPDATE_NOTIFY{TeamInfo: team.ToPb()})
			// 发送系统消息
			this.SendTeamSysMsg(team.Uid, slg.TEAM_MSG_TYPE_EXIT, nickname)
		}
	}
	return
}

// 队伍信息通知
func (this *Lobby) RcpTeamNotify(team *TeamInfo, topic string, m protoreflect.ProtoMessage) {
	if team == nil || team.UserList == nil {
		return
	}
	data, err := pb.ProtoMarshal(m)
	if err != "" {
		log.Error("RcpTeamNotify m: %v, err: %v", m, err)
		return
	}
	userList := []string{}
	team.userListLock.RLock()
	for _, teamUser := range team.UserList {
		userList = append(userList, teamUser.Uid)
	}
	team.userListLock.RUnlock()
	for _, v := range userList {
		this.InvokeUserFuncNR(v, slg.RPC_SEND_NOTIFY, topic, data)
	}
}
