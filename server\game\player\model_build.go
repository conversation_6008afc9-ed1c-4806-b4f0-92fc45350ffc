package player

import (
	"time"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/effect"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/sasha-s/go-deadlock"
)

/**
建筑相关
*/

type BTQueueList struct {
	deadlock.RWMutex
	List []*BTInfo
}

// 建造信息
type BTInfo struct {
	BUid      string `json:"b_uid"`      // 建筑uid
	StartTime int64  `json:"start_time"` // 开始时间
	AIndex    int32  `json:"a_index"`    // 区域位置
	Bid       int32  `json:"bid"`        // 建筑id
	BLv       int32  `json:"b_lv"`       // 要建造的等级
	NeedTime  int32  `json:"need_time"`  // 需要时间
}

func NewBTInfo(index int32, uid string, id, lv, needTime int32) *BTInfo {
	return &BTInfo{
		AIndex:    index,
		BUid:      uid,
		Bid:       id,
		BLv:       lv,
		StartTime: 0,
		NeedTime:  needTime,
	}
}

func (this *BTInfo) Strip() map[string]interface{} {
	return map[string]interface{}{
		"index":       this.AIndex,
		"uid":         this.BUid,
		"id":          this.Bid,
		"lv":          this.BLv,
		"needTime":    this.NeedTime,
		"surplusTime": this.GetSurplusTime(),
	}
}

func (this *BTInfo) ToPb() *pb.BTInfo {
	return &pb.BTInfo{
		Index:       int32(this.AIndex),
		Uid:         this.BUid,
		Id:          int32(this.Bid),
		Lv:          int32(this.BLv),
		NeedTime:    int32(this.NeedTime),
		SurplusTime: int32(this.GetSurplusTime()),
	}
}

func (this *BTInfo) GetSurplusTime() int32 {
	return int32(ut.MaxInt64(this.StartTime+int64(this.NeedTime)-time.Now().UnixMilli(), 0))
}

// 建造队列
func (this *Model) ToBTQueues() []map[string]interface{} {
	this.BTQueues.RLock()
	defer this.BTQueues.RUnlock()
	ret := []map[string]interface{}{}
	for _, m := range this.BTQueues.List {
		ret = append(ret, m.Strip())
	}
	return ret
}

func (this *Model) ToBTQueuesPb() []*pb.BTInfo {
	this.BTQueues.RLock()
	defer this.BTQueues.RUnlock()
	ret := []*pb.BTInfo{}
	for _, m := range this.BTQueues.List {
		ret = append(ret, m.ToPb())
	}
	return ret
}

func (this *Model) GetBTQueueCount() int32 {
	this.BTQueues.RLock()
	defer this.BTQueues.RUnlock()
	return int32(len(this.BTQueues.List))
}

// 建造队列是否满了
func (this *Model) IsBTQueueFull() bool {
	return this.GetBTQueueCount() >= this.GetBTQueueMaxCount()
}

// 获取建造信息
func (this *Model) GetBTInfoByBUid(uid string) *BTInfo {
	this.BTQueues.RLock()
	defer this.BTQueues.RUnlock()
	for _, m := range this.BTQueues.List {
		if m.BUid == uid {
			return m
		}
	}
	return nil
}

// 添加到建造队列
func (this *Model) PutBTQueue(index int32, uid string, id, lv, needTime int32) {
	this.BTQueues.Lock()
	defer this.BTQueues.Unlock()
	cd := this.GetBuildCd() * 0.01
	needTime = int32(float64(needTime) * 1000.0 * (1.0 - cd))
	this.BTQueues.List = append(this.BTQueues.List, NewBTInfo(index, uid, id, lv, needTime))
	// 设置第一个的开始时间
	if len(this.BTQueues.List) == 1 {
		this.BTQueues.List[0].StartTime = time.Now().UnixMilli()
	}
	// 添加到离线消息检测
	var endTime int64
	for _, v := range this.BTQueues.List {
		endTime += v.StartTime + int64(v.NeedTime)
	}
	wld := this.room.GetWorld()
	wld.AddCheckPlayerOfflineMsg(this.Uid, constant.OFFLINE_MSG_TYPE_BUILD, endTime, ut.String(id), ut.String(lv))
	// 上报
	wld.TaTrack(this.Uid, this.ReCreateMainCityCount, "ta_building", map[string]interface{}{
		"build": map[string]interface{}{
			"build_id":   id,
			"build_lv":   lv,
			"build_time": needTime,
		},
		"maincitylevel": wld.GetPlayerMainBuildLv(this.Uid),
		"pawncount":     len(wld.GetPlayerPawnTrackInfo(this.Uid)),
		"landcount":     len(wld.GetPlayerOwnCells(this.Uid)),
		"cereal":        this.Cereal.Value,
		"timber":        this.Timber.Value,
		"stone":         this.Stone.Value,
		"expbook":       this.ExpBook,
		"iron":          this.Iron,
		"scroll":        this.UpScroll,
		"nail":          this.Fixator,
	})
}

// 取消一个建造
func (this *Model) CancelBT(uid string) {
	this.BTQueues.Lock()
	// 从队列中删除
	list := this.BTQueues.List
	var needTime, id, lv int32
	index := len(list)
	for i := len(list) - 1; i >= 0; i-- {
		if list[i].BUid == uid {
			this.BTQueues.List = append(list[:i], list[i+1:]...)
			index = i
			needTime = list[i].NeedTime
			id = list[i].Bid
			lv = list[i].BLv
			break
		}
	}
	// 如果取消的第一个 还有的话就设置开始时间
	if len(this.BTQueues.List) > 0 && this.BTQueues.List[0].StartTime == 0 {
		this.BTQueues.List[0].StartTime = time.Now().UnixMilli()
	}
	this.BTQueues.Unlock()
	// 移除玩家离线消息检测
	this.room.GetWorld().RemoveCheckPlayerOfflineMsg(this.Uid, constant.OFFLINE_MSG_TYPE_BUILD, ut.String(id), ut.String(lv))
	// 调整取消的建造队列后面的在离线消息中的检测时间
	for i := index; i < len(list); i++ {
		this.room.GetWorld().ChangeCheckPlayerOfflineMsgTime(this.Uid, constant.OFFLINE_MSG_TYPE_BUILD, -needTime, ut.String(id))
	}
}

// 取消修建
func (this *Model) CancelBTByID(id int32) bool {
	this.BTQueues.RLock()
	bt := array.Find(this.BTQueues.List, func(m *BTInfo) bool { return m.Bid == id })
	this.BTQueues.RUnlock()
	if bt != nil {
		this.CancelBT(bt.BUid)
	}
	return bt != nil
}

// 立即完成所有的修建
func (this *Model) CompleteAllBt() {
	this.BTQueues.Lock()
	defer this.BTQueues.Unlock()
	builds := []map[string]interface{}{}
	buildsNum := len(this.BTQueues.List)
	for _, m := range this.BTQueues.List {
		builds = append(builds, map[string]interface{}{
			"build_id":   m.Bid,
			"build_lv":   m.BLv,
			"build_time": m.GetSurplusTime(),
		})
		this.UpdateBTComplete(m)
		// 移除玩家离线消息检测
		this.room.GetWorld().RemoveCheckPlayerOfflineMsg(this.Uid, constant.OFFLINE_MSG_TYPE_BUILD, ut.String(m.Bid), ut.String(m.BLv))
	}
	// 置空
	this.BTQueues.List = []*BTInfo{}
	// 添加玩家防作弊检测积分
	this.AddAntiCheatScore(constant.ANTI_CHEAT_LV_UP_BUILD_SCORE * int32(buildsNum))
	// 上报
	this.room.GetWorld().TaTrack(this.Uid, this.ReCreateMainCityCount, "ta_inDoneBt", map[string]interface{}{
		"builds":    builds,
		"gold_cost": constant.IN_DONE_BT_GOLD,
	})
}

// 刚上线的时候初始化队列
func (this *Model) InitUpdateBTQueue(now int64) {
	wld := this.room.GetWorld()
	this.BTQueues.Lock()
	defer this.BTQueues.Unlock()
	for len(this.BTQueues.List) > 0 {
		// 取出第一个开始
		m := this.BTQueues.List[0]
		if now-m.StartTime < int64(m.NeedTime) {
			break
		}
		wld.AreaBuildBTComplete(m.AIndex, m.BUid, m.BLv)
		if m.Bid == constant.MAIN_BUILD_ID {
			// 如果是主城建筑 刷新政策槽位信息
			this.UpdatePolicySlotInfo(m.BLv, true)
		} else if m.Bid == constant.SMITHY_BUILD_ID {
			// 如果是铁匠铺 刷新装备槽位信息
			this.UpdateEquipSlotInfo(m.BLv, true)
		} else if m.Bid == constant.BARRACKS_BUILD_ID {
			// 如果是兵营 刷新士兵槽位信息
			this.UpdatePawnSlotInfo(m.BLv, true)
		}
		// 删除
		this.BTQueues.List = append(this.BTQueues.List[:0], this.BTQueues.List[1:]...)
		if len(this.BTQueues.List) > 0 {
			this.BTQueues.List[0].StartTime = m.StartTime + int64(m.NeedTime)
		} else {
			break
		}
	}
	// 兼容一下 没有在建筑队列种但是是0级的建筑直接设置为1级
	wld.CompatiBuildLv0(this.MainCityIndex, array.Map(this.BTQueues.List, func(m *BTInfo, _ int) string { return m.BUid }))
}

// 检测更新建造队列
func (this *Model) CheckUpdateBTQueue(now int64) {
	this.BTQueues.Lock()
	if len(this.BTQueues.List) == 0 {
		this.BTQueues.Unlock()
		return
	}
	// 取出第一个开始
	m := this.BTQueues.List[0]
	if now-m.StartTime < int64(m.NeedTime) {
		this.BTQueues.Unlock()
		return
	}
	this.BTQueues.List = append(this.BTQueues.List[:0], this.BTQueues.List[1:]...) // 删除
	if len(this.BTQueues.List) > 0 {
		this.BTQueues.List[0].StartTime = m.StartTime + int64(m.NeedTime)
	}
	this.BTQueues.Unlock()
	// 先通知队列
	this.PutNotifyQueue(constant.NQ_BT_QUEUE, &pb.OnUpdatePlayerInfoNotify{Data_6: this.ToBTQueuesPb()})
	// 刷新完成
	this.UpdateBTComplete(m)
	// 添加玩家防作弊检测积分
	this.AddAntiCheatScore(constant.ANTI_CHEAT_LV_UP_BUILD_SCORE)
}

// 通知建造升级
func (this *Model) UpdateBTComplete(m *BTInfo) {
	if build := this.room.GetWorld().AreaBuildBTComplete(m.AIndex, m.BUid, m.BLv); build != nil {
		this.PutNotifyQueue(constant.NQ_BUILD_UP, &pb.OnUpdatePlayerInfoNotify{Data_5: build.ToShortDataPb()}) // 通知玩家
		this.updateBuildUpLvInfo(m.Bid, m.BLv)
	}
}

// 直接设置建筑等级
func (this *Model) SetBuildLv(id, lv int32) {
	if build := this.room.GetWorld().SetBuildLv(this.MainCityIndex, id, lv); build != nil { // 通知玩家
		this.PutNotifyQueue(constant.NQ_BUILD_UP, &pb.OnUpdatePlayerInfoNotify{Data_5: build.ToShortDataPb()}) // 通知玩家
		this.updateBuildUpLvInfo(id, lv)
	}
}

// 刷新建筑升级 这个方法只会在玩家在线的时候生效
func (this *Model) updateBuildUpLvInfo(id, lv int32) {
	// 刷新建筑效果
	this.UpdateBuildEffect()
	if id == constant.MAIN_BUILD_ID {
		// 如果是主城建筑 刷新政策槽位信息
		this.room.GetWorld().UpdatePolicySlotInfo(this.Uid, lv, false)
	} else if id == constant.SMITHY_BUILD_ID {
		// 如果是铁匠铺 刷新装备槽位信息
		this.UpdateEquipSlotInfo(lv, false)
	} else if id == constant.BARRACKS_BUILD_ID {
		// 如果是兵营 刷新士兵槽位信息
		this.UpdatePawnSlotInfo(lv, false)
	}
}

// 刷新建筑效果
func (this *Model) UpdateBuildEffect() {
	eos := this.room.GetWorld().GetAreaAllBuildEffect(this.MainCityIndex)
	merchantCount := 0
	granaryCap, warehouseCap := this.GetGranaryCap(), this.GetWarehouseCap()
	this.effectMutex.Lock()
	this.effects[effect.FORGE_CD] = 0      // 减少打造时间
	this.effects[effect.ARMY_COUNT] = 0    // 军队最大数量
	this.effects[effect.GRANARY_CAP] = 0   // 粮食容量
	this.effects[effect.WAREHOUSE_CAP] = 0 // 仓库容量
	for _, m := range eos {
		if m.Type == effect.ALLIANCE_PERS { // 联盟人数
		} else if m.Type == effect.MERCHANT_COUNT { // 商人数量
			merchantCount += int(m.Value)
		} else {
			this.effects[m.Type] += m.Value
		}
	}
	this.effectMutex.Unlock()
	// 容量
	if newGranaryCap, newWarehouseCap := this.GetGranaryCap(), this.GetWarehouseCap(); granaryCap != newGranaryCap || warehouseCap != newWarehouseCap {
		data := &pb.UpdateOutPut{
			GranaryCap:   int32(newGranaryCap),
			WarehouseCap: int32(newWarehouseCap),
			Flag:         pb.AddFlags(int64(pb.OutPutFlagEnum_GranaryCap), int64(pb.OutPutFlagEnum_WarehouseCap)),
		}
		this.PutNotifyQueue(constant.NQ_OUTPUT, &pb.OnUpdatePlayerInfoNotify{Data_1: data})
	}
	// 刷新商人数量
	if merchantCount != len(this.Merchants.List) {
		this.UpdateMerchantCount(merchantCount)
	}
}
