package sdk

import (
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/url"

	"github.com/huyangv/vmqant/log"
)

const (
	TWITTER_APPID  = ""
	TWITTER_SECRET = ""
	TWITTER_URL    = "https://api.twitter.com/oauth/access_token"
)

type TwitterVerifyTokenRet struct {
	OauthToken       string `json:"oauth_token"`
	OauthTokenSecret string `json:"oauth_token_secret"`
	UserId           string `json:"user_id"`
	ScreenName       string `json:"screen_name"`
}

// Twitter登录验证
func TwitterLoginCheck(oauthToken string, oauthVerify string) (res TwitterVerifyTokenRet, err error) {
	resp, err := http.PostForm(TWITTER_URL, url.Values{"oauth_token": {oauthToken}, "oauth_verifier": {oauthVerify}})
	defer func() {
		if resp != nil && resp.Body != nil {
			resp.Body.Close()
		}
	}()
	if err != nil {
		log.Error("TwitterLoginCheck http err:%v", err)
		return
	}
	if resp.StatusCode != http.StatusOK {
		err = errors.New("TwitterLoginCheck http resp status err")
		log.Error("TwitterLoginCheck http resp status err, status: %v", resp.Status)
		return
	}
	body, _ := io.ReadAll(resp.Body)
	// fmt.Println(string(body))
	err = json.Unmarshal(body, &res)
	if err != nil {
		log.Error("TwitterLoginCheck ret err:%v", err)
		return
	}
	return
}
