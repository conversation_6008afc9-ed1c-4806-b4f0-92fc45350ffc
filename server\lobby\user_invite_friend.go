package lobby

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/utils/array"
)

type InviteFriendInfo struct {
	UID     string `json:"uid" bson:"uid"`
	UseType int32  `json:"useType" bson:"use_type"`
}

// 添加被邀请好友
func (this *User) AddInviteFriends(uid string) {
	this.InviteFriendsLock.Lock()
	if this.InviteFriends == nil {
		this.InviteFriends = []*InviteFriendInfo{}
	} else if array.Some(this.InviteFriends, func(m *InviteFriendInfo) bool { return m.UID == uid }) {
		this.InviteFriendsLock.Unlock()
		return
	}
	this.InviteFriends = append(this.InviteFriends, &InviteFriendInfo{UID: uid, UseType: 0})
	this.InviteFriendsLock.Unlock()
	if this.FlagUpdateDB() { // 如果在线就要通知
		this.PutNotifyQueue(constant.NQ_UPDATE_INVITES, &pb.OnUpdatePlayerInfoNotify{Data_35: this.InveteFriendsToPb()})
	}
}

// 获取邀请好友未使用数量
func (this *User) GetInviteFriendNotUseCount() int32 {
	this.InviteFriendsLock.RLock()
	defer this.InviteFriendsLock.RUnlock()
	return int32(len(array.Filter(this.InviteFriends, func(m *InviteFriendInfo, _ int) bool { return m.UseType == 0 })))
}

// 使用邀请好友
func (this *User) UseInviteFriend(count, useType int32) {
	this.InviteFriendsLock.RLock()
	defer this.InviteFriendsLock.RUnlock()
	for _, m := range this.InviteFriends {
		if m.UseType == 0 && count > 0 {
			m.UseType = useType
			count -= 1
			if count == 0 {
				break
			}
		}
	}
	this.FlagUpdateDB()
}

func (this *User) InveteFriendsToPb() []*pb.InviteFriendInfo {
	arr := []*pb.InviteFriendInfo{}
	this.InviteFriendsLock.RLock()
	defer this.InviteFriendsLock.RUnlock()
	for _, v := range this.InviteFriends {
		inviteFriend := &pb.InviteFriendInfo{
			Uid:     v.UID,
			UseType: v.UseType,
		}
		arr = append(arr, inviteFriend)
	}
	return arr
}
