package match

var isRunning = false

func RunTick(app *Match) {
	isRunning = true
	app.RoomCheck()
	app.RunLoadRoom()
	app.RunCreateRoom()
	app.RunOpenRoom()
	app.RunCloseRoom()
	app.UpdateServerTaskTick()
	app.MatchTick()
	RunCleanMachLogsTick()
	RunDelRoomsDbTick()
	RunPreGenRoomTick()
	app.RunOnlineUserLogTick()
	RunSaveCustomRoomsDbTick()
}

func StopTick() {
	isRunning = false
	SaveCunstomRoomsDb()
}
