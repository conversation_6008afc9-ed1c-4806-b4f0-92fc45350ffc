package slg

const (
	// 大厅相关
	RPC_LOBYY_NOTIFY_ALL_USER     = "OnLobyyNotifyAllUser"
	RPC_LOBBY_PUT_USER_NOTIFY     = "OnLobbyPutUserNotify"
	RPC_LOBBY_PUT_ALL_USER_NOTIFY = "OnLobbyPutAllUserNotify"

	// 登录相关
	RPC_LEAVE               = "OnLeave"
	RPC_LEAVE_BY_CHANGE_SER = "OnLeaveByChangeSer"
	RPC_KICK                = "OnKick"
	RPC_LOBBY_QUEUE         = "OnLobbyQueue"
	RPC_LOBBY_QUEUE_MOVE    = "OnLobbyQueueMove"

	// 区服相关
	RPC_SELECT_ROOM                    = "OnSelectRoom"
	RPC_GET_ROOM_INFO                  = "OnGetRoomInfo"
	RPC_CLOSE_ROOM                     = "OnCloseRoom"
	RPC_STOP_SERVER                    = "OnStopServer"
	RPC_GET_ROOMS                      = "OnGetRooms"
	RPC_GET_LOBBY_INFO                 = "OnGetLobbyInfo"
	RPC_GET_LOBBY_MACH_INFO            = "OnGetLobbyMachInfo"
	RPC_CANCEL_APPLY_SERVER            = "OnCancelApplyServer"
	RPC_GAME_SERVER_STATE_CHANGE       = "OnGameServerStateChange"
	RPC_GET_ROOM_NEXT_MATCH_TIME       = "OnGetRoomNextMatchTime"
	RPC_MATCH_APPLY_SERVER             = "OnMatchApplyServer"
	RPC_MATCH_CANCEL_APPLY_SERVER      = "OnMatchCancelApplyServer"
	RPC_MATCH_USER_CANCEL_APPLY_SERVER = "OnMatchUserCancelApplyServer"
	RPC_CREATE_CUSTOM_ROOM             = "OnCreateCustomRoom"

	// 物品相关
	RPC_CHANGE_COST_TYPE_OBJS = "OnChangeCostByTypeObjs"
	RPC_GET_GOLD              = "OnGetGold"
	RPC_CHANGEIGOLD           = "OnChangeGold"
	RPC_CHECK_HAS_PAWN_SKIN   = "OnCheckHasPawnSkin"
	RPC_CHECK_HAS_CHAT_EMOJI  = "OnCheckHasChatEmoji"
	RPC_ADD_USER_ITEMS        = "OnAddUserItems"
	RPC_ADD_PLAYER_ITEMS      = "OnAddPlayerItems"
	RPC_CLEAR_PWANS_SKIN      = "OnClearPawnsSkin"
	RPC_CHECK_USE_CITY_SKIN   = "OnCheckUseCitySkin"

	// 任务相关
	RPC_TRIGGER_TASK_BY_LOBBY     = "OnTriggerTaskByLobby"
	RPC_CHECK_GAME_TASK_CONDITION = "OnCheckGameTaskCondition"
	RPC_TRIGGER_TASK_BY_GAME      = "OnTriggerTaskByGame"

	// 用户信息相关
	RPC_CHANGE_LANGUAGE                = "OnChangeLanguage"
	RPC_CHANGE_FCM_TOKEN               = "OnChangeFCMToken"
	RPC_PUT_NOTIFY_QUEUE               = "OnPutNotifyQueue"
	RPC_PUT_PLAYER_NOTIFY              = "OnPutPlayerNotify"
	RPC_CHANGE_OFFLINE_OPT             = "OnChangeOfflineOpt"
	RPC_GET_USER_BY_CREATE_PLAYER      = "OnGetUserByCreatePlayer"
	RPC_SET_USER_PLAYSID               = "OnSetUserPlaySid"
	RPC_GET_ONLINE_USER_INFO           = "OnGetOnlineUserInfo"
	RPC_CHANGE_NICKNAME                = "OnChangeNickname"
	RPC_CHANGE_HEADICON                = "OnChangeHeadIcon"
	RPC_CHANGE_PERSONAL_DESC           = "OnChangePersonalDesc"
	RPC_CHANGE_USER_TITLE              = "OnChangeUserTitle"
	RPC_CHANGE_PAWN_SKIN               = "OnChangePawnSkin"
	RPC_CHECKUP_DATE_NEXT_TO_DAYTIME   = "OnCheckUpdateNextToDayTime"
	RPC_PLAYER_GIVEUP_GAME             = "OnPlayerGiveupGame"
	RPC_MAX_LAND_COUNT_CHANGE          = "OnMaxLandCountChange"
	RPC_GET_OFFLINE_REPORT_INFO        = "OnGetOfflineReportInfo"
	RPC_GET_USER_CHECK_CHAT_INFO       = "OnGetUserCheckChatInfo"
	RPC_GET_USER_BANNED_CHAT_ENDTIME   = "OnGetUserBannedChatEndTime"
	RPC_GET_APPLY_USER_BY_CREATEPLAYER = "OnGetApplyUserByCreatePlayer"
	RPC_GET_TEAM_USERS_BY_CREATEPLAYER = "OnGetTeamUserByCreatePlayer"
	RPC_ADD_INVITE_FRIEND              = "OnAddInviteFriend"
	RPC_UPDATE_USER_INFO               = "OnUpdateUserInfo"
	RPC_GET_USER_BASE_INFO             = "OnGetUserBaseInfo"
	RPC_GET_USER_BY_WEB                = "OnGetUserByWeb"
	RPC_CANCEL_DEL_USER                = "OnCancelDelUser"
	RPC_GET_USER_HERO_INFO             = "OnGetUserHeroInfo"
	RPC_CHANGE_HERO_ATTR               = "OnChangeHeroAttr"
	RPC_GET_USER_INFO                  = "OnGetUserInfo"
	RPC_SET_TRESURE_LOST_COUNT         = "OnSetTresureLostCount"
	RPC_USER_ANTI_CHEAT_CHECK          = "OnUserAntiCheatCheck"
	RPC_USER_BAN_ACCCOUNT_CHECK        = "OnUserBanAccountCheck"
	RPC_ADD_BATTLE_PASS_SCORE          = "OnAddBattlePassScore"
	RPC_GET_PLAYER_CUR_GAME_INFO       = "OnGetPlayerCurGameInfo"

	// 人气相关
	RPC_GET_USER_POPULARITY    = "OnGetUserPopularity"
	RPC_CHANGE_USER_POPULARITY = "OnChangeUserPopularity"

	// 邮件相关
	RPC_SEND_MAIL_ITEM_ONE = "OnSendMailItemOne"
	RPC_SEND_MAIL_MANY     = "OnSendMailMany"
	RPC_DEL_MAIL_BY_SID    = "OnDelMailBySid"

	// 支付相关
	RPC_ORDER_REFUND_BY_NOTIFY       = "OnOrderRefundByNotify"
	RPC_DID_RENEW_BY_NOTIFY          = "OnDidRenewByNotify"
	RPC_RENEW_STATE_CHANGE_BY_NOTIFY = "OnRenewStateChangeByNotify"
	RPC_SUB_REFUND_BY_NOTIFY         = "OnSubRefundByNotify"
	RPC_DEL_REPEAT_USE_SUB           = "OnDelRepeatUseSub"
	RPC_USER_REFUND_REDUCE_ITEM      = "OnUserRefundReduceItem"
	RPC_PAY_ORDER_BY_NOTIFY          = "OnPayOrderByNotify"
	RPC_SUB_PAY_ORDER_BY_NOTIFY      = "OnSubPayOrderByNotify"

	// 结算相关
	RPC_GAMEOVER_SETTLE      = "OnGameOverSettle"
	RPC_USER_GAMEOVER_SETTLE = "OnUserGameOverSettle"

	// 好友相关
	RPC_USER_APPLY_FRIEND_BY_OTHER   = "OnUserApplyFriendByOther"
	RPC_USER_APPLY_RESPONSE_BY_OTHER = "OnUserApplyResponseByOhter"
	RPC_USER_DEL_FRIEND_BY_OTHER     = "OnUserDelFriendByOther"
	RPC_USER_RECEIVE_FRIEND_MSG      = "OnUserReceiveFriendMsg"
	RPC_USER_UPDATE_FRIEND_INFO      = "OnUserUpdateFriendInfo"
	RPC_GET_FRIEND_CHAT              = "OnGetFriendChat"
	RPC_SEND_FRIEND_CHAT             = "OnSendFriendChat"
	RPC_FRIEND_SEND_GIFT             = "OnFriendSendGift"
	RPC_SKIN_ITEM_TRACK              = "OnSkinItemTrack"

	// 组队相关
	RPC_GET_TEAM_INFO           = "OnGetTeamInfo"
	RPC_ADD_TEAM_INVITE_INFO    = "OnAddTeamInviteInfo"
	RPC_TEAM_INVITE             = "OnTeamInvite"
	RPC_TEAM_INVITE_RESPONSE    = "OnTeamInviteResponse"
	RPC_TEAM_DEL_TEAMMATE       = "OnTeamDelTeammate"
	RPC_USER_KICK_BY_TEAM       = "OnUserKickByTeam"
	RPC_USER_LEAVE_TEAM_FORCE   = "OnUserLeaveTeamForce"
	RPC_LEAVE_TEAM              = "OnLeaveTeam"
	RPC_GAMEOVER_LEAVE_TEAM     = "OnGameOverLeaveTeam"
	RPC_GET_TEAM_CHATS          = "OnGetTeamChats"
	RPC_TEAM_CHAT               = "OnTeamChat"
	RPC_CHANGE_TEAM_MODE        = "OnChangeTeamMode"
	RPC_DEL_TEAM                = "OnDelTeam"
	RPC_UPDATE_TEAM_USER_INFO   = "OnUpdateTeamUserInfo"
	RPC_TEAM_APPLY_SERVER       = "OnTeamApplyServer" // 队伍申请报名
	RPC_SET_USER_TEAMID         = "OnSetUserTeamId"   // 设置用户的队伍id
	RPC_CHECK_USER_TEAM         = "OnCheckUserTeam"   // 检测用户的队伍
	RPC_TEAM_START_MATCH        = "OnTeamStartMatch"
	RPC_TEAM_ENTER_GAME         = "OnTeamEnterGame"
	RPC_TEAM_DELAY_MATCH        = "OnTeamDelayMatch"       // 匹配人数不够延迟匹配
	RPC_TEAM_RESET_STATE        = "OnTeamResetState"       // 重置队伍状态
	RPC_DISBAND_TEAM            = "OnDisbandTeam"          // 解散队伍
	RPC_TEAM_CHANGE_JOB         = "OnTeamChangeJob"        // 更改队员职位
	RPC_SET_USER_TEAM_FORCE     = "OnSetUserTeamForce"     // 强制设置玩家队伍
	RPC_CHECK_ENTER_CUSTOM_TEAM = "OnCheckEnterCustomTeam" // 检测进入自定义房间的队伍
	RPC_ENTER_CUSTOM_ROOM       = "OnEnterCustomRoom"      // 进入自定义房间
	RPC_EXIT_CUSTOM_ROOM        = "OnExitCustomRoom"       // 退出自定义房间

	// 聊天相关
	RPC_SEND_CHAT      = "OnSendChat"
	RPC_LIKE_JWM       = "OnLikeJwm"
	RPC_ADD_BLACK_LIST = "OnAddBlackList"
	RPC_GET_BLACK_LIST = "OnGetBlackList"

	// 活动相关
	RPC_GET_DC_FOLLOW_GIFT = "OnGetDcFollowGift" // DC关注礼物

	// 后台相关
	RPC_REQ_LOG                = "OnReqLog"              // 客户端请求统计
	RPC_SERVER_UPDATE_TIME     = "OnServerUpdateTime"    // 更新服务器维护时间
	RPC_UPDATE_VERSION         = "OnUpdateVersion"       // 更新版本
	RPC_HTTP_DELAY_RESTART     = "OnHttpDelayRestart"    // 延时重启工具服
	RPC_SET_TA_PARAMS          = "OnSetTaParams"         // 设置TA参数
	RPC_SET_FRIEND_PARAMS      = "OnSetFriendParams"     // 设置好友相关参数
	RPC_SET_TEAM_PARAMS        = "OnSetTeamParams"       // 设置队伍相关参数
	RPC_SET_LOGIN_LIMIT        = "OnSetLoginLimit"       // 设置登录限制类型
	RPC_SET_CHEAT_CHECK_OPEN   = "OnSetCheatCheckOpen"   // 设置作弊检测开关
	RPC_TEAM_GM_DEL_TEAM       = "OnGMDelTeam"           // 后台删除队伍
	RPC_TEAM_GM_DEL_TEAM_USER  = "OnGMDelTeamUser"       // 后台删除队员
	RPC_MATCH_CHECK_APPLY_TEAM = "OnMatchCheckApplyTeam" // 检测报名中的队伍
	RPC_MATCH_CHECK_APPLY_USER = "OnMatchCheckApplyUser" // 检测报名中的队伍的玩家
	RPC_SET_APPLY_CLOSE        = "OnSetApplyClose"       // 后台设置报名是否关闭
	RPC_GET_ONLINE_SUM         = "OnGetOnlineSum"        // 获取在线人数
	RPC_DEL_NOTICE_TICK        = "OnDelNoticeTick"       // 删除公告tick
	RPC_SET_PORTRAYAL_ODDS     = "OnSetPortrayalOdds"    // 设置天选概率
	RPC_SET_OFFLINE_MSG_PARAMS = "OnSetOfflineMsgParams" // 设置离线通知参数

	// 通知
	RPC_SEND_NOTIFY = "OnSendNotify" // 发送通知
)
