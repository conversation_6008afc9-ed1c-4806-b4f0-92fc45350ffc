package g

import (
	"sort"
	"strings"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/enums/eeffect"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/sasha-s/go-deadlock"
)

// 装备信息
type EquipInfo struct {
	json map[string]interface{}

	Attrs          [][]int32         `json:"attrs" bson:"attrs"`
	LastAttrs      [][]int32         `json:"lastAttrs" bson:"last_attrs"` // 上一次重铸属性
	effects        []*EquipEffectObj // 效果
	skillIntensify []int32           // 技能强化 0.技能id 1.技能等级

	UID             string `json:"uid" bson:"uid"`
	ID              int32  `json:"id" bson:"id"`
	RecastCount     int32  `json:"recastCount" bson:"recast_count"` // 重铸次数
	ViceId          int32  `json:"viceId" bson:"vice_id"`           // 融炼id 老版本兼容使用
	attack          int32
	hp              int32
	exclusivePawnId int32 // 专属士兵

	NextForgeFree bool `json:"nextForgeFree" bson:"next_forge_free"` //下次是否免费重铸

	effectsLock *deadlock.RWMutex //效果锁
}

func NewEquip() *EquipInfo {
	return &EquipInfo{
		Attrs:       [][]int32{},
		LastAttrs:   [][]int32{},
		RecastCount: 0,
		effectsLock: new(deadlock.RWMutex),
	}
}

func NewEquipByJson(data map[string]interface{}) *EquipInfo {
	equip := NewEquip().SetIDOrUID(ut.String(data["uid"]), ut.Int32(data["id"]))
	if attrs := ut.Int32Arrays(data["attrs"]); len(attrs) > 0 {
		for _, m := range attrs {
			attr := []int32{}
			for _, val := range m {
				attr = append(attr, ut.Int32(val))
			}
			if attr[0] == 1 {
				continue // 去掉副属性 兼容4.0前版本
			}
			equip.Attrs = append(equip.Attrs, attr)
		}
	}
	equip.UpdateAttr()
	return equip
}

func NewEquipByPb(data *pb.EquipInfo) *EquipInfo {
	equip := NewEquip().SetIDOrUID(data.Uid, data.Id)
	if data.Attrs != nil {
		for _, m := range data.Attrs {
			attr := []int32{}
			for _, val := range m.Attr {
				attr = append(attr, ut.Int32(val))
			}
			if attr[0] == 1 {
				continue // 去掉副属性 兼容4.0前版本
			}
			equip.Attrs = append(equip.Attrs, attr)
		}
	}
	equip.UpdateAttr()
	return equip
}

type EquipStrip struct {
	Id    int32     `bson:"id"`
	Attrs [][]int32 `bson:"attrs"`
}

func (this *EquipInfo) ToStrip() *EquipStrip {
	return &EquipStrip{
		Id:    this.ID,
		Attrs: CloneAttrs(this.Attrs),
	}
}

func (this *EquipInfo) ToJson() map[string]interface{} {
	return map[string]interface{}{
		"uid":   this.UID,
		"attrs": CloneAttrs(this.Attrs),
	}
}

func (this *EquipInfo) ToPb() *pb.EquipInfo {
	return &pb.EquipInfo{
		Uid:           this.UID,
		RecastCount:   int32(this.RecastCount),
		NextForgeFree: this.NextForgeFree,
		Attrs:         CloneEffectAttrsToPb(this.Attrs),
		LastAttrs:     CloneEffectAttrsToPb(this.LastAttrs),
	}
}

func (this *EquipInfo) ToBasePb() *pb.EquipInfo {
	return &pb.EquipInfo{
		Id:    int32(this.ID),
		Attrs: CloneEffectAttrsToPb(this.Attrs),
	}
}

func (this *EquipInfo) GetJson() map[string]interface{} { return this.json }
func (this *EquipInfo) GetAttack() int32                { return this.attack }
func (this *EquipInfo) GetHP() int32                    { return this.hp }
func (this *EquipInfo) GetSkillIntensify() []int32      { return this.skillIntensify }
func (this *EquipInfo) GetExclusivePawnId() int32       { return this.exclusivePawnId }
func (this *EquipInfo) CloneAttrs() [][]int32           { return CloneAttrs(this.Attrs) }
func (this *EquipInfo) IsExclusive() bool               { return this.exclusivePawnId != 0 }

func (this *EquipInfo) GetEquipEffects() []*EquipEffectObj {
	rst := []*EquipEffectObj{}
	if this.effectsLock == nil {
		this.effectsLock = new(deadlock.RWMutex)
	}
	this.effectsLock.RLock()
	defer this.effectsLock.RUnlock()
	for _, effect := range this.effects {
		if effect != nil {
			rst = append(rst, effect)
		}
	}
	return rst
}

// 检测是否专属士兵的装备
func (this *EquipInfo) CheckExclusivePawn(id int32) bool {
	return this.exclusivePawnId == 0 || this.exclusivePawnId == id
}

func (this *EquipInfo) GetEquipEffectByType(tp int32) *EquipEffectObj {
	if effect := array.Find(this.effects, func(m *EquipEffectObj) bool { return m.Type == tp }); effect != nil {
		return effect
	}
	return nil
}

func (this *EquipInfo) SetIDOrUID(uid string, id int32) *EquipInfo {
	if uid != "" {
		this.SetUID(uid)
	} else if id > 0 {
		this.SetID(id)
	} else {
		// log.Error("SetIDOrUID no value!")
	}
	return this
}

// 设置id
func (this *EquipInfo) SetID(id int32) *EquipInfo {
	if id > 10000 { // 表示融炼装备 兼容4.0版本之前的内容
		this.ViceId = id % 10000
		id /= 10000
	}
	this.ID = id
	this.UID = ut.Itoa(id) + "_" + ut.ID()
	this.InitBaseJson()
	return this
}

// 设置uid
func (this *EquipInfo) SetUID(uid string) *EquipInfo {
	this.UID = uid
	this.ID = int32(ut.Atoi(strings.Split(uid, "_")[0]))
	this.InitBaseJson()
	return this
}

// 获取槽位等级
func (this *EquipInfo) GetSlotLv() int32 {
	uidArr := strings.Split(this.UID, "_")
	if len(uidArr) < 2 {
		return 0
	}
	return ut.Int32(uidArr[1])
}

func (this *EquipInfo) Clean() {
	this.UID = ""
	this.ID = 0
	this.SetAttr([][]int32{})
}

func (this *EquipInfo) InitBaseJson() {
	this.json = config.GetJsonData("equipBase", this.ID)
	if skill_intensify := this.json["skill_intensify"]; skill_intensify != nil {
		this.skillIntensify = ut.StringToInt32s(ut.String(skill_intensify), ",")
	} else {
		this.skillIntensify = nil
	}
	this.exclusivePawnId = ut.Int32(this.json["exclusive_pawn"])
}

// 设置属性
func (this *EquipInfo) SetAttr(attrs [][]int32) {
	this.Attrs = CloneAttrs(attrs)
	this.UpdateAttr()
}

// 刷新属性
func (this *EquipInfo) UpdateAttr() {
	if this.effectsLock == nil {
		this.effectsLock = new(deadlock.RWMutex)
	}
	this.attack = 0
	this.hp = 0
	this.effectsLock.Lock()
	defer this.effectsLock.Unlock()
	this.effects = []*EquipEffectObj{}
	for _, arr := range this.Attrs {
		fieldType, tp, value := arr[0], arr[1], arr[2]
		if fieldType == 0 { // 属性
			if tp == 1 {
				this.hp += value
			} else if tp == 2 {
				this.attack += value
			}
		} else if fieldType == 2 { // 效果
			if len(arr) >= 4 && arr[3] > 0 {
				this.effects = append(this.effects, NewEquipEffectObj(tp, float64(value), arr[3]))
			} else {
				this.effects = append(this.effects, NewEquipEffectObj(tp, float64(value), 0))
			}
		}
	}
	if len(this.effects) == 0 {
		return
	}
	// 效果排序
	sort.Slice(this.effects, func(i, j int) bool {
		as, bs := 0, 0
		if aj := config.GetJsonData("equipEffect", this.effects[i].Type); aj != nil {
			as = ut.Int(aj["sort"])
		}
		if bj := config.GetJsonData("equipEffect", this.effects[j].Type); bj != nil {
			bs = ut.Int(bj["sort"])
		}
		return as < bs
	})
}

// 随机属性
// attrs = [{a,b,c}]
// a = 0.主属性 1.副属性 2.效果
// b = (a == 0 || a == 1)
//
//	  1.血量 2.攻击
//	(a == 2)
//	  效果类型
//
// c = 对应值
func (this *EquipInfo) RandomAttr(lockEffect int32, effectList []int32) {
	// 先记录上一次的属性 并把融炼的属性锁起来
	lockAttrs := this.RecordAttrs()
	// 随机效果数量
	effectCount := ut.Int(this.json["effect_count"])
	// 锁定属性 只有专属才可以锁
	if lockEffect > 0 && effectCount >= 2 {
		if attr := array.Find(this.LastAttrs, func(m []int32) bool { return len(m) >= 2 && m[0] == 2 && m[1] == lockEffect }); attr != nil {
			effectCount -= 1 // 如果有锁定效果 这里就要少一个
			lockAttrs = append(lockAttrs, attr)
		}
	}
	this.Attrs = [][]int32{}
	// 主属性 0
	if hp := ut.String(this.json["hp"]); hp != "" { // 1
		arr := ut.StringToInts(hp, ",")
		this.Attrs = append(this.Attrs, []int32{0, 1, int32(ut.Random(arr[0], arr[1]))})
	}
	if attack := ut.String(this.json["attack"]); attack != "" { // 2
		arr := ut.StringToInts(attack, ",")
		this.Attrs = append(this.Attrs, []int32{0, 2, int32(ut.Random(arr[0], arr[1]))})
	}
	// 效果
	effectIds := ut.StringToInt32s(ut.String(this.json["effect"]), "|")
	if this.IsExclusive() && effectList != nil && len(effectList) > 0 {
		effectIds = array.Clone(effectList) // 专属装备从服务器获取
	}
	// 从效果列表中删除锁定属性
	for i := len(effectIds) - 1; i >= 0; i-- {
		if eType := effectIds[i]; array.Some(lockAttrs, func(attr []int32) bool { return len(attr) >= 2 && attr[0] == 2 && attr[1] == eType }) {
			effectIds = append(effectIds[:i], effectIds[i+1:]...)
		}
	}
	// 开随
	ids := []int32{}
	for i := 0; i < effectCount && i < len(effectIds); i++ {
		index := ut.Random(0, len(effectIds)-1)
		ids = append(ids, effectIds[index])
		effectIds = append(effectIds[:index], effectIds[index+1:]...)
	}
	// 包装属性
	attrs := [][]int32{}
	for _, id := range ids {
		if id == eeffect.MINGGUANG_ARMOR || id == eeffect.BAIBI_SWORD { // 明光铠，百辟刀
			value := int32(ut.If(ut.Chance(8), 1, 0))
			if attr := array.Find(this.LastAttrs, func(arr []int32) bool { return arr[0] == 2 }); len(attr) >= 3 && attr[1] == id {
				value += attr[2]
			}
			attrs = append(attrs, []int32{2, id, value})
		} else if json := config.GetJsonData("equipEffect", id); json != nil {
			v := []int32{2, id}
			if arr := ut.StringToInt32s(ut.String(json["value"]), ","); len(arr) == 2 {
				v = append(v, ut.RandomInt32(arr[0], arr[1]))
			} else {
				v = append(v, 0) // 这里就算没有 也要push一个0因为 后面可能有概率
			}
			if arr := ut.StringToInt32s(ut.String(json["odds"]), ","); len(arr) == 2 { // 效果概率
				v = append(v, ut.RandomInt32(arr[0], arr[1]))
			}
			attrs = append(attrs, v)
		}
	}
	// 如果是时光旗 只要对应的主属性
	if this.ID == 6025 && len(attrs) > 0 && len(attrs[0]) > 0 {
		if attrs[0][1] == eeffect.TODAY_ADD_HP { // 加攻生命 删除攻击
			this.Attrs = array.RemoveItem(this.Attrs, func(arr []int32) bool { return arr[0] == 0 && arr[1] == 2 })
		} else if attrs[0][1] == eeffect.TODAY_ADD_ATTACK { // 加攻击力 删除生命
			this.Attrs = array.RemoveItem(this.Attrs, func(arr []int32) bool { return arr[0] == 0 && arr[1] == 1 })
		}
	}
	this.Attrs = append(this.Attrs, attrs...)
	this.Attrs = append(this.Attrs, lockAttrs...) // 把锁定的属性重新加入
	this.UpdateAttr()
}

// 记录属性
func (this *EquipInfo) RecordAttrs() [][]int32 {
	lockAttrs := [][]int32{}
	this.LastAttrs = [][]int32{}
	for i, l := 0, len(this.Attrs); i < l; i++ {
		attr := this.Attrs[i]
		if len(attr) < 5 { // 只保留基础属性 不保留融炼数据
			this.LastAttrs = append(this.LastAttrs, array.Clone(attr))
		} else {
			lockAttrs = append(lockAttrs, array.Clone(attr)) // 融炼属性
		}
	}
	return lockAttrs
}

// 还原属性
func (this *EquipInfo) RestoreAttr() {
	if len(this.LastAttrs) == 0 {
		return
	}
	smeltAttrs := this.GetSmeltAttrs()
	this.Attrs = CloneAttrs(this.LastAttrs)
	this.Attrs = append(this.Attrs, smeltAttrs...)
	this.LastAttrs = [][]int32{}
	this.UpdateAttr()
}

// 还原融炼属性
func (this *EquipInfo) RestoreEquipSmelt() {
	for i := len(this.Attrs) - 1; i >= 0; i-- {
		attr := this.Attrs[i]
		if len(attr) >= 5 {
			this.Attrs = append(this.Attrs[:i], this.Attrs[i+1:]...)
		}
	}
}

// 是否满属性
func (this *EquipInfo) IsMaxAttr() bool {
	if len(this.Attrs) < 2 || this.IsSmelt() || this.ID == 6025 { // 时光旗排除
		return false
	}
	for _, arr := range this.Attrs {
		fieldType, tp, value := arr[0], arr[1], arr[2]
		if len(arr) >= 5 {
			continue // 融炼数据不算
		} else if fieldType == 0 {
			if tp == 1 {
				if hp := ut.String(this.json["hp"]); hp != "" && ut.StringToInt32s(hp, ",")[1] != value { // 1
					return false
				}
			} else if tp == 2 {
				if attack := ut.String(this.json["attack"]); attack != "" && ut.StringToInt32s(attack, ",")[1] != value { // 1
					return false
				}
			}
		} else if fieldType == 2 {
			if json := config.GetJsonData("equipEffect", tp); json != nil {
				if arr1 := ut.StringToInt32s(ut.String(json["value"]), ","); len(arr1) == 2 {
					if arr1[1] != value {
						return false
					}
				}
				if arr1 := ut.StringToInt32s(ut.String(json["odds"]), ","); len(arr1) == 2 { // 效果概率
					if len(arr) != 4 || arr[3] != arr1[1] {
						return false
					}
				}
			}
		}
	}
	return true
}

// 是否有一样的属性效果
func (this *EquipInfo) HasAttrEffect(attrs [][]int32) bool {
	for _, arr := range this.Attrs {
		if arr[0] != 2 || len(arr) >= 5 {
			continue // 如果是融炼属性可以忽略掉
		} else if array.Some(attrs, func(m []int32) bool { return m[0] == 2 && m[1] == arr[1] }) {
			return true
		}
	}
	return false
}

// 是否熔炼过相同的属性
func (this *EquipInfo) HasSmeltEquipSameEffect(viceId int32, attrs [][]int32) bool {
	// 主装备中找到被融炼的属性
	smeltAttrs := array.Filter(this.Attrs, func(m []int32, _ int) bool { return len(m) >= 5 && m[4] == viceId })
	if len(smeltAttrs) == 0 || len(smeltAttrs) < len(attrs) {
		return false
	}
	// 比较属性 每一条都相同则返回true
	for i, attr := range attrs {
		smeltArrt := smeltAttrs[i]
		if len(smeltArrt) < len(attr) {
			return false
		}
		for j, v := range attr {
			if j == 2 && attr[0] == 0 { // 如果是基础属性 则熔炼的值减半
				v = ut.RoundInt32(float64(v) / 2)
			}
			if smeltArrt[j] != v {
				return false
			}
		}
	}
	return true
}

// 转换为被熔炼的属性
func (this *EquipInfo) ToViceAttrs() [][]int32 {
	// 属性attr := [字段类型, 效果类型, 值, odds, 被熔炼的装备id]
	return array.Map(this.Attrs, func(m []int32, _ int) []int32 {
		ret := make([]int32, 5)
		for i := 0; i < 5; i++ {
			// 熔炼属性数组 下标4固定为被熔炼装备id
			if len(m) > i {
				// 属性在当前下标有值则赋值
				if i == 2 && (m[0] == 0 || m[0] == 1) {
					// 基础属性则熔炼的值减半
					ret[i] = ut.RoundInt32(float64(m[i]) / 2)
				} else {
					ret[i] = m[i]
				}
			} else if i == 4 {
				ret[i] = this.ID
			} else {
				ret[i] = 0
			}
		}
		return ret
	})
}

// 是否为融炼装备
func (this *EquipInfo) IsSmelt() bool {
	return array.Some(this.Attrs, func(m []int32) bool { return len(m) >= 5 })
}

// 获取已融炼的属性
func (this *EquipInfo) GetSmeltAttrs() [][]int32 {
	return array.Filter(this.Attrs, func(attr []int32, _ int) bool { return len(attr) >= 5 })
}

// 获取已熔炼数量
func (this *EquipInfo) GetSmeltEquipsNum() int {
	smeltEquipIdMap := map[int32]bool{}
	for _, attr := range this.Attrs {
		if len(attr) >= 5 {
			smeltEquipIdMap[attr[4]] = true
		}
	}
	return len(smeltEquipIdMap)
}

// 获取消耗资源
func (this *EquipInfo) GetCosts() []*TypeObj {
	if this.json == nil {
		return []*TypeObj{}
	}
	cost := StringToTypeObjs(this.json["forge_cost"])
	for _, v := range cost {
		if this.RecastCount > 0 {
			v.Count += this.RecastCount * v.Count
		}
	}
	return cost
}
