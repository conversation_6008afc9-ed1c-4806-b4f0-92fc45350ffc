package chat

import (
	"time"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

const SORT_MIN_TIME = ut.TIME_MINUTE * 10

const COMMENTS_COUNT_IN_CACHE = 30 // 每个图鉴加载到内存的评论数量

type GalleryModel struct {
	GalleryMap             deadlock.Map // 图鉴信息map k=>id v=>图鉴信息
	GalleryCommentMap      deadlock.Map // 图鉴评论信息map k=>uid v=>图鉴评论信息
	galleryInfoDbChan      chan int32   // 图鉴信息db队列
	galleryCommentDbChan   chan string  // 图鉴评论db队列
	lastUpdateGalleryTime  int64        // 上次保存图鉴时间
	lastUpdateCommentTime  int64        // 上次保存评论时间
	lastRefreshCommentTime int64        // 上次更新内存中的评论时间
}

var galleryModel = &GalleryModel{
	GalleryMap:           deadlock.Map{},
	GalleryCommentMap:    deadlock.Map{},
	galleryInfoDbChan:    make(chan int32, 100),
	galleryCommentDbChan: make(chan string, 1000),
}

// 图鉴模块初始化
func InitGalleryModel() {
	startTime := time.Now().UnixMilli()
	// 获取所有图鉴信息
	gallerys, err := galleryDb.FindAllGalleryInfos()
	if err != nil {
		log.Error("InitGalleryModel FindAllGalleryInfos err: %v", err)
		return
	}
	galleryModel.lastRefreshCommentTime = time.Now().UnixMilli()
	wrongGidArr := []*GalleryInfo{}
	for _, v := range gallerys {
		gid := getGalleryId(v.Type, v.Id)
		if v.Gid != gid {
			// 数据库gid不对 错误数据需要兼容
			wrongGidArr = append(wrongGidArr, v)
			continue
		}
		galleryModel.GalleryMap.Store(v.Gid, v)
		v.RefreshCacheComments()
		if v.commentCache == nil {
			v.commentCache = &GalleryCommentCache{cache: ut.NewLFUCache(COMMENTS_COUNT_IN_CACHE)}
		}
	}
	// 3.0版本错误gid兼容
	for _, v := range wrongGidArr {
		gid := getGalleryId(v.Type, v.Id)
		data, ok := galleryModel.GalleryMap.Load(gid)
		if ok {
			galleryInfo := data.(*GalleryInfo)
			galleryInfo.TotalStars += v.TotalStars
			galleryInfo.Star = float64(galleryInfo.TotalStars) / float64(galleryInfo.commentCount)
			galleryModel.galleryInfoDbChan <- galleryInfo.Gid
		}
		// 数据库删除错误数据
		galleryDb.DelGallery(v.Gid)
	}

	costTime := time.Now().UnixMilli() - startTime
	log.Info("InitGalleryModel finish cost time: %v", costTime)
}

// 保存评论到数据库
func (this *GalleryModel) updateComment(comment *GalleryComment, gid int32) {
	galleryModel.setCommentUpdateFlag(comment)
	galleryModel.galleryInfoDbChan <- gid
}

// 图鉴信息
type GalleryInfo struct {
	Gid               int32   `bson:"gid"`              // 图鉴id
	Id                int32   `bson:"id"`               // 配置id
	Type              int32   `bson:"type"`             // 类型
	Star              float64 `bson:"star"`             // 平均星数
	TotalStars        int32   `bson:"total_stars"`      // 总星数
	TotalStarsFixFlag bool    `bson:"total_stars_flag"` // 总星数修复flag

	commentList  []*GalleryComment    // 评论列表
	commentCount int32                // 评论数量
	needSort     bool                 // 需要排序标记
	lastSortTime int64                // 上次排序时间
	commentCache *GalleryCommentCache // 图鉴的LFU评论缓存
	mutex        deadlock.RWMutex
}

// 图鉴评论
type GalleryComment struct {
	Uid      string `bson:"uid"`
	Version  string `bson:"version"`  // 评价时的版本
	UserId   string `bson:"user_id"`  // 玩家uid
	Nickname string `bson:"nickname"` // 玩家昵称
	HeadIcon string `bson:"headicon"` // 玩家头像
	Content  string `bson:"content"`  // 评论

	LikeMap    map[string]bool `bson:"like_map"`    // 点赞的玩家uid map
	DislikeMap map[string]bool `bson:"dislike_map"` // 不喜欢的玩家uid map
	JokerMap   map[string]bool `bson:"joker_map"`   // 评论小丑的玩家uid map

	mutex deadlock.RWMutex

	Time        int64 `bson:"time"`         // 时间
	Id          int32 `bson:"id"`           // 配置id
	Type        int32 `bson:"type"`         // 类型
	Star        int32 `bson:"star"`         // 星数
	RankScore   int32 `bson:"rank_core"`    // 排位分
	ModifyCount int32 `bson:"modify_count"` // 修改次数
	Flag        bool  `bson:"flag"`         // 点赞标记
}

// 评论缓存
type GalleryCommentCache struct {
	cache *ut.LFUCache
}

func (this *GalleryInfo) ToPb() *pb.GalleryInfo {
	return &pb.GalleryInfo{
		Id:           int32(this.Id),
		Type:         int32(this.Type),
		Star:         int32(this.Star * 2),
		CommentCount: int32(this.commentCount),
	}
}

// 刷新内存中的评论
func (this *GalleryInfo) RefreshCacheComments() {
	comments, err := galleryCommentDb.FindAllGalleryComments(this.Type, this.Id)
	if err != nil {
		log.Error("InitGalleryModel FindAllGalleryComments type: %v, id: %v, err: %v", this.Type, this.Id, err)
		return
	}
	commentCount := int32(len(comments))
	this.commentCount = commentCount
	// 修复图鉴总星数
	this.totalStarsFix(comments)
	// 总数小于30则加载全部
	if commentCount <= COMMENTS_COUNT_IN_CACHE {
		this.commentList = comments
		return
	}
	// 否则只加载最高点赞 和上一次加载时被点赞的 剩余的随机
	list := []*GalleryComment{}
	var topHotIndex, topHotLikeCount int32 = -1, 0 // 最多点赞的评论下标
	for i := commentCount - 1; i >= 0; i-- {
		c := comments[i]
		likeCount := int32(len(c.LikeMap))
		if likeCount > topHotLikeCount {
			// 更新最多点赞
			topHotIndex = i
			topHotLikeCount = likeCount
			continue
		}
		isFull := (topHotIndex == -1 && len(list) >= COMMENTS_COUNT_IN_CACHE) || (topHotIndex >= 0 && len(list) >= COMMENTS_COUNT_IN_CACHE-1)
		if c.Flag && !isFull {
			// 上次在加载被点过赞
			list = append(list, c)
			comments = append(comments[:i], comments[i+1:]...)
			c.Flag = false
			if topHotIndex > 0 {
				topHotIndex--
			}
		}
	}
	if topHotIndex >= 0 {
		// 添加最热评论
		hotComment := comments[topHotIndex]
		list = append(list, hotComment)
		comments = append(comments[:topHotIndex], comments[topHotIndex+1:]...)
	}
	if len(list) < COMMENTS_COUNT_IN_CACHE {
		// 剩余的评论打乱顺序
		ut.ShuffleSlice(comments)
		tail := ut.Min(COMMENTS_COUNT_IN_CACHE-len(list), len(comments))
		list = append(list, comments[0:tail]...)
	}
	this.commentList = list
}

// 获取评论
func (this *GalleryInfo) GetComments(userId string) []*pb.GalleryCommentInfo {
	arr := []*pb.GalleryCommentInfo{}
	var hasMyComment bool
	for _, v := range this.commentList {
		arr = append(arr, v.ToPb(userId))
		if v.UserId == userId {
			hasMyComment = true
		}
	}
	if hasMyComment {
		// 内存中有该玩家的评论 无需再从缓存获取
		return arr
	}
	// 从缓存获取该玩家的评论
	myComment := this.GetCommentByCache(userId)
	if myComment != nil {
		arr = append(arr, myComment.ToPb(userId))
	}
	return arr
}

// 获取缓存中的图鉴评论
func (this *GalleryInfo) GetCommentByCache(userId string) *GalleryComment {
	data, ok := this.commentCache.cache.Get(userId)
	if !ok {
		// 没有则从数据库查询该玩家的评论
		data, err := galleryCommentDb.FindUserGalleryComment(this.Type, this.Id, userId)
		if err == nil {
			comment := &data
			// 添加进缓存
			this.AddCommentToCache(comment)
			return comment
		}
	}
	comment, ok := data.(*GalleryComment)
	if ok {
		return comment
	}
	return nil
}

// 图鉴评论添加到缓存
func (this *GalleryInfo) AddCommentToCache(comment *GalleryComment) {
	this.commentCache.cache.Set(comment.UserId, comment)
}

// 添加评论
func (this *GalleryInfo) AddComment(uid, nickName, headIcon string, star, rankScore int32, content string, version string) *GalleryComment {
	comment := this.GetCommentByCache(uid)
	if comment != nil {
		// 之前评论过
		comment.ModifyCount++
		comment.Content = content
		comment.Version = version
		comment.Time = time.Now().UnixMilli()
		// 更新总星数
		this.TotalStars += star - comment.Star
		this.Star = float64(this.TotalStars) / float64(this.commentCount)
		comment.Star = star
		galleryModel.updateComment(comment, this.Gid)
	} else {
		// 新评论
		comment = &GalleryComment{
			Uid:        ut.ID(),
			Id:         this.Id,
			Type:       this.Type,
			Star:       star,
			Version:    version,
			UserId:     uid,
			Nickname:   nickName,
			HeadIcon:   headIcon,
			Content:    content,
			Time:       time.Now().UnixMilli(),
			RankScore:  rankScore,
			LikeMap:    map[string]bool{},
			DislikeMap: map[string]bool{},
			JokerMap:   map[string]bool{},
		}
		this.TotalStars += star
		this.commentCount++
		this.Star = float64(this.TotalStars) / float64(this.commentCount)
		if len(this.commentList) >= COMMENTS_COUNT_IN_CACHE {
			// 内存中的评论数量超过上限 则放到缓存并保存到数据库
			this.AddCommentToCache(comment)
			galleryCommentDb.UpdateGalleryComments([]*GalleryComment{comment})
			galleryModel.galleryInfoDbChan <- this.Gid
		} else {
			// 没超过则直接放到内存中
			this.commentList = append(this.commentList, comment)
			galleryModel.updateComment(comment, this.Gid)
		}
	}
	return comment
}

// 评论点赞/取消
func (this *GalleryInfo) CommentPraise(comment *GalleryComment, userId string, praiseType int, dbForce bool) {
	comment.mutex.Lock()
	// likeCount := len(comment.LikeMap)
	hasLike := comment.LikeMap[userId]
	// 先删除
	delete(comment.LikeMap, userId)
	delete(comment.DislikeMap, userId)
	delete(comment.JokerMap, userId)
	// 在添加
	switch praiseType {
	case 0: // 点赞
		comment.LikeMap[userId] = true
		if !hasLike {
			// 点赞则添加标记
			comment.Flag = true
		}
	case 1: // 不喜欢
		comment.DislikeMap[userId] = true
	case 2: // 小丑
		comment.JokerMap[userId] = true
	}
	comment.mutex.Unlock()
	if dbForce {
		// 直接保存
		galleryCommentDb.UpdateGalleryComments([]*GalleryComment{comment})
	} else {
		// 标记更新
		galleryModel.setCommentUpdateFlag(comment)
	}
	// 点赞数变动且超过排序时间间隔 评论列表排序
	// if praiseType == 0 && len(comment.LikeMap) != likeCount && time.Now().UnixMilli()-this.lastSortTime >= SORT_MIN_TIME {
	// 	this.CommentsSort()
	// }
}

// 获取评论信息
func (this *GalleryInfo) GetCommentByUid(uid string) *GalleryComment {
	this.mutex.RLock()
	defer this.mutex.RUnlock()
	for _, v := range this.commentList {
		if v.Uid == uid {
			return v
		}
	}
	return nil
}

// 评论排序
func (this *GalleryInfo) CommentsSort() {
	// if len(this.commentList) > 1 {
	// 	this.mutex.Lock()
	// 	sort.Slice(this.commentList, func(i, j int) bool {
	// 		// 按点赞数排序
	// 		return len(this.commentList[i].LikeMap) > len(this.commentList[j].LikeMap)
	// 	})
	// 	this.mutex.Unlock()
	// }
}

// 总星数修复
func (this *GalleryInfo) totalStarsFix(comments []*GalleryComment) {
	if this.TotalStarsFixFlag {
		return
	}
	this.TotalStarsFixFlag = true
	this.TotalStars = 0
	for _, v := range comments {
		this.TotalStars += v.Star
	}
	this.Star = float64(this.TotalStars) / float64(len(comments))
	galleryModel.galleryInfoDbChan <- this.Gid
}

// 获取所有图鉴信息
func (this *GalleryModel) GetAllGalleryInfos() []*pb.GalleryInfo {
	arr := []*pb.GalleryInfo{}
	this.GalleryMap.Range(func(key, value any) bool {
		galleryInfo := value.(*GalleryInfo)
		arr = append(arr, galleryInfo.ToPb())
		return true
	})
	return arr
}

// 评论更新标记
func (this *GalleryModel) setCommentUpdateFlag(comment *GalleryComment) {
	this.galleryCommentDbChan <- comment.Uid
	this.GalleryCommentMap.Store(comment.Uid, comment)
}

// 刷新图鉴db
func (this *GalleryModel) UpdateGalleryDb(close bool) {
	sum := len(this.galleryInfoDbChan)
	if sum == 0 || this.lastUpdateGalleryTime > 0 {
		return
	}
	this.lastUpdateGalleryTime = time.Now().UnixMilli()
	datas := []*GalleryInfo{}
	idMap := map[int32]bool{}
	for len(this.galleryInfoDbChan) > 0 {
		id := <-this.galleryInfoDbChan
		if idMap[id] {
			continue
		}
		idMap[id] = true
	}
	for id := range idMap {
		if galleryInfo, ok := this.GalleryMap.Load(id); ok {
			datas = append(datas, galleryInfo.(*GalleryInfo))
		}
	}
	count, surplus := len(datas), len(this.galleryInfoDbChan)
	if close || count > 5 {
		log.Info("===== UpdateGalleryDb sum=" + ut.Itoa(sum) + ", count=" + ut.Itoa(count) + ", surplus=" + ut.Itoa(surplus))
	}
	galleryDb.UpdateGalleryInfos(datas)
	dt := time.Now().UnixMilli() - this.lastUpdateGalleryTime
	this.lastUpdateGalleryTime = 0
	if close || dt >= 1000 {
		log.Info("===== UpdateGalleryDb time=" + ut.Itoa(dt) + "ms, count=" + ut.Itoa(count) + ", surplus=" + ut.Itoa(surplus))
	}
	if close && surplus > 0 {
		this.UpdateGalleryDb(close)
	}
}

// 刷新图鉴评论db
func (this *GalleryModel) UpdateGalleryCommentDb(close bool) {
	sum := len(this.galleryCommentDbChan)
	if sum == 0 || this.lastUpdateCommentTime > 0 {
		return
	}
	this.lastUpdateCommentTime = time.Now().UnixMilli()
	datas := []*GalleryComment{}
	uidMap := map[string]bool{}
	for len(this.galleryCommentDbChan) > 0 {
		id := <-this.galleryCommentDbChan
		if uidMap[id] {
			continue
		}
		uidMap[id] = true
		if len(uidMap) >= 100 {
			log.Warning("UpdateGalleryCommentDb len over 100...")
			break
		}
	}
	for uid := range uidMap {
		if galleryInfo, ok := this.GalleryCommentMap.Load(uid); ok {
			datas = append(datas, galleryInfo.(*GalleryComment))
			this.GalleryCommentMap.Delete(uid)
		}
	}
	count, surplus := len(datas), len(this.galleryCommentDbChan)
	if close || count > 5 {
		log.Info("===== UpdateGalleryCommentDb sum=" + ut.Itoa(sum) + ", count=" + ut.Itoa(count) + ", surplus=" + ut.Itoa(surplus))
	}
	galleryCommentDb.UpdateGalleryComments(datas)
	dt := time.Now().UnixMilli() - this.lastUpdateCommentTime
	this.lastUpdateCommentTime = 0
	if close || dt >= 1000 {
		log.Info("===== UpdateGalleryCommentDb time=" + ut.Itoa(dt) + "ms, count=" + ut.Itoa(count) + ", surplus=" + ut.Itoa(surplus))
	}
	if close && surplus > 0 {
		this.UpdateGalleryCommentDb(close)
	}
}

// 每天凌晨4点更新内存中的评论
func (this *GalleryModel) RefreshCommentTick() {
	timeFour := ut.NowFourTime()
	if this.lastRefreshCommentTime < timeFour {
		this.lastRefreshCommentTime = timeFour
		log.Info("RefreshCommentTick start")
		this.GalleryMap.Range(func(key, value any) bool {
			galleryInfo := value.(*GalleryInfo)
			galleryInfo.RefreshCacheComments()
			return true
		})
		log.Info("RefreshCommentTick end")
	}
}

// 获取图鉴信息
func (this *GalleryModel) GetGalleryInfo(id, typeId int32) *GalleryInfo {
	if value, ok := this.GalleryMap.Load(getGalleryId(typeId, id)); ok {
		return value.(*GalleryInfo)
	}
	return nil
}

// 获取指定图鉴评论信息
func (this *GalleryModel) GetGalleryComments(id, typeId int32, userId string) []*pb.GalleryCommentInfo {
	arr := []*pb.GalleryCommentInfo{}
	value, ok := this.GalleryMap.Load(getGalleryId(typeId, id))
	if ok {
		galleryInfo := value.(*GalleryInfo)
		if galleryInfo.commentList == nil {
			return arr
		}
		for _, v := range galleryInfo.commentList {
			arr = append(arr, v.ToPb(userId))
		}
	}
	return arr
}

// 获取图鉴信息
func (this *GalleryModel) GetGalleryInfoOrAdd(id, typeId int32) *GalleryInfo {
	if info := this.GetGalleryInfo(id, typeId); info != nil {
		return info
	}
	// 新增图鉴信息
	return this.AddGalleryInfo(id, typeId)
}

// 点赞指定评论
func (this *GalleryModel) GalleryCommentPraise(userId string, id, typeId int32, praises map[string]int32) {
	if value, ok := this.GalleryMap.Load(getGalleryId(typeId, id)); ok {
		galleryInfo := value.(*GalleryInfo)
		list := []*GalleryComment{}
		galleryInfo.mutex.RLock()
		for _, v := range galleryInfo.commentList {
			if praises[v.Uid] != 0 {
				list = append(list, v)
			} else {
				comment := galleryInfo.GetCommentByCache(userId)
				if comment != nil {
					// 缓存中的评论点赞直接保存
					galleryInfo.CommentPraise(comment, userId, int(praises[comment.Uid])-2, true)
				}
			}
		}
		galleryInfo.mutex.RUnlock()
		for _, v := range list {
			galleryInfo.CommentPraise(v, userId, int(praises[v.Uid])-2, false)
		}
	}
}

// 新增图鉴信息
func (this *GalleryModel) AddGalleryInfo(id, typeId int32) *GalleryInfo {
	// 检测配置中是否存在
	var cfg map[string]interface{}
	switch typeId {
	case 1: // 政策
		cfg = config.GetJsonData("policy", id)
	case 2: // 士兵
		cfg = config.GetJsonData("pawnBase", id)
	case 3: // 装备
		cfg = config.GetJsonData("equipBase", id)
	case 4: // 画像
		cfg = config.GetJsonData("portrayalBase", id)
	}
	if cfg == nil {
		return nil
	}
	galleryInfo := &GalleryInfo{
		Gid:               getGalleryId(typeId, id),
		Id:                id,
		Type:              typeId,
		commentList:       []*GalleryComment{},
		TotalStarsFixFlag: true,
		commentCache:      &GalleryCommentCache{cache: ut.NewLFUCache(COMMENTS_COUNT_IN_CACHE)},
	}
	this.GalleryMap.Store(galleryInfo.Gid, galleryInfo)
	this.galleryInfoDbChan <- galleryInfo.Gid
	return galleryInfo
}

// 图鉴db tick
func RunGalleryDbTick() {
	go func() {
		tiker := time.NewTicker(time.Minute * 1)
		for isRunning {
			<-tiker.C
			galleryModel.UpdateGalleryDb(false)
			galleryModel.UpdateGalleryCommentDb(false)
			galleryModel.RefreshCommentTick()
		}
	}()
}

func (this *GalleryComment) ToPb(userId string) *pb.GalleryCommentInfo {
	rst := &pb.GalleryCommentInfo{
		Uid:         this.Uid,
		Id:          int32(this.Id),
		Type:        int32(this.Type),
		Star:        int32(this.Star),
		Version:     this.Version,
		UserId:      this.UserId,
		Nickname:    this.Nickname,
		HeadIcon:    this.HeadIcon,
		Content:     this.Content,
		ModifyCount: int32(this.ModifyCount),
		Time:        int64(this.Time),
		RankScore:   int32(this.RankScore),
		Praise:      []int32{int32(len(this.LikeMap)), int32(len(this.DislikeMap)), int32(len(this.JokerMap))},
		MePraise:    -1,
	}
	if this.LikeMap[userId] {
		rst.MePraise = 0
	} else if this.DislikeMap[userId] {
		rst.MePraise = 1
	} else if this.JokerMap[userId] {
		rst.MePraise = 2
	}
	return rst
}

func getGalleryId(typeId, id int32) int32 {
	prefix := ut.GetNumberLen(id) * 10 * typeId
	return prefix + id
}
