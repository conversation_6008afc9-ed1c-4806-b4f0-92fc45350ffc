package behavior

type IBaseWorker interface {
	OnInit(conf map[string]interface{})
	OnOpen()
	OnEnter()
	OnTick(dt int32) Status
	OnLeave(state Status)
	OnClean()
}

type BaseWorker struct{}

func (this *BaseWorker) OnInit(conf map[string]interface{}) {}
func (this *BaseWorker) OnOpen()                            {} // 第一次打开这个节点
func (this *BaseWorker) OnEnter()                           {}
func (this *BaseWorker) OnTick(dt int32) Status             { return ERROR }
func (this *BaseWorker) OnLeave(state Status)               {}
func (this *BaseWorker) OnClean()                           {} // 清理
