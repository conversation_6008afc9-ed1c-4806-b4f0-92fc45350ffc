syntax = "proto3";

package proto;

option go_package = "./pb";

// 服务器返回协议
message S2C_RESULT {
  bytes data    = 1;
  string error  = 2;
}

// 游客登录
message LOGIN_HD_GUESTLOGIN_C2S {
  string guestId    = 1; //游客id
  string distinctId = 2; //访客id
  string os         = 3; //操作系统
  string nickname   = 4; //昵称
  string inviteUid  = 5; //邀请的玩家uid
  string lang       = 6; //语言
  string platform   = 7; //平台
}
// 游客登录返回
message LOGIN_HD_GUESTLOGIN_S2C {
  string accountToken = 1;  //登录token
  string guestId  = 2; //游客id
}

// 微信登录
message LOGIN_HD_WXLOGIN_C2S {
  string code       = 1; //微信登录code
  string distinctId = 2; //访客id
  string os         = 3; //操作系统
  string nickname   = 4; //昵称
  string headicon   = 5; //头像
  string inviteUid  = 6; //邀请的玩家uid
  string lang       = 7; //语言
  string platform   = 8; //平台
}
// 微信登录返回
message LOGIN_HD_WXLOGIN_S2C {
  string accountToken = 1;  //登录token
}

// facebook登录
message LOGIN_HD_FACEBOOKLOGIN_C2S {
  string token      = 1; //登录token
  string distinctId = 2; //访客id
  string os         = 3; //操作系统
  string userId     = 4; //facebook id
  string inviteUid  = 5; //邀请的玩家uid
  string lang       = 6; //语言
  string platform   = 7; //平台
  string jwtToken   = 8; //受限登陆token
}
message LOGIN_HD_FACEBOOKLOGIN_S2C {
  string accountToken = 1;  //登录token
}

// apple登录
message LOGIN_HD_APPLELOGIN_C2S {
  string token      = 1; //登录token
  string distinctId = 2; //访客id
  string os         = 3; //操作系统
  string nickname   = 4; //昵称
  string userId     = 5; //apple openid
  string code       = 6; //登录code
  string inviteUid  = 7; //邀请的玩家uid
  string lang       = 8; //语言
  string platform   = 9; //平台
}
message LOGIN_HD_APPLELOGIN_S2C {
  string accountToken = 1;  //登录token
}

// 尝试登录
message LOBBY_HD_TRYLOGIN_C2S {
  string accountToken   = 1; //登录token
  string distinctId     = 2; //访客id
  string os             = 3; //操作系统
  string lang           = 4; //语言
  string platform       = 5; //平台
  string inviteUid      = 6; //邀请的玩家uid
  string appsflyerId    = 7; //af id
  string advertisingId  = 8; //ad id
  string version        = 9; //版本
}
// 尝试登录返回
message LOBBY_HD_TRYLOGIN_S2C {
  UserInfo user       = 1;  //玩家信息
  string accountToken = 2;  //登录token
  int32 banAccountType        = 3; //封禁类型
  int64 banAccountSurplusTime = 4; //封禁剩余时间
}

// facebook绑定
message LOBBY_HD_FACEBOOKBIND_C2S {
  string token      = 1; //登录token
  string userId     = 2; //facebook id
  string jwtToken   = 3; //受限登陆token
}
message LOBBY_HD_FACEBOOKBIND_S2C {
  string loginType = 1; //登录类型
  string nickname  = 2; //昵称
}

// apple绑定
message LOBBY_HD_APPLEBIND_C2S {
  string token      = 1; //登录token
  string nickname   = 2; //昵称
  string userId     = 3; //apple openid
  string code       = 4; //登录code
}
message LOBBY_HD_APPLEBIND_S2C {
  string loginType = 1; //登录类型
  string nickname  = 2; //昵称
}

// google登录
message LOGIN_HD_GOOGLELOGIN_C2S {
  string idToken    = 1; //登录idToken
  string distinctId = 2; //访客id
  string os         = 3; //操作系统
  string nickname   = 4; //昵称
  string headicon   = 5; //头像
  string inviteUid  = 7; //邀请的玩家uid
  string lang       = 8; //语言
  string platform   = 9; //平台
}
message LOGIN_HD_GOOGLELOGIN_S2C {
  string accountToken = 1;  //登录token
  string email    = 2;
}

// google绑定
message LOBBY_HD_GOOGLEBIND_C2S {
  string idToken    = 1; //登录idToken
  string nickname   = 2; //昵称
  string headicon   = 3; //头像
  string userId     = 4; //google openid
}
message LOBBY_HD_GOOGLEBIND_S2C {
  string loginType = 1; //登录类型
  string nickname  = 2; //昵称
}

// 上传推送消息token
message LOBBY_HD_UPLOADFCMTOKEN_C2S {
  string token = 1; //FcmToken
}
message LOBBY_HD_UPLOADFCMTOKEN_S2C {
}

// 离线推送设置
message LOBBY_HD_SETOFFLINENOTIFY_C2S {
  repeated int32 opt = 1; //推送设置
}
message LOBBY_HD_SETOFFLINENOTIFY_S2S {
}

// 账号注销
message LOBBY_HD_DELACCOUNT_C2S {
  string name     = 1; //姓名
  string email    = 2; //邮箱
  string identity = 3; //身份证  
}
message LOBBY_HD_DELACCOUNT_S2C {
  int32 surplusLogoutTime = 1; //剩余注销时间
}

// 撤回注销
message LOBBY_HD_REVOCATIONLOGOUT_C2S {
}
message LOBBY_HD_REVOCATIONLOGOUT_S2C {
}

// 获取服务器列表
message LOBBY_HD_GETSERVERS_C2S {
  int32 serverType = 1;
}
// 获取服务器列表返回
message LOBBY_HD_GETSERVERS_S2C {
  repeated ServerInfo servers = 1; 
}

// 选择服务器
message LOBBY_HD_SELECTGAMESERVER_C2S {
  int32 sid = 1; //服务器id
}
// 选择服务器返回
message LOBBY_HD_SELECTGAMESERVER_S2C {
  string uid    = 1; //玩家id
  int32 sid     = 2; //服务器id
}

// 重置选择的服务器
message LOBBY_HD_RESETSELECTSERVER_C2S {
}
// 重置选择的服务器返回
message LOBBY_HD_RESETSELECTSERVER_S2C {
  int32 playSid = 1;
  repeated int32 totalGameCount   = 2; //总局数
  repeated int32 totalNewbieCount = 3; //新手总局数
  int32 passNewbieIndex           = 4; //第X局通关了新手区
  int32 giveupCount               = 5; //放弃次数
}

// 获取动态士兵消耗
message LOBBY_HD_GETPAWNCOST_C2S {
}
message LOBBY_HD_GETPAWNCOST_S2C {
  map<int32, int32> costMap = 1; //士兵消耗map
}

// 同步配合设置
message LOBBY_HD_SYNCPREFERENCEDATA_C2S {
}
// 同步配合设置返回
message LOBBY_HD_SYNCPREFERENCEDATA_S2C {
}

// 检测敏感名字
message LOBBY_HD_CHECKSENSITIVENAME_C2S {
  string name = 1; //名字
}
// 检测敏感名字 返回
message LOBBY_HD_CHECKSENSITIVENAME_S2C {
}

// 检测实名
message LOBBY_HD_CHECKREALNAME_C2S {
  string name     = 1; //姓名
  string email    = 2; //邮箱
  string identity = 3; //身份证  
}
// 检测实名 返回
message LOBBY_HD_CHECKREALNAME_S2C {
}

// 同步新手引导标记
message LOBBY_HD_SYNCGUIDETAG_C2S {
  int32 id    = 1; //引导id
  string tag  = 2; //标记
}
// 同步新手引导标记返回
message LOBBY_HD_SYNCGUIDETAG_S2C {
}

// 获取用户的称号列表
message LOBBY_HD_GETUSERTITLES_C2S {
  string uid = 1; //玩家id
}
// 获取用户的称号列表返回
message LOBBY_HD_GETUSERTITLES_S2C {
  int32 title               = 1; //当前佩戴的称号
  repeated TitleInfo titles = 2; //称号列表
}

// 获取用户的个人简介
message LOBBY_HD_GETUSERPERSONALDESC_C2S {
  string uid = 1; //玩家id
}
// 获取用户的个人简介 返回
message LOBBY_HD_GETUSERPERSONALDESC_S2C {
  string personalDesc = 1; //个人简介
}

// 获取用户总局数
message LOBBY_HD_GETUSERTOTALGAMECOUNT_C2S {
  string uid = 1;
}
message LOBBY_HD_GETUSERTOTALGAMECOUNT_S2C {
  repeated int32 totalGameCount = 1;  //总局数
}

// 获取单条游戏对局记录
message LOBBY_HD_GETGAMERECORDONE_C2S {
  int32 sid = 1;
}
// 获取单条游戏对局记录 返回
message LOBBY_HD_GETGAMERECORDONE_S2C {
  GameRecordInfo data = 1;
}

// 获取游戏对局记录列表
message LOBBY_HD_GETGAMERECORDLIST_C2S {
  int32 page       = 1; //当前页数
  int32 serverType = 2; //区服类型
}
// 获取游戏对局记录列表 返回
message LOBBY_HD_GETGAMERECORDLIST_S2C {
  repeated GameRecordInfo datas = 1;
  int32 totalPage               = 2;//总页数
}

// 获取历史对局记录
message CHAT_HD_GETGAMEHISTORYLIST_C2S {
  int32 page       = 1; //当前页数
  int32 serverType = 2; //区服类型
}
message CHAT_HD_GETGAMEHISTORYLIST_S2C {
  repeated GameHistory datas    = 1;
  int32 totalPage               = 2;//总页数
}

// 获取点赞九万亩数量
message CHAT_HD_GETLIKEJWMCOUNT_C2S {
}
message CHAT_HD_GETLIKEJWMCOUNT_S2C {
  int64 sumLikeJwmCount = 1;
}

// 同步检测触发好评次数
message LOBBY_HD_SYNCCHECKPRAISECOUNT_C2S {
  int32 checkPraiseCount  = 1; //记录触发好评弹窗x值
  int32 mailId            = 2;
}
message LOBBY_HD_SYNCCHECKPRAISECOUNT_S2C {
}

// 修改昵称
message LOBBY_HD_MODIFYUSERNICKNAME_C2S {
  string nickname = 1; //昵称
}
message LOBBY_HD_MODIFYUSERNICKNAME_S2C {
  int32 modifyNameCount = 1; //已修改次数
  int32 gold            = 2; //金币数量
}

// 改变头像
message LOBBY_HD_CHANGEUSERHEADICON_C2S {
  string headIcon = 1; //头像
}
// 改变头像返回
message LOBBY_HD_CHANGEUSERHEADICON_S2C {
}

// 改变个人简介
message LOBBY_HD_CHANGEUSERPERSONALDESC_C2S {
  string personalDesc = 1; //个人简介
}
// 改变个人简介 返回
message LOBBY_HD_CHANGEUSERPERSONALDESC_S2C {
}

// 改变称号
message LOBBY_HD_CHANGEUSERTITLE_C2S {
  int32 id = 1; //称号id
}
// 改变称号返回
message LOBBY_HD_CHANGEUSERTITLE_S2C {
}

// 购买头像
message LOBBY_HD_BUYHEADICON_C2S {
  int32 id = 1; //头像id
}
// 购买头像返回
message LOBBY_HD_BUYHEADICON_S2C {
  repeated string unlockHeadIcons = 1; //解锁的头像列表
  int32 gold                      = 2; //金币
  int32 ingot                     = 3; //元宝
}

// 购买士兵皮肤
message LOBBY_HD_BUYPAWNSKIN_C2S {
  int32 id = 1; //皮肤id
}
// 购买士兵皮肤返回
message LOBBY_HD_BUYPAWNSKIN_S2C {
  repeated int32 unlockPawnSkinIds  = 1; //解锁的士兵皮肤列表
  int32 gold                        = 2; //金币
  int32 ingot                       = 3; //元宝
}

// 购买主城皮肤
message LOBBY_HD_BUYCITYSKIN_C2S {
  int32 id = 1; //皮肤id
}
// 购买主城皮肤返回
message LOBBY_HD_BUYCITYSKIN_S2C {
  repeated int32 unlockCitySkinIds  = 1; //解锁的主城皮肤列表
  int32 gold                        = 2; //金币
  int32 ingot                       = 3; //元宝
}

// 购买皮肤盲盒
message LOBBY_HD_BUYSKINBLINDBOX_C2S {
  int32 id    = 1; //盲盒id
}
message LOBBY_HD_BUYSKINBLINDBOX_S2C {
  int32 skinId                   = 1; //皮肤id
  repeated SkinItem skinItemList = 2; //皮肤物品列表
  int32 ingot                    = 3; //剩余元宝
}

// 赠送皮肤
message LOBBY_HD_SENDSKINGIFT_C2S {
  int32 id         = 1; //皮肤id
  string friendUid = 2; //好友uid
  int32 boxId      = 3; //礼盒id
}
message LOBBY_HD_SENDSKINGIFT_S2C {
  int32 ingot      = 1; //剩余元宝
  string uid       = 2; //赠送的皮肤uid
}

// 领取好友礼物
message LOBBY_HD_RECEIVEFRIENDGIFT_C2S {
  string uid       = 1; //礼物uid
  string friendUid = 2; //好友uid
}
message LOBBY_HD_RECEIVEFRIENDGIFT_S2C {
  UpdateOutPut rewards = 1; //资源更新
}

// 礼物赠送记录查询
message LOBBY_HD_GETFRIENDGIFTRECORDS_C2S {
  int32 type = 1; //0全部 1仅赠送 2仅接收
  int32 page = 2; //当前页数
}
message LOBBY_HD_GETFRIENDGIFTRECORDS_S2C {
  repeated FriendGiftRecordInfo list = 1; //记录列表
  int32 totalPage                     = 2; //总页数
}

// 购买聊天表情
message LOBBY_HD_BUYCHATEMOJI_C2S {
  int32 id = 1; //表情id
}
// 购买聊天表情返回
message LOBBY_HD_BUYCHATEMOJI_S2C {
  repeated int32 unlockChatEmojiIds = 1; //已解锁的表情
  int32 gold                        = 2; //金币
  int32 ingot                       = 3; //元宝
}

// 兑换金币
message LOBBY_HD_EXCHANGEGOLD_C2S {
  int32 count = 1; //兑换数量
}
// 兑换金币 返回
message LOBBY_HD_EXCHANGEGOLD_S2C {
  int32 ingot = 1; //元宝
  int32 gold  = 2; //金币
}

// 购买免费金币
message LOBBY_HD_BUYFREEGOLD_C2S {
}
// 购买免费金币 返回
message LOBBY_HD_BUYFREEGOLD_S2C {
  int32 gold  = 1; //金币
  int64 buyFreeGoldSurplusTime  = 2;
}

// 购买英雄
message LOBBY_HD_BUYHERO_C2S {
  int32 id = 1; //兑换数量
}
// 购买英雄 返回
message LOBBY_HD_BUYHERO_S2C {
  int32 ingot = 1; //元宝
  repeated PortrayalInfo portrayals = 2;
  int64 buyOptionalHeroSurplusTime  = 3;
}

// 获取转盘信息
message LOBBY_HD_GETWHEELINFO_C2S {
}
// 获取转盘信息返回
message LOBBY_HD_GETWHEELINFO_S2C {
  WheelInfo info                        = 1; //转盘信息
  repeated WheelRecordInfo wheelRecords = 2; //记录列表
}

// 转动开始
message LOBBY_HD_WHEELBEGIN_C2S {
}
// 转动开始返回
message LOBBY_HD_WHEELBEGIN_S2C {
  WheelInfo info = 1; //转盘信息
}

// 获取转动结果
message LOBBY_HD_GETWHEELRET_C2S {
  bool ok = 1; //是否成功
}
// 获取转动结果返回
message LOBBY_HD_GETWHEELRET_S2C {
  WheelInfo info                        = 1; //转盘信息
  int32 id                              = 2; //本次id
  repeated WheelRecordInfo wheelRecords = 3; //记录列表
  int32 lastMul                         = 4; //下一次倍数
  repeated UserSubInfo subData          = 5; //用户订阅数据
}

// 领取转盘奖励
message LOBBY_HD_CLAIMWHEELAWARD_C2S {
  string uid  = 1; //唯一id
  int32 sid   = 2; //服务器id
}
// 领取转盘奖励返回
message LOBBY_HD_CLAIMWHEELAWARD_S2C {
  UpdateOutPut rewards = 1; //资源更新
}

// 获取所有任务列表
message LOBBY_HD_GETTASKS_C2S {
}
// 获取所有任务列表返回
message LOBBY_HD_GETTASKS_S2C {
  repeated TaskInfo generalTasks = 1; //常规任务列表
  repeated TaskInfo achieveTasks = 2; //成就任务列表
}

// 领取常规任务奖励
message LOBBY_HD_CLAIMGENERALTASKREWARD_C2S {
  int32 id      = 1; //配置id
  int32 sid     = 2; //游戏服id
  int32 heroId  = 3; //自选英雄
}
// 领取常规任务奖励返回
message LOBBY_HD_CLAIMGENERALTASKREWARD_S2C {
  repeated TaskInfo tasks                 = 1; //常规任务列表
  bool isInviteFriend                     = 2; //是否邀请好友任务
  repeated InviteFriendInfo inviteFriends = 3; //邀请好友列表
  UpdateOutPut rewards                    = 4; //资源更新
}

// 领取成就任务奖励
message LOBBY_HD_CLAIMACHIEVETASKREWARD_C2S {
  int32 id = 1; //配置id
}
// 领取成就任务奖励返回
message LOBBY_HD_CLAIMACHIEVETASKREWARD_S2C {
  repeated TitleInfo titles = 1; //解锁的称号列表
  repeated TaskInfo tasks   = 2; //成就任务列表
}

// 获取用户的人气信息
message LOBBY_HD_GETUSERPOPULARITY_C2S {
  string uid = 1; //玩家uid
}
// 获取用户的人气信息返回
message LOBBY_HD_GETUSERPOPULARITY_S2C {
  repeated PopularityInfo list          = 1; //评价列表
  repeated PopularityRecordInfo records = 2; //记录列表
}

// 改变用户的人气信息
message LOBBY_HD_CHANGEUSERPOPULARITY_C2S {
  string uid  = 1; //玩家uid
  int32 id    = 2; //是否增加
}
// 改变用户的人气信息返回
message LOBBY_HD_CHANGEUSERPOPULARITY_S2C {
  repeated PopularityInfo list      = 1; //评价列表
  int32 gold                        = 2;
  repeated BotanyInfo unlockBotanys = 3; //解锁的植物
}

// 获取用户评分
message LOBBY_HD_GETUSERRANKSCORE_C2S {
  string uid = 1; //玩家uid
}
// 获取用户评分 返回
message LOBBY_HD_GETUSERRANKSCORE_S2C {
  int32 rankScore         = 1; //评分
  int32 accTotalRankCount = 2; //排位次数
}

// 获取评分排行榜
message LOBBY_HD_GETSCORERANK_C2S {
  string uid = 1; //玩家uid
}
// 获取评分排行榜 返回
message LOBBY_HD_GETSCORERANK_S2C {
	repeated UserScoreRankInfo list = 1; //排行列表
	UserScoreRankInfo me            = 2; //自己排名信息
	int32 no                        = 3; //自己排名
}

// 兑换城市皮肤
message LOBBY_HD_RSEXCHANGECITYSKIN_C2S {
  int32 id = 1;
}
// 兑换城市皮肤 返回
message LOBBY_HD_RSEXCHANGECITYSKIN_S2C {
  repeated int32 unlockCitySkinIds  = 1; //解锁的城市皮肤列表
  int32 rankCoin                    = 2; //段位积分
}

// 兑换士兵皮肤
message LOBBY_HD_RSEXCHANGEPAWNSKIN_C2S {
  int32 id = 1;
}
// 兑换士兵皮肤 返回
message LOBBY_HD_RSEXCHANGEPAWNSKIN_S2C {
  repeated int32 unlockPawnSkinIds  = 1; //解锁的士兵皮肤列表
  int32 rankCoin                    = 2; //段位积分
}

// 兑换聊天表情
message LOBBY_HD_RSEXCHANGECHATEMOJI_C2S {
  int32 id = 1;
}
// 兑换聊天表情 返回
message LOBBY_HD_RSEXCHANGECHATEMOJI_S2C {
  repeated int32 unlockChatEmojiIds = 1; //解锁的聊天表情列表
  int32 rankCoin                    = 2; //段位积分
}

// 兑换头像
message LOBBY_HD_RSEXCHANGEHEADICON_C2S {
  int32 id = 1;
}
// 兑换头像 返回
message LOBBY_HD_RSEXCHANGEHEADICON_S2C {
  repeated string unlockHeadIcons = 1; //解锁的头像列表
  int32 rankCoin                  = 2; //段位积分
}

// 种植
message LOBBY_HD_PLANTING_C2S {
  int32 id = 1;
}
// 种植 返回
message LOBBY_HD_PLANTING_S2C {
  int32 gold          = 1;
  PlantInfo plantData = 2; //种植信息
}

// 浇水
message LOBBY_HD_WATERING_C2S {
  int32 type = 1;
}
// 浇水 返回
message LOBBY_HD_WATERING_S2C {
  int32 gold          = 1;
  PlantInfo plantData = 2; //种植信息
}

// 采集
message LOBBY_HD_GATHERBOTANY_C2S {
}
// 采集 返回
message LOBBY_HD_GATHERBOTANY_S2C {
  repeated BotanyInfo unlockBotanys = 1; //解锁的植物
  PlantInfo plantData               = 2; //种植信息
}

// 创建订单
message LOBBY_HD_CREATEPAYORDER_C2S {
  string productId = 1; //商品id
  string platform  = 2; //支付方式
}
// 创建订单返回
message LOBBY_HD_CREATEPAYORDER_S2C {
  string uid      = 1; //订单号
  string uuid     = 2; //玩家uuid
}

// 验证订单
message LOBBY_HD_VERIFYPAYORDER_C2S {
  string productId    = 1; //商品id
  string orderId      = 2; //外部订单号
  string cpOrderId    = 3; //内部订单号
  string token        = 4; //验证数据
  string platform     = 5; //支付方式
  string price        = 6; //价格
  int64 purchaseTime  = 7; //支付时间
  string currencyType = 8; //支付币种
  float payAmount     = 9; //支付金额
  int32 quantity      = 10;//数量
}
message LOBBY_HD_VERIFYPAYORDER_S2C {
  string cpOrderId    = 1; //内部订单号
}

// 领取订单物品
message LOBBY_HD_GETPAYREWARDS_C2S {
  string uid    = 1; //订单号
  string token  = 2; //验证数据
}
// 领取订单物品返回
message LOBBY_HD_GETPAYREWARDS_S2C {
  int32 ingot     = 1; //元宝
  int32 addCount  = 2;
}

// 创建订阅订单
message LOBBY_HD_CREATESUBORDER_C2S {
  string productId = 1; //商品id
  string platform  = 2; //支付方式
  string type      = 3; //订阅类型
  string offerId   = 4; //折扣id
}
// 创建订阅订单返回
message LOBBY_HD_CREATESUBORDER_S2C {
  string uid      = 1; //订单号
  string sign     = 2; //签名
  string nounce   = 3; //随机数
  string uuid     = 4; //玩家uuid
}

// 验证订阅订单
message LOBBY_HD_VERIFYSUBORDER_C2S {
  string productId    = 1; //商品id
  string orderId      = 2; //外部订单号
  string cpOrderId    = 3; //内部订单号
  string token        = 4; //验证数据
  string platform     = 5; //支付方式
  string price        = 6; //价格
  int64 purchaseTime  = 7; //支付时间
  string currencyType = 8; //支付币种
  float payAmount     = 9; //支付金额
}
message LOBBY_HD_VERIFYSUBORDER_S2C {
  string cpOrderId          = 1; //内部订单号
  repeated TypeObj rewards  = 2; //奖励列表
}

// 订阅恢复购买
message LOBBY_HD_SUBRESTORE_C2S {
  string orderId      = 1; //外部订单号
  string platform     = 2; //支付平台  
}
message LOBBY_HD_SUBRESTORE_S2C {  
}

// 获取验证成功的订阅
message LOBBY_HD_SUBORDERCHECK_C2S {
  repeated string list = 1; //订单id列表
}
message LOBBY_HD_SUBORDERCHECK_S2C {
  repeated string list = 1; //已验证的订单id列表
}

// 获取玩家的订阅
message LOBBY_HD_GETUSERSUBINFO_C2S {
}
message LOBBY_HD_GETUSERSUBINFO_S2C {
  repeated UserSubInfo list = 1; //玩家订阅列表
}

// 检测订阅是否失效
message LOBBY_HD_CHECKINVALIDSUBORDER_C2S {
  repeated SubReqInfo list = 1; //订单数据列表
  string platform          = 2; //支付平台 
}
message LOBBY_HD_CHECKINVALIDSUBORDER_S2C {
  repeated string list = 1; //已失效的订单id列表
}

// 领取月卡每日奖励
message LOBBY_HD_GETMONTHCARDAWARD_C2S {
  string type = 1; // 月卡类型
}
message LOBBY_HD_GETMONTHCARDAWARD_S2C {
  repeated TypeObj rewards   = 1; //奖励列表
   repeated UserSubInfo list = 2; //玩家订阅列表
}

// 新区服报名
message LOBBY_HD_APPLYNEWSERVER_C2S {
  int32 roomType = 1; //房间类型
}
message LOBBY_HD_APPLYNEWSERVER_S2C {
}

// 取消报名
message LOBBY_HD_CANCELAPPLYNEWSERVER_C2S {
}
message LOBBY_HD_CANCELAPPLYNEWSERVER_S2C {
}

// 语言设置
message LOBBY_HD_SETLANGUAGE_C2S {
  string language = 1; //语言
}
message LOBBY_HD_SETLANGUAGE_S2C {
}

// 黑名单
message LOBBY_HD_BLACKLIST_C2S {
  string uid = 1;
  bool state = 2;
}
// 黑名单 返回
message LOBBY_HD_BLACKLIST_S2C {
  repeated BlacklistInfo blacklists = 1;
  string channel = 2; //私聊频道
}
// 查询玩家
message LOBBY_HD_SEARCHUSER_C2S {
  string uid      = 1;
  string nickname = 2;
}
message LOBBY_HD_SEARCHUSER_S2C {
  string uid              = 1;
  string nickname         = 2;
  string headIcon         = 3; //头像
  int64 lastLoginTime     = 4; //最后登录时间
}

// 好友申请
message LOBBY_HD_APPLYFRIEND_C2S {
  string uid = 1;
}
message LOBBY_HD_APPLYFRIEND_S2C {  
}

// 好友申请回复
message LOBBY_HD_APPLYFRIENDRESPONSE_C2S {
  string uid = 1; //好友uid
  bool agree = 2; //是否同意
  bool ban   = 3; //是否不再收到该玩家的好友请求
}
message LOBBY_HD_APPLYFRIENDRESPONSE_S2C {
  repeated FriendInfo friendsList         = 1; //好友列表
  repeated FriendApplyInfo friendsApplys  = 2; //好友申请列表  
  repeated BlacklistInfo blacklists       = 3; //黑名单
}

// 删除好友
message LOBBY_HD_DELFRIEND_C2S {
  string uid = 1; //好友uid
}
message LOBBY_HD_DELFRIEND_S2C {
}

// 获取好友聊天消息
message LOBBY_HD_GETFRIENDCHATS_C2S {
  string uid  = 1; //好友uid
  int32 start = 2; //开始下标
  int32 count = 3; //数量
}
message LOBBY_HD_GETFRIENDCHATS_S2C {
  string uid              = 1;  //好友uid
  repeated ChatInfo chats = 2;  //聊天列表
}

// 好友聊天已读
message LOBBY_HD_FRIENDCHATREAD_C2S {
  string uid = 1; //好友uid
}
message LOBBY_HD_FRIENDCHATREAD_S2C {
}

// 好友聊天
message LOBBY_HD_FRIENDCHAT_C2S {
  string uid        = 1;
  string friendUid  = 2; //好友uid
  string content    = 3; //内容
  int32 emoji       = 4; //表情id
}
// 好友聊天返回
message LOBBY_HD_FRIENDCHAT_S2C {
}

// 设置好友备注名
message LOBBY_HD_SETFRIENDNOTENAME_C2S {
  string uid      = 1; //好友uid
  string noteName = 2; //备注名
}
message LOBBY_HD_SETFRIENDNOTENAME_S2C {
}

// 观战好友
message LOBBY_HD_SPECTATEFRIEND_C2S {
  string uid = 1;
}
message LOBBY_HD_SPECTATEFRIEND_S2C {
  int32 sid = 1;
}

// 文本翻译
message LOBBY_HD_TRANSLATETEXT_C2S {
  string lang = 1; //目标语言
  string text = 2; //文本
  string uid  = 3;
}
message LOBBY_HD_TRANSLATETEXT_S2C {
}

// 预测战斗费用
message LOBBY_HD_BATTLEFORECAST_C2S {
  int32 sid           = 1;
  int32 landCount     = 2; //当前地块数
  int32 maincityLevel = 3; //当前主城等级
  int32 landDis       = 4; //距离
  int32 landlv        = 5; //当前地块等级
}
message LOBBY_HD_BATTLEFORECAST_S2C {
  int32 battleForecastCount = 1;
  int32 gold = 2;
}

// 发送喇叭
message LOBBY_HD_SENDTRUMPET_C2S {
  string content = 1;
}
message LOBBY_HD_SENDTRUMPET_S2C {
  int32 ingot = 1;
  int32 todaySendTrumpetCount = 2; //每日发送次数
}

// 点赞九万亩
message LOBBY_HD_LIKEJWM_C2S {
}
message LOBBY_HD_LIKEJWM_S2C {
}

// 获取排位奖励
message LOBBY_HD_GETRANKREWARD_C2S {
  int32 id = 1;
}
message LOBBY_HD_GETRANKREWARD_S2C {
  UpdateOutPut rewards            = 1; //资源更新
  repeated int32 rankRewardRecord = 2; //排位奖励领取记录
}

// 完成新手村
message LOBBY_HD_COMPLETENOVICE_C2S {
  int32 gold     = 1; //新手村金币
  int32 warToken = 2; //兵符
}
message LOBBY_HD_COMPLETENOVICE_S2C {
}

// 获取队伍信息
message LOBBY_HD_GETTEAMINFO_C2S {
}
message LOBBY_HD_GETTEAMINFO_S2C {
  TeamInfo teamInfo                       = 1; //队伍信息
  repeated TeamInviteInfo teamInviteList  = 2; //收到队伍邀请列表
  int32 userNumMax                        = 3; //队伍人数上限
}

// 邀请队友
message LOBBY_HD_INVITETEAMMATE_C2S {
  string uid      = 1; //玩家uid
  int32 roomType  = 2; //报名时选择的模式
}
message LOBBY_HD_INVITETEAMMATE_S2C {
}

// 组队邀请回复
message LOBBY_HD_INVITETEAMMATERESPONSE_C2S {
  string uid    = 1; //队伍uid
  bool isAgree  = 2; //是否同意
}
message LOBBY_HD_INVITETEAMMATERESPONSE_S2C {
  TeamInfo teamInfo = 1; //队伍信息
}

// 删除队员
message LOBBY_HD_DELTEAMMATE_C2S {
  string uid = 1;
}
message LOBBY_HD_DELTEAMMATE_S2C {
}

// 退出队伍
message LOBBY_HD_EXITTEAM_C2S {
}
message LOBBY_HD_EXITTEAM_S2C {
}

// 切换队伍模式
message LOBBY_HD_CHANGETEAMMODE_C2S {
  int32 roomType = 1;
}
message LOBBY_HD_CHANGETEAMMODE_S2C {
}

// 解散队伍
message LOBBY_HD_DIDSBANDTEAM_C2S {
}
message LOBBY_HD_DIDSBANDTEAM_S2C {
}

// 变更队员职位
message LOBBY_HD_TEAMCHANGEJOB_C2S {
  string uid = 1; //队员uid
  int32 job  = 2; //职位
}
message LOBBY_HD_TEAMCHANGEJOB_S2C {
}

// 获取队友备战信息
message LOBBY_HD_GETTEAMMATEPREPAREINFO_C2S {
  string uid = 1; //队员uid
}
message LOBBY_HD_GETTEAMMATEPREPAREINFO_S2C {
  int32 expectPosition = 1; //期望位置
  int32 farmType       = 2; //开荒方式
}

// 设置备战信息
message LOBBY_HD_SETPREPAREINFO_C2S {
  int32 pos      = 1; //期望位置
  int32 farmType = 2; //开荒方式
}
message LOBBY_HD_SETPREPAREINFO_S2C {
}

// 获取指定房间类型的相关信息
message LOBBY_HD_GETROOMSTATEINFOS_C2S {
}
message LOBBY_HD_GETROOMSTATEINFOS_S2C {
  int32 playSid                   = 1;
  int32 allotPlaySid              = 2;
  repeated RoomStateInfo states   = 3; //状态列表
  repeated int32 canPlayRoomTypes = 4; //可以玩的区服类型
}

// 创建自定义房间
message LOBBY_HD_CREATECUSTOMROOM_C2S {
  string name     = 1;
  string password = 2;
}
message LOBBY_HD_CREATECUSTOMROOM_S2C {
  int32 ingot = 1;
}

// 进入自定义房间
message LOBBY_HD_ENTERCUSTOMROOM_C2S {
  int32 sid       = 1;
  string password = 2;
}
message LOBBY_HD_ENTERCUSTOMROOM_S2C {
}

// 退出自定义房间
message LOBBY_HD_EXITCUSTOMROOM_C2S {
}
message LOBBY_HD_EXITCUSTOMROOM_S2C {
}

// 大厅服聊天
message LOBBY_HD_LOBBYCHAT_C2S {
  string channel    = 1; //频道
  string uid        = 2; //消息uid
  string content    = 3; //内容
  int32 emoji       = 4; //表情
  int32 portrayalId = 5; //画像id
  string replyUid   = 6; //回复uid
}
message LOBBY_HD_LOBBYCHAT_S2C {
  int64 bannedSurplusTime = 1; //禁言剩余时间
}

// 点将
message LOBBY_HD_POINTSETS_C2S {
  int32 count = 1;
  bool skip   = 2; //是否跳过动画
}
message LOBBY_HD_POINTSETS_S2C {
  repeated int32 ids                = 1;
  repeated PortrayalInfo portrayals = 2;
  int32 warToken                    = 3;
  int32 gold                        = 4;
  repeated int32 counts             = 5;
}

// 画像合成
message LOBBY_HD_PORTRAYALCOMP_C2S {
  int32 id = 1;
}
message LOBBY_HD_PORTRAYALCOMP_S2C {
  PortrayalInfo info = 1;
}

// 合成指定残卷
message LOBBY_HD_COMPPORTRAYALDEBRIS_C2S {
  int32 id                  = 1;
  map<int32, int32>  idMap  = 2;  //消耗的残卷map k=>id v=>count
}
message LOBBY_HD_COMPPORTRAYALDEBRIS_S2C {
  repeated PortrayalInfo portrayals = 1;
}

// 保存画像
message LOBBY_HD_SAVEPORTRAYAL_C2S {
  int32 id            = 1; //英雄id
  int32 slotIndex     = 2; //保存槽位下标
  int32 historyIndex  = 3; //历史记录下标
  int32 costType      = 4; //费用类型 0.兵符 1.金币
}
message LOBBY_HD_SAVEPORTRAYAL_S2C {
  PortrayalInfo info  = 1;
  int32 warToken      = 2;
  int32 gold          = 3;
}

// 还原画像
message LOBBY_HD_RESTOREPORTRAYAL_C2S {
  int32 id        = 1; //英雄id
  int32 slotIndex = 2; //保存槽位下标
}
message LOBBY_HD_RESTOREPORTRAYAL_S2C {
  PortrayalInfo info  = 1;
}

// 购买画像保存栏位
message LOBBY_HD_BUYPORTRAYALSLOT_C2S {
  int32 id = 1; //英雄id
}
message LOBBY_HD_BUYPORTRAYALSLOT_S2C {
  PortrayalInfo info  = 1;
  int32 ingot         = 2; //元宝
}

// 作弊封禁中点击或挂机
message LOBBY_HD_BANCHEATTAP_C2S {
  int32 count = 1; //点击次数
  int32 time  = 2; //挂机时间 毫秒
}
message LOBBY_HD_BANCHEATTAP_S2C {
  int64 banCheatSurplusTime = 1; //封禁剩余时间
}

// 获取战令信息
message LOBBY_HD_GETBATTLEPASSINFO_C2S {  
}
message LOBBY_HD_GETBATTLEPASSINFO_S2C {
  BattlePassInfo info = 1;
}

// 领取战令奖励
message LOBBY_HD_GETBATTLEPASSREWARD_C2S {
  int32 id             = 1; //奖励id -1为一键领取
  bool isPay           = 2; //是否是付费奖励
  repeated int32 heros = 3; //自选英雄id列表
}
message LOBBY_HD_GETBATTLEPASSREWARD_S2C {
  UpdateOutPut rewards = 1; //奖励
}

// 购买战令积分
message LOBBY_HD_BUYBATTLEPASSSCORE_C2S {
}
message LOBBY_HD_BUYBATTLEPASSSCORE_S2C {
  BattlePassInfo info = 1; //战令信息
  int32 ingot         = 2; //元宝
}

// 玩家放弃对局
message LOBBY_HD_GIVEUPGAME_C2S {
}
// 玩家放弃对局 返回
message LOBBY_HD_GIVEUPGAME_S2C {
  int32 rankScore       = 1;
  int32 passNewbieIndex = 2;
}

// 获取当前对局信息
message LOBBY_HD_GETCURGAMEINFO_C2S {
}
message LOBBY_HD_GETCURGAMEINFO_S2C {
  int32 score   = 1; //当前积分
  int32 rank    = 2; //当前排名
  int64 runTime = 3; //服务器运行时间
}

// ------------------ 游戏服协议 -------------------------

// 进入游戏服
message GAME_HD_ENTRY_C2S {
  int32 sid         = 1; //游戏服id 
  string token      = 2; //游戏服token
  string version    = 3; //版本
  bool isReconnect  = 4; //是否重连
  string distinctId = 5; //访客id
  string lang       = 6; //客户端语言
  string os         = 7; //操作系统
  string platform   = 8; //平台
  int32 pos         = 9; //测试模式 选择的位置
}
// 进入游戏服返回
message GAME_HD_ENTRY_S2C {
  EnterGameRst rst                  = 1; //进入游戏返回数据
  repeated int32 canCreateAreaCount = 2; //可以创建主城的区域数量
  string clientVersion              = 3; //要求客户端版本
  string clientBigVersion           = 4; //客户端大版本
}

// 创建玩家
message GAME_HD_CREATEPLAYER_C2S {
  int32 sid                   = 1; //服务器id
  int32 pos                   = 2; //位置  
  string distinctId           = 3; //访客id
  string lang                 = 4; //客户端语言
}
// 创建玩家返回
message GAME_HD_CREATEPLAYER_S2C {
  EnterGameRst rst                  = 1; //进入游戏返回数据
  repeated int32 canCreateAreaCount = 2; //可以创建主城的区域数量
}

// 重新创建主城
message GAME_HD_RECREATEMAINCITY_C2S {
  string lang = 1; //客户端语言
}
// 重新创建主城返回
message GAME_HD_RECREATEMAINCITY_S2C {
  PlayerInfo playerInfo = 1; //玩家游戏数据
}

// 获取自己的军队
message GAME_HD_GETPLAYERARMYS_C2S {
}
// 获取自己的军队返回
message GAME_HD_GETPLAYERARMYS_S2C {
  repeated AreaArmyInfo list = 1; //军队列表
}

// 获取自己的军队记录列表
message GAME_HD_GETARMYRECORDS_C2S {
  bool isBattle = 1; //是否在战斗中
}
// 获取自己的军队记录列表返回
message GAME_HD_GETARMYRECORDS_S2C {
  repeated ArmyRecordInfo list = 1; //军队记录列表
}

// 根据uid获取军队记录
message GAME_HD_GETARMYRECORDSBYUIDS_C2S {
  repeated string uids = 1; //军队uid列表
  string battleUid     = 2; //战斗记录uid
}
// 根据uid获取军队记录返回
message GAME_HD_GETARMYRECORDSBYUIDS_S2C {
  repeated ArmyRecordInfo list = 1; //军队记录列表
}

// 获取战斗记录
message GAME_HD_GETBATTLERECORD_C2S {
  string uid = 1; //战斗记录uid
}
// 获取战斗记录返回
message GAME_HD_GETBATTLERECORD_S2C {
  BattleRecordInfo record = 1; //战斗记录数据
}

// 获取市场记录
message GAME_HD_GETBAZAARRECORDS_C2S {
}
// 获取市场记录返回
message GAME_HD_GETBAZAARRECORDS_S2C {
  repeated BazaarRecordInfo list = 1; //市场记录列表
}

// 获取个人战报列表
message GAME_HD_GETBATTLERECORDSLIST_C2S {
}
message GAME_HD_GETBATTLERECORDSLIST_S2C {
  repeated BattleScoreRecord list = 1; //个人战报列表
}

// 查询玩家
message GAME_HD_QUERYPLAYER_C2S {
  string name = 1; //名字
  int32 index = 2; //主城位置
}
// 查询玩家返回
message GAME_HD_QUERYPLAYER_S2C {
  string nickname = 1; //名字
  int32 index     = 2; //主城位置
}

// 改变配置士兵的装备
message GAME_HD_CHANGECONFIGPAWNEQUIP_C2S {
  int32 id          = 1; //士兵id
  string equipUid   = 2; //装备id
  int32 skinId      = 3; //皮肤id
  int32 attackSpeed = 4; //出手速度
}
// 改变配置士兵的装备返回
message GAME_HD_CHANGECONFIGPAWNEQUIP_S2C {
}

// 打开宝箱
message GAME_HD_OPENTREASURE_C2S {
  string auid = 1; //军队uid
  string puid = 2; //士兵uid
  string uid  = 3; //宝箱uid
}
// 打开宝箱返回
message GAME_HD_OPENTREASURE_S2C {
  repeated TypeObj rewards        = 1; //奖励列表
  string armyUid                  = 2; //军队uid
  string uid                      = 3; //士兵uid
  repeated TreasureInfo treasures = 4; //士兵剩余宝箱列表
}

// 打开军队宝箱
message GAME_HD_OPENARMYTREASURE_C2S {
  int32 index = 1;
  string auid = 2; //军队uid
}
// 打开军队宝箱 返回
message GAME_HD_OPENARMYTREASURE_S2C {
  string armyUid                          = 1; //军队uid
  repeated PawnTreasureInfo pawnTreasures = 2; //士兵宝箱信息
}

// 领取宝箱
message GAME_HD_CLAIMTREASURE_C2S {
  string auid = 1; //军队uid
  string puid = 2; //士兵uid
  string uid  = 3; //宝箱uid
}
// 领取宝箱返回
message GAME_HD_CLAIMTREASURE_S2C {
  UpdateOutPut rewards            = 1; //资源更新
  string armyUid                  = 2; //军队uid
  string uid                      = 3; //士兵uid
  repeated TreasureInfo treasures = 4; //士兵剩余宝箱列表
}

// 领取军队宝箱
message GAME_HD_CLAIMARMYTREASURE_C2S {
  int32 index = 1;
  string auid = 2; //军队uid
}
// 领取军队宝箱 返回
message GAME_HD_CLAIMARMYTREASURE_S2C {
  UpdateOutPut rewards                    = 1; //资源更新
  string armyUid                          = 2; //军队uid
  repeated PawnTreasureInfo pawnTreasures = 3; //士兵宝箱信息
}

// 玩家请求结算对局
message GAME_HD_SETTLEGAME_C2S {
}
message GAME_HD_SETTLEGAME_S2C {
}

// 获取战场信息
message GAME_HD_GETAREAINFO_C2S {
  int32 index = 1; //位置
}
// 获取战场信息返回
message GAME_HD_GETAREAINFO_S2C {
  AreaInfo data = 1; //战场信息
}

// 获取城市皮肤信息
message GAME_HD_GETCITYSKINS_C2S {
}
// 获取城市皮肤信息 返回
message GAME_HD_GETCITYSKINS_S2C {
  map<int32, int32> citySkins = 1;
}

// 获取行军信息
message GAME_HD_GETMARCHS_C2S {
}
// 获取行军信息返回
message GAME_HD_GETMARCHS_S2C {
  repeated MarchInfo list = 1; //行军信息列表
}

// 获取运送信息
message GAME_HD_GETTRANSITS_C2S {
}
// 获取运送信息返回
message GAME_HD_GETTRANSITS_S2C {
  repeated TransitInfo list = 1;
}

// 获取地图上面的战斗分布情况
message GAME_HD_GETBATTLEDIST_C2S {
}
// 获取地图上面的战斗分布情况返回
message GAME_HD_GETBATTLEDIST_S2C {
  map<int32, UidsList> battleDistMap = 1; //战斗分布
}

// 获取当前地图免战分布情况
message GAME_HD_GETAVOIDWARDIST_C2S {
}
// 获取当前地图免战分布情况返回
message GAME_HD_GETAVOIDWARDIST_S2C {
  map<int32, int32> avoidWarAreas   = 1; //免战信息map k=>位置 v=>剩余时间
  map<int32, int32> avoidWarAreas2  = 2; //免战信息map k=>位置 v=>剩余时间 (战斗超过3小时的免战)
}

// 获取当前地图免战分布情况
message GAME_HD_GETTONDENDIST_C2S {
}
// 获取当前地图免战分布情况返回
message GAME_HD_GETTONDENDIST_S2C {
  map<int32, CellTondenInfo> tondens   = 1; //屯田信息map k=>位置 v=>屯田信息
}

// 获取当前修建城市列表
message GAME_HD_GETBTCITYQUEUES_C2S {
}
// 获取当前修建城市列表返回
message GAME_HD_GETBTCITYQUEUES_S2C {
  repeated BTCityInfo btCityQueues = 1;
}

// 获取选择军队列表
message GAME_HD_GETSELECTARMYS_C2S {
  int32 index = 1; //位置
  int32 type  = 2; //获取类型 1.移动 2.攻占
}
// 获取选择军队列表返回
message GAME_HD_GETSELECTARMYS_S2C {
  repeated AreaArmyInfo list  = 1; //军队列表
  int32 canGotoCount          = 2; //可前往数量
}

// 进攻
message GAME_HD_OCCUPYCELL_C2S {
  repeated int32 indexs = 1; //位置列表
  repeated string uids  = 2; //军队uid列表
  int32 target          = 3; //目标位置
  bool isGuideBattle    = 4; //是否新手引导战斗
  int32 autoBackType    = 5; //自动返回位置
  bool isSameSpeed      = 6; //是否同时抵达
}
// 进攻返回
message GAME_HD_OCCUPYCELL_S2C {
}

// 移动军队
message GAME_HD_MOVECELLARMY_C2S {
  repeated int32 indexs = 1; //位置列表
  repeated string uids  = 2; //军队uid列表
  int32 target          = 3; //目标位置
  bool isSameSpeed      = 4; //是否同时抵达
}
// 移动军队返回
message GAME_HD_MOVECELLARMY_S2C {
}

// 屯田
message GAME_HD_CELLTONDEN_C2S {
  int32 index   = 1; //位置
  string uid    = 2; //军队uid
  int32 target  = 3; //目标位置
}
message GAME_HD_CELLTONDEN_S2C {
}

// 取消屯田
message GAME_HD_CANCELCELLTONDEN_C2S {
  int32 index   = 1; //位置
}
message GAME_HD_CANCELCELLTONDEN_S2C {
}

// 取消行军
message GAME_HD_CANCELMARCH_C2S {
  string uid = 1; //行军uid
}
// 取消行军返回
message GAME_HD_CANCELMARCH_S2C {

}

// 强制撤回军队
message GAME_HD_FORCEREVOKEARMY_C2S {
  string uid  = 1; //军队uid
}
message GAME_HD_FORCEREVOKEARMY_S2C {
}

// 修建城市
message GAME_HD_CREATECITY_C2S {
  int32 index = 1; //位置
  int32 id    = 2; //城市id
}
// 修建城市返回
message GAME_HD_CREATECITY_S2C {
  UpdateOutPut output = 1; //产出信息
}

// 拆除城市
message GAME_HD_DISMANTLECITY_C2S {
  int32 index = 1; //位置
}
// 拆除城市返回
message GAME_HD_DISMANTLECITY_S2C {  
}

// 改变城市皮肤
message GAME_HD_CHANGECITYSKIN_C2S {
  int32 index   = 1; //位置
  int32 skinId  = 2; //皮肤
}
// 改变城市皮肤 返回
message GAME_HD_CHANGECITYSKIN_S2C {  
}

// 同步地块
message GAME_HD_SYNCCELLINFO_C2S {
  int32 index = 1; //位置
}
// 同步地块 返回

message GAME_HD_SYNCCELLINFO_S2C {
  repeated CellInfo cells = 1; //地块信息
}

// 地图表情
message GAME_HD_SENDCELLEMOJI_C2S {
  int32 index = 1; //位置
  int32 emoji = 2; //表情
}
// 地图表情 返回
message GAME_HD_SENDCELLEMOJI_S2C {
}

// 领取城市产出
message GAME_HD_CLAIMCITYOUTPUT_C2S {
  int32 index = 1; //位置
}
// 领取城市产出 返回
message GAME_HD_CLAIMCITYOUTPUT_S2C {
  UpdateOutPut rewards = 1; //资源更新
}

// 地图标记
message GAME_HD_ALLIMAPFLAG_C2S {
  int32 index = 1; //位置
  int32 flag  = 2; //标记
  string desc = 3; //描述
}
// 地图标记 返回
message GAME_HD_ALLIMAPFLAG_S2C {  
}

// 删除地图标记
message GAME_HD_DELALLIMAPFLAG_C2S {
  int32 index = 1; //位置
}
// 删除地图标记 返回
message GAME_HD_DELALLIMAPFLAG_S2C {  
}

// 移动建筑
message GAME_HD_MOVEAREABUILD_C2S {
  int32 index = 1; //位置
  string uid  = 2; //建筑uid
  Vec2 point  = 3; //坐标
}
// 移动建筑返回
message GAME_HD_MOVEAREABUILD_S2C {
}

// 升级建筑
message GAME_HD_UPAREABUILD_C2S {
  int32 index = 1; //位置
  string uid  = 2; //建筑uid
}
// 升级建筑返回
message GAME_HD_UPAREABUILD_S2C {
  UpdateOutPut output     = 1; //产出信息
  repeated BTInfo queues  = 2; //建筑队列
}

// 修建建筑
message GAME_HD_ADDAREABUILD_C2S {
  int32 index = 1; //位置
  int32 id    = 2; //建筑id
}
// 修建建筑返回
message GAME_HD_ADDAREABUILD_S2C {
  AreaBuildInfo build     = 1; //建筑信息
  UpdateOutPut output     = 2; //产出信息
  repeated BTInfo queues  = 3; //建筑队列
}

// 取消修建
message GAME_HD_CANCELBT_C2S {
  int32 index = 1; //位置
  string uid  = 2; //建筑uid
}
// 取消修建返回
message GAME_HD_CANCELBT_S2C {
  UpdateOutPut output     = 1; //产出信息
  repeated BTInfo queues  = 2; //建筑队列
}

// 立即完成修建
message GAME_HD_INDONEBT_C2S {
}
// 立即完成修建返回
message GAME_HD_INDONEBT_S2C {
  repeated BTInfo queues  = 1; //建筑队列
  int32 gold              = 2; //金币
}

// 检测军队名字
message GAME_HD_CHECKARMYNAME_C2S {
  string name = 1; //名字
}
// 检测军队名字返回
message GAME_HD_CHECKARMYNAME_S2C {  
}

// 招募士兵
message GAME_HD_DRILLPAWN_C2S {
  int32 index     = 1; //位置
  string buildUid = 2; //建筑uid
  int32 id        = 3; //士兵id
  string armyUid  = 4; //军队uid
  string armyName = 5; //军队昵称
}
// 招募士兵返回
message GAME_HD_DRILLPAWN_S2C {
  UpdateOutPut output                   = 1; //产出信息
  map<string, DrillPawnInfoList> queues = 2; //招募队列
  AreaArmyInfo army                     = 3; //军队信息
  int32 upRecruitPawnCount              = 4; //加速招募数量
  int32 freeRecruitPawnCount            = 5; //免费招募数量
}

// 取消招募士兵
message GAME_HD_CANCELDRILLPAWN_C2S {
  int32 index     = 1; //位置
  string buildUid = 2; //建筑uid
  string uid      = 3; //招募uid
}
// 取消招募士兵返回
message GAME_HD_CANCELDRILLPAWN_S2C {
  UpdateOutPut output                   = 1; //产出信息
  map<string, DrillPawnInfoList> queues = 2; //招募队列
  AreaArmyInfo army                     = 3; //军队信息
  repeated TypeObj needCost             = 4; //花费
}

// 士兵练级
message GAME_HD_PAWNLVING_C2S {
  int32 index = 1; //位置
  string auid = 2; //军队uid
  string puid = 3; //士兵uid
}
// 士兵练级返回
message GAME_HD_PAWNLVING_S2C {
  UpdateOutPut cost             = 1; //花费资源更新
  repeated PawnLvingInfo queues = 2; //练级队列
  int32 freeLevingPawnCount     = 3; //免费训练次数
}

// 取消士兵练级
message GAME_HD_CANCELPAWNLVING_C2S {
  int32 index = 1; //位置
  string uid  = 2; //练级队列uid
}
// 取消士兵练级返回
message GAME_HD_CANCELPAWNLVING_S2C {
  UpdateOutPut cost             = 1; //花费资源更新
  repeated PawnLvingInfo queues = 2; //练级队列
  repeated TypeObj needCost     = 3; //花费
}

// 治疗士兵
message GAME_HD_CUREINJURYPAWN_C2S {
  int32 index     = 1; //位置
  string armyUid  = 2; //军队uid
  string armyName = 3; //军队昵称
  string pawnUid  = 4; //治疗士兵uid
}
// 治疗士兵返回
message GAME_HD_CUREINJURYPAWN_S2C {
  UpdateOutPut output                    = 1; //产出信息
  repeated PawnCuringInfo queues         = 2; //治疗队列
  AreaArmyInfo army                      = 3; //军队信息
  int32 freeCurePawnCount                = 4; //免费治疗次数
}

// 取消治疗士兵
message GAME_HD_CANCELCUREPAWN_C2S {
  int32 index     = 1; //位置
  string uid      = 2; //士兵uid
}
// 取消治疗士兵返回
message GAME_HD_CANCELCUREPAWN_S2C {
  UpdateOutPut output                    = 1; //产出信息
  repeated PawnCuringInfo queues         = 2; //治疗队列
  AreaArmyInfo army                      = 3; //军队信息
  repeated TypeObj needCost              = 4; //花费
}

// 放弃治疗受伤士兵
message GAME_HD_GIVEUPINJURYPAWN_C2S {
  string uid      = 1; //士兵uid
}
// 放弃治疗受伤士兵返回
message GAME_HD_GIVEUPINJURYPAWN_S2C {
  repeated InjuryPawnInfo injuryPawns = 1; //受伤士兵列表
}

// 加速治疗士兵
message GAME_HD_SPEEDUPCURINGPAWN_C2S {
  int32 index     = 1; //位置
  int32 costBook  = 2; //消耗书
}
// 加速治疗士兵返回
message GAME_HD_SPEEDUPCURINGPAWN_S2C {
  int32 expBook                    = 1; //剩余书
  repeated PawnCuringInfo queues   = 2; //治疗队列
}

// 设置医馆通知开关
message GAME_HD_SETHOSPITALNOTICEOP_C2S {
  bool isClose     = 1; //是否关闭
}
// 设置医馆通知开关返回
message GAME_HD_SETHOSPITALNOTICEOP_S2C {
}

// 移动士兵
message GAME_HD_MOVEAREAPAWNS_C2S {
  int32 index                 = 1; //位置
  string armyUid              = 2; //军队uid
  repeated MovePawnInfo pawns = 3; //士兵移动数据列表
}
// 移动士兵返回
message GAME_HD_MOVEAREAPAWNS_S2C {
}

// 改变士兵属性
message GAME_HD_CHANGEPAWNATTR_C2S {
  int32 index       = 1; //位置
  string armyUid    = 2; //军队uid
  string uid        = 3; //士兵uid
  int32 attackSpeed = 4; //出手速度
  string equipUid   = 5; //装备uid
  int32 skinId      = 6; //皮肤id
  int32 syncEquip   = 7; //同步装备 0.不同步 1.同步场景中的 2.同步军队中的
  int32 petId       = 8; //宠物id
  int32 syncSkin    = 9; //同步皮肤 0.不同步 1.同步场景中的 2.同步军队中的
}
// 改变士兵属性返回
message GAME_HD_CHANGEPAWNATTR_S2C {
}

// 使用士兵皮肤
message GAME_HD_USEPAWNSKIN_C2S {
  int32 pawnId = 1; //士兵id
  int32 skinId = 2; //皮肤id
}
// 使用士兵皮肤返回
message GAME_HD_USEPAWNSKIN_S2C {
  bool hasPawn = 1; //是否有该士兵
}

// 改变士兵所属军队
message GAME_HD_CHANGEPAWNARMY_C2S {
  int32 index       = 1; //位置
  string armyUid    = 2; //军队uid
  string uid        = 3; //士兵uid
  int32 attackSpeed = 4; //出手速度
  string equipUid   = 5; //装备uid
  int32 skinId      = 6; //皮肤id
  string newArmyUid = 7; //新军队uid
  bool isNewCreate  = 8; //是否新建军队
  int32 petId       = 9; //宠物id
}
// 改变士兵所属军队返回
message GAME_HD_CHANGEPAWNARMY_S2C {
  string uid  = 1; //新军队uid
  string name = 2; //新军队名
}

// 修改军队名字
message GAME_HD_MODIFYAMRYNAME_C2S {
  int32 index     = 1; //位置
  string armyUid  = 2; //军队uid
  string name     = 3; //名字
}
// 修改军队名字返回
message GAME_HD_MODIFYAMRYNAME_S2C {

}

// 升级士兵
message GAME_HD_USEUPSCROLLUPPAWNLV_C2S {
  int32 index     = 1; //位置
  string armyUid  = 2; //军队uid
  string uid      = 3; //士兵uid
}
// 升级士兵返回
message GAME_HD_USEUPSCROLLUPPAWNLV_S2C {
  UpdateOutPut cost = 1; //资源更新
  int32 lv          = 2; //等级
}

// 改变要塞是否自动支援
message GAME_HD_CHANGEFORTAUTOSUPPORT_C2S {
  int32 index = 1; //位置
  bool isAuto = 2; //是否自动支援
}
// 改变要塞是否自动支援返回
message GAME_HD_CHANGEFORTAUTOSUPPORT_S2C {
}

// 离开区域
message GAME_HD_LEAVEAREA_C2S {
  int32 index = 1; //位置
}
// 离开区域返回
message GAME_HD_LEAVEAREA_S2C {
}

// 观战区域
message GAME_HD_WATCHAREA_C2S {
  int32 index = 1; //位置
  bool state  = 2;
}
// 观战区域 返回
message GAME_HD_WATCHAREA_S2C {
}

// 区域发送聊天
message GAME_HD_AREASENDCHAT_C2S {
  int32 index = 1; //位置
  int32 emoji = 2;
}
// 观战区域 返回
message GAME_HD_AREASENDCHAT_S2C {
}

// 获取所有联盟政策
message GAME_HD_GETALLALLIBASEINFO_C2S {
}
// 获取所有联盟政策 返回
message GAME_HD_GETALLALLIBASEINFO_S2C {
  repeated AlliBaseInfo allis = 1;
}

// 创建联盟
message GAME_HD_CREATEALLIANCE_C2S {
  string name = 1;
  int32 icon  = 2;
}
// 创建联盟返回
message GAME_HD_CREATEALLIANCE_S2C {
  AllianceInfo alliance = 1; //联盟信息
  UpdateOutPut cost     = 2; //资源更新
}

// 获取联盟列表
message GAME_HD_GETALLIANCES_C2S {
}
// 获取联盟列表返回
message GAME_HD_GETALLIANCES_S2C {
  repeated AllianceShortInfo list = 1;
}

// 获取联盟
message GAME_HD_GETALLIANCE_C2S {
  string uid = 1; //联盟uid
}
// 获取联盟返回
message GAME_HD_GETALLIANCE_S2C {
  AllianceInfo alliance = 1; //联盟信息
}

// 申请加入联盟
message GAME_HD_APPLYJOINALLIANCE_C2S {
  string uid  = 1; //联盟uid
  string desc = 2; //申请说明
}
// 申请加入联盟返回
message GAME_HD_APPLYJOINALLIANCE_S2C {
}

// 取消申请加入联盟
message GAME_HD_CANCELJOINALLIANCE_C2S {
  string uid = 1; //联盟uid
}
// 取消申请加入联盟返回
message GAME_HD_CANCELJOINALLIANCE_S2C {
}

// 同意拒绝加入联盟
message GAME_HD_AGREEJOINALLIANCE_C2S {
  string uid    = 1; //联盟uid
  bool isAgree  = 2; //是否同意
}
// 同意拒绝加入联盟返回
message GAME_HD_AGREEJOINALLIANCE_S2C {
  repeated AllianceApplyInfo applys = 1; //申请列表
}

// 踢出联盟
message GAME_HD_KICKOUTALLIANCE_C2S {
  string uid = 1; //踢出玩家uid
}
// 踢出联盟返回
message GAME_HD_KICKOUTALLIANCE_S2C {
}

// 退出联盟
message GAME_HD_EXITALLIANCE_C2S {
}
// 退出联盟返回
message GAME_HD_EXITALLIANCE_S2C {
  int32 time              = 1; //多长时间可以退出联盟
  int32 exitAllianceCount = 2; //退出联盟次数
}

// 编辑联盟公告
message GAME_HD_CHANGEALLIANCENOTICE_C2S {
  string notice = 1; //公告文本
}
// 编辑联盟公告 返回
message GAME_HD_CHANGEALLIANCENOTICE_S2C {
  string notice       = 1; //公告文本
  int32 editNoticeCD  = 2; //编辑cd
}

// 改变成员职位
message GAME_HD_CHANGEALLIMEMBERJOB_C2S {
  string uid  = 1; //成员uid
  int32 job   = 2; //职位
}
// 改变成员职位 返回
message GAME_HD_CHANGEALLIMEMBERJOB_S2C {
  int32 job = 1; //职位
}

// 获取交易中心的资源列表
message GAME_HD_GETTRADINGRESS_C2S {
}
// 获取交易中心的资源列表返回
message GAME_HD_GETTRADINGRESS_S2C {
  repeated TradingRessInfo tradingRess = 1; //资源列表
}

// 出售资源
message GAME_HD_BAZAARSELLRES_C2S {
  int32 sellType      = 1; //卖出资源类型
  int32 sellCount     = 2; //卖出资源数量
  int32 buyType       = 3; //买资源类型
  int32 buyCount      = 4; //买资源数量
  bool onlyAlli       = 5; //是否仅盟友可见
}
// 出售资源返回
message GAME_HD_BAZAARSELLRES_S2C {
  UpdateOutPut items              = 1; //资源更新
  repeated MerchantInfo merchants = 2; //商人信息列表
}

// 取消出售资源
message GAME_HD_BAZAARCANCELSELL_C2S {
  string uid = 1; //交易uid
}
// 取消出售资源返回
message GAME_HD_BAZAARCANCELSELL_S2C {
  UpdateOutPut items              = 1; //资源更新
  repeated MerchantInfo merchants = 2; //商人信息列表
}

// 购买资源
message GAME_HD_BAZAARBUYRES_C2S {
  string uid = 1; //交易uid
}
// 购买资源返回
message GAME_HD_BAZAARBUYRES_S2C {
  UpdateOutPut items = 1; //资源更新
}

// 赠送资源
message GAME_HD_BAZAARGIVERES_C2S {
  string name = 1; //受赠方名字
  int32 index = 2; //受赠方位置
  int32 type  = 3; //资源类型
  int32 count = 4; //资源数量
}
// 赠送资源返回
message GAME_HD_BAZAARGIVERES_S2C {
  UpdateOutPut items              = 1; //资源更新
  repeated MerchantInfo merchants = 2; //商人信息列表
}

// 与系统置换资源
message GAME_HD_BAZAARSELLTOSYS_C2S {
  int32 sellType      = 1; //卖出资源类型
  int32 sellCount     = 2; //卖出资源数量
  int32 buyType       = 3; //买资源类型
}
// 与系统置换资源返回
message GAME_HD_BAZAARSELLTOSYS_S2C {
  UpdateOutPut items          = 1; //资源更新
  int32 todayReplacementCount = 2; //每日置换次数
}

// 发送邮件
message GAME_HD_SENDMAIL_C2S {
  string title        = 1; //标题
  string content      = 2; //内容
  int32 receiverType  = 3; //接收类型 0系统 1玩家 2联盟
  string receiver     = 4; //接收者
}
// 发送邮件返回
message GAME_HD_SENDMAIL_S2C {
}

// 获取邮件列表
message LOBBY_HD_GETMAILS_C2S {
}
// 获取邮件列表返回
message LOBBY_HD_GETMAILS_S2C {
  repeated MailInfo list = 1; //邮件列表
}

// 读邮件
message LOBBY_HD_READMAIL_C2S {
  string uid = 1; //邮件uid
}
// 读邮件返回
message LOBBY_HD_READMAIL_S2C { 
}

// 领取道具
message LOBBY_HD_CLAIMMAILITEM_C2S {
  string uid = 1; //邮件uid
}
// 领取道具返回
message LOBBY_HD_CLAIMMAILITEM_S2C {
  UpdateOutPut rewards = 1; //资源更新
}

// 领取道具单个
message LOBBY_HD_CLAIMMAILITEMONE_C2S {
  string uid   = 1; //邮件uid
  int32 index  = 2; //奖励下标
  int32 heroId = 3; //自选英雄id
}
// 领取道具单个返回
message LOBBY_HD_CLAIMMAILITEMONE_S2C {
  UpdateOutPut rewards = 1; //资源更新
}

// 删除邮件
message LOBBY_HD_REMOVEMAIL_C2S {
  string uid = 1; //邮件uid
}
// 删除邮件返回
message LOBBY_HD_REMOVEMAIL_S2C {
}

// 删除已读邮件
message LOBBY_HD_REMOVEALLREADMAIL_C2S {
  repeated string uids = 1; //邮件uid列表
}
// 删除已读邮件返回
message LOBBY_HD_REMOVEALLREADMAIL_S2C {
}

// 兑换礼包
message LOBBY_HD_EXCHANGEGIFT_C2S {
  string code = 1;
}
// 兑换礼包返回
message LOBBY_HD_EXCHANGEGIFT_S2C {
}

// 获取皮肤物品
message LOBBY_HD_GETSKINITEMS_C2S {  
}
message LOBBY_HD_GETSKINITEMS_S2C {
  repeated SkinItem list            = 1; //皮肤物品列表
  repeated int32 unlockPawnSkinIds  = 2; //解锁的士兵皮肤列表
}

// 合成皮肤物品
message LOBBY_HD_COMPOSESKINITEMS_C2S {
  int32 id = 1;
}
message LOBBY_HD_COMPOSESKINITEMS_S2C {
  repeated SkinItem list            = 1; //皮肤物品列表
  repeated int32 unlockPawnSkinIds  = 2; //解锁的士兵皮肤列表
}

// 获取聊天列表
message GAME_HD_GETCHATS_C2S {
}
// 获取聊天列表返回
message GAME_HD_GETCHATS_S2C {
  repeated ChatInfo list              = 1;
  map<string, int64> canCloseTimeMap  = 2; //剩余可关闭的时间 私聊
}

// 添加私聊
message GAME_HD_ADDPCHAT_C2S {
  string name = 1; //昵称或UID
}
// 添加私聊返回
message GAME_HD_ADDPCHAT_S2C {
  string uid = 1; //玩家UID
}

// 删除私聊
message GAME_HD_REMOVEPCHAT_C2S {
  string channel = 1;
}
// 删除私聊返回
message GAME_HD_REMOVEPCHAT_S2C {
  string uid = 1; //最后一个聊天uid
}

// 发送聊天
message GAME_HD_SENDCHAT_C2S {
  string uid      = 1; //唯一id
  string channel  = 2; //频道
  string content  = 3; //内容
  int32 emoji     = 4; //表情id
  int32 portrayalId = 5; //画像id
  string equipUid   = 6; //装备uid
  ShareBattleInfo battleInfo = 7; //战报信息
  string replyUid   = 8; //回复uid
}
// 发送聊天返回
message GAME_HD_SENDCHAT_S2C {
  int64 bannedSurplusTime = 1;
}

// 领取任务奖励
message GAME_HD_CLAIMTASKREWARD_C2S {
  int32 id = 1; //任务id
}
// 领取任务奖励返回
message GAME_HD_CLAIMTASKREWARD_S2C {
  UpdateOutPut rewards          = 1; //资源更新
  repeated TaskInfo tasks       = 2; //任务列表
  repeated TaskInfo todayTasks  = 3; //每日任务列表
}

// 领取每日任务奖励
message GAME_HD_CLAIMTODAYTASKREWARD_C2S {
  int32 id            = 1; //任务id
  int32 treasureIndex = 2; //宝箱奖励下标
  int32 selectIndex   = 3; //选择奖励下标
}
// 领取每日任务奖励返回
message GAME_HD_CLAIMTODAYTASKREWARD_S2C {
  UpdateOutPut rewards          = 1; //资源更新
  repeated TaskInfo todayTasks  = 2; //每日任务列表
}

// 领取其他任务奖励
message GAME_HD_CLAIMOTHERTASKREWARD_C2S {
  int32 id            = 1; //任务id
  int32 treasureIndex = 2; //宝箱奖励下标
  int32 selectIndex   = 3; //选择奖励下标
}
// 领取其他任务奖励返回
message GAME_HD_CLAIMOTHERTASKREWARD_S2C {
  UpdateOutPut rewards          = 1; //资源更新
  repeated TaskInfo otherTasks  = 2; //其他任务列表
}

// 打造装备
message GAME_HD_FORGEEQUIP_C2S {
  string uid        = 1; //装备uid
  int32 lockEffect  = 2; //锁定效果
}
// 打造装备返回
message GAME_HD_FORGEEQUIP_S2C {
  UpdateOutPut cost             = 1; //资源更新
  ForgeEquipInfo currForgeEquip = 2; //强化信息
  bool nextForgeFree            = 3; //下次是否免费重铸
  int32 freeForgeCount          = 4; //免费重铸次数
}

// 打造装备立即完成
message GAME_HD_INDONEFORGE_C2S {
}
// 打造装备立即完成返回
message GAME_HD_INDONEFORGE_S2C {
  int32 gold = 1; //金币
}

// 还原重铸
message GAME_HD_RESTOREFORGE_C2S {
  string uid = 1; //装备uid
}
// 还原重铸返回
message GAME_HD_RESTOREFORGE_S2C {
  EquipInfo equip = 1; //装备信息
  int32 iron      = 2; //铁
}

// 融炼装备
message GAME_HD_SMELTINGEQUIP_C2S {
  string mainUid         = 1; //主装备
  repeated int32 viceIds = 2; //副装备列表
}
// 融炼装备 返回
message GAME_HD_SMELTINGEQUIP_S2C {
  SmeltEquipInfo currSmeltEquip = 1; //融炼信息
  int32 fixator                 = 2; //钉子
}

// 还原融炼装备
message GAME_HD_RESTORESMELTEQUIP_C2S {
  string mainUid = 1; //主装备
}
message GAME_HD_RESTORESMELTEQUIP_S2C {
  EquipInfo equip = 1; //装备信息
}

// 获取玩家排行榜
message GAME_HD_GETPLAYERRANKLIST_C2S {
}
// 获取玩家排行榜返回
message GAME_HD_GETPLAYERRANKLIST_S2C {
  repeated PlayerRankInfo list  = 1; //排行列表
  int32 no                      = 2; //自己排名
}

// 获取联盟排行榜
message GAME_HD_GETALLIRANKLIST_C2S {
}
// 获取联盟排行榜返回
message GAME_HD_GETALLIRANKLIST_S2C {
  repeated AlliRankInfo list = 1; //排行列表
}

// 获取玩家积分排行榜
message GAME_HD_GETPLAYERSCORELIST_C2S {
  int32 type = 1;
}
// 获取玩家积分排行榜返回
message GAME_HD_GETPLAYERSCORELIST_S2C {
  repeated PlayerScoreInfo list = 1; //排行列表
  MeScoreInfo me                = 2; //自己排名信息
  int32 no                      = 3; //自己排名
}

// 购买添加产量
message GAME_HD_BUYADDOUTPUT_C2S {
  int32 type = 1; //资源类型
}
// 购买添加产量返回
message GAME_HD_BUYADDOUTPUT_S2C {
  map<int32, int32> addOutputSurplusTime  = 1; //增加产出剩余时间 k=>资源id v=>剩余时间
  UpdateOutPut output                     = 2; //产出信息
  int32 gold                              = 3; //金币
}

// 选择研究
message GAME_HD_STUDYSELECT_C2S {
  int32 lv    = 1; //建筑等级
  int32 id    = 2; //id
  int32 tp    = 3; //类型
}
message GAME_HD_STUDYSELECT_S2C {
  map<int32, CeriSlotInfo> slots = 1; //研究槽位map k=>建筑等级 v=>研究槽位信息
}

// 重置研究选择
message GAME_HD_CERIRESETSELECT_C2S {
  int32 lv = 1; //槽位等级
  int32 tp = 2; //类型
}
// 重置研究选择返回
message GAME_HD_CERIRESETSELECT_S2C {
  int32 gold                = 1; //金币
  repeated int32 selectIds  = 2; //选择列表
  int32 resetCount          = 3; //重置次数
  bool useGold              = 4; //是否使用金币
}

// 回放数据
message GAME_HD_BATTLEPLAYBACK_C2S {
  BattleRecordInfo record = 1;  //回放数据
  int32 speed             = 2;  //速度
}
message GAME_HD_BATTLEPLAYBACK_S2C {  
}

// 获取玩家指定日期的战斗积分数据
message GAME_HD_GETALLIBATTLERECORD_C2S {
  string date = 1; //日期
}
message GAME_HD_GETALLIBATTLERECORD_S2C {
  repeated BattleScoreRecordByDate records = 1; //当天的数据总和
}

// 获取指定联盟成员的全部战斗记录
message GAME_HD_GETALLIMEMBERBATTLERECORD_C2S {
  string uid = 1; //日期
}
message GAME_HD_GETALLIMEMBERBATTLERECORD_S2C {
  BattleScoreRecordByDate record = 1; //数据总和
}

// 联盟选择政策
message GAME_HD_ALLISELECTPOLICY_C2S {
  int32 index = 1; //位置
  int32 id    = 2; //政策id
}
message GAME_HD_ALLISELECTPOLICY_S2C {
}

// 获取市场交易价格信息
message GAME_HD_GETTRADEPRICE_C2S {
}
message GAME_HD_GETTRADEPRICE_S2C {
  repeated TradePriceInfo tradePrice  = 1; //市场交易价格信息
}

// 设置军队行军速度
message GAME_HD_SETARMYSPEED_C2S {
  string uid        = 1; // 军队Uid
  int32 index       = 2; // 坐标
  int32 marchSpeed  = 3; // 行军速度
}
message GAME_HD_SETARMYSPEED_S2C {
  int32 marchSpeed  = 1; // 行军速度
}

// 获取联盟日志
message GAME_HD_GETALLILOGS_C2S {
}
message GAME_HD_GETALLILOGS_S2C {
  repeated AllienceLogInfo list = 1; //日志列表
}

// 改变申请说明
message GAME_HD_CHANGEALLIAPPLYDESC_C2S {
  string applyDesc = 1;
}
message GAME_HD_CHANGEALLIAPPLYDESC_S2C {
}

// 创建联盟聊天副频道
message GAME_HD_CREATEALLICHATCHANNEL_C2S {
  string name  = 1; //名字
  string color = 2; //颜色
  repeated string memberUids = 3; //成员uid列表
  bool memberFilter          = 4; //是否开启成员过滤
}
message GAME_HD_CREATEALLICHATCHANNEL_S2C {
  AlliChannelInfo info = 1;
}

// 删除联盟聊天副频道
message GAME_HD_DELALLICHATCHANNEL_C2S {
  string uid = 1;
}
message GAME_HD_DELALLICHATCHANNEL_S2C {
}

// 更新联盟副频信息
message GAME_HD_UPDATEALLICHATCHANNEL_C2S {
  string uid                 = 1;
  repeated string memberUids = 2; //成员uid列表
  bool memberFilter          = 3; //是否开启成员过滤
}
message GAME_HD_UPDATEALLICHATCHANNEL_S2C {
}

// 盟主投票
message GAME_HD_VOTEALLILEADER_C2S {
  string uid = 1; //支持当盟主的玩家uid
}
message GAME_HD_VOTEALLILEADER_S2C {
  int32 voteTimes = 1; //投票次数
}

// 盟主确认
message GAME_HD_ALLILEADERCONFIRM_C2S {
  string name = 1; //联盟名字
  int32 icon  = 2; //图标
}
message GAME_HD_ALLILEADERCONFIRM_S2C {
}

// 古城捐献资源
message GAME_HD_ANCIENTCONTRIBUTE_C2S {
  int32 index     = 1; //位置
  int32 type      = 2; //捐献类型 0升级 1加速
  int32 resType   = 3; //资源类型
  int32 count     = 4; //资源数量
}
message GAME_HD_ANCIENTCONTRIBUTE_S2C {
  UpdateOutPut cost      = 1; //资源消耗
  int32 curContribute    = 2; //当前捐献标记
  int32 ctbSurplusCount  = 3; //捐献剩余次数
  int32 ctbCdSurplusTime = 4;//捐献cd剩余时间 
}

// 获取古城信息
message GAME_HD_GETANCIENTINFO_C2S {
}
message GAME_HD_GETANCIENTINFO_S2C {
  repeated AncientCityInfo list = 1;
}

// 获取古城捐献记录
message GAME_HD_GETANCIENTLOGS_C2S {
  int32 index = 1; //古城位置
}
message GAME_HD_GETANCIENTLOGS_S2C {
  repeated AncientContributeLog list = 1; //捐献记录列表
}

// 获取古城捐献统计
message GAME_HD_GETANCIENTDONATEACC_C2S {
  int32 index = 1; //古城位置
}
message GAME_HD_GETANCIENTDONATEACC_S2C {
  repeated AncientContributeAccInfo list = 1; //捐献统计
}

// 供奉英雄
message GAME_HD_WORSHIPHERO_C2S {
  int32 index = 1;
  int32 id    = 2;
}
message GAME_HD_WORSHIPHERO_S2C {
  HeroSlotInfo slot                   = 1;
  repeated int32 unlockPawnIds        = 2; //当前额外解锁的兵种列表
  map<int32, CeriSlotInfo> pawnSlots  = 3; //士兵研究槽位map k=>建筑等级 v=>槽位信息
}

// 改变士兵画像
message GAME_HD_CHANGEPAWNPORTRAYAL_C2S {
  int32 index       = 1; //位置
  string armyUid    = 2; //军队uid
  string uid        = 3; //士兵uid
  int32 portrayalId = 4; //画像id
}
// 改变士兵画像
message GAME_HD_CHANGEPAWNPORTRAYAL_S2C {
  HeroSlotInfo slot = 1;
}

// 观战
message GAME_HD_SPECTATE_C2S {
  int32 sid         = 1; //游戏服id 
  bool isReconnect  = 2; //是否重连
  string version    = 3; //版本
  string friendUid  = 4; //好友uid
}
message GAME_HD_SPECTATE_S2C {
  EnterGameRst rst                  = 1; //进入游戏返回数据
  repeated int32 canCreateAreaCount = 2; //可以创建主城的区域数量
  string clientVersion              = 3; //要求客户端版本
  string clientBigVersion           = 4; //客户端大版本
}

// 个人标记
message GAME_HD_MAPMARKPOINT_C2S {
  string desc = 1; //描述
  int32 index = 2; //位置
  int32 flag  = 3; //标记
}
message GAME_HD_MAPMARKPOINT_S2C {
}

// 删除个人标记
message GAME_HD_REMOVEMAPMARK_C2S {
   int32 index = 1; //位置
}
message GAME_HD_REMOVEMAPMARK_S2C {
}

// 获取防作弊题目
message GAME_HD_GETANTICHEATQUESTION_C2S {
}
message GAME_HD_GETANTICHEATQUESTION_S2C {
  string item            = 1; //特征
  repeated int32 options = 2; //选项
  int32 surplusTime      = 3; //剩余时间
}

// 防作弊答题
message GAME_HD_ANTICHEATANSWER_C2S {
  int32 answer = 1; //图片id
}
message GAME_HD_ANTICHEATANSWER_S2C {
  bool rst           = 1; //回答结果
  int32 wrongCount   = 2; //答错次数
  int32 notPassCount = 3; //未通过次数
  int32 surplusTime  = 4; //剩余时间
}

// 获取玩家士兵等级阵亡数据
message GAME_HD_GETPAWNDEADLVMAP_C2S {
}
message GAME_HD_GETPAWNDEADLVMAP_S2C {
  map<int32, int32> pawnDeadLvMap = 1; //士兵等级阵亡map
}

// 获取全局随机信息
message GAME_HD_GETWORLDRANDOMINFO_C2S {
}
message GAME_HD_GETWORLDRANDOMINFO_S2C {
  map<int32, Int32ArrayInfo> exclusiveMap = 1; //专武随机属性map
  map<int32, int32> pawnCostMap           = 2; //士兵资源消耗map
}

// 获取世界事件
message GAME_HD_GETWORLDEVENT_C2S {
}
message GAME_HD_GETWORLDEVENT_S2C {
  map<int32, string> eventMap = 1; //世界事件map
}

//-----------------------------聊天服协议------------------------------------

// 获取聊天列表
message CHAT_HD_GETCHATS_C2S {
  string channel = 1; //频道
}
// 获取聊天列表返回
message CHAT_HD_GETCHATS_S2C {
  repeated LobbyChatInfo list = 1;
}

// 获取所有图鉴信息
message CHAT_HD_GETGALLERYINFO_C2S {
}
message CHAT_HD_GETGALLERYINFO_S2C {
  repeated GalleryInfo list = 1; //图鉴列表
}

// 获取指定图鉴评论信息
message CHAT_HD_GETGALLERYCOMMENTS_C2S {
  int32 id    = 1; //配置id
  int32 type  = 2; //类型
}

message CHAT_HD_GETGALLERYCOMMENTS_S2C {
  repeated GalleryCommentInfo list  = 1; //评论列表
  GalleryInfo galleryInfo           = 2;
}

// 发表图鉴评论
message CHAT_HD_COMMENTGALLERY_C2S {
  int32 id        = 1; //配置id
  int32 type      = 2; //类型
  int32 star      = 3; //星数
  string content  = 4; //评论
  string version  = 5; //版本
}
message CHAT_HD_COMMENTGALLERY_S2C {
  GalleryCommentInfo comment  = 1;
  GalleryInfo galleryInfo     = 2;
}

// 图鉴评论点赞
message CHAT_HD_GALLERYCOMMENTPRAISE_C2S {
  int32 id                    = 1; //图鉴配置id
  int32 type                  = 2; //图鉴类型
  map<string, int32> praises  = 3; //点赞类型
}
message CHAT_HD_GALLERYCOMMENTPRAISE_S2C {
}

// 获取邮件列表
message MAIL_HD_GETMAILS_C2S {
  int32 sid = 1;
}
// 获取邮件列表返回
message MAIL_HD_GETMAILS_S2C {
  repeated MailInfo list = 1; //邮件列表
}

// 读邮件
message MAIL_HD_READMAIL_C2S {
  string uid = 1; //邮件uid
}
// 读邮件返回
message MAIL_HD_READMAIL_S2C { 
}

// 领取道具
message MAIL_HD_CLAIMMAILITEM_C2S {
  string uid = 1; //邮件uid
}
// 领取道具返回
message MAIL_HD_CLAIMMAILITEM_S2C {
  UpdateOutPut rewards = 1; //资源更新
}

// 领取道具单个
message MAIL_HD_CLAIMMAILITEMONE_C2S {
  string uid   = 1; //邮件uid
  int32 index  = 2; //奖励下标
  int32 heroId = 3; //自选英雄id
}
// 领取道具单个返回
message MAIL_HD_CLAIMMAILITEMONE_S2C {
  UpdateOutPut rewards = 1; //资源更新
}

// 删除邮件
message MAIL_HD_REMOVEMAIL_C2S {
  string uid = 1; //邮件uid
}
// 删除邮件返回
message MAIL_HD_REMOVEMAIL_S2C {
}

// 删除已读邮件
message MAIL_HD_REMOVEALLREADMAIL_C2S {
  repeated string uids = 1; //邮件uid列表
}
// 删除已读邮件返回
message MAIL_HD_REMOVEALLREADMAIL_S2C {
}

// 获取自定义房间列表
message MATCH_HD_GETCUSTOMROOMS_C2S {
}
message MATCH_HD_GETCUSTOMROOMS_S2C {
  repeated CustomRoomInfo list = 1;
}

// 获取自定义房间信息
message MATCH_HD_GETCUSTOMROOMINFO_C2S {
  int32 sid = 1;
}
message MATCH_HD_GETCUSTOMROOMINFO_S2C {
  CustomRoomInfo info = 1;
}

// 开启自定义区服
message MATCH_HD_OPENCUSTOMROOMINFO_C2S {
  int32 sid = 1;
}
message MATCH_HD_OPENCUSTOMROOMINFO_S2C {
}

//-----------------------------数据结构------------------------------------

// int32数组
message Int32ArrayInfo { 
  repeated int32 arr = 1;
}

// 通用属性数组
message AttrArrayInfo {
  repeated int32 attr = 1;
}

// int32键值对map
message Mapint32Info {
  map<int32, int32> data = 1;
}

// 玩家数据
message UserInfo {
  string uid                              = 1;  //uid
  int32 sid                               = 2;  //所选服务器id 用于判断是否在大厅界面
  string loginType                        = 3;  //登录方式
  int32 gold                              = 4;  //金币数量
  repeated int32 totalGameCount           = 5;  //总局数
  int32 loginDayCount                     = 6;  //登录天数
  int32 cloginDayCount                    = 7;  //连续登录天数
  string nickname                         = 8;  //昵称
  string headIcon                         = 9;  //头像
  int32 modifyNameCount                   = 10; //修改昵称次数
  repeated string unlockHeadIcons         = 11; //解锁的头像列表
  repeated int32 unlockPawnSkinIds        = 12; //解锁的士兵皮肤列表
  repeated int32 unlockChatEmojiIds       = 13; //解锁的聊天表情列表
  repeated TitleInfo titles               = 14; //解锁的称号列表
  repeated InviteFriendInfo inviteFriends = 15; //我邀请的好友列表
  repeated GuideInfo guides               = 16; //新手引导列表
  int32 wheelCurrCount                    = 17; //每日已转动次数
  int32 wheelFreeCount                    = 18; //转盘免费次数
  int32 wheelTime                         = 19; //转盘等待时间
  bool hasWheelAward                      = 20; //是否有转盘奖励未领取
  bool carryNoviceData                    = 21; //是否携带新手村数据
  repeated OrderInfo notFinishOrders      = 22; //已支付未完成的订单
  int32 checkPraiseCount                  = 23; //记录触发好评弹窗x值
  int32 maxLandCount                      = 24; //历史最大领地
  int64 createTime                        = 25; //创建时间
  repeated int32 applyServers             = 26; //已报名区服
  int32 maxWheelMul                       = 27; //历史最大转动倍数
  int32 rankScore                         = 28; //段位分
  int32 logoutSurplusTime                 = 29; //注销剩余时间
  repeated int32 totalRankCount           = 30; //排位次数
  repeated int32 offlineNotifyOpt         = 31; //离线推送设置
  repeated UserSubInfo subData            = 32; //用户订阅数据
  repeated BlacklistInfo blacklists       = 33; //黑名单
  repeated FriendInfo friendsList         = 34; //好友列表
  repeated FriendApplyInfo friendsApplys  = 35; //好友申请列表
  int32 rankCoin                          = 36; //段位积分
  int32 battleForecastCount               = 37; //战斗预测次数
  string personalDesc                     = 38; //个人简介
  int32 playSid                           = 39; //正在玩的服务器id 用于显示大厅服务器
  string teamUid                          = 40; //所在队伍uid
  repeated int32 totalNewbieCount         = 41; //新手总局数
  repeated int32 totalFreeCount           = 42; //自由总局数
  int32 giveupCount                       = 43; //放弃次数
  int32 title                             = 44; //称号
  int32 ingot                             = 45; //元宝
  int32 warToken                          = 46; //兵符
  repeated PortrayalInfo portrayals       = 47; //画像列表
  int32 todaySendTrumpetCount             = 48; //每日发送喇叭次数
  int64 buyOptionalHeroSurplusTime        = 49; //购买自选礼包剩余时间
  int64 sumOnlineTime                     = 50; //累计在线时间
  map<string, int32> rechargeCountRecord  = 51; //充值次数记录
  int32 expectPosition                    = 52; //期望位置
  int64 buyFreeGoldSurplusTime            = 53; //购买免费金币剩余时间
  int32 passNewbieIndex                   = 54; //第X局通关了新手区
  int64 accLikeJwmCount                   = 55; //累计点赞九万亩次数
  repeated SkinItem skinItemList          = 56; //皮肤物品列表
  string sessionId                        = 57; //每次登录唯一id
  repeated int32 unlockCitySkinIds        = 58; //解锁的城市皮肤列表
  int32 rankSeason                        = 59; //当前赛季
  map<int32, int32> activityRecord        = 60; //活动记录map
  repeated int32 rankRewardRecord         = 61; //排位奖励领取记录
  PlantInfo plantData                     = 62; //种植信息
  repeated BotanyInfo unlockBotanys       = 63; //解锁的植物
  int32 farmType                          = 64; //开荒方式
  int32 signDays                          = 65; //签到天数
}

// 称号数据
message TitleInfo {
  int32 id          = 1; //称号id
  int64 time        = 2; //获取时间
  int64 remainTime  = 3; //剩余时间
}

// 邀请好友数据
message InviteFriendInfo {
  string uid    = 1; //玩家id
  int32 useType = 2; //邀请方式
}

// 新手引导数据
message GuideInfo {
  int32 id    = 1; //引导id
  string tag  = 2; //标记
}

// 订阅数据
message UserSubInfo {
  string orderUid     = 1; //订单号
  string productId    = 2; //商品id
  int64 surplusTime   = 3; //剩余时间
  string currencyType = 4; //货币类型
  float payAmount     = 5; //支付金额
  bool isOneTime      = 6; //是否一次性购买
  int64 lastAwardTime = 7; //上一次领奖时间
}

// 订阅查询数据
message SubReqInfo {
  string orderId   = 1; //外部订单号
  string productId = 2; //商品id
  string token     = 3; //购买时的令牌
}

// 黑名单信息
message BlacklistInfo {
  string uid      = 1;
  string nickname = 2;
  string headIcon = 3;
  int64 time      = 4;
}
// 好友信息
message FriendInfo {
  string uid            = 1;
  string nickname       = 2;
  string headIcon       = 3;
  int64 time            = 4; //时间
  int64 offlineTime     = 5; //离线时间
  string noteName       = 6; //备注名
  int32 notReadCount    = 7; //未读消息数量
  int32 playSid         = 8; //当前正在玩的区
  repeated FriendGift giftList = 9; //礼物列表
  ChatInfo lastChatInfo       = 10; //最后一条聊天记录
}

// 好友申请信息
message FriendApplyInfo {
  string uid            = 1;
  string nickname       = 2;
  string headIcon       = 3;
  int64 time            = 4; //时间
  int32 playSid         = 5; //当前正在玩的区
}

// 队伍信息
message TeamInfo {
  string uid                              = 1; //队伍uid
  int32 roomType                          = 2; //游戏服类型
  int32 playSid                           = 3; //当前游玩的服务器id
  int32 cancelApplySurplusTime            = 4; //取消报名剩余时间
  repeated TeamUserInfo userList          = 5; //玩家列表
  repeated TeamInviteUserInfo inviteList  = 6; //邀请的玩家信息
  int64 matchOpenTime                     = 7; //匹配完成的开服时间
  bool independent                        = 8; //是否为独立队伍 独立队伍不参与自动匹配联盟
}

// 队伍玩家信息
message TeamUserInfo {
  string uid      = 1; //玩家uid
  string nickname = 2; //昵称
  string headIcon = 3; //头像
  int32 job       = 4; //职位 0普通 1队长
  int32 expectPosition  = 5; //期望位置
  int32 farmType        = 6; //开荒模式
}

// 队伍邀请信息
message TeamInviteInfo {
  string teamUid  = 1; //队伍uid
  string uid      = 2; //玩家uid
  string nickname = 3; //昵称
  string headIcon = 4; //头像
  int64 time      = 5; //邀请时间
}

// 邀请的玩家信息
message TeamInviteUserInfo {
  string uid      = 1; //玩家uid
  string nickname = 2; //昵称
  string headIcon = 3; //头像
}

// 订阅发布信息
message PublishInfo {
  string topic  = 1; //订阅主题
  bytes data    = 2; //数据
}

// 游戏服数据
message ServerInfo {
  int32 id         = 1; //id
  int64 createTime = 2; //创建时间
  int32 winType    = 3; //获胜类型 1.玩家 2.联盟
  string winName   = 4; //获胜方名字
  int64 endTime    = 5; //结束时间
  bool isClose     = 6; //是否关闭
  int32 pers       = 7; //人数
  int32 persCap    = 8; //剩余人数容量
  int32 surplusDay = 9; //剩余天数
  int64 closeTime  = 10;//关服时间
  string owner     = 11;//当前玩家昵称
  int32 state      = 12;//服务器状态
  string clientVersion    = 13; //要求客户端版本
  string clientBigVersion = 14; //客户端大版本
  int32 type              = 15; //区类型
  int32 surplusOpenTime   = 16; //剩余开启时间
  repeated int32 canCreateAreaCount = 17; //可以选择的区域人数
  int32 cancelCD          = 18; //取消cd
  bool isGiveupGame       = 19; //是否已经放弃对局
  int32 winCellCount      = 20; //胜利时的地块数
  int32 winCondType       = 21; //胜利条件类型 1.领地争夺 2.修建遗迹
}

// 游戏服数据列表
message ServerList {
  repeated ServerInfo list     = 1; //服务器列表
}

// 历史对局记录
message GameHistory {
  int32 id                    = 1; //id
  int64 createTime            = 2; //创建时间
  int32 winType               = 3; //获胜类型 1.玩家 2.联盟
  string winName              = 4; //获胜方名字
  int64 endTime               = 5; //结束时间
  int32 winCondType           = 6; //胜利条件类型 1.领地争夺 2.修建遗迹
  repeated GameOverWinnerInfo winners = 7; //胜利者信息列表
  GameOverWinAlliInfo winAlliInfo     = 8; //获胜联盟信息
  int32 landCount                     = 9; //地块数
}

// 游戏结束获胜者信息
message GameOverWinnerInfo {
  string uid        = 1;
  string name       = 2; //名字
  string headicon   = 3; //头像
  int32 occupyCount = 4; //攻陷次数
  int32 score       = 5; //积分
  int32 job         = 6; //职位
  int32 killPawn    = 7; //击杀士兵数量
  int32 deadPawn    = 8; //阵亡士兵数量
}

// 游戏结束获胜联盟信息
message GameOverWinAlliInfo {
  string uid        = 1;
  string name       = 2; //名字
  int32 headicon    = 3; //头像    
}

// 进入游戏返回数据
message EnterGameRst {
  int32 sid         = 1; //游戏服id 
  MapSize mapSize   = 2; //地图大小数据
  PlayerInfo player = 3; //游戏服玩家数据
  WorldInfo world   = 4; //世界数据
  repeated int32 canBattleTime          = 5; //交战时间
  bool guestCanCreateAlli               = 6; //游客是否可创建联盟
  map<int32, int32> alliPolicySlotConf  = 7; //联盟政策槽位配置
}

// 地图大小数据
message MapSize {
  int32 x = 1; // x轴
  int32 y = 2; // y轴
}

// 携带的士兵数据
message NovicePawnData {
  int32 id                  = 1; //id
  int32 lv                  = 2; //等级
  Vec2 point                = 3; //位置
  repeated int32 treasures  = 4; //宝箱
}

// 通用地图对象数据
message MapObjData {
  int32 id    = 1; //id
  int32 lv    = 2; //等级
  Vec2 point  = 3; //位置
}

// 军队数据
message ArmyData {
  string name                   = 1; //军队名字
  repeated NovicePawnData pawns = 2; //士兵列表
}

// 游戏服玩家数据
message PlayerInfo {
  string uid              = 1; //玩家uid
  int64 sumOnlineTime     = 2; //累计在线时间
  int32 expBook           = 3; //经验书
  int32 iron              = 4; //铁
  int32 upScroll          = 5; //卷轴
  int32 fixator           = 6; //固定器
  int32 granaryCap        = 7; //粮食容量
  int32 warehouseCap      = 8; //仓库容量
  OutPutInfo cereal       = 9; //粮食
  OutPutInfo timber       = 10; //木头
  OutPutInfo stone        = 11; //石头
  double cerealConsume    = 12; //粮耗
  int32 mainCityIndex     = 13; //主城位置
  CaptureInfo captureInfo = 14; //被沦陷的信息
  repeated AreaBuildInfo builds   = 15; //建筑信息
  string allianceUid              = 16; //所在联盟
  repeated int32 unlockPawnIds    = 18; //当前额外解锁的兵种列表
  repeated int32 unlockEquipIds   = 19; //当前额外解锁的装备列表
  repeated BTInfo btQueues        = 20; //建造队列
  map<string, DrillPawnInfoList> pawnDrillQueues  = 21; //士兵训练队列map k=>训练建筑uid v=>训练队列
  repeated PawnLvingInfo pawnLevelingQueues       = 22; //士兵练级队列
  repeated AreaDistInfo armyDists                 = 23; //军队分布
  repeated MerchantInfo merchants                 = 24; //商人列表
  repeated TaskInfo guideTasks                    = 25; //新手任务
  repeated TaskInfo todayTasks                    = 26; //每日任务
  repeated EquipInfo equips                       = 27; //装备
  ForgeEquipInfo currForgeEquip                   = 28; //当前强化装备信息
  map<int32, PawnConfigInfo> configPawnMap        = 29; //士兵装备配置map k=>士兵id v=>装备配置
  repeated ForAutoSupport fortAutoSupports        = 30; //要塞自动支援配置列表
  map<int32, int32> addOutputSurplusTime          = 31; //增加产出剩余时间 k=>资源id v=>剩余时间
  map<string, string> hidePChatChannels           = 33; //隐藏的私聊频道
  map<int32, CeriSlotInfo> policySlots            = 34; //政策map k=>建筑等级 v=>政策槽位信息
  int32 resetPolicyNeedGold             = 35; //当前重置政策需要的金币
  int32 exitAllianceCount               = 36; //退出联盟次数
  bool hasNewTreasure                   = 37; //是否有新宝箱
  int32 todayOccupyCellCount            = 38; //每日占领地块数量
  int64 accTotalGiveResCount            = 39; //累计赠送资源数
  int32 todayReplacementCount           = 40; //每日置换次数
  int32 upRecruitPawnCount              = 41; //加速招募士兵数量
  int32 stamina                         = 42; //奖励点
  int32 landScore                       = 43; //领地分数
  map<int32, OccupyLandCountInfo> occupyLandCountMap = 44; //攻占的野地数
  SmeltEquipInfo currSmeltEquip         = 45; //当前强化装备信息
  int32 smelCount                       = 46; //融炼次数
  repeated TaskInfo otherTasks          = 47; //其他任务
  repeated HeroSlotInfo heroSlots       = 48; //英雄列表
  map<int32,int32> killRecordMap        = 49; //击杀记录
  map<int32, MapMarkInfo> mapMarks      = 50; //地图标记map
  int32 reCreateMainCityCount           = 51; //重新创建主城次数
  int32 cellTondenCount                 = 52; //当天已屯田次数
  repeated InjuryPawnInfo injuryPawns   = 53; //医馆受伤士兵列表
  repeated PawnCuringInfo curingQueues  = 54; //士兵治疗队列
  map<int32, CeriSlotInfo> equipSlots   = 55; //装备研究槽位map k=>建筑等级 v=>槽位信息
  map<int32, CeriSlotInfo> pawnSlots    = 56; //士兵研究槽位map k=>建筑等级 v=>槽位信息
  int32 freeRecruitPawnCount            = 57; //已免费招募士兵数量
  int32 freeLevingPawnCount             = 58; //已免费训练士兵数量
  int32 freeCurePawnCount               = 59; //已免费治疗士兵数量
  int32 freeForgeCount                  = 60; //已免费打造/重铸数量
  bool isSettled                        = 61; //是否已结算
  int32 maxOccupyLandDifficulty         = 62; //历史最大攻占野地难度
}

//
message OccupyLandCountInfo {
  repeated int32 arr = 1;
}

// 资源产出信息
message OutPutInfo {
  int32 value  = 1; //当前值
  int32 opHour = 2; //小时产
}

// 沦陷信息
message CaptureInfo {
  string uid      = 1; //玩家uid
  int64 time      = 2; //时间
  string attacker = 3; //进攻者
}

// 建筑信息
message AreaBuildInfo {
  int32 index = 1; //所在区域位置
  string uid  = 2; //唯一id
  Vec2 point  = 3; //坐标
  int32 id    = 4; //配置id
  int32 lv    = 5; //等级
}

// 建筑信息
message AreaBuildPointInfo {
  string uid  = 1; //唯一id
  Vec2 point  = 2; //坐标
}

// 建造信息
message BTInfo {
  int32 index       = 1; //区域位置
  string uid        = 2; //建筑uid
  int32 id          = 3; //建筑id
  int32 lv          = 4; //要建造的等级
  int32 needTime    = 5; //开始时间
  int32 surplusTime = 6; //剩余时间
}

// 训练士兵信息
message DrillPawnInfo {
  string uid        = 1; //唯一id
  int32 index       = 2; //区域位置
  string buid       = 3; //建筑uid
  string auid       = 4; //军队uid
  int32 id          = 5; //士兵id
  int32 lv          = 6; //训练等级
  int32 needTime    = 7; //需要时间
  int32 surplusTime = 8; //剩余时间
}

// 训练士兵信息列表
message DrillPawnInfoList {
  repeated DrillPawnInfo list = 1;
}

// 士兵练级信息
message PawnLvingInfo {
  string uid        = 1; //唯一id
  int32 index       = 2; //区域位置
  string buid       = 3; //建筑uid
  string auid       = 4; //军队uid
  string puid       = 5; //士兵uid
  int32 id          = 6; //士兵id
  int32 lv          = 7; //训练等级
  int32 needTime    = 8; //需要时间
  int32 surplusTime = 9; //剩余时间
}

// 军队分布信息
message AreaDistInfo {
  int32 index                 = 1; //位置
  repeated AreaArmyInfo armys = 2; //军队
}

// 商人信息
message MerchantInfo {
  int32 state = 1; //状态
}

// 装备信息
message EquipInfo {
  string uid                        = 1; //装备uid
  int32 id                          = 2; //装备id
  repeated AttrArrayInfo attrs      = 3; //装备属性
  repeated AttrArrayInfo lastAttrs  = 4; //上一次重铸属性
  int32 recastCount                 = 5; //重铸次数
  bool nextForgeFree                = 6; //下次是否免费重铸
}

// 强化装备信息
message ForgeEquipInfo {
  string uid        = 1; //装备uid
  int32 needTime    = 2; //需要时间
  int32 surplusTime = 3; //剩余时间
  int32 lockEffect  = 4; //锁定效果
}

// 融炼装备信息
message SmeltEquipInfo {
  string uid                        = 1; //主装备
  repeated AttrArrayInfo viceAttrs  = 2; //熔炼信息
  int32 needTime                    = 3; //需要时间
  int32 surplusTime                 = 4; //剩余时间
}

// 熔炼信息
message SmeltInfo {
  int32 id             = 1; //熔炼材料装备id
  repeated int32 attrs = 2; //熔炼属性
}

// 士兵装备配置
message PawnConfigInfo {
  string equipUid   = 1; //装备id
  int32 skinId      = 2; //皮肤id
  int32 attackSpeed = 3; //出手速度
}

// 要塞自动支援配置信息
message ForAutoSupport {
  int32 index = 1; //位置
  bool val    = 2; //是否支援
}

// 研究槽位信息
message CeriSlotInfo {
  repeated int32 selectIds = 1; //选择列表
  int32 id                 = 2; //当前选择的id
  int32 resetCount         = 3; //重置次数
  int32 lv                 = 4; //对应建筑等级
}

// 世界数据
message WorldInfo {
  map<string, PlayerCellBytesInfo> cells    = 1; //单元格map k=>玩家uid v=>信息
  map<string, TempPlayerInfo> players       = 2; //玩家数据map k=>uid v=>数据
  int32 mapId                               = 3; //地图id
  int64 runTime                             = 4; //运行时间
  GameOverInfo gameOver                     = 5; //游戏结束信息
  repeated int64 winCond                    = 6; //胜利条件 [type,value,time] 1.领地争夺 2.修建遗迹
  SeasonInfo season                         = 7; //季节信息
  int32 captureNum                          = 8; //已沦陷人数
}

// 单元格信息
message CellInfo {
  int32 index               = 1; //位置
  string owner              = 2; //拥有者
  int32 cityId              = 3; //城市ID
  TempPlayerInfo player     = 4; //主城携带玩家信息
}

// 玩家拥有的单元格信息
message PlayerCellInfo {
  repeated PlayerCell cells = 1; //单元格信息
}

// 玩家单元格信息
message PlayerCell {
  int32 index           = 1; //位置
  bytes cityByteId      = 2; //城市ID
}

// 玩家拥有的单元格信息
message PlayerCellBytesInfo {
  bytes indexs1 = 1; //位置 19-19 矩形地块
  bytes indexs2 = 2; //位置 19 单个地块
  bytes cities  = 3; //城市ID 19+8
}

// 玩家临时信息
message TempPlayerInfo {
  string uid                    = 1; //玩家id
  int32 mainCityIndex           = 2; //主城位置
  string nickname               = 3; //昵称
  string headIcon               = 4; //头像
  string allianceUid            = 5; //所在联盟
  string allianceName           = 6; //所在联盟名字
  map<int32, int32> towerLvMap  = 7; //当前玩家的箭塔等级对应士兵id
  map<int32, int32> policys     = 8; //当前政策
  int32 title                   = 9; //当前佩戴的称号
  int32 rodeleroCadetLv         = 10;//见习勇士 层数
  int32 allianceIcon            = 11; //所在联盟图标
  int32 maxLandCount            = 12; //历史最大地块数
  bool isGiveupGame             = 13; //是否放弃了对局
  string personalDesc           = 14; //个人简介
  map<int32, TypeObjList> cityOutputMap = 15; //城市产出
  int32 spectateIndex           = 16; //所观战玩家的主城
  bool isSettled                = 17; //是否结算
  int32 farmType                = 18; //开荒模式
}

// 游戏结束信息
message GameOverInfo {
  int32 winType       = 1; //获胜类型 1.玩家 2.联盟
  string winUid       = 2; //获胜方uid
  string winName      = 3; //获胜方名字
  int64 closeTime     = 4; //结束时间
  int64 runTime       = 5; //运行时间
  int32 winCellCount  = 6; //胜利时的地块数
  repeated int32 ancientInfo = 7; //遗迹信息[id,lv]
}

// 季节信息
message SeasonInfo {
  int32 type = 1; //当前季节 0.春 1.夏 2.秋 3.冬
  int64 nextSeasonSurplusTime = 2; //下一个季节剩余时间
  map<int32, int32> policys   = 3; //季节政策
}

// 军队信息
message AreaArmyInfo {
  int32 index                       = 1; //所在区域位置
  string uid                        = 2; //唯一id
  string name                       = 3; //名字
  repeated AreaPawnInfo pawns       = 4; //士兵列表
  repeated int32 drillPawns         = 5; //招募中的士兵列表
  int32 state                       = 6; //状态
  int32 enterDir                    = 7; //进入方向
  string owner                      = 8; //拥有者
  int32 marchSpeed                  = 9; //行军速度
  repeated InjuryPawnInfo curingPawns  = 10; //治疗中的士兵列表
}

// 宝箱信息
message TreasureInfo {
  string uid                = 1; //唯一id
  int32 id                  = 2; //配置id
  repeated TypeObj rewards  = 3; //奖励
}

// 宝箱简短信息
message TreasureBaseInfo {
  string uid                = 1; //唯一id
  int32 id                  = 2; //配置id
}

// 士兵宝箱信息
message PawnTreasureInfo {
  string uid = 1; //士兵uid
  repeated TreasureInfo treasures = 2; //宝箱列表
}

// 军队宝箱信息
message ArmyTreasuresInfo {
  string armyUid                          = 1; //军队uid
  repeated PawnTreasureInfo pawnTreasures = 2; //士兵宝箱信息
}

// 通用类型对象
message TypeObj {
  int32 type  = 1; //类型
  int32 id    = 2; //配置id
  int32 count = 3; //数量 
}

message TypeObjList {
  repeated TypeObj list = 1;
}

// 军队记录信息
message ArmyRecordInfo {
  int32 type                  = 1; //类型 0.战斗 1.移动 2.撤回 3.遣返 4.解散 5.被遣返
  string armyName             = 2; //名字
  int32 armyIndex             = 3; //位置
  int32 targetIndex           = 4; //目标位置
  int64 time                  = 5; //发生时间
  ArmyBattleRecordInfo battle = 6; //战斗信息
}

// 军队战斗记录信息
message ArmyBattleRecordInfo {
  repeated AreaPawnInfo pawns    = 1; //士兵信息
  map<int32, int32> battleInfo   = 2; //战斗数据
}

// 战斗记录数据
message BattleRecordInfo {
  string uid                = 1; //唯一id
  int32 sid                 = 2; //服务器id
  int32 version             = 3; //版本号
  int32 index               = 4; //位置
  int64 beginTime           = 5; //开始时间
  int64 endTime             = 6; //结束时间
  repeated FrameInfo frames = 7; //帧数据列表
  repeated TreasureBaseInfo treasures = 8; //获取的宝箱
}

// 帧数据
message FrameInfo {
  int32 type                    = 1; //类型
  int32 currentFrameIndex       = 2; //当前帧
  string armyUid                = 3; //士兵所属队伍uid
  AreaPawnInfo pawn             = 4; //添加士兵数据
  repeated FighterInfo fighters = 5; //战斗者列表
  repeated int32 hp             = 6; //血量
  string uid                    = 7; //删除军队uid
  AreaArmyInfo army             = 8; //添加军队信息
  string snapshootMD5           = 9; //MD5快照
  int32 index                   = 10;//位置
  int32 fps                     = 11;//帧率
  string owner                  = 12;//拥有者
  int32 cityId                  = 13;//城市id
  repeated AreaBuildInfo builds = 14;//建筑列表
  repeated AreaArmyInfo armys   = 15;//初始军队列表
  int32 camp                    = 16;//所在阵营
  int32 randSeed                = 17;//随机种子
  repeated int32 buildInfo      = 18;//建筑信息 [id, lv]
  repeated BuffInfo environmentBuffs = 19; //环境buff
}

// 士兵数据
message AreaPawnInfo {
  int32 index                     = 1; //位置
  string uid                      = 2; //唯一id
  Vec2 point                      = 3; //坐标
  int32 id                        = 4; //配置id
  int32 lv                        = 5; //等级
  int32 skinId                    = 6; //皮肤id
  int32 curAnger                  = 7; //当前怒气
  int32 attackSpeed               = 8; //出手速度
  EquipInfo equip                 = 9; //装备
  repeated TreasureInfo treasures = 10;//宝箱列表
  repeated BuffInfo buffs         = 11;//buff列表
  map<int32, int32> hp            = 12;//血量 0=>curHp 1=>maxHp
  string armyUid                  = 13;//所属队伍uid
  map<int32, int32> battleRecordData = 14; //战斗记录
  int32 rodeleroCadetLv           = 15; //见习勇者 层数
  PortrayalInfo portrayal         = 16; //画像
  int32 petId                     = 17; //宠物
  bool isFight                    = 18; //是否战斗中
}

// 二维坐标数据
message Vec2 {
  int32 x = 1; //x轴
  int32 y = 2; //y轴
}

// buff数据
message BuffInfo {
  int32 type      = 1; //类型
  int32 lv        = 2; //等级
  string provider = 3; //施加者
  int32 round     = 4; //持续回合数 -1.表示永久
  int32 times     = 5; //触发次数 -1.表示永久
  double value    = 6; //数值
  int32 needRound = 7; //需要的回合数
}

// 战斗者信息
message FighterInfo {
  string uid              = 1; //唯一id
  int32 camp              = 2; //阵营
  int32 attackIndex       = 3; //出手顺序
  int32 waitRound         = 4; //等待回合
  string attackTarget     = 5; //攻击目标
  int32 roundCount        = 6; //当前回合数
  int32 attackCount       = 7; //攻击次数
  int32 towerLv           = 8; //哨塔等级
  Vec2 point              = 9; //哨塔坐标
  repeated int32 hp       = 10;//血量-军旗才有
  string owner            = 11;//所属玩家-军旗才有
  bool isFalg             = 12;//是否军旗
  int32 towerId           = 13;//哨塔id
  int32 id                = 14;
  int32 lv                = 15;
  bool isPet              = 16;//是否宠物
  bool isNoncombat        = 17;//是否非战斗单位
  int32 enterDir          = 18; //进入方向
  repeated BuffInfo buffs = 19;//buff列表
}

// 当前战斗者信息
message CurFighterInfo {
  string uid                    = 1; //唯一id
  map<int32, BlackboardInfo> blackboard = 2; //行为树map k=>id v=>info
}

// 行为树信息
message BlackboardInfo {
  map<string, bool> boolMap       = 1; //行为树信息map=>bool
  map<string, int32> intMap       = 2; //行为树信息map=>int
  map<string, Vec2List> vecArray  = 3; //行为树信息map=>Vec2List
  map<string, string> stringMap   = 4; //行为树信息map=>string
  map<string, Vec2> vec2Map       = 5; //行为树信息map=>vec2
}

// 二维坐标列表
message Vec2List {
  repeated Vec2 list = 1;
}

// 市场记录数据
message BazaarRecordInfo {
  string uid                    = 1; //唯一id
  int32 sid                     = 2; //服务器id
  int32 type                    = 3; //类型
  int64 time                    = 4; //时间
  string owner                  = 5; //所属玩家
  string target                 = 6; //目标玩家
  BazaarRecordResourceInfo res  = 7; //资源信息
  repeated string names         = 8; //交易者名字
}

// 市场记录交易资源信息
message BazaarRecordResourceInfo {
  int32 resType   = 1; //购买(出售)资源类型
  int32 resCount  = 2; //购买(出售)资源数量
  int32 costType  = 3; //花费资源类型
  int32 costCount = 4; //花费资源数量
  int32 actCount  = 5; //自动下架后添加的资源数量
}

// 产出更新
message UpdateOutPut {
  int64 flag                = 1;//位标记
  int32 granaryCap          = 2;//粮仓容量
  int32 warehouseCap        = 3;//仓库容量
  OutPutInfo cereal         = 4;//粮食
  OutPutInfo timber         = 5;//木材
  OutPutInfo stone          = 6;//石头
  double cerealConsume      = 7;//粮耗
  int32 expBook             = 8;//经验书
  int32 iron                = 9;//铁
  int32 gold                = 10;//金币
  int32 upScroll            = 11;//升级卷轴
  int32 fixator             = 12;//固定器
  repeated int32 pawnSkins          = 13;//士兵皮肤
  repeated int32 unlockEquipIds     = 14;//装备
  repeated int32 unlockPawnIds      = 15;//士兵
  repeated int32 unlockPolicyIds    = 16;//政策
  int32 stamina                     = 17;//奖励点
  repeated TitleInfo titles         = 18;//称号
  int32 ingot                       = 19;//元宝
  int32 warToken                    = 20;//兵符
  repeated PortrayalInfo portrayals = 21;//画像列表
  int32 upRecruitPawnCount          = 22;//加速招募次数
  repeated SkinItem skinItems       = 23;//皮肤物品
  repeated int32 citySkins          = 24;//城市皮肤
  repeated string unlockHeadIcons   = 25;//头像
  repeated int32 unlockChatEmojiIds = 26;//聊天表情
  int32 rankCoin                    = 27;//段位积分
  repeated BotanyInfo unlockBotanys = 28;//解锁的植物
  int32 freeRecruitPawnCount        = 29; //已免费招募士兵数量
  int32 freeLevingPawnCount         = 30; //已免费训练士兵数量
  int32 freeCurePawnCount           = 31; //已免费治疗士兵数量
  int32 freeForgeCount              = 32; //已免费打造/重铸数量
}

// 士兵移动通知信息
message MovePawnNotifyInfo {
  string armyUid              = 1; //军队uid
  repeated MovePawnInfo pawns = 2; //士兵移动信息列表
}

// 士兵移动信息
message MovePawnInfo {
  string uid = 1; //唯一id
  Vec2 point = 2; //坐标
}

// 行军信息
message MarchInfo {
  string uid        = 1; //唯一id
  string owner      = 2; //谁的行军
  string armyUid    = 3; //军队id
  string armyName   = 4; //军队名字
  int32 roleId      = 5; //行军角色id
  int32 armyIndex   = 6; //军队所在的位置
  int32 startIndex  = 7; //起点位置
  int32 targetIndex = 8; //目标位置
  int32 needTime    = 9; //行军时间
  bool autoRevoke   = 10;//自动撤回 遣返
  int32 surplusTime = 11;//剩余时间
  bool forceRevoke  = 12;//强制撤回
  bool cellTonden   = 13;//是否屯田
  int32 notifyIndex = 14;//通知序号
}

// 战场信息
message AreaInfo {
  int32 index                   = 1; //位置
  string owner                  = 2; //拥有者
  int32 cityId                  = 3; //城市id
  repeated int32 hp             = 4; //血量
  repeated AreaBuildInfo builds = 5; //建筑列表
  repeated AreaArmyInfo armys   = 6; //军队列表
  BattleInfo battle             = 7; //战斗信息
  bool isBattleEndTime          = 8; //是否战斗时间到了
}

// 战斗信息
message BattleInfo {
  int32 fps                     = 1; //帧率
  int32 checkFrameCount         = 2; //服务器多少帧向客户端同步一次
  int32 currentFrameIndex       = 3; //当前帧
  int32 mul                     = 4; //加速倍数
  int32 upSpeedFrame            = 5; //多少帧开始加速
  int32 battleTime              = 6; //战斗经过时间
  int32 camp                    = 7; //阵营
  int32 randSeed                = 8; //随机种子
  repeated FighterInfo fighters = 9; //战斗者列表
  CurFighterInfo currentFighter = 10;//当前战斗者信息
  map<string, LockedMovePointInfo> lockedMovePointMap = 11; //士兵移动锁定点位Map
  int32 attackerArmyAcc         = 12;//进攻方累计军队数
  int32 defenderArmyAcc         = 13;//防守方累计军队数
  int32 accAttackIndex          = 14;//累计攻击下标
  repeated BuffInfo environmentBuffs = 15; //环境buff列表
}

// 战斗结束信息
message BattleEndInfo {
  AreaInfo data                       = 1; //战场信息
  string attacker                     = 2; //攻占者
  repeated TreasureBaseInfo treasures = 3; //获取的宝箱
  bool noTreasureByNotStamina         = 4; //奖励点不足导致没有宝箱
  int32 fullLostCount                 = 5; //背包已满遗失的宝箱数量
}

// 点位锁定信息
message LockedMovePointInfo {
  string fighterUid = 1; //战斗中uid
  int64 weight      = 2; //权重
  Vec2 movePoint    = 3; //移动后所在点位
}

// uid列表
message UidsList {
  repeated string list = 1;
}

// 运送信息
message TransitInfo {
  string uid            = 1; //唯一id
  string owner          = 2; //谁的商队
  int32 type            = 3; //运送类型 1.购买 2.赠送
  int32 merchantCount   = 4; //商人数量
  int32 goodsType       = 5; //携带出去物资类型
  int32 goodsCount      = 6; //携带出去物资数量
  int32 backGoodsType   = 7; //需要带回来的物资类型
  int32 backGoodsCount  = 8; //需要带回来的物资数量
  int32 startIndex      = 9; //起点位置
  int32 targetIndex     = 10;//目标位置
  int32 needTime        = 11;//行军时间
  int32 surplusTime     = 12;//剩余时间
}

// 战斗分布信息
message BattleDistInfo {
  int32 index           = 1; //位置
  repeated string uids  = 2; //这个位置参与战斗的玩家uid 没有说明结束了
}

// 免战信息
message AvoidWarInfo {
  int32 index = 1; //位置
  int32 time  = 2; //剩余时间
  int32 type  = 3; //0.普通免战 1.超出战斗时间的免战
}

// 屯田信息
message CellTondenInfo {
  int32 index = 1; //位置
  int32 time  = 2; //剩余时间
  string auid = 3; //军队uid
  repeated int32 treasureIds = 4; //屯田完成后的宝箱奖励
}

// 建造城市信息
message BTCityInfo {
  int32 index       = 1; //位置
  int32 id          = 2; //城市id
  int32 needTime    = 3; //需要时间
  int32 surplusTime = 4; //剩余时间
}

// 更新修建城市信息
message UpdateBTCityInfo {
  int32 index                   = 1;
  CellInfo cell                 = 2;
  repeated AreaBuildInfo builds = 3;
}

// 加入和退出联盟
message JoinAndExitAlliInfo {
  string uid            = 1; //玩家uid
  string allianceName   = 2; //联盟名
  AlliBaseInfo baseInfo = 3; //联盟基础信息
}

// 修改昵称信息
message UpdateChangeName {
  string uid      = 1; //玩家uid
  string nickname = 2; //新名字
}

// 更新箭塔等级信息
message UpdateTowerLvInfo {
  string uid                    = 1; //玩家uid
  map<int32, int32> towerLvMap  = 2; //箭塔等级map k=>对应士兵id v=>等级
}

// 更新称号信息
message UpdateTitleInfo {
  string uid  = 1; //玩家uid
  int32 title = 2; //称号id
}

// 更新每日信息
message UpdateTodayInfo {
  int32 todayOccupyCellCount      = 1; //每日占领地块数量
  int32 todayReplacementCount     = 2; //每日置换次数
  repeated TaskInfo todayTasks    = 3; //每日任务
  int32 cellTondenCount           = 4; //每日屯田次数
}

// 更新玩家政策
message UpdatePolicys {
  string uid                = 1; //玩家uid
  map<int32, int32> policys = 2; //政策map k=>槽位序号 v=>政策id
}

// 系统消息
message SysMsgInfo {
  int32 id                  = 1; //消息id
  repeated string parames   = 2; //消息参数
}

// 系统公告
message SysNoticeInfo {
  int32 id                  = 1; //消息id
  repeated string parames   = 2; //参数列表
}

// 玩家喇叭信息
message UserTrumpetInfo {
  string nickname = 1;
  string content  = 2;
}

// 见习勇者信息
message RodeleroCadetInfo {
  string uid  = 1;
  int32 lv    = 2; //见习勇者层数
}

// 联盟成员信息
message AllianceMemberInfo {
  string uid        = 1; //玩家uid
  int32 job         = 2; //职位
  int64 joinTime    = 3; //加入时间
  string nickname   = 4; //昵称
  string headIcon   = 5; //头像
  int32 offlineTime = 6; //离线时间
  repeated int32 alliScores = 7; //战损积分[当前，历史最高]
  int32 embassyLv           = 8; //大使馆等级
  repeated EquipInfo exclusiveEquips = 9; //专属装备列表
  repeated int32 landScores = 10; //领地积分[当前，历史最高]
}

// 联盟申请加入信息
message AllianceApplyInfo {
  string uid        = 1; //玩家uid
  int64 time        = 2; //申请时间
  int32 offlineTime = 3; //离线时间
  string desc       = 4; //申请说明
}

// 联盟副频道信息
message AlliChannelInfo {
  string uid   = 1; //uid
  string name  = 2; //频道名
  string color = 3; //颜色
  repeated string memberUids = 4; //成员uid列表
  bool memberFilter          = 5; //是否开启成员过滤
}

// 联盟信息
message AllianceInfo {
  string uid                          = 1; //联盟uid
  string name                         = 2; //名称
  string creater                      = 3; //创建者
  repeated AllianceMemberInfo members = 4; //成员列表
  repeated AllianceApplyInfo applys   = 5; //申请加入列表
  int32 persLimit                     = 6; //成员人数上限
  string notice                       = 7; //联盟公告
  int32 editNoticeCD                  = 8; //编辑公告cd
  int32 icon                          = 9; //图标
  map<int32, MapMarkInfo> mapFlag     = 10;//地图标记
  int64 createTime                    = 11; //联盟创建时间
  int32 sumEmbassyLv                  = 12; //总的大使馆等级
  map<int32, Int32ArrayInfo> selectPolicys = 13; //可选择的政策
  repeated AllienceLogInfo logs            = 14; //联盟日志
  string applyDesc                         = 15; //申请说明
  repeated AlliChannelInfo chatChannels    = 16; //聊天频道
  repeated AlliLeaderVoteInfo leadervotes  = 17; //盟主投票信息
  int32 confirmSurplusTime                 = 18; //盟主确认剩余时间
  int32 voteTimes                          = 19; //投票次数
}

// 联盟基础信息
message AlliBaseInfo {
  string uid                = 1;
  int32 icon                = 2; //联盟图标
  int32 sumEmbassyLv        = 3; //联盟总大使馆等级
  map<int32, int32> policys = 4; //联盟政策
  int32 createrIndex        = 5; //盟主位置
}

// 联盟简略信息
message AllianceShortInfo {
  string uid        = 1; //联盟uid
  string name       = 2; //名称
  int32 pers        = 3; //成员数量
  int32 persLimit   = 4; //成员人数上限
  int32 dis         = 5; //距离
  bool isApply      = 6; //是否在申请列表中
  int32 icon        = 7; //图标
  string applyDesc  = 8; //申请说明
}

message AlliLeaderVoteInfo {
  string uid           = 1; //候选人uid
  repeated string list = 2; //投票的玩家uid列表
}

// 转盘记录信息
message WheelRecordInfo {
  string uid              = 1; //唯一id
  int32 id                = 2; //配置id
  repeated TypeObj items  = 3; //奖励列表
  int32 nextMul           = 4; //下一次倍数
  int32 needSid           = 5; //需要在几区领取
  bool isClaim            = 6; //是否领取
  int64 time              = 7; //时间
}

// 转盘信息
message WheelInfo {
  int64 wheelWaitTime                   = 1; //下一次等待时间
  int32 wheelCurrCount                  = 2; //每日已转动次数
  int32 wheelResidueCount               = 3; //剩余次数
  int32 wheelFreeCount                  = 4; //免费次数
  repeated TypeObj wheelRandomAwards    = 5; //随机奖励
  repeated WheelRandomAwardRecordInfo wheelRandomAwardRecords = 6; //随机奖励记录
  int32 wheelInRoomRunDay               = 7; //所在服务器的运行天数
  int32 freeAdCount                     = 8; //免广告次数
}

message PopularityInfo {
  repeated int32 value = 1; //[id,count]
}

// 人气记录
message PopularityRecordInfo {
  string uid      = 1; //玩家uid
  string nickname = 2; //玩家昵称
  int32 value     = 3; //给出的评价
  int64 time      = 4; //更改时间
}

// 市场交易资源信息
message TradingRessInfo {
  string uid            = 1; //交易uid
  string owner          = 2; //拥有者
  int32 sellType        = 3; //卖出资源类型
  int32 sellCount       = 4; //卖出资源数量
  int32 buyType         = 5; //买资源类型
  int32 buyCount        = 6; //买资源数量
  int32 merchantCount   = 7; //商人数量
  int32 surplusTime     = 8; //剩余时间
  int32 noticeWaitTime  = 9; //公示期剩余时间
  int32 cancelTime      = 10;//可取消剩余时间
}

// 邮件数据
message MailInfo {
  string uid                        = 1; //唯一id
  string sender                     = 2; //发送人
  string senderName                 = 3; //发送人名字
  string title                      = 4; //标题
  string content                    = 5; //内容
  repeated TypeObj items            = 6; //奖励物品
  int64 createTime                  = 7; //创建时间
  int64 autoDelSurplusTime          = 8; //自动删除剩余时间 0.表示永不删除
  int32 state                       = 9; //状态 0未读 1已读未领取 2已读
  repeated int32 oneClaims          = 10;//已领取奖励的下标
  int32 contentId                   = 11;
}

// 分享的战报信息
message ShareBattleInfo {
  string uid  = 1; //uid
  int32 index = 2; //位置
}

// 回复信息
message ReplyChatInfo {
  string uid  = 1; //聊天uid
  string text = 2; //文本 玩家昵称：内容
}

// 聊天信息
message ChatInfo {
  string uid              = 1; //唯一id
  string channel          = 2; //频道
  string sender           = 3; //发送者
  string content          = 4; //内容
  int32 emoji             = 5; //表情id
  int64 time              = 6; //发送时间
  int64 bannedSurplusTime = 7; //禁言剩余时间
  PortrayalInfo portrayalInfo = 8; //画像
  EquipInfo equipInfo         = 9; //装备
  string senderNickname       = 10; //发送者昵称
  string senderHeadicon       = 11; //发送者头像
  int32 senderTitle           = 12; //发送者称号
  ShareBattleInfo battleInfo  = 13; //战报信息
  ReplyChatInfo replyInfo     = 14; //回复信息
}

// 大厅聊天信息
message LobbyChatInfo {
  string uid              = 1; //唯一id
  string channel          = 2; //频道
  string sender           = 3; //发送者
  string senderNickname   = 4; //发送者昵称
  string senderHeadicon   = 5; //发送者头像
  string content          = 6; //内容
  int32 emoji             = 7; //表情id
  int64 time              = 8; //发送时间
  int64 bannedSurplusTime = 9; //禁言剩余时间
  repeated string params  = 10;//系统消息参数
  PortrayalInfo portrayalInfo = 11; //画像
  ReplyChatInfo replyInfo = 12; //回复信息
}

// 玩家排行榜数据
message PlayerRankInfo {
  string uid      = 1; //玩家uid
  string headIcon = 2; //头像
  string nickname = 3; //名字
  string alliName = 4; //联盟名
  int32 landCount = 5; //领地数量
  int64 time      = 6; //上次更新时间
}

// 联盟排行榜数据
message AlliRankInfo {
  string uid            = 1; //联盟UID
  string name           = 2; //名字
  repeated int32 pers   = 3; //人数
  int32 landCount       = 4; //领地数量
  int64 time            = 5; //上次更新时间
  int32 beAddLandCount  = 6; //待添加领地数量 一般用于联盟
  int32 icon            = 7; //图标
  int64 resAcc          = 8; //总经济
  int32 occupyCount     = 9; //攻占次数
  int32 score           = 10; //联盟总积分
}

// 玩家积分排行榜数据
message PlayerScoreInfo {
  string uid        = 1; //玩家uid
  string headIcon   = 2; //头像
  string nickname   = 3; //名字
  string alliName   = 4; //联盟名
  int32 landScore   = 5; //领地积分
  int32 alliScore   = 6; //联盟积分
  int32 extraScore  = 7; //奖励积分
  int64 time        = 8; //上次更新时间
  int32 alliIcon    = 9; //联盟图标
}

// 自己积分排行榜数据
message MeScoreInfo {
  int32 landScore     = 1; //领地积分
  int32 alliScore     = 2; //联盟积分
  int32 landScoreTop  = 3; //领地积分 历史最高
  int32 alliScoreTop  = 4; //联盟积分 历史最高
  int32 extraScore    = 5; //奖励积分
}

// 用户评分排行榜数据
message UserScoreRankInfo {
  string uid      = 1; //玩家uid
  string headIcon = 2; //头像
  string nickname = 3; //名字
  int32 score     = 4; //分数
  int32 rankCount = 5; //排位次数
}

// 任务数据
message TaskInfo {
  int32 id        = 1; //任务id
  int32 progress  = 2; //进度
  repeated TreasureInfo treasureRewards = 3;
  int32 serverRunDay = 4; //服务器运行天数
}

// Login服检测是否过天返回数据
message CheckUpdateNextToDayTimeInfo {
  int64 nextToDayTime             = 1; //明日开始时间
  repeated TaskInfo generalTasks  = 2; //常规任务列表
  int32 wheelCurrCount            = 3; //转盘次数
}

// 任务进度更新通知
message TaskUpdateNotify {
  repeated TaskInfo generalTasks  = 1; //常规任务列表
  repeated TaskInfo achieveTasks  = 2; //成就任务列表
  repeated TaskInfo guideTasks    = 3; //引导任务列表
  repeated TaskInfo todayTasks    = 4; //每日任务列表
  repeated TaskInfo otherTasks    = 5; //其他任务列表
}

// 玩家领地分数信息
message LandScoreInfo {
  int32 landScore = 1; //领地分数
  map<int32, OccupyLandCountInfo> occupyLandCountMap = 2; //攻占的野地数
  int32 maxOccupyLandDifficulty = 3; //历史最大攻占野地难度
}

// 订单数据
message OrderInfo {
  string cpOrderId    = 1; //订单号
  string productId    = 2; //商品id
  int32 quantity      = 3; //数量
  string platform     = 4; //平台
  string orderId      = 5; //外部订单号
}

// 战斗积分记录
message BattleScoreRecord {
  string uid                      = 1; //uid
  int64 beginTime                 = 2; //开始时间
  int64 endTime                   = 3; //结束时间
  string date                     = 4; //日期 以开始时间为准
  map<int32, int32> validInfo     = 5; //有效战斗数据
  map<int32, int32> invalidInfo   = 6; //无效战斗数据
  repeated PawnDeadInfo deadInfo  = 7; //阵亡数据
  repeated string armyUidList     = 8; //参与战斗的军队uid列表
  int32 index                     = 9; //位置
  bool isWin                      = 10; //是否胜利
  bool isCanPlay                  = 11; //是否可播放
}

// 士兵阵亡数据
message PawnDeadInfo {
  int32 id            = 1; //阵亡士兵配置id
	int32 lv            = 2; //阵亡士兵等级
	int32 killerId      = 3; //击杀者配置id
	string killerOwner  = 4; //击杀者所属玩家uid
}

// 战斗积分记录(指定玩家按日期统计)
message BattleScoreRecordByDate {
  string owner        = 1; //玩家uid
  string date         = 2; //日期 以开始时间为准
  int32 battleCount   = 3; //战斗次数
  int32 score         = 4; //联盟积分
  int32 killCount     = 5; //击杀数
  int32 deadCount     = 6; //阵亡数
  int32 pawnDamage    = 7; //士兵造成的伤害
  int32 PawnHitDamage = 8; //士兵承伤
  int32 buildDamage   = 9; //建筑造成的伤害
  int32 damageToBuild = 10; //对建筑造成的伤害
}

// 游戏对局记录
message GameRecordInfo {
  int32 sid                     = 1;
  int64	startTime               = 2;
  int64	endTime                 = 3;
  bool	win                     = 4; //是否胜利
  int32	score                   = 5; //游戏积分
  int32	rank                    = 6; //排名 -1表示没排名 -2表示放弃
  int32	addRankScore            = 7; //添加的段位分
  int32	curRankScore            = 8; //当前的段位分
  map<int32, int32> statistics  = 9; //统计
  int32 addWarToken             = 10; //添加的兵符
  repeated int32 useHero        = 11; //使用的英雄
  map<int32, Mapint32Info>  pawnStatistics = 12; //士兵数据统计
  string alliUid                = 13; //联盟uid
  string alliName               = 14; //联盟名字
  int32 alliHeadicon            = 15; //联盟头像
}

// 市场价格信息
message TradePriceInfo {
  int32 sellType = 1; //出售类型
  int32 buyType  = 2; //购买类型
  double price   = 3; //价格
}

// 地图表情信息
message CellEmojiInfo {
  string uid  = 1; //玩家uid
  int32 index = 2;
  int32 emoji = 3; //表情
}

// 转盘随机奖励记录信息
message WheelRandomAwardRecordInfo {
  int64 time              = 1;
  repeated TypeObj items  = 2;
}

// 联盟政策信息
message AlliPolicyInfo {
  string uid = 1;
  map<int32, int32> info = 2;
}

// 图鉴信息
message GalleryInfo {
  int32 id           = 1; //配置id
  int32 type         = 2; //类型
  int32 star         = 3; //星数
  int32 commentCount = 4; //评论数
}

// 图鉴评论信息
message GalleryCommentInfo {
  string uid            = 1;
  int32 id              = 2; //配置id
  int32 type            = 3; //类型
  int32 star            = 4; //星数
  string userId         = 5; //玩家uid
  string nickname       = 6; //玩家昵称
  string headIcon       = 7; //玩家头像
  string content        = 8; //评论
  int32 modifyCount     = 9; //修改次数
  int64 time            = 10; //时间
  int32 rankScore       = 11; //排位分
  repeated int32 praise = 12; //点赞列表
  int32 mePraise        = 13; //该玩家的点赞
  string version        = 14;
}

// 联盟日志
message AllienceLogInfo {
  int32 type             = 1; //日志类型
  int64 time             = 2; //时间
  repeated string params = 3; //参数
}

// 玩家城市产出信息
message PlayerCityOutputInfo {
  string uid = 1;
  map<int32, TypeObjList> outputMap = 2;
}

// 古城信息
message AncientCityInfo {
  int32 index               = 1; //坐标
  repeated TypeObj lvUpRes  = 2; //升级资源
  int32 speedUpRes          = 3; //加速资源
  int32 lv                  = 4; //等级
  int32 surplusTime         = 5; //剩余时间
  int32 state               = 6; //修建状态 0未修建 1修建中
  int32 curContribute       = 7; //捐献状态 位运算 0升级 1加速
  int32 pauseState          = 8; //暂停状态 0未暂停 1交战暂停
  int32 ctbSurplusCount     = 9; //捐献剩余次数
  int32 firstSurplusTime    = 10;//首次占领效果剩余时间
  int32 ctbCdSurplusTime    = 11;//捐献cd剩余时间
}

// 古城捐献记录
message AncientContributeLog {
  string uid    = 1; //玩家uid
  int32 type    = 2; //捐献类型 0升级 1加速
  int32 resType = 3; //资源类型
  int32 count   = 4; //资源数量
  int64 time    = 5; //时间
}

// 遗迹捐献统计信息
message AncientContributeAccInfo {
  string uid              = 1; //玩家uid
  repeated TypeObj items  = 2; //捐献资源列表
}

// 区域观战玩家信息
message WatchPlayerInfo {
  string uid = 1;
  int64 time = 2;
}

// 区域发送聊天信息
message AreaChatInfo {
  string uid  = 1;
  int32 emoji = 2;
}

// 区服状态信息
message RoomStateInfo {
  int32 roomType = 1; //房间类型
  int32 state    = 2; //当前状态 0未报名 1已报名 2匹配中 3游戏中
  int64 time     = 3; //时间 未报名和已报名为下次匹配时间时间戳 匹配中为开服时间时间戳 游戏中为已开服时间
}

// 画像信息
message PortrayalInfo {
  int32 id                          = 1;
  int32 recompCount                 = 2; //重新合成次数
  int32 debris                      = 3; //拥有的碎片数量
  repeated AttrArrayInfo attrs      = 4; //属性
  repeated PortrayalStoreSlot slots = 5; //保存槽位
  repeated PortrayalHistory history = 6; //历史记录
}

// 画像保存槽位信息
message PortrayalStoreSlot {
  repeated AttrArrayInfo attrs = 1; //属性
}

// 画像历史记录
message PortrayalHistory {
  repeated AttrArrayInfo attrs = 1; //属性
}

// 英雄槽位信息
message HeroSlotInfo {
  int32 lv                = 1;
  PortrayalInfo hero      = 2; //英雄
  int64 reviveSurplusTime = 3; //复活剩余时间
  string avatarArmyUID    = 4; //化身军队UID
}

// 地图标记信息
message MapMarkInfo {
  string desc = 1; //描述
  int32 flag  = 2; //编号
}

// 皮肤物品
message SkinItem {
  int32 id    = 1; //配置id
  string uid  = 2; //物品uid
  int64 state = 3; //状态
}

// 好友礼物
message FriendGift {
  string uid     = 1;
  int32 id       = 2; //礼物id
  int32 giftType = 3; //礼物类型
  int32 boxId    = 4; //礼盒id
  int64 time     = 5; //赠送时间
}

// 城市皮肤信息
message CitySkinInfo {
  int32 index = 1; //位置
  int32 id    = 2; //皮肤id
}

// 称号列表信息
message TitleListInfo {
  int32 title               = 1;
  repeated TitleInfo titles = 2;
}

// 种植信息 
message PlantInfo {
  int32 id          = 1; //植物id
  int64 remainTime  = 2; //剩余时间
  bool isWatering   = 3; //是否浇水了
}

// 植物信息
message BotanyInfo {
  int32 id    = 1; //植物id
  int32 count = 2; //数量
}

// 战令信息
message BattlePassInfo {
  int32 id                   = 1;
  int32 score                = 2; //积分
  bool buyPass               = 3; //是否购买
  repeated int32 rewarded    = 4; //已领奖
  repeated int32 rewardedPay = 5; //付费奖励已领奖
  int32 todayScore           = 6; //当天获得积分
  int32 buyScoreCount        = 7; //当天购买积分次数
}

// 屯田结束信息
message CellTondenEndInfo {
  string auid                         = 1; //军队uid
  repeated TreasureBaseInfo treasures = 2; //获取的宝箱
  int32 fullLostCount                 = 3; //背包已满遗失的宝箱数量
}

// 受伤士兵信息
message InjuryPawnInfo {
  string uid      = 1; //士兵uid
  int32 id        = 2; //配置id
  int32 lv        = 3; //等级
  int32 cureCount = 4; //已治疗次数
  bool curing     = 5; //是否治疗中
  int64 deadTime  = 6; //死亡时间
}

// 治疗中士兵信息
message PawnCuringInfo {
  string uid        = 1; //士兵uid
  string auid       = 2; //军队uid
  int32 index       = 3; //区域位置
  int32 needTime    = 4; //需要时间
  int32 surplusTime = 5; //剩余时间
  int32 id          = 6; //配置id
  int32 lv          = 7; //等级
}

// 联盟结算通知信息
message AlliSettleNotify {
  string uid = 1; //联盟uid
  repeated string list = 2; //玩家uid列表
  bool isGameOver = 3; //是否结束
}

// 自定义房间信息
message CustomRoomInfo {
  repeated TeamInfo teamList  = 1; //队伍列表
  string name                 = 2; //房间名
  string creator              = 3; //创建者uid
  repeated int32 winCond      = 4; //胜利条件
  int64 createTime            = 5; //创建时间
  int64 openTime              = 6; //开服时间
  int32 userLimit             = 7; //人数上限
  int32 sid                   = 8; //区服id
  int32 userNum               = 9; //玩家数量
}

// 好友礼物赠送记录
message FriendGiftRecordInfo {
  string uid        = 1;
  string sender     = 2; //赠送者
  string receiver   = 3; //接收者
  int64 time        = 4; //赠送时间
  int64 receiveTime = 5; //领取时间
  int32 id          = 6; //礼物id
}

//-----------------------------服务器主动推送------------------------------------

// 大厅通知
message LOBBY_ONNOTIFY_NOTIFY {
  repeated OnUpdateLobbyNotify list = 1; //通知列表
}

// 大厅通知数据
message OnUpdateLobbyNotify {
  int32 type              = 1; //类型
  SysMsgInfo data_1       = 2; //系统消息(局内)
  SysNoticeInfo data_2    = 3; //系统公告消息(全服)
  UserTrumpetInfo data_3  = 4; //玩家喇叭消息
}

// 用户数据更新通知
message LOBBY_ONUPDATEUSERINFO_NOTIFY {
  repeated OnUpdatePlayerInfoNotify list = 1; //玩家数据更新通知列表
}

// 玩家数据更新通知
message GAME_ONUPDATEPLAYERINFO_NOTIFY {
  repeated OnUpdatePlayerInfoNotify list = 1; //玩家数据更新通知列表
}

// 玩家数据更新通知数据
message OnUpdatePlayerInfoNotify {
  int32 type                              = 1; //类型
  UpdateOutPut data_1                     = 2; //产出更新
  repeated AreaDistInfo data_2            = 3; //军队分布
  AreaBuildInfo data_5                    = 4; //建筑升级
  repeated BTInfo data_6                  = 5; //建造队列
  map<string, DrillPawnInfoList> data_18  = 6; //士兵训练队列
  repeated MerchantInfo data_20           = 7; //更新商人
  bool data_33                            = 8; //新邮件
  EquipInfo data_34                       = 9; //打造装备结果
  repeated InviteFriendInfo data_35       = 10; //刷新邀请列表
  map<int32, int32> data_38               = 11; //刷新添加的产量时间 k=>资源id v=>剩余时间
  repeated PawnLvingInfo data_40          = 12; //刷新士兵练级队列
  UpdateOutPut data_41                    = 13; //通知更新物品
  int32 data_42                           = 14; //刷新重置政策需要的金币
  repeated CeriSlotInfo data_46           = 15; //刷新研究所槽位信息
  CeriSlotInfo data_47                    = 16; //研究完成
  repeated TaskInfo data_48               = 17; //刷新常规任务列表
  bool data_50                            = 18; //是否有新宝箱
  int32 data_51                           = 19; //转盘次数
  UpdateTodayInfo data_52                 = 20; //更新每日信息
  TaskUpdateNotify data_55                = 21; //任务进度更新通知
  LandScoreInfo data_57                   = 22; //更新玩家领地分数
  repeated UserSubInfo data_59            = 23; //更新玩家订阅信息
  EquipInfo data_64                       = 24; //融炼装备结果
  HeroSlotInfo data_70                    = 25; //英雄殿信息改变
  int32 data_72                           = 26; //当天喇叭次数
  int64 data_73                           = 27; //每日免费金币剩余时间
  map<int32, int32> data_74               = 28; //战损补偿
  TitleListInfo data_76                   = 29; //刷新称号列表
  map<int32, int32> data_77               = 30; //活动记录
  bool data_78                            = 31; //战令有奖励可领取
  int32 data_81                           = 32; //当天屯田次数
  repeated PawnCuringInfo data_82         = 33; //士兵治疗队列
  InjuryPawnInfo data_83                  = 34; //受伤士兵添加
  string data_84                          = 35; //受伤士兵移除
  map<int32, CeriSlotInfo> data_85        = 36; //政策槽位更新
  map<int32, CeriSlotInfo> data_86        = 37; //装备槽位更新
  map<int32, CeriSlotInfo> data_87        = 38; //士兵槽位更新
}

// 世界更新通知
message GAME_ONUPDATEWORLDINFO_NOTIFY {
  repeated OnUpdateWorldInfoNotify list = 1; //世界更新通知列表
}

// 世界更新通知数据
message OnUpdateWorldInfoNotify {
  int32 type                    = 1; //类型
  CellInfo data_3               = 2; //添加地块
  repeated int32 data_4         = 3; //删除地块
  MarchInfo data_13             = 4; //通知行军
  MarchInfo data_14             = 5; //删除行军
  BattleDistInfo data_17        = 6; //战斗分布
  TransitInfo data_21           = 7; //添加运送
  string data_22                = 8; //删除运送
  AvoidWarInfo data_26          = 9; //通知免战信息
  BTCityInfo data_27            = 10; //添加修建城市
  UpdateBTCityInfo data_28      = 11; //删除修建城市
  CaptureInfo data_29           = 12; //有玩家被攻陷
  string data_30                = 13; //解散联盟
  JoinAndExitAlliInfo data_32   = 14; //有玩家加入联盟
  GameOverInfo data_36          = 15; //游戏结束
  UpdateChangeName data_37      = 16; //修改昵称
  JoinAndExitAlliInfo data_43   = 17; //有玩家退出联盟
  string data_44                = 18; //彻底删除玩家
  UpdateTowerLvInfo data_45     = 19; //刷新玩家的箭塔等级
  UpdateTitleInfo data_49       = 20; //改变称号
  UpdatePolicys data_53         = 21; //更新玩家政策
  SysMsgInfo data_54            = 22; //系统消息
  RodeleroCadetInfo data_56     = 23; //更新玩家见习勇者
  string data_58                = 24; //玩家放弃对局
  TradePriceInfo data_60        = 25; //市场价格更新
  CellEmojiInfo data_61         = 26; //更新地图表情
  repeated AlliBaseInfo data_62 = 27; //所有联盟基础信息列表
  AlliPolicyInfo data_64        = 28; //联盟政策
  PlayerCityOutputInfo data_31  = 29; //玩家城市产出
  SeasonInfo data_65            = 30; //刷新季节信息
  AncientCityInfo data_66       = 31; //古城信息更新
  TempPlayerInfo data_71        = 32; //添加玩家
  CitySkinInfo data_75          = 33; //改变城市皮肤
  CellTondenInfo data_79        = 34; //通知屯田信息
  AlliSettleNotify data_88      = 35; //联盟结算通知
  map<int32, string> data_89    = 36; //世界事件通知
}

// 战场更新通知
message GAME_ONUPDATEAREAINFO_NOTIFY {
  int32 type                    = 1;
  int32 index                   = 2;
  AreaBuildInfo data_5          = 3; //建筑升级
  AreaBuildPointInfo data_7     = 4; //移动建筑
  AreaBuildInfo data_8          = 5; //添加建筑
  string data_9                 = 6;  //删除建筑
  AreaArmyInfo data_10          = 7; //添加军队
  MovePawnNotifyInfo data_11    = 8; //移动士兵
  string data_12                = 9; //删除军队
  AreaInfo data_15              = 10; //发生战斗
  BattleEndInfo data_16         = 11; //战斗结束
  AreaArmyInfo data_19          = 12; //更新军队
  repeated AreaArmyInfo data_24 = 13; //刷新所有士兵血量
  repeated AreaPawnInfo data_25 = 14; //改变士兵属性
  AreaPawnInfo data_39          = 15; //刷新士兵宝箱信息
  ArmyTreasuresInfo data_63     = 16; //刷新军队宝箱信息
  repeated WatchPlayerInfo data_67 = 17; //刷新战场人员
  AreaChatInfo data_68          = 18; //战场发送聊天信息
  AreaPawnInfo data_69          = 19; //改变士兵画像
  CellTondenEndInfo data_80     = 20; //屯田结束信息
  string data_90                = 21; //删除行军军队
}

// 联盟玩家更新通知
message GAME_ONUPDATEALLIMEMBERS_NOTIFY {
  repeated AllianceMemberInfo members = 1; //联盟玩家列表
}

// 联盟成员大使馆等级更新通知
message GAME_ONUPDATEALLIMEMBEREMBASSYLV_NOTIFY {
  string uid          = 1; //成员uid
  int32 embassyLv     = 2; //成员大使馆等级
  int32 sumEmbassyLv  = 3; //总大使馆等级
  int32 persLimit     = 4; //成员上限
  map<int32, Int32ArrayInfo> selectPolicys = 5; //可选择的政策
}

// 联盟申请加入通知
message GAME_ONUPDATEALLIAPPLYS_NOTIFY {
  repeated AllianceApplyInfo applys = 1; //申请信息列表
}

// 通知其他成员有人加入联盟
message GAME_ONPLAYERJOINALLIANCE_NOTIFY {
  string uid                          = 1; //新成员uid
  repeated AllianceMemberInfo members = 2; //联盟玩家列表
}

// 通知玩家被同意加入联盟
message GAME_ONAGREEJOINALLIANCE_NOTIFY {
  AllianceInfo data = 1; //联盟信息
}

// 通知玩家被踢出联盟
message GAME_ONKICKOUTALLIANCE_NOTIFY {
}

// 通知联盟公告变更
message GAME_ONALLIANCECHANGENOTICE_NOTIFY {
  string notice = 1; //公告文本
}

// 通知联盟成员职位变更
message GAME_ONALLIANCECHANGEMEMBERJOB_NOTIFY {
  string uid  = 1; //成员uid
  int32 job   = 2; //职位
}

// 通知刷新联盟地图标记
message GAME_ONUPDATEALLIMAPFLAG_NOTIFY {
  map<int32, MapMarkInfo> mapFlag = 1; //地图标记
}

// 通知联盟申请说明变更
message GAME_ONALLIANCECHANGEAPPLYDESC_NOTIFY {
  string applyDesc = 1; //申请说明
}

// 通知创建联盟聊天副频道
message GAME_ONALLIANCECREATECHATCHANNEL_NOTIFY {
  AlliChannelInfo info = 1;
}

// 通知删除联盟聊天副频道
message GAME_ONALLIANCEDELCHATCHANNEL_NOTIFY{
  string uid  = 1;
  string name = 2;
}

// 通知盟主投票
message GAME_ONALLIANCEVOTELEADER_NOTIFY{
  repeated AlliLeaderVoteInfo info = 1; //盟主投票信息
  string creater                   = 2; //盟主uid
  int32 confirmSurplusTime         = 3; //盟主确认剩余时间
}

// 通知联盟确认结果
message GAME_ONALLIANCECONFIRM_NOTIFY {
  string name = 1; //联盟名字
  int32 icon  = 2; //图标
}

// 通知地图表情
message GAME_ONCELLEMOJI_NOTIFY {
  CellEmojiInfo info = 1; //地图表情信息
}

// 通知战场同步帧信息
message GAME_ONFSPCHECKFRAME_NOTIFY {
  FrameInfo data = 1;
}

// 通知聊天信息
message GAME_ONCHAT_NOTIFY {
  ChatInfo data = 1;
}

// 游戏通知
message GAME_ONNOTICE_NOTIFY {
  map<string, string> text  = 1; //内容
  repeated string parames   = 2; //参数列表
}

// 主动踢下线通知
message GAME_ONKICK_NOTIFY {
  int32 type    = 1;
  int32 banType = 2;
}

// 服务器关闭通知
message GAME_ONGAMECLOSE_NOTIFY {
  bool isClose = 1;
}

// 好友申请通知
message LOBBY_ONFRIENDAPPLY_NOTIFY {
  string uid            = 1;
  string nickname       = 2;
  string headIcon       = 3;
  int64 time            = 4; //时间
  int32 playSid         = 5; //当前正在玩的区
}

// 好友添加通知
message LOBBY_ONFRIENDADD_NOTIFY {
  string uid            = 1;
  string nickname       = 2;
  string headIcon       = 3;
  int64 time            = 4; //时间
  int64 offlineTime     = 5; //离线时间
  int32 playSid         = 6; //当前正在玩的区
}

// 好友信息更新通知
message LOBBY_ONFRIENDUPDATE_NOTIFY {
  string uid            = 1;
  int32 sid             = 2; //当前区服id
  int64 offlineTime     = 3; //离线时间
  string nickname       = 4;
  string headIcon       = 5;
  int32 playSid         = 6; //当前正在玩的区
}

// 好友聊天通知
message LOBBY_ONFRIENDCHAT_NOTIFY {
  ChatInfo chatInfo = 1; //聊天信息
  string uid        = 2; //好友uid
}

// 删除好友通知
message LOBBY_ONFRIENDDEL_NOTIFY {
  string uid  = 1;
}

// 好友赠送通知
message LOBBY_ONFRIENDSENDGIFT_NOTIFY {
  string uid        = 1;
  int32 id          = 2; //礼物id
  int32 giftType    = 3; //礼物类型
  int32 boxId       = 4; //礼盒id
  int64 time        = 5; //赠送时间
  string friendUid  = 6; //好友uid
}

// 文本翻译通知
message LOBBY_ONTRANSLATETEXT_NOTIFY {
  string text = 1; //翻译后文本
  string uid  = 2;
}

// 队伍信息更新通知
message LOBBY_ONTEAMUPDATE_NOTIFY {
  TeamInfo teamInfo = 1; //队伍信息
}

// 队伍邀请通知
message LOBBY_ONTEAMINVITE_NOTIFY {
  TeamInviteInfo inviteInfo = 1; //邀请信息
}

// 队伍删除队员通知
message LOBBY_ONTEAMDELTEAMMATE_NOTIFY {
  int32 type = 1; // 0踢出 1解散
}

// 队伍改变模式
message LOBBY_ONTEAMCHANGEMODE_NOTIFY {
  int32 roomType = 1;
}

// 队伍取消报名
message LOBBY_ONTEAMCANCELAPPLY_NOTIFY {
  string uid      = 1;
  string nickname = 2;
}

// 队员信息变更
message LOBBY_ONTEAMMATEINFOCHANGE_NOTIFY {
  TeamUserInfo info = 1;
}

// 大厅聊天通知
message CHAT_ONLOBBYCHAT_NOTIFY {
  LobbyChatInfo data = 1;
}

// 用户对局结算通知
message LOBBY_ONUSERGAMEOVER_NOTIFY {
}

// redis存储聊天数据
message REDIS_LOBBY_CHAT_LIST {
  repeated LobbyChatInfo list = 1;
}

// ----------------------------枚举---------------------------

// 产出位标记枚举 最大63
enum OutPutFlagEnum {
  Unknown         = 0;
  GranaryCap      = 1; //粮仓容量
  WarehouseCap    = 2; //仓库容量
  Cereal          = 3; //粮食 
  Timber          = 4; //木材
  Stone           = 5; //石头
  CerealConsume   = 6; //粮耗
  ExpBook         = 7; //经验书
  Iron            = 8; //铁
  Gold            = 9; //金币
  UpScroll        = 10;//卷轴
  Fixator         = 11;//固定器
  PawnSkin        = 12;//士兵皮肤
  Equip           = 13;//装备
  Pawn            = 14;//士兵
  Policy          = 15;//政策
  Stamina         = 16; //奖励点
  Title           = 17; //称号
  Ingot           = 18; //元宝
  WarToken        = 19; //兵符
  Portrayal       = 20; //画像
  UpRecruit       = 21; //加速招募次数
  SkinItemEnum    = 22; //皮肤物品
  CitySkin        = 23; //城市皮肤
  HeadIcon        = 24; //头像
  ChatEmoji       = 25; //聊天表情
  RankCoin        = 26; //段位积分
  Botany          = 27; //植物
  FreeRecruit     = 28; //免费招募
  FreeLeving      = 29; //免费训练
  FreeCure        = 30; //免费治疗
  FreeForge       = 31; //免费打造
}

// 玩家兼容数据位标记
enum PlayerFixedFlagEnum {
  PlayerFixedUnknown           = 0;
  PlayerFixedRechargeCount     = 1; //充值次数
  PlayerFixedRechargeCountTask = 2; //充值次数任务
  PlayerFixedMaxLandCount      = 3; //最大地块数
}