package http

import (
	"os/exec"
	"time"

	slg "slgsrv/server/common"
	ut "slgsrv/utils"
)

func (this *Http) InitRpc() {
	this.GetServer().RegisterGO(slg.RPC_REQ_LOG, this.reqLog)
	this.GetServer().RegisterGO(slg.RPC_SERVER_UPDATE_TIME, this.serverUpdateTime)
	this.GetServer().RegisterGO(slg.RPC_UPDATE_VERSION, this.updateVersion)
	this.GetServer().RegisterGO(slg.RPC_HTTP_DELAY_RESTART, this.delayRestart)
	this.GetServer().RegisterGO(slg.RPC_DEL_NOTICE_TICK, this.delNotice)
}

// 客户端请求统计
func (this *Http) reqLog(uid, topic string, time int) (result interface{}, err string) {
	// AddReqLog(uid, topic, time)
	return
}

// 更新服务器维护时间
func (this *Http) serverUpdateTime(openTime int64) (result interface{}, err string) {
	allServerMaintainTime = openTime
	return
}

// 更新版本
func (this *Http) updateVersion(newVersion, newBigVersion string) (result interface{}, err string) {
	if newVersion != "" {
		version.Val = newVersion
		if _, e := setGmConfig("version", newVersion); e != nil {
			err = e.Error()
		}
	}
	if newBigVersion != "" {
		bigVersion.Val = newBigVersion
		if _, e := setGmConfig("bigVersion", newBigVersion); e != nil {
			err = e.Error()
		}
	}
	return
}

// 延时重启
func (this *Http) delayRestart() (result interface{}, err string) {
	delayTime := ut.Max(int(ut.Now()-allServerMaintainTime), 0)
	go func() {
		time.Sleep(time.Duration(delayTime) * time.Millisecond)
		cmd := exec.Command("pm2", "restart", "all")
		cmd.Run()
	}()
	return
}

// 删除公告
func (this *Http) delNotice(uid string) (result interface{}, err string) {
	delete(noticeMap, uid)
	return
}
