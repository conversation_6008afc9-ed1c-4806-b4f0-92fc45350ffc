package lobby

import (
	"strings"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sdk"
	"slgsrv/server/common/sensitive"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	lc "slgsrv/server/lobby/common"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
)

func (this *Lobby) initHDUser() {
	this.GetServer().RegisterGO("HD_TryLogin", this.tryLogin)                 // 尝试登录
	this.GetServer().RegisterGO("HD_FacebookBind", this.faceBookBind)         // facebook绑定
	this.GetServer().RegisterGO("HD_AppleBind", this.appleBind)               // apple绑定
	this.GetServer().RegisterGO("HD_GoogleBind", this.googleBind)             // google绑定
	this.GetServer().RegisterGO("HD_UploadFcmToken", this.uploadFcmToken)     // 上传推送消息token
	this.GetServer().RegisterGO("HD_SetOfflineNotify", this.setOfflineNotify) // 离线推送设置
	this.GetServer().RegisterGO("HD_DelAccount", this.delAccount)             // 账号注销
	this.GetServer().RegisterGO("HD_RevocationLogout", this.revocationLogout) // 账号注销
	this.GetServer().RegisterGO("HD_GiveupGame", this.giveupGame)             // 放弃对局
	this.GetServer().RegisterGO("HD_GetCurGameInfo", this.getCurGameInfo)     // 获取当前对局信息
	// this.GetServer().RegisterGO("HD_BanCheatTap", this.banCheatTap)           //作弊封禁中点击或挂机

	this.GetServer().RegisterGO("HD_SyncPreferenceData", this.syncPreferenceData)       // 同步配合设置
	this.GetServer().RegisterGO("HD_SyncGuideTag", this.syncGuideTag)                   // 同步新手引导标记
	this.GetServer().RegisterGO("HD_GetUserTitles", this.getUserTitles)                 // 获取用户的称号列表
	this.GetServer().RegisterGO("HD_GetUserPersonalDesc", this.getUserPersonalDesc)     // 获取个人简介
	this.GetServer().RegisterGO("HD_GetUserTotalGameCount", this.getUserTotalGameCount) // 获取用户总局数
	this.GetServer().RegisterGO("HD_GetGameRecordOne", this.getGameRecordOne)           // 获取单条游戏对局记录
	this.GetServer().RegisterGO("HD_GetGameRecordList", this.getGameRecordList)         // 获取游戏对局记录列表
	this.GetServer().RegisterGO("HD_SyncCheckPraiseCount", this.syncCheckPraiseCount)   // 同步检测触发好评次数
	this.GetServer().RegisterGO("HD_BattleForecast", this.battleForecast)               // 战斗预测扣除费用
	this.GetServer().RegisterGO("HD_SendTrumpet", this.sendTrumpet)                     // 发送喇叭
	this.GetServer().RegisterGO("HD_LikeJwm", this.likeJwm)                             // 点赞九万亩
	this.GetServer().RegisterGO("HD_GetRankReward", this.getRankReward)                 // 获取排位奖励
	this.GetServer().RegisterGO("HD_CompleteNovice", this.completeNovice)               // 完成新手村

	this.GetServer().RegisterGO("HD_ModifyUserNickname", this.modifyUserNickname)         // 修改昵称
	this.GetServer().RegisterGO("HD_ChangeUserHeadIcon", this.changeUserHeadIcon)         // 改变头像
	this.GetServer().RegisterGO("HD_ChangeUserPersonalDesc", this.changeUserPersonalDesc) // 改变个人简介
	this.GetServer().RegisterGO("HD_ChangeUserTitle", this.changeUserTitle)               // 改变称号
}

func (this *Lobby) BindUserAndSend(session gate.Session, user *User) (ret *pb.UserInfo, accountToken, err string) {
	log.Info("1 BindUser uid: %v, sid: %v, playSid: %v, newUser: %v", user.UID, user.SID, user.GetPlaySid(), user.LastLoginTime == 0)
	if !isRunning {
		// 服务器已关闭
		err = ecode.ROOM_CLOSE.String()
		return
	}
	sid := user.GetPlaySid()
	if sid == 0 {
		user.SID = 0
	} else {
		roomState := GetRoomState(sid)
		if roomState == slg.SERVER_STATUS_STOP {
			user.SID = 0
			log.Info("1 BindUserAndSend room isStop. uid: %v, sid: %v", user.UID, sid)
		} else if roomState == slg.SERVER_STATUS_CLOSE {
			user.SID = 0
			user.SetPlaySid(0)
			log.Info("1 BindUserAndSend room isClose. uid: %v, sid: %v", user.UID, sid)
		} else if _, e := this.InvokeGameRpc(sid, slg.RPC_SELECT_ROOM, user.UID); e != "" {
			user.SID = 0
			if err == ecode.ROOM_NOT_EXIST.String() {
				log.Info("2 BindUserAndSend room isStop. sid: %v", sid)
			} else if isGiveGame := err == ecode.YET_GIVE_GAME.String(); isGiveGame || err == ecode.ROOM_CLOSE.String() {
				if isGiveGame {
					user.AddGiveupGame(sid)
				}
				user.SetPlaySid(0)
				log.Info("2 BindUserAndSend room isClose or isGiveGame. uid: %v, sid: %v", user.UID, sid)
			} else {
				log.Info("2 BindUserAndSend room error. uid: %v, sid: %v", user.UID, sid)
			}
		}
	}
	// 兼容
	if user.SID != user.GetPlaySid() {
		user.SID = 0
	}
	// 直接绑定uid
	session.Bind(user.UID)
	session.Set("sid", ut.Itoa(user.SID)) // 在这里设置一下服务器id
	session.Push()
	user.Session = session
	// 记录登录天数
	now := time.Now().UnixMilli()
	t1 := ut.DateZeroTime(user.LastLoginTime) // 最后一次登录时间
	t2 := ut.DateZeroTime(now)                // 当前登录时间
	day := (t2 - t1) / ut.TIME_DAY
	if day > 0 {
		user.LoginDayCount += 1
		if user.SignDays < lc.SIGN_DAYS_MAX {
			user.SignDays++
		}
		// 连续登录天数
		if day == 1 {
			user.CLoginDayCount += 1
		} else {
			user.CLoginDayCount = 1
		}
	} else if user.SignDays == 0 {
		user.SignDays = 1
	}
	// 生成sessionId
	user.SessionId = user.UID + ut.String(time.Now().UnixMilli())
	// 通知好友
	user.FriendInfoNotify()
	// 设置离线时间到redis
	user.SetUserOfflineTime(0)
	// 获取好友在线状态
	user.GetFriendsOfflineTime()
	// 战令检测
	user.BattlePass.SeasonTimeoutCheck(user.UID)
	log.Info("2 BindUser uid: %v, sid: %v, playSid: %v, done.", user.UID, user.SID, user.GetPlaySid())
	// 返回
	accountToken = GetGameToken(user, session.GetIP())
	ret = user.ToPb()
	user.FlagUpdateDB()
	return
}

func (this *Lobby) tryLogin(session gate.Session, msg *pb.LOBBY_HD_TRYLOGIN_C2S) (bytes []byte, err string) {
	ip := session.GetIP()
	if !this.checkLoginLimit(ip) {
		log.Info("tryLogin checkLoginLimit. ip: %v", ip)
		// 登录ip限制检测
		return nil, ecode.ROOM_CLOSE.String()
	} else if !initFinish {
		log.Info("tryLogin noInitFinish. ip: %v", ip)
		// 游戏服信息未初始化
		return nil, ecode.ROOM_CLOSE.String()
	} else if msg.GetVersion() != slg.CLIENT_VERSION {
		return nil, ecode.VERSION_TOOLOW.String() // 版本号不对
	}
	accountToken := msg.GetAccountToken()
	user, err := this.DecodeToken(accountToken, this.GetLid()) // 解析token并获取user
	if err != "" {
		return nil, err
	} else if user.LogoutApplyTime > 0 && ut.Now() >= user.LogoutApplyTime+slg.LOGOUT_APPLY_TIME {
		DelUserAndDBAndMap(user.UID) // 删除玩家
		return nil, ecode.NOT_ACCOUNT_TOKEN.String()
	}
	isNewUser := user.LastLoginTime == 0 // 是否新用户
	if distictId := msg.GetDistinctId(); distictId != "" {
		user.DistinctId = distictId
	}
	if os := msg.GetOs(); os != "" {
		user.DeviceOS = os
	}
	if appsflyerId := msg.GetAppsflyerId(); appsflyerId != "" {
		user.AppsflyerId = appsflyerId
	}
	if adId := msg.GetAdvertisingId(); adId != "" {
		user.AdvertisingId = msg.GetAdvertisingId()
	}
	// 检测封禁
	banAccountEndTime := ut.MaxInt64(0, user.BanAccountEndTime-time.Now().UnixMilli())
	if user.BanAccountType != -1 && banAccountEndTime > 0 {
		body, _ := pb.ProtoMarshal(&pb.LOBBY_HD_TRYLOGIN_S2C{BanAccountType: user.BanAccountType, BanAccountSurplusTime: banAccountEndTime})
		SetUserOffline(user)
		return body, ecode.BAN_ACCOUNT.String()
	}
	rst, accountToken, err := this.BindUserAndSend(session, user)
	if err != "" {
		SetUserOffline(user)
		return nil, err
	}
	if platform := msg.GetPlatform(); platform != "" {
		user.Platform = platform
	}
	this.updateUserLanguage(user, msg.GetLang())
	if isNewUser {
		this.updateUserInvite(user, msg.GetInviteUid())
	}
	// dh上报
	user.DhLoginLog()
	// 检测月卡奖励补发
	user.CheckMonthCardDailyAward()
	// 登录相关任务更新进度
	user.LoginUpdateTask()
	return pb.ProtoMarshal(&pb.LOBBY_HD_TRYLOGIN_S2C{User: rst, AccountToken: accountToken})
}

// facebook绑定
func (this *Lobby) faceBookBind(session gate.Session, msg *pb.LOBBY_HD_FACEBOOKBIND_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	user := GetUserByOnline(uid)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	// 游客登录才可以绑定
	if user.LoginType != slg.LOGIN_TYPE_GUEST {
		log.Error("faceBookBind not guest uid: %v", uid)
		return nil, ecode.BIND_USER_NOT_GUEST.String()
	}
	token, jwtToken, fbId := msg.GetToken(), msg.GetJwtToken(), msg.GetUserId()
	// 检测该facebook账号是否绑定过用户
	otherUser, err := db.FindByLoginTypeOpenid(slg.LOGIN_ID_TYPE_FACEBOOK, fbId)
	if err == "" && otherUser.Uid != "" {
		log.Error("faceBookBind already bind uid: %v, openid: %v", uid, fbId)
		return nil, ecode.ACCOUNT_ALREADY_BIND.String()
	}
	res, e := sdk.FacebookLoginVerify(token, jwtToken, fbId)
	if e != nil {
		return nil, ecode.LOGIN_CHECK_FAIL.String()
	}
	if user.ModifyNameCount == 0 && res.Name != "" && checkNickname(res.Name) {
		user.Nickname = res.Name
		// 通知游戏服
		this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_CHANGE_NICKNAME, user.UID, user.Nickname)
	}
	user.LoginType = slg.LOGIN_TYPE_FACEBOOK
	db.UpdateAccountBind(uid, slg.LOGIN_TYPE_FACEBOOK, slg.LOGIN_ID_TYPE_FACEBOOK, fbId)
	return pb.ProtoMarshal(&pb.LOBBY_HD_FACEBOOKBIND_S2C{LoginType: slg.LOGIN_TYPE_FACEBOOK, Nickname: user.Nickname})
}

// apple绑定
func (this *Lobby) appleBind(session gate.Session, msg *pb.LOBBY_HD_APPLEBIND_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	user := GetUserByOnline(uid)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	// 游客登录才可以绑定
	if user.LoginType != slg.LOGIN_TYPE_GUEST {
		log.Error("appleBind not guest uid: %v", uid)
		return nil, ecode.BIND_USER_NOT_GUEST.String()
	}
	code, token, userId, nickName := msg.GetCode(), msg.GetToken(), msg.GetUserId(), msg.GetNickname()
	// 检测该apple账号是否绑定过用户
	otherUser, err := db.FindByLoginTypeOpenid(slg.LOGIN_ID_TYPE_APPLE, userId)
	if err == "" && otherUser.Uid != "" {
		log.Error("appleBind already bind uid: %v, openid: %v", uid, userId)
		return nil, ecode.ACCOUNT_ALREADY_BIND.String()
	}
	_, e := sdk.AppleLoginCheck(code, token, userId)
	if e != nil {
		return nil, ecode.LOGIN_CHECK_FAIL.String()
	}
	if user.ModifyNameCount == 0 && nickName != "" && checkNickname(nickName) {
		user.Nickname = nickName
		// 通知游戏服
		this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_CHANGE_NICKNAME, user.UID, user.Nickname)
	}
	user.LoginType = slg.LOGIN_TYPE_APPLE
	db.UpdateAccountBind(uid, slg.LOGIN_TYPE_APPLE, slg.LOGIN_ID_TYPE_APPLE, userId)
	return pb.ProtoMarshal(&pb.LOBBY_HD_APPLEBIND_S2C{LoginType: slg.LOGIN_TYPE_APPLE, Nickname: user.Nickname})
}

// google绑定
func (this *Lobby) googleBind(session gate.Session, msg *pb.LOBBY_HD_GOOGLEBIND_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	user := GetUserByOnline(uid)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	// 游客登录才可以绑定
	if user.LoginType != slg.LOGIN_TYPE_GUEST {
		log.Error("googleBind not guest uid: %v", uid)
		return nil, ecode.BIND_USER_NOT_GUEST.String()
	}
	token, userId, nickName := msg.GetIdToken(), msg.GetUserId(), msg.GetNickname()
	// 检测该google账号是否绑定过用户
	otherUser, err := db.FindByLoginTypeOpenid(slg.LOGIN_ID_TYPE_GOOGLE, userId)
	if err == "" && otherUser.Uid != "" {
		log.Error("googleBind already bind uid: %v, openid: %v", uid, userId)
		return nil, ecode.ACCOUNT_ALREADY_BIND.String()
	}
	_, e := sdk.GoogleLoginCheck(token)
	if e != nil {
		return nil, ecode.LOGIN_CHECK_FAIL.String()
	}
	if user.ModifyNameCount == 0 && nickName != "" && checkNickname(nickName) {
		user.Nickname = nickName
		// 通知游戏服
		this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_CHANGE_NICKNAME, user.UID, user.Nickname)
	}
	user.LoginType = slg.LOGIN_TYPE_GOOGLE
	db.UpdateAccountBind(uid, slg.LOGIN_TYPE_GOOGLE, slg.LOGIN_ID_TYPE_GOOGLE, userId)
	return pb.ProtoMarshal(&pb.LOBBY_HD_GOOGLEBIND_S2C{LoginType: slg.LOGIN_TYPE_GOOGLE, Nickname: user.Nickname})
}

// 上传推送消息token
func (this *Lobby) uploadFcmToken(session gate.Session, msg *pb.LOBBY_HD_UPLOADFCMTOKEN_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	this.updateFCMToken(user, msg.GetToken())
	return
}

// 离线推送设置
func (this *Lobby) setOfflineNotify(session gate.Session, msg *pb.LOBBY_HD_SETOFFLINENOTIFY_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	this.updateOfflineNotifyOpt(user, ut.Int32Array(msg.GetOpt()))
	return
}

// 注销账号
func (this *Lobby) delAccount(session gate.Session, msg *pb.LOBBY_HD_DELACCOUNT_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	user := GetUserByOnline(uid)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.IsGuest() {
		return nil, ecode.GUEST_NOT_LOGOUT.String()
	}
	name, email, identity := msg.GetName(), msg.GetEmail(), msg.GetIdentity()
	if !slg.IsDebug() {
		if time := time.Now().UnixMilli() - user.CreateTime; time < slg.LOGOUT_MAX_DAY { // 超过7天才能注销
			ret, _ := pb.ProtoMarshal(&pb.LOBBY_HD_DELACCOUNT_S2C{SurplusLogoutTime: int32(slg.LOGOUT_MAX_DAY - time)})
			return ret, ecode.LOGOUT_WAIT_TIME_UNMET.String()
		} else if err = lc.CheckRealName(name, email, identity); err != "" {
			return
		} /*  else if user.GetBannedChatEndTime() > 0 {
			return nil, ecode.BAN_OPT.String()
		} */
	}
	user.LogoutApplyTime = time.Now().UnixMilli()
	data, _ := db.FindByUid(uid)
	openId := ""
	switch user.LoginType {
	case slg.LOGIN_TYPE_WX:
		openId = data.Openid
	case slg.LOGIN_TYPE_GOOGLE:
		openId = data.GoogleOpenid
	case slg.LOGIN_ID_TYPE_APPLE:
		openId = data.AppleOpenid
	case slg.LOGIN_TYPE_FACEBOOK:
		openId = data.FacebookOpenid
	}
	ApplyDelUser(user, name, email, identity, openId)
	return
}

// 撤回注销
func (this *Lobby) revocationLogout(session gate.Session, msg *pb.LOBBY_HD_REVOCATIONLOGOUT_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	user := GetUserByOnline(uid)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	user.LogoutApplyTime = 0
	this.CancelDelUser(user.UID)
	return pb.ProtoMarshal(&pb.LOBBY_HD_GETSERVERS_S2C{})
}

// 放弃对局
func (this *Lobby) giveupGame(session gate.Session, msg *pb.LOBBY_HD_GIVEUPGAME_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	user := GetUserByOnline(uid)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	if user.PlaySID == 0 {
		return nil, ecode.UNKNOWN.String()
	}
	sid := user.PlaySID
	room := GetRoomById(sid)
	if room == nil || room.IsClose {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}

	// 通知游戏服
	rst, e := ut.RpcInterfaceMap(this.InvokeGameRpc(user.PlaySID, slg.RPC_PLAYER_GIVEUP_GAME, uid))
	if e != "" {
		return nil, e
	}
	settleInfo := ut.MapInterface(rst["settleInfo"])
	alliMap := ut.MapInterface(rst["settleAlliInfo"])

	session.SetPush("sid", "") // 在这里设置一下服务器id
	user.SID = 0
	user.SetPlaySid(0)
	// 记录到放弃里面
	user.AddGiveupGame(sid)
	// 扣除评分 只有排位区才扣除
	var g int32
	if room.Type == slg.RANK_SERVER_TYPE {
		g = AddUserRankScore(user, -120)
	}
	score, rank, alliUid := ut.Int32(settleInfo["score"]), ut.Int32(settleInfo["rank"]), ut.String(settleInfo["alli_uid"])
	roomType := sid / slg.ROOM_TYPE_FLAG
	if roomType == slg.ROOKIE_SERVER_TYPE {
		// 判断是否通关新手区
		user.CheckPassNewBie(rank, score, true)
	}
	pawnStatistic := GetPlayerPawnStatistics(uid, ut.String(sid))
	gameRecordDB.AddGameRecord(CreateGameRecord(sid, []int64{room.CreateTime, time.Now().UnixMilli()}, settleInfo, g, user.RankScore, 0, pawnStatistic, alliMap[alliUid]))
	user.FriendInfoNotify()
	user.FlagUpdateDB()
	// 退出队伍
	_, err = this.InvokeTeamFunc(user.TeamUid, slg.RPC_GAMEOVER_LEAVE_TEAM, user.UID)
	if err != "" {
		log.Error("playerGiveupGame rpc exist team uid: %v, err: %v", uid, err)
	}
	return pb.ProtoMarshal(&pb.LOBBY_HD_GIVEUPGAME_S2C{
		RankScore:       int32(rank),
		PassNewbieIndex: int32(user.PassNewbieIndex),
	})
}

// 获取当前对局信息
func (this *Lobby) getCurGameInfo(session gate.Session, msg *pb.LOBBY_HD_GETCURGAMEINFO_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	user := GetUserByOnline(uid)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	if user.PlaySID == 0 {
		return pb.ProtoMarshal(&pb.LOBBY_HD_GETCURGAMEINFO_S2C{})
	}
	rst, e := ut.RpcInterfaceMap(this.InvokeGameRpc(user.PlaySID, slg.RPC_GET_PLAYER_CUR_GAME_INFO, uid))
	if e == "" && rst != nil {
		return pb.ProtoMarshal(&pb.LOBBY_HD_GETCURGAMEINFO_S2C{
			Score:   ut.Int32(rst["score"]),
			Rank:    ut.Int32(rst["rank"]),
			RunTime: ut.Int64(rst["runTime"]),
		})
	} else {
		err = e
		log.Error("getCurGameInfo err: %v", e)
	}
	return
}

// 作弊封禁中点击或挂机
// func (this *Lobby) banCheatTap(session gate.Session, msg *pb.LOBBY_HD_BANCHEATTAP_C2S) (bytes []byte, err string) {
// 	uid := session.GetUserID()
// 	user := GetUserByOnline(uid)
// 	if uid == "" {
// 		return nil, ecode.NOT_BIND_UID.String()
// 	}
// 	count, hangUpTime := ut.Int(msg.GetCount()), ut.Int(msg.GetTime())
// 	if count <= 0 && hangUpTime <= 0 {
// 		return
// 	}
// 	oldCount, oldHangupTime := 0, 0
// 	if user.BanCheatTapData == nil {
// 		user.BanCheatTapData = []int{0, 0}
// 	} else {
// 		oldCount = user.BanCheatTapData[0]
// 		oldHangupTime = user.BanCheatTapData[1]
// 	}
// 	if count > oldCount {
// 		user.BanCheatTapData[0] = count
// 		// 检测点击是否触发惩罚
// 		failTime := 0
// 		for k, v := range constant.ANTI_CHEAT_BAN_TAP_MAP {
// 			if count >= k && v > failTime {
// 				failTime = v
// 			}
// 		}
// 		if failTime > 0 {
// 			// 增加连续失败次数和封禁时间
// 			this.ChangeUserAntiCheatData(user, false, failTime)
// 		}
// 	}
// 	if hangUpTime > oldHangupTime {
// 		user.BanCheatTapData[1] = hangUpTime
// 		// 检测挂机是否触发惩罚
// 		failTime := 0
// 		for k, v := range constant.ANTI_CHEAT_BAN_HANG_UP_MAP {
// 			if hangUpTime >= k && v > failTime {
// 				failTime = v
// 			}
// 		}
// 		if failTime > 0 {
// 			// 增加连续失败次数和封禁时间
// 			this.ChangeUserAntiCheatData(user, false, failTime)
// 		}
// 	}
// 	return pb.ProtoMarshal(&pb.LOBBY_HD_BANCHEATTAP_S2C{BanCheatSurplusTime: int64(ut.Max(0, user.AntiCheatBanEndTime-time.Now().UnixMilli()))})
// }

// 同步偏好设置
func (this *Lobby) syncPreferenceData(session gate.Session, msg *pb.LOBBY_HD_SYNCPREFERENCEDATA_C2S) (bytes []byte, err string) {
	// uid := session.GetUserID()
	// if uid == "" {
	// 	log.Error("not bind uid")
	// 	return nil, ecode.NOT_BIND_UID.String()
	// }
	// key, val := ut.String(msg["key"]), msg["val"]
	// PutPreferenceData(uid, key, val)
	return pb.ProtoMarshal(&pb.LOBBY_HD_SYNCPREFERENCEDATA_S2C{})
}

// 同步新手引导标记
func (this *Lobby) syncGuideTag(session gate.Session, msg *pb.LOBBY_HD_SYNCGUIDETAG_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		log.Error("not bind uid")
		return nil, ecode.NOT_BIND_UID.String()
	}
	id, tag := ut.Int(msg.GetId()), ut.String(msg.GetTag())
	user.GuidesLock.Lock()
	guide := array.Find(user.Guides, func(m *GuideInfo) bool { return m.ID == id })
	if guide == nil {
		user.Guides = append(user.Guides, &GuideInfo{ID: id, Tag: tag})
	} else {
		guide.Tag = tag
	}
	user.GuidesLock.Unlock()
	user.FlagUpdateDB()
	// slg.Log("syncGuideTag", id, tag)
	return pb.ProtoMarshal(&pb.LOBBY_HD_SYNCGUIDETAG_S2C{})
}

// 获取称号列表
func (this *Lobby) getUserTitles(session gate.Session, msg *pb.LOBBY_HD_GETUSERTITLES_C2S) (bytes []byte, err string) {
	user := GetUserByDBNotAdd(msg.GetUid())
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	return pb.ProtoMarshal(&pb.LOBBY_HD_GETUSERTITLES_S2C{
		Title:  user.LastTitle,
		Titles: user.TitlesToPb(),
	})
}

// 获取个人简介
func (this *Lobby) getUserPersonalDesc(session gate.Session, msg *pb.LOBBY_HD_GETUSERPERSONALDESC_C2S) (bytes []byte, err string) {
	user := GetUserByDBNotAdd(msg.GetUid())
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	return pb.ProtoMarshal(&pb.LOBBY_HD_GETUSERPERSONALDESC_S2C{PersonalDesc: user.PersonalDesc})
}

// 获取用户总局数
func (this *Lobby) getUserTotalGameCount(session gate.Session, msg *pb.LOBBY_HD_GETUSERTOTALGAMECOUNT_C2S) (ret []byte, err string) {
	user := GetUserByDBNotAdd(msg.GetUid())
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	return pb.ProtoMarshal(&pb.LOBBY_HD_GETUSERTOTALGAMECOUNT_S2C{
		TotalGameCount: array.Map(user.AccTotalGameCounts, func(m int32, _ int) int32 { return m }),
	})
}

// 获取单条游戏对局记录
func (this *Lobby) getGameRecordOne(session gate.Session, msg *pb.LOBBY_HD_GETGAMERECORDONE_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	data, err := gameRecordDB.FindOne(user.UID, int(msg.GetSid()))
	if err != "" {
		return nil, ecode.DB_ERROR.String()
	}
	return pb.ProtoMarshal(&pb.LOBBY_HD_GETGAMERECORDONE_S2C{Data: data.ToPb()})
}

// 获取游戏对局记录列表
func (this *Lobby) getGameRecordList(session gate.Session, msg *pb.LOBBY_HD_GETGAMERECORDLIST_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	serverType, page := pb.Int(msg.GetServerType()), pb.Int(msg.GetPage())
	datas, err := gameRecordDB.FindAll(user.UID, serverType, page)
	if err != "" {
		return nil, ecode.DB_ERROR.String()
	}
	s2c := &pb.LOBBY_HD_GETGAMERECORDLIST_S2C{Datas: array.Map(datas, func(m GameRecordData, _ int) *pb.GameRecordInfo { return m.ToPb() })}
	if page == 1 {
		var totalPage int
		// 第一页则返回总页数
		if len(datas) < slg.SHOW_USER_SERVER_RECORD_PAGE_NUM {
			totalPage = 1
		} else {
			totalCount, err := gameRecordDB.FindCount(user.UID, serverType)
			if err != "" {
				return nil, ecode.DB_ERROR.String()
			}
			totalPage = totalCount / slg.SHOW_USER_SERVER_RECORD_PAGE_NUM
			if totalCount%slg.SHOW_USER_SERVER_RECORD_PAGE_NUM > 0 {
				totalPage++
			}
		}
		s2c.TotalPage = int32(totalPage)
	}
	return pb.ProtoMarshal(s2c)
}

// 同步检测触发好评次数
func (this *Lobby) syncCheckPraiseCount(session gate.Session, msg *pb.LOBBY_HD_SYNCCHECKPRAISECOUNT_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return
	} else if user.CheckPraiseCount >= 4 {
		return
	}
	user.CheckPraiseCount = msg.GetCheckPraiseCount()
	if mailId := msg.GetMailId(); mailId > 0 { // 表示 已经评论了 发放奖励
		user.CheckPraiseCount = 4
		this.InvokeMailRpcNR(slg.RPC_SEND_MAIL_ITEM_ONE, 0, mailId, "", "", "-1", user.UID, []*g.TypeObj{g.NewTypeObj(ctype.GOLD, 0, 20)})
	}
	user.FlagUpdateDB()
	return
}

// 战斗预测扣除费用
func (this *Lobby) battleForecast(session gate.Session, msg *pb.LOBBY_HD_BATTLEFORECAST_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	room := GetRoomById(msg.GetSid())
	if room == nil {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	var cost, cost_reason int32
	user.BattleForecastCount += 1
	/* if user.FreeAdSubCheck(time.Now().UnixMilli()) { // 有订阅
		cost_reason = -1
	} else  */if user.BattleForecastCount > slg.BATTLE_FORECAST_FREE_COUNT { // 扣除费用
		cost = slg.BATTLE_FORECAST_COST
		user.ChangeGold(-cost, constant.GOLD_CHANGE_BATTLE_FORECAST_COST)
	} else {
		cost_reason = -3
	}
	user.FlagUpdateDB()
	// 上报
	ta.Track(user.SID, user.UID, user.DistinctId, 0, "ta_battle_forecast", map[string]interface{}{
		"count":         user.BattleForecastCount,
		"gold_cost":     cost,
		"cost_reason":   cost_reason,
		"landcount":     int(msg.GetLandCount()),
		"maincitylevel": int(msg.GetMaincityLevel()),
		"field_dis":     int(msg.GetLandDis()),
		"field_lv":      int(msg.GetLandlv()),
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_BATTLEFORECAST_S2C{
		BattleForecastCount: user.BattleForecastCount,
		Gold:                user.Gold,
	})
}

// 发送喇叭
func (this *Lobby) sendTrumpet(session gate.Session, msg *pb.LOBBY_HD_SENDTRUMPET_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if !slg.IsDebug() && (user.IsGuest() || user.GetTotalGameCount() < 1) {
		return nil, ecode.NOT_SEND.String()
	} else if user.IsBannedChat() {
		return nil, ecode.BAN_OPT.String() // 被禁言不可操作
	}
	cost := slg.SEND_TRUMPET_COST + user.TodaySendTrumpetCount*slg.SEND_TRUMPET_ACC_COST
	if user.ChangeIngot(-cost, constant.GOLD_CHANGE_SEND_TRUMPET_COST) == -1 {
		return nil, ecode.INGOT_NOT_ENOUGH.String()
	}
	// 记录次数
	user.TodaySendTrumpetCount += 1
	user.FlagUpdateDB()
	// 通知全服
	this.PutNotifyQueue(-1, &pb.OnUpdateLobbyNotify{
		Type: NQ_USER_TRUMPET,
		Data_3: &pb.UserTrumpetInfo{
			Nickname: user.Nickname,
			Content:  sensitive.Replace(msg.GetContent()),
		},
	})
	// 上报
	ta.Track(user.SID, user.UID, user.DistinctId, 0, "ta_sendTrumpet", map[string]interface{}{
		"ingot_cost": cost,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_SENDTRUMPET_S2C{
		Ingot:                 user.Ingot,
		TodaySendTrumpetCount: user.TodaySendTrumpetCount,
	})
}

// 点赞九万亩
func (this *Lobby) likeJwm(session gate.Session, msg *pb.LOBBY_HD_LIKEJWM_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	user.AccLikeJwmCount += 1
	user.FlagUpdateDB()
	this.InvokeChatRpcNR(slg.RPC_LIKE_JWM, 1)
	return
}

// 领取排位奖励
func (this *Lobby) getRankReward(session gate.Session, msg *pb.LOBBY_HD_GETRANKREWARD_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}

	if user.RankSeason != RANK_SEASON {
		return nil, ecode.UNKNOWN.String()
	}
	id := msg.GetId()
	// 判断是否领取过
	if user.RankRewardRecord.Get(id) {
		return nil, ecode.YET_CLAIM.String()
	}
	cfg := config.GetJsonData("seasonReward", id)
	seasonStr := "s" + ut.String(RANK_SEASON)
	if cfg == nil || cfg[seasonStr] == nil {
		return nil, ecode.UNKNOWN.String()
	}
	if user.RankScore < ut.Int32(cfg["rank_score"]) { // 判断是否达到排位分
		return nil, ecode.UNKNOWN.String()
	}
	user.RankRewardRecord.Set(id, true)
	items := g.StringToTypeObjs(cfg[seasonStr])
	s2c := &pb.LOBBY_HD_GETRANKREWARD_S2C{
		Rewards:          &pb.UpdateOutPut{},
		RankRewardRecord: user.ToRankRewardRecordPb(),
	}
	user.ChangeUserItems(items, constant.GOLD_CHANGE_RANK_REWARD_GET, s2c.Rewards)
	user.FlagUpdateDB()
	return pb.ProtoMarshal(s2c)
}

// 完成新手村
func (this *Lobby) completeNovice(session gate.Session, msg *pb.LOBBY_HD_COMPLETENOVICE_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	gold, warToken := msg.GetGold(), msg.GetWarToken()
	if user.CarryNoviceData {
		// 未完成过则发放奖励
		if gold > 0 && gold <= slg.NOVICE_GOLD {
			user.ChangeGold(gold, constant.GOLD_CHANGE_COMPLETE_NOVICE)
		}
		if warToken > 0 && warToken <= slg.NOVICE_WAR_TOKEN {
			user.ChangeWarToken(warToken, constant.GOLD_CHANGE_COMPLETE_NOVICE)
		}
	}
	user.CarryNoviceData = false
	user.FlagUpdateDB()
	return pb.ProtoMarshal(&pb.LOBBY_HD_COMPLETENOVICE_S2C{})
}

// 修改昵称
func (this *Lobby) modifyUserNickname(session gate.Session, msg *pb.LOBBY_HD_MODIFYUSERNICKNAME_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	nickname := msg.GetNickname()
	if ut.GetStringLen(nickname) > 14 {
		return nil, ecode.TEXT_LEN_LIMIT.String()
	} else if sta := sensitive.CheckName(nickname); sta != 0 {
		return nil, ut.If(sta == 1, ecode.TEXT_HAS_SENSITIVE.String(), ecode.TEXT_HAS_SPECIAL_SYMBOL.String())
	} else if db.HasNickname(nickname) {
		return nil, ecode.NICKNAME_EXIST.String()
	} else if user.ModifyNameCount > 0 && user.ChangeGold(-constant.MODIFY_NICKNAME_GOLD, constant.GOLD_CHANGE_CHANGE_NAME_COST) == -1 {
		return nil, ecode.GOLD_NOT_ENOUGH.String() // 金币不足
	}
	user.ModifyNameCount += 1 // 增加修改次数
	user.Nickname = nickname
	user.FriendInfoNotify()
	user.FlagUpdateDB()
	// 刷新队伍的用户信息
	this.InvokeTeamFuncNR(user.TeamUid, slg.RPC_UPDATE_TEAM_USER_INFO, user.UID, user.Nickname, user.HeadIcon, user.ExpectPosition, user.FarmType)
	// 立马保存一下
	db.UpdateData(user.UID, bson.M{"nickname": nickname})
	// 通知游戏服务器改动
	this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_CHANGE_NICKNAME, user.UID, nickname)
	// 返回
	return pb.ProtoMarshal(&pb.LOBBY_HD_MODIFYUSERNICKNAME_S2C{
		ModifyNameCount: user.ModifyNameCount,
		Gold:            user.Gold,
	})
}

// 修改头像
func (this *Lobby) changeUserHeadIcon(session gate.Session, msg *pb.LOBBY_HD_CHANGEUSERHEADICON_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	headIcon := msg.GetHeadIcon()
	if !strings.Contains(headIcon, "head_icon_free_") && !user.HasHeadIcon(headIcon) {
		return nil, ecode.HEAD_ICON_NOT_EXIST.String() // 如果不是免费头像就看是否解锁了这个头像
	}
	user.HeadIcon = headIcon
	user.FriendInfoNotify()
	user.FlagUpdateDB()
	// 刷新队伍的用户信息
	this.InvokeTeamFuncNR(user.TeamUid, slg.RPC_UPDATE_TEAM_USER_INFO, user.UID, user.Nickname, user.HeadIcon, user.ExpectPosition, user.FarmType)
	// 通知游戏服务器改动
	this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_CHANGE_HEADICON, user.UID, headIcon)
	return
}

// 修改个人简介
func (this *Lobby) changeUserPersonalDesc(session gate.Session, msg *pb.LOBBY_HD_CHANGEUSERPERSONALDESC_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	personalDesc := msg.GetPersonalDesc()
	if ut.GetStringLen(personalDesc) > 42 {
		return nil, ecode.UNKNOWN.String()
	} else if strings.Contains(personalDesc, "\n") || strings.Contains(personalDesc, "\t") {
		return nil, ecode.TEXT_HAS_SPECIAL_SYMBOL.String()
	} else if sensitive.Validate(personalDesc) {
		return nil, ecode.TEXT_HAS_SENSITIVE.String()
	}
	user.PersonalDesc = personalDesc
	user.FlagUpdateDB()
	// 通知游戏服务器改动
	this.InvokeGameRpc(user.GetPlaySid(), slg.RPC_CHANGE_PERSONAL_DESC, user.UID, personalDesc)
	return
}

// 改变用户称号
func (this *Lobby) changeUserTitle(session gate.Session, msg *pb.LOBBY_HD_CHANGEUSERTITLE_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	if id != 0 && !user.HasTitle(id) {
		return nil, ecode.TITLE_NOT_EXIST.String()
	}
	user.LastTitle = id
	user.FlagUpdateDB()
	// 通知游戏服务器改动
	this.InvokeGameRpc(user.GetPlaySid(), slg.RPC_CHANGE_USER_TITLE, user.UID, id)
	return
}

// 检测登录ip限制
func (this *Lobby) checkLoginLimit(ip string) bool {
	switch this.loginLimitType {
	case slg.LOGIN_LIMIT_TYPE_NULL:
		// 无限制
		return true
	case slg.LOGIN_LIMIT_TYPE_WHITE_LIST:
		// 仅白名单可登录
		rst, err := rds.RdsHGet(rds.RDS_LOGIN_WHITE_MAP_KEY, ip)
		if err == nil && rst != "" {
			return true
		} else {
			return false
		}
	case slg.LOGIN_LIMIT_TYPE_BLACK_LIST:
		// 黑名单不可登录
		rst, err := rds.RdsHGet(rds.RDS_LOGIN_BLACK_MAP_KEY, ip)
		if err == nil && rst != "" {
			return false
		} else {
			return true
		}
	}
	return true
}
