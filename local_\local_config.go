package local

const (
	DEBUG                = false                   // 是否调试模式
	SERVER_AREA          = "hk"                    // 所属区域 china.国内 hk.香港
	CONSUL_URL           = "127.0.0.1:8500"        // 服务发现
	NATS_URL             = "nats://127.0.0.1:4222" // 服务发现
	IS_SANDBOX           = false                   // 是否沙盒测试
	SERVER_IP            = "127.0.0.1"             // 服务器ip
	AUTO_CREATE_SERVER   = true                    // 自动开服
	CHECK_PLAYER_OFFLINE = true                    // 自动检测玩家是否很久没上线了
)

// TA上报参数
var (
	TA_OPEN  = true  // 是否开启数数上报
	TA_DEBUG = false // 数数上报是否调试模式
)

// ------------  GM参数 -------------
type GameGmParams struct {
	marchSpeedUp    int   // 行军加速
	ceriSpeedUp     int   // 研究加速
	drillSpeedUp    int   // 招募加速
	openGuide       bool  // 是否开启新手引导
	canOccupyTime   []int // 攻占时间
	alliSwitch      bool  // 攻占时间是否可创建/加入联盟
	buildSpeedUp    int   // 建筑修建加速
	guestCreateAlli bool  // 游客是否可创建联盟
	tradeNoticeTime []int // 交易公示期相关时间 [公示时间，公示时间(特殊)，上架超时时间，特殊时间段开始，特殊时间段结束, 可下架时间]
	transitSpeedUp  int   // 商人加速
	notJoinAlliTime []int // 不可加入联盟时间
	ancientCtbLimit bool  // 古城捐献次数限制
}

// GM参数
var GameGmParam = &GameGmParams{
	marchSpeedUp:    1,
	ceriSpeedUp:     1,
	drillSpeedUp:    1,
	openGuide:       true,
	canOccupyTime:   []int{19, 0, 23, 0},
	alliSwitch:      false,
	buildSpeedUp:    1,
	guestCreateAlli: false,
	tradeNoticeTime: []int{3600 * 1000 * 1, 3600 * 1000 * 6, 3600 * 1000 * 12, 0, 4, 10 * 60 * 1000},
	transitSpeedUp:  1,
	notJoinAlliTime: []int{19, 0, 24, 0},
	ancientCtbLimit: true,
}
