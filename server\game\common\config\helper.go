package config

import (
	"strings"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/enums/eeffect"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
)

func GetLandAttrLvByDis(dis, landLv int32) int32 {
	var maxDis int32 = 12
	if landLv == 1 {
		maxDis = 10
	} else if landLv == 2 {
		maxDis = 8
	} else if landLv == 3 {
		maxDis = 14
	} else if landLv == 4 {
		maxDis = 15
	} else if landLv == 5 {
		maxDis = 16
	}
	return ut.ClampInt32(dis, 1, maxDis)
}

// 获取区域的配置信息
func GetAreaConfInfo(landId, dis, index int32, monsterEquipIdMap map[int32]map[string]interface{}) map[string]interface{} {
	// 每距离1格算一个阶段 最大16个阶段
	landJson := GetJsonData("land", landId)
	if landJson == nil {
		return nil
	}
	landLv := ut.Int32(landJson["lv"])
	landType := ut.Int32(landJson["type"])
	level := GetLandAttrLvByDis(dis, landLv)
	json := GetJsonData("landAttr", landLv*1000+level)
	if json == nil {
		return nil
	}
	// 血量
	hp := ut.Int32(json["hp"])
	// 军队
	pawns := []map[string]interface{}{}
	armysString := ut.String(json["armys_"+ut.Itoa(landType)])
	if armysString == "" {
		armysString = ut.String(json["armys_3"])
	}
	if arr := strings.Split(armysString, "|"); len(arr) > 0 {
		for _, str := range arr {
			if str == "" {
				continue
			}
			p := strings.Split(str, ",")
			id, point, lv := ut.Int(p[0]), ut.NewVec2ByString(p[1], "_"), ut.Int(p[2])
			pawn := map[string]interface{}{
				"index": index,
				"uid":   ut.ID(),
				"point": point,
				"id":    id,
				"lv":    lv,
			}
			if equip := monsterEquipIdMap[int32(id)]; equip != nil {
				pawn["equip"] = equip
			}
			pawns = append(pawns, pawn)
		}
	}
	// 放入临时里面
	return map[string]interface{}{
		"index": index,
		"hp":    []int32{hp, hp},
		"armys": []map[string]interface{}{ // 默认就一个军队
			{
				"index": index,
				"uid":   ut.ID(),
				"pawns": pawns,
			},
		},
	}
}

// 区域信息json转pb
func AreaConfInfoJsonToPb(json map[string]interface{}) *pb.AreaInfo {
	ret := &pb.AreaInfo{
		Index: pb.Int32(json["index"]),
		Hp:    pb.Int32Array(json["hp"]),
	}
	if armys, ok := json["armys"].([]map[string]interface{}); ok {
		for _, v := range armys {
			army := &pb.AreaArmyInfo{
				Index: pb.Int32(v["index"]),
				Uid:   pb.String(v["uid"]),
			}
			if pawns, ok := v["pawns"].([]map[string]interface{}); ok {
				for _, p := range pawns {
					pawn := &pb.AreaPawnInfo{
						Index: pb.Int32(p["index"]),
						Uid:   pb.String(p["uid"]),
						Point: pb.NewVec2(p["point"]),
						Id:    pb.Int32(p["id"]),
						Lv:    pb.Int32(p["lv"]),
					}
					if equipData := p["equip"]; equipData != nil {
						equip := ut.MapInterface(equipData)
						attrs, ok := equip["attrs"].([][]int32)
						if ok {
							equipPb := &pb.EquipInfo{
								Uid:   ut.String(equip["uid"]),
								Attrs: []*pb.AttrArrayInfo{},
							}
							for _, attr := range attrs {
								equipPb.Attrs = append(equipPb.Attrs, &pb.AttrArrayInfo{Attr: attr})
							}
							pawn.Equip = equipPb
						}
					}
					army.Pawns = append(army.Pawns, pawn)
				}
			}
			ret.Armys = append(ret.Armys, army)
		}
	}
	return ret
}

// 获取古城的区域配置信息
func GetAncientCityAreaConfigPb(index, cityId int32) *pb.AreaInfo {
	cityCfg := GetJsonData("city", cityId)
	if cityCfg == nil {
		log.Error("GetAncientCityAreaConfigPb cityCfg nil index: %v, cityId: %v", index, cityId)
		return nil
	}
	// hp := ut.Int(cityCfg["hp"]) 血量在外面赋值
	ret := &pb.AreaInfo{
		Index: int32(index),
		// Hp:     []int32{int32(hp), int32(hp)},
		CityId: int32(cityId),
		Armys:  []*pb.AreaArmyInfo{},
	}
	// 获取配置军队 获取的军队配置下标为坐标对配置数量求余
	cfgLen := len(ANCIENT_ARMYS)
	cfgIndex := int(index) % cfgLen
	// cfgIndex := 4
	army := &pb.AreaArmyInfo{
		Index: pb.Int32(index),
		Uid:   ut.ID(),
		Pawns: []*pb.AreaPawnInfo{},
	}
	cfg := ANCIENT_ARMYS[cfgIndex]
	armyCfg, equipCfg := ut.MapArray(cfg["armys"]), cfg["equip"].(map[int32]string)
	for _, pawnCfg := range armyCfg {
		pawn := &pb.AreaPawnInfo{
			Index:   int32(index),
			Uid:     ut.ID(),
			ArmyUid: army.Uid,
			Id:      pb.Int32(pawnCfg["id"]),
			Lv:      pb.Int32(pawnCfg["lv"]),
		}
		// 加上石像皮肤
		pawn.SkinId = pawn.Id*1000 + 201
		// 位置
		pointStr := ut.String(pawnCfg["point"])
		pointArr := strings.Split(pointStr, ",")
		if len(pointArr) >= 2 {
			pawn.Point = &pb.Vec2{X: pb.Int32(pointArr[0]), Y: pb.Int32(pointArr[1])}
		}
		// 装备
		equip := &pb.EquipInfo{Attrs: []*pb.AttrArrayInfo{}}
		equipArr := strings.Split(equipCfg[pawn.Id], ":")
		if len(equipArr) >= 2 {
			equip.Id = pb.Int32(equipArr[0])
			equipAttrsArr := strings.Split(equipArr[1], "|")
			for _, attrStr := range equipAttrsArr {
				attrArr := strings.Split(attrStr, ",")
				equip.Attrs = append(equip.Attrs, &pb.AttrArrayInfo{
					Attr: pb.Int32Array(attrArr),
				})
			}
		}
		pawn.Equip = equip
		army.Pawns = append(army.Pawns, pawn)
	}
	ret.Armys = append(ret.Armys, army)
	return ret
}

// 获取古城升级时间
func GetAncientLvUpTime(id, lv int32) int64 {
	attrId := id*1000 + lv
	json := GetJsonData("buildAttr", attrId)
	if json == nil {
		return -1
	}
	// 配置的单位是秒
	return ut.Int64(json["bt_time"]) * 1000
}

// 获取古城名字
func GetAncientNameById(cityId int32) string {
	// 直接返回指定字符串处理多语言
	return "cityText.name_" + ut.String(cityId)
}

// 根据productId获取商品配置
func GetRechargeConfByProductId(productId string) map[string]interface{} {
	if arr := GetJson("recharge").Get("product_id", productId); len(arr) > 0 {
		return arr[0]
	}
	return nil
}

// 获取指定效果类型的装备配置
func GetEquipConfByEffct(effect string) map[string]interface{} {
	datas := GetJson("equipBase")
	if datas.Datas == nil {
		return nil
	}
	for _, v := range datas.Datas {
		if ut.String(v["effect"]) == effect {
			return v
		}
	}
	return nil
}

// 获取指定装备id的最高属性
func GetEquipTopAttrs(id int32) [][]int32 {
	rst := [][]int32{}
	equipCfg := GetJsonData("equipBase", id)
	// 主属性
	if hp := ut.String(equipCfg["hp"]); hp != "" {
		arr := ut.StringToInt32s(hp, ",")
		rst = append(rst, []int32{0, 1, arr[1]})
	}
	if attack := ut.String(equipCfg["attack"]); attack != "" {
		arr := ut.StringToInt32s(attack, ",")
		rst = append(rst, []int32{0, 2, arr[1]})
	}

	// 效果
	effectIds := ut.StringToInt32s(ut.String(equipCfg["effect"]), "|")
	// 随机效果数量
	effectCount := ut.Int(equipCfg["effect_count"])
	// 根据数量随机效果
	ids := []int32{}
	for i := 0; i < effectCount && i < len(effectIds); i++ {
		index := ut.Random(0, len(effectIds)-1)
		ids = append(ids, effectIds[index])
		effectIds = append(effectIds[:index], effectIds[index+1:]...)
	}
	// 包装属性
	attrs := [][]int32{}
	for _, id := range ids {
		if json := GetJsonData("equipEffect", id); json != nil {
			// 天选下标
			topIndex := ut.Int(json["value_max"])
			v := []int32{2, id}
			if arr := ut.StringToInt32s(ut.String(json["value"]), ","); len(arr) == 2 {
				v = append(v, arr[topIndex])
			} else {
				v = append(v, 0) // 这里就算没有 也要push一个0因为 后面可能有概率
			}
			if arr := ut.StringToInt32s(ut.String(json["odds"]), ","); len(arr) == 2 { // 效果概率
				v = append(v, arr[topIndex])
			}
			attrs = append(attrs, v)
		}
	}
	// 如果是时光旗 只要对应的主属性
	if id == 6025 && len(attrs) > 0 && len(attrs[0]) > 0 {
		switch attrs[0][1] {
		case eeffect.TODAY_ADD_HP: // 加攻生命 删除攻击
			rst = array.RemoveItem(rst, func(arr []int32) bool { return arr[0] == 0 && arr[1] == 2 })
		case eeffect.TODAY_ADD_ATTACK: // 加攻击力 删除生命
			rst = array.RemoveItem(rst, func(arr []int32) bool { return arr[0] == 0 && arr[1] == 1 })
		}
	}
	rst = append(rst, attrs...)
	return rst
}

// 获取指定士兵id的需要等级
func GetPawnNeedLv(id int32) int32 {
	cfg := GetJsonData("pawnBase", id)
	if cfg == nil {
		return 0
	}
	return ut.Int32(cfg["need_build_lv"])
}
