package ut

import (
	"sync"
)

type MapLock[K comparable, V any] struct {
	sync.RWMutex
	Map map[K]V
}

func NewMapLock[K comparable, V any]() *MapLock[K, V] {
	return &MapLock[K, V]{Map: map[K]V{}}
}

func FromMapLock[K comparable, V any](data map[K]V) *MapLock[K, V] {
	m := &MapLock[K, V]{Map: map[K]V{}}
	if data == nil {
		return m
	}
	for k, v := range data {
		m.Map[k] = v
	}
	return m
}

func (this *MapLock[K, V]) GetMap() map[K]V {
	return this.Map
}

func (this *MapLock[K, V]) FromMap(data map[K]V) *MapLock[K, V] {
	if data == nil {
		return this
	}
	this.Lock()
	for k, v := range data {
		this.Map[k] = v
	}
	this.Unlock()
	return this
}

func (this *MapLock[K, V]) Get(key K) V {
	this.RLock()
	defer this.RUnlock()
	return this.Map[key]
}

func (this *MapLock[K, V]) Set(key K, val V) {
	this.Lock()
	this.Map[key] = val
	this.Unlock()
}

func (this *MapLock[K, V]) Del(key K) {
	this.Lock()
	delete(this.Map, key)
	this.Unlock()
}

// 删除多个
func (this *MapLock[K, V]) DelAnyByKey(keys []K) {
	this.Lock()
	defer this.Unlock()
	for _, key := range keys {
		delete(this.Map, key)
	}
}

// 拷贝一个
func (this *MapLock[K, V]) Clone() map[K]V {
	this.RLock()
	defer this.RUnlock()
	obj := map[K]V{}
	for k, v := range this.Map {
		obj[k] = v
	}
	return obj
}

func (this *MapLock[K, V]) Count() int {
	this.RLock()
	defer this.RUnlock()
	return len(this.Map)
}

func (this *MapLock[K, V]) ForEach(callback func(v V, k K) bool) {
	this.RLock()
	defer this.RUnlock()
	for k, v := range this.Map {
		if !callback(v, k) {
			return
		}
	}
}

func (this *MapLock[K, V]) Find(callback func(v V, k K) bool) (_k K, _v V, _ok bool) {
	this.RLock()
	defer this.RUnlock()
	for k, v := range this.Map {
		if callback(v, k) {
			return k, v, true
		}
	}
	return
}

func (this *MapLock[K, V]) Some(callback func(v V, k K) bool) bool {
	this.RLock()
	defer this.RUnlock()
	for k, v := range this.Map {
		if callback(v, k) {
			return true
		}
	}
	return false
}

func (this *MapLock[K, V]) DeleteEach(callback func(v V, k K) bool) {
	this.Lock()
	defer this.Unlock()
	for k, v := range this.Map {
		if callback(v, k) {
			delete(this.Map, k)
		}
	}
}

func (this *MapLock[K, V]) Clean() {
	this.Lock()
	this.Map = map[K]V{}
	this.Unlock()
}
