package main

// var (
// 	lobbyDbUri  = "mongodb://localhost:27017" //大厅服数据库地址
// 	lobbyDbName = "slgsrv"                    //大厅服数据库名

// 	md5Salt     = "prize_question_1" //md5盐
// 	mailUserNum = 500                //批量发送邮件的玩家数量

// 	mailContentMap = map[int]map[string]string{
// 		// 日本
// 		0: {
// 			"title":   "『九万石』アンケート調査報酬配布",
// 			"content": "親愛なるプレイヤーの皆様、この度はアンケート調査にご協力いただき、誠にありがとうございます。報酬「コイン×30、兵符×10」をお受け取りください。どうぞ楽しくゲームをお楽しみください！",
// 		},
// 		// 韩国
// 		1: {
// 			"title":   "[9만 에이커] 만족도 설문조사 보상 지급",
// 			"content": "사랑하는 플레이어, 이번 설문조사에 참여해 주셔서 감사합니다.보상 [금화*30, 병부*10]을 확인하시고 받아주시기 바랍니다. 즐거운 게임생활 되시길 바랍니다!",
// 		},
// 		// 繁中
// 		2: {
// 			"title":   "九萬畝問卷調查獎勵發放",
// 			"content": "親愛的玩家，感謝您參與問卷調查，請查收獎勵[金幣*30,兵符*10]。祝您遊戲生活愉快！",
// 		},
// 		// 东南亚
// 		3: {
// 			"title":   "[Ninety Thousand Acres] Game Experience Questionnaire Rewards",
// 			"content": "Dear players, thank you for taking part in our survey. Please check your rewards [gold coins*30, military tokens*10]. Hope you enjoy playing our game!",
// 		},
// 	}
// )

// func main() {
// 	log.Printf("send research reward mail start!")
// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()
// 	// 连接大厅服数据库
// 	clientLobby, err := mongo.NewClient(options.Client().ApplyURI(lobbyDbUri))
// 	if err != nil {
// 		log.Fatal("parse lobbyDbUri err")
// 	}
// 	err = clientLobby.Connect(ctx)
// 	if err != nil {
// 		log.Fatal("connect lobbyDb err")
// 	}
// 	defer clientLobby.Disconnect(ctx)
// 	lobbyDb := clientLobby.Database(lobbyDbName)

// 	// 打开 Excel 文件
// 	f, err := excelize.OpenFile("九万亩问卷调研UID码.xlsx")
// 	if err != nil {
// 		log.Fatal(err)
// 	}
// 	// 遍历每一个工作表
// 	for i := 0; i < 4; i++ {
// 		// 获取工作表名称
// 		sheetName := f.GetSheetName(i) // 获取第一个工作表
// 		if sheetName == "" {
// 			log.Fatal("工作表不存在")
// 		}

// 		mailConf := mailContentMap[i]
// 		// 获取工作表的所有行数据
// 		rows, err := f.GetRows(sheetName)
// 		if err != nil {
// 			log.Fatal(err)
// 		}
// 		mailUserMap := map[string]bool{}
// 		// 遍历每一行
// 		for i, row := range rows {
// 			if i == 0 {
// 				continue
// 			}
// 			var uid, md5 string
// 			// 遍历行中的每一列
// 			for j, cell := range row {
// 				if j == 0 {
// 					continue
// 				}
// 				if j == 1 {
// 					// 第二列为uid
// 					uid = cell
// 				} else if j == 2 {
// 					// 第三列为md5码
// 					md5 = cell
// 				}
// 			}
// 			// 验证md5
// 			curMd5 := ut.MD5(uid + md5Salt)
// 			if md5 != curMd5 {
// 				log.Printf("md5 check err uid: %v, sheet: %v, md5: %v, curMd5: %v", uid, sheetName, md5, curMd5)
// 				continue
// 			}
// 			mailUserMap[uid] = true
// 			if len(mailUserMap) >= mailUserNum {
// 				// 每500玩家发放一封邮件
// 				SendMail(lobbyDb, mailUserMap, mailConf)
// 				mailUserMap = map[string]bool{}
// 			}
// 		}
// 		if len(mailUserMap) > 0 {
// 			// 该表格遍历完成 给剩余的玩家发放
// 			SendMail(lobbyDb, mailUserMap, mailConf)
// 		}

// 	}

// }

// // 发送邮件
// func SendMail(database *mongo.Database, uidMap map[string]bool, mailConf map[string]string) {
// 	items := []*g.TypeObj{g.NewTypeObj(ctype.GOLD, 0, 30), g.NewTypeObj(ctype.WAR_TOKEN, 0, 10)}
// 	mail := &mail.MailBaseInfo{
// 		UID:         ut.ID(),
// 		Receiver:    "",
// 		Title:       mailConf["title"],
// 		ContentId:   0,
// 		Content:     mailConf["content"],
// 		Sender:      "-1",
// 		SID:         0,
// 		Items:       items,
// 		CreateTime:  ut.Now(),
// 		ReceiverMap: uidMap,
// 	}
// 	database.Collection(slg.DB_COLLECTION_NAME_MAIL).InsertOne(context.TODO(), mail)
// }
