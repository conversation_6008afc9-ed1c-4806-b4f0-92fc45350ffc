package g

import (
	"slgsrv/server/game/common/config"
	ut "slgsrv/utils"
)

// 画像技能
type PortrayalSkill struct {
	Params interface{}
	json   map[string]interface{}

	Id     int32
	Value  int32 // 随机值
	Target int32
}

func NewPortrayalSkill(id, value int32) *PortrayalSkill {
	json := config.GetJsonData("portrayalSkill", id)
	if json == nil {
		return nil
	}
	if valueStr := ut.String(json["value"]); valueStr != "" {
		if arr := ut.StringToInt32s(valueStr, ","); len(arr) >= 2 {
			value = ut.ClampInt32(value, arr[0], arr[1])
		}
	}
	return &PortrayalSkill{
		Id:     id,
		Value:  value,
		Target: ut.Int32(json["target"]),
		Params: json["params"],
		json:   json,
	}
}

func (this *PortrayalSkill) GetId() int32              { return this.Id }
func (this *PortrayalSkill) GetTarget() int32          { return this.Target }
func (this *PortrayalSkill) GetValue() float64         { return float64(this.Value) }
func (this *PortrayalSkill) GetParamsInt() int32       { return ut.Int32(this.Params) }
func (this *PortrayalSkill) GetParamsFloat64() float64 { return ut.Float64(this.Params) }
func (this *PortrayalSkill) GetValueMaxIndex() int32   { return ut.Int32(this.json["value_max"]) }

func (this *PortrayalSkill) GetParamsInts() []int32 {
	return ut.StringToInt32s(ut.String(this.Params), ",")
}

func (this *PortrayalSkill) GetParamsFloats() []float64 {
	return ut.StringToFloats(ut.String(this.Params), ",")
}

// 是否天选
func (this *PortrayalSkill) IsChosenOne() bool {
	index := this.GetValueMaxIndex()
	skillValueArr := ut.StringToInt32s(ut.String(this.json["value"]), ",")
	if len(skillValueArr) <= int(index) {
		return false
	}
	return skillValueArr[index] == this.Value
}
