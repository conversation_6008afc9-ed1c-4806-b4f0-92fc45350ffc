package match

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
)

func (this *Match) InitRpc() {
	this.GetServer().RegisterGO(slg.RPC_MATCH_APPLY_SERVER, this.applyServer)
	this.GetServer().RegisterGO(slg.RPC_MATCH_CANCEL_APPLY_SERVER, this.cancleApplyServer)
	this.GetServer().RegisterGO(slg.RPC_MATCH_USER_CANCEL_APPLY_SERVER, this.cancleUserApplyServer)
	this.GetServer().RegisterGO(slg.RPC_GET_ROOMS, this.getRooms)
	this.GetServer().RegisterGO(slg.RPC_GAME_SERVER_STATE_CHANGE, this.gameServerStateChange)
	this.GetServer().RegisterGO(slg.RPC_GET_ROOM_NEXT_MATCH_TIME, this.rpcGetRoomNextMatchTime)
	this.GetServer().RegisterGO(slg.RPC_GET_LOBBY_MACH_INFO, this.rpcGetRoomNextMatchTime)
	this.GetServer().RegisterGO(slg.RPC_CREATE_CUSTOM_ROOM, this.rpcCreateCustomRoom)
	this.GetServer().RegisterGO(slg.RPC_ENTER_CUSTOM_ROOM, this.rpcTeamEnterCustomRoom)
	this.GetServer().RegisterGO(slg.RPC_EXIT_CUSTOM_ROOM, this.rpcTeamExitCustomRoom)
	this.GetServer().RegisterGO(slg.RPC_TEAM_INVITE, this.rpcCustomTeamInvite)
	this.GetServer().RegisterGO(slg.RPC_TEAM_INVITE_RESPONSE, this.rpcCustomTeamInviteResponse)
}

// 新区服报名
func (this *Match) applyServer(roomType uint8, teamUid string, userList []string, rankScoreAvg, gameTotalAvg, creatTimeAvg float64) (result interface{}, err string) {
	err = TeamApply(roomType, teamUid, userList, rankScoreAvg, gameTotalAvg, creatTimeAvg)
	return
}

// 取消报名
func (this *Match) cancleApplyServer(roomType uint8, teamUid string) (result interface{}, err string) {
	err = TeamCancelApply(teamUid)
	return
}

// 个人取消报名
func (this *Match) cancleUserApplyServer(roomType uint8, teamUid, uid string) (result interface{}, err string) {
	err = UserCancelApply(roomType, teamUid, uid)
	return
}

// 获取区服信息
func (this *Match) getRooms() (result []byte, err string) {
	if !loadRoomsFinish {
		return nil, ecode.UNKNOWN.String()
	}
	ServerList := &pb.ServerList{List: []*pb.ServerInfo{}}
	now := time.Now().UnixMilli()
	// 区服信息
	rooms.ForEach(func(v *Room, k int32) bool {
		if v.IsClose() {
			// 不返回已关闭的
			return true
		}
		ServerList.List = append(ServerList.List, v.ToPb(now))
		return true
	})
	return pb.ProtoMarshal(ServerList)
}

// 游戏服信息更新
func (this *Match) gameServerStateChange(msg map[string]interface{}) (result map[string]interface{}, err string) {
	OnRoomStateChange(msg)
	return
}

// 获取区服类型下次开启时间
func (this *Match) rpcGetRoomNextMatchTime() (result map[string]interface{}, err string) {
	defaultMatchTime := time.Now().UnixMilli() + slg.INIT_ROOM_MATCH_TIME
	result = map[string]interface{}{
		"0": defaultMatchTime,
		"2": defaultMatchTime,
	}
	if info := applyInfoMap[slg.NORMAL_SERVER_TYPE]; info != nil {
		result["0"] = info.MatchTime
	}
	if info := applyInfoMap[slg.RANK_SERVER_TYPE]; info != nil {
		result["2"] = info.MatchTime
	}
	return
}

// 创建自定义房间
func (this *Match) rpcCreateCustomRoom(name, creator, password string) (result map[string]interface{}, err string) {
	sid, e := CreateCustomRoom(name, creator, password)
	result = map[string]interface{}{
		"sid": sid,
	}
	err = e
	return
}

// 队伍加入自定义房间
func (this *Match) rpcTeamEnterCustomRoom(sid int32, password string, teamInfoBytes []byte) (result map[string]interface{}, err string) {
	room := CustinRooms.Get(sid)
	if room == nil { // 自定义房间不存在
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	if password != room.Password { // 密码错误
		return nil, ecode.PASSWORD_ERROR.String()
	}
	teamInfo := &pb.TeamInfo{}
	e := pb.ProtoUnMarshal(teamInfoBytes, teamInfo)
	if e != nil {
		err = ecode.ROOM_NOT_EXIST.String()
		return
	}
	err = room.TeamEnterCustomRoom(teamInfo)
	return
}

// 队伍退出自定义房间
func (this *Match) rpcTeamExitCustomRoom(sid int32, teamUid, userId string) (result map[string]interface{}, err string) {
	room := CustinRooms.Get(sid)
	if room == nil { // 自定义房间不存在
		return
	}
	err = room.TeamExitCustomRoom(teamUid, userId)
	return
}

// 队伍邀请玩家进入自定义房间
func (this *Match) rpcCustomTeamInvite(sid int32, teamUid, userId, nickname, headicon string) (result map[string]interface{}, err string) {
	room := CustinRooms.Get(sid)
	if room == nil { // 自定义房间不存在
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	err = room.TeamInviteUser(teamUid, userId, nickname, headicon)
	return
}

// 队伍邀请玩家进入自定义房间回复
func (this *Match) rpcCustomTeamInviteResponse(sid int32, teamUid, userId string, agree bool) (result map[string]interface{}, err string) {
	room := CustinRooms.Get(sid)
	if room == nil { // 自定义房间不存在
		if !agree {
			return
		}
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	err = room.TeamInviteUserResponse(teamUid, userId, agree)
	return
}

// 获取用户服物理机信息
func (this *Match) getLobbyMachInfo(ip string) (result map[string]interface{}, err string) {
	mach := lobbyMachs.MachsMap.Get(ip)
	if mach == nil {
		err = ecode.DB_ERROR.String()
		return
	}
	result = map[string]interface{}{
		"ip":      mach.Addr,
		"status":  mach.Status,
		"abandon": mach.Abandon,
	}
	return
}
