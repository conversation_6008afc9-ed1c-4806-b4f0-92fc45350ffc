package match

import (
	"go/constant"
	"go/token"
	"reflect"
)

//go:generate yaegi extract ./

var Symbols = map[string]map[string]reflect.Value{}

func init() {
	Symbols["slgsrv/server/match/match"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"CLOSE_GAME_TIME":              reflect.ValueOf(constant.MakeFromLiteral("259200000", token.INT, 0)),
		"CreateRoom":                   reflect.ValueOf(CreateRoom),
		"DelGameCol":                   reflect.ValueOf(DelGameCol),
		"DelGameColDataBySid":          reflect.ValueOf(DelGameColDataBySid),
		"GAME_SERVER_UPDATE_TIME":      reflect.ValueOf(constant.MakeFromLiteral("10000", token.INT, 0)),
		"GenRoomData":                  reflect.ValueOf(GenRoomData),
		"GetGenIdMapKey":               reflect.ValueOf(GetGenIdMapKey),
		"GetRoomById":                  reflect.ValueOf(GetRoomById),
		"GetSubTypeBySid":              reflect.ValueOf(GetSubTypeBySid),
		"GetWinCond":                   reflect.ValueOf(GetWinCond),
		"HandleCleanMachLogs":          reflect.ValueOf(HandleCleanMachLogs),
		"InitRoomDB":                   reflect.ValueOf(InitRoomDB),
		"LoadAllRoom":                  reflect.ValueOf(LoadAllRoom),
		"LoadGameMachs":                reflect.ValueOf(LoadGameMachs),
		"LoadLobbyMachs":               reflect.ValueOf(LoadLobbyMachs),
		"LoadRoom":                     reflect.ValueOf(LoadRoom),
		"LoadRoomFromDbData":           reflect.ValueOf(LoadRoomFromDbData),
		"LoadSupMachs":                 reflect.ValueOf(LoadSupMachs),
		"MATCH_POOL_STATE_CREATE_ROOM": reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"MATCH_POOL_STATE_FINISH":      reflect.ValueOf(constant.MakeFromLiteral("100", token.INT, 0)),
		"MATCH_POOL_STATE_MATCHING":    reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"MachsSort":                    reflect.ValueOf(MachsSort),
		"Module":                       reflect.ValueOf(&Module).Elem(),
		"OnRoomStateChange":            reflect.ValueOf(OnRoomStateChange),
		"ROOM_GEN_COL_KEY_MAP_GEN_ID":  reflect.ValueOf(constant.MakeFromLiteral("\"mapGenId\"", token.STRING, 0)),
		"ROOM_GEN_COL_KEY_WIN_COND":    reflect.ValueOf(constant.MakeFromLiteral("\"win_cond\"", token.STRING, 0)),
		"RunCleanMachLogsTick":         reflect.ValueOf(RunCleanMachLogsTick),
		"RunDelRoomsDbTick":            reflect.ValueOf(RunDelRoomsDbTick),
		"RunPreGenRoomTick":            reflect.ValueOf(RunPreGenRoomTick),
		"RunTick":                      reflect.ValueOf(RunTick),
		"SetMatchIntervalTime":         reflect.ValueOf(SetMatchIntervalTime),
		"StopTick":                     reflect.ValueOf(StopTick),
		"Symbols":                      reflect.ValueOf(&Symbols).Elem(),
		"TeamApply":                    reflect.ValueOf(TeamApply),
		"TeamCancelApply":              reflect.ValueOf(TeamCancelApply),
		"ToHttpRooms":                  reflect.ValueOf(ToHttpRooms),
		"ToPbRooms":                    reflect.ValueOf(ToPbRooms),
		"UserCancelApply":              reflect.ValueOf(UserCancelApply),

		// type definitions
		"ApplyInfo":             reflect.ValueOf((*ApplyInfo)(nil)),
		"LobbyMach":             reflect.ValueOf((*LobbyMach)(nil)),
		"LobbyMachsInfo":        reflect.ValueOf((*LobbyMachsInfo)(nil)),
		"Mach":                  reflect.ValueOf((*Mach)(nil)),
		"MachMongodb":           reflect.ValueOf((*MachMongodb)(nil)),
		"MachsInfo":             reflect.ValueOf((*MachsInfo)(nil)),
		"MapUseInfo":            reflect.ValueOf((*MapUseInfo)(nil)),
		"Match":                 reflect.ValueOf((*Match)(nil)),
		"MatchPool":             reflect.ValueOf((*MatchPool)(nil)),
		"MatchTeam":             reflect.ValueOf((*MatchTeam)(nil)),
		"Mongodb":               reflect.ValueOf((*Mongodb)(nil)),
		"Room":                  reflect.ValueOf((*Room)(nil)),
		"RoomMongodb":           reflect.ValueOf((*RoomMongodb)(nil)),
		"SpuMachsInfo":          reflect.ValueOf((*SpuMachsInfo)(nil)),
		"SupMach":               reflect.ValueOf((*SupMach)(nil)),
		"UpdateServerTask":      reflect.ValueOf((*UpdateServerTask)(nil)),
		"UpdateServerTaskQueue": reflect.ValueOf((*UpdateServerTaskQueue)(nil)),
	}
}
