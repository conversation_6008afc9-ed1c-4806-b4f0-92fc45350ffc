package slg

import (
	"fmt"
	"time"

	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

var (
	serverZoneOffset       = 0 // 当前服务器的时区
	isInitServerZoneOffset = false
	OfflineMsgNotify       = false // 是否开启离线通知
)

func IsDebug() bool {
	return DEBUG
}

func IsLocal() bool {
	return SERVER_AREA == "local"
}

func IsOpenOfflineMsg() bool {
	if !IsDebug() { // 正式环境默认开启
		return true
	}
	return OfflineMsgNotify
}

// 获取服务器发现ip
func GetConsulUrl() string {
	return CONSUL_URL
}

func GetNatsUrl() string {
	return NATS_URL
}

// 是否国内区域
func IsChinaArea() bool {
	return SERVER_AREA == SERVER_AREA_CHINA
}

func Log(a ...interface{}) {
	if DEBUG {
		fmt.Println(a...)
	}
}

func GetAlliPolicySlotConf() map[int32]int32 {
	ret := map[int32]int32{}
	for k, v := range constant.ALLI_POLICY_SLOT_CONF {
		ret[int32(k)] = int32(v)
	}
	return ret
}

func GetServerZoneOffset() int {
	if isInitServerZoneOffset {
		return serverZoneOffset
	}
	_, offset := time.Now().Zone()
	serverZoneOffset = offset / 3600
	isInitServerZoneOffset = true
	return serverZoneOffset
}

// 获取服务器名字
func GetServerNoName(sid int, lang string) string {
	sType := sid / ROOM_TYPE_FLAG
	subType := sid / ROOM_SUB_TYPE_FLAT % (ROOM_TYPE_FLAG / ROOM_SUB_TYPE_FLAT)
	typeName := SERVER_TYPE_NAME[sType][subType][lang]
	sidStr := ut.Itoa(sid % ROOM_TYPE_FLAG)
	if subType != 0 {
		sidStr = ut.Itoa(sid % ROOM_SUB_TYPE_FLAT)
	}
	return typeName + sidStr
}

// 获取服务器名字key
func GetServerNoNameKey(sid int32) string {
	sType := sid / ROOM_TYPE_FLAG
	subType := sid / ROOM_SUB_TYPE_FLAT % (ROOM_TYPE_FLAG / ROOM_SUB_TYPE_FLAT)
	sidStr := ut.Itoa(sid % ROOM_TYPE_FLAG)
	if subType != 0 {
		sidStr = ut.Itoa(sid % ROOM_SUB_TYPE_FLAT)
	}
	return "@serverName_" + ut.Itoa(sType) + "_" + sidStr
}

// 获取夏季开始时间
func GetSummerStartTime(createTime int64) int64 {
	createZeroTime := ut.DateZeroTime(createTime)
	// 计算夏季开始时间
	return createZeroTime + 3*ut.TIME_DAY // 一个季节持续的天数
}

// ------------  GM参数 -------------

// 获取行军加速度
func GetMarchUpSpeed() int        { return GameGmParam.marchSpeedUp }
func SetMarchUpSpeed(speedup int) { GameGmParam.marchSpeedUp = speedup }

// 获取研究加速
func GetCeriSpeedUp() int        { return GameGmParam.ceriSpeedUp }
func SetCeriSpeedUp(speedup int) { GameGmParam.ceriSpeedUp = speedup }

// 获取训练加速
func GetDrillSpeedUp() int        { return GameGmParam.drillSpeedUp }
func SetDrillSpeedUp(speedup int) { GameGmParam.drillSpeedUp = speedup }

// 获取默认开启新手引导
func GetDefaultOpenGuide() bool { return GameGmParam.openGuide }

// 获取可攻占时间
func GetCanOccupyTime() []int    { return GameGmParam.canOccupyTime }
func SetCanOccupyTime(val []int) { GameGmParam.canOccupyTime = val }
func ToCanOccupyTime[T int32 | int]() []T {
	return array.Map(GameGmParam.canOccupyTime, func(m int, _ int) T { return T(m) })
}

// 攻占时间是否可创建/加入联盟
func GetAlliSwitch() bool    { return GameGmParam.alliSwitch }
func SetAlliSwitch(val bool) { GameGmParam.alliSwitch = val }

// 获取建筑修建加速
func GetBuildSpeedUp() int        { return GameGmParam.buildSpeedUp }
func SetBuildSpeedUp(speedup int) { GameGmParam.buildSpeedUp = speedup }

// 获取游客是否可创建联盟
func GetGuestCreateAlli() bool    { return GameGmParam.guestCreateAlli }
func SetGuestCreateAlli(val bool) { GameGmParam.guestCreateAlli = val }

// 获取交易公示时间
func GetTradeNoticeTime() []int    { return GameGmParam.tradeNoticeTime }
func SetTradeNoticeTime(val []int) { GameGmParam.tradeNoticeTime = val }

// 获取商人加速
func GetTransitSpeedUp() int        { return GameGmParam.transitSpeedUp }
func SetTransitSpeedUp(speedup int) { GameGmParam.transitSpeedUp = speedup }

// 获取可攻占时间
func GetNotJoinAlliTime() []int    { return GameGmParam.notJoinAlliTime }
func SetNotJoinAlliTime(val []int) { GameGmParam.notJoinAlliTime = val }
func ToCanJoinAlliTime[T int32 | int]() []T {
	return array.Map(GameGmParam.notJoinAlliTime, func(m int, _ int) T { return T(m) })
}

// 攻占时间是否可创建/加入联盟
func GetAncientCtbLimit() bool    { return GameGmParam.ancientCtbLimit }
func SetAncientCtbLimit(val bool) { GameGmParam.ancientCtbLimit = val }

// 兼容数值
func CompatiValue(oMin, oMax, nMin, nMax float64, val int) int {
	r := (float64(val) - oMin) / (oMax - oMin)
	return ut.Clamp(ut.Round(r*(nMax-nMin)+nMin), int(nMin), int(nMax))
}

func CompatiValueInt32(oMin, oMax, nMin, nMax float64, val int32) int32 {
	r := (float64(val) - oMin) / (oMax - oMin)
	return ut.ClampInt32(ut.RoundInt32(r*(nMax-nMin)+nMin), int32(nMin), int32(nMax))
}
