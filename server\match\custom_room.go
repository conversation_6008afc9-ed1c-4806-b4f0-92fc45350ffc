package match

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// 自定义房间
type CustomRoom struct {
	TeamList []*pb.TeamInfo    `bson:"-"` // 队伍列表
	teamLock *deadlock.RWMutex `bson:"-"`

	Name       string  `bson:"name"`        // 房间名
	Creator    string  `bson:"creator"`     // 创建者
	Password   string  `bson:"password"`    // 密码
	MachAddr   string  `bson:"addr"`        // 开服的物理机地址
	Wincond    []int32 `bson:"win_cond"`    // 胜利条件
	CreateTime int64   `bson:"create_time"` // 创建时间
	OpenTime   int64   `bson:"open_time"`   // 开服时间
	UserLimit  int32   `bson:"user_limit"`  // 人数上限
	Sid        int32   `bson:"sid"`         // 房间id
	Opened     bool    `bson:"opened"`      // 是否已开服
}

var (
	CustinRooms      = ut.NewMapLock[int32, *CustomRoom]() // 游戏服map
	customRoomLock   = new(deadlock.Mutex)
	customRoomDbChan = make(chan int32, 1024)
)

// 加载所有未开服的自定义房间
func LoadAllCustomRooms() {
	rooms, err := customRoomDb.FindAllReadyCustomRooms()
	if err == nil {
		for _, info := range rooms {
			// 从队伍表中获取在该房间的队伍信息
			teamList, err := teamDb.FindTeamsByCustomRoomId(info.Sid)
			if err != nil {
				log.Warning("LoadAllCustomRooms FindTeamsByCustomRoomId sid: %v, err: %v", info.Sid, err)
				continue
			}
			info.TeamList = []*pb.TeamInfo{}
			for _, v := range teamList {
				info.TeamList = append(info.TeamList, v.ToPb())
			}
		}
	} else {
		log.Warning("LoadAllCustomRooms err: %v", err)
	}
}

// 创建自定义房间
func CreateCustomRoom(name, creator, password string) (sid int32, err string) {
	customRoomLock.Lock()
	defer customRoomLock.Unlock()
	// 检测名字是否重复
	if _, _, ok := CustinRooms.Find(func(v *CustomRoom, k int32) bool { return v.Name == name }); ok {
		return 0, ecode.NAME_EXIST.String()
	}
	sid = getGenSid(slg.CUSTOM_SERVER_TYPE, 0)
	// 获取并设置空闲的物理机
	addr := AddServerToMach(sid, slg.CUSTOM_SERVER_TYPE)
	if addr == "" {
		// 物理机已占满
		return 0, ecode.ROOM_FULL.String()
	}
	now := ut.Now()
	// 创建自定义房间
	customRoom := &CustomRoom{
		Sid:        sid,
		TeamList:   []*pb.TeamInfo{},
		Name:       name,
		Creator:    creator,
		Password:   password,
		MachAddr:   addr,
		CreateTime: now,
		OpenTime:   now + slg.CUSTOM_ROOM_MAX_DELAY_TIME, // 最晚3天后开服
		Wincond:    slg.WIN_COND_ANCIENT,                 // 默认修建遗迹
		UserLimit:  slg.CUSTOM_ROOM_MAX_USER_NUM,
		teamLock:   new(deadlock.RWMutex),
	}
	typeKey := GetGenIdMapKey(slg.CUSTOM_SERVER_TYPE, 0)
	CustinRooms.Set(sid, customRoom)
	// 更新区服自增id
	setGenSid(typeKey, sid)
	return
}

// 队伍加入自定义房间
func (this *CustomRoom) TeamEnterCustomRoom(teamInfo *pb.TeamInfo) (err string) {
	this.teamLock.Lock()
	defer this.teamLock.Unlock()
	var userTotal int32
	for _, v := range this.TeamList {
		if v.Uid == teamInfo.Uid { // 队伍已存在
			return ecode.ALREADY_IN_CUSTOM_ROOM.String()
		}
		userTotal += getCustomTeamUserNum(v)
	}
	if userTotal+getCustomTeamUserNum(teamInfo) > this.UserLimit { // 人数上限
		return ecode.CUSTOM_ROOM_FULL.String()
	}
	this.TeamList = append(this.TeamList, teamInfo)
	// TODO 通知
	return
}

// 队伍退出自定义房间
func (this *CustomRoom) TeamExitCustomRoom(teamUid, userId string) (err string) {
	if this.Opened { // 已开服不能退出
		return ecode.IN_GAME.String()
	}
	this.teamLock.Lock()
	defer this.teamLock.Unlock()
	for i := len(this.TeamList) - 1; i >= 0; i-- {
		team := this.TeamList[i]
		if team.Uid == teamUid {
			if teamUid == userId {
				// 队长操作退出 移除整个队伍
				this.TeamList = append(this.TeamList[:i], this.TeamList[i+1:]...)
			} else {
				// 移除单个成员
				removeUser := false
				if team.UserList != nil {
					userIndex := array.FindIndex(team.UserList, func(m *pb.TeamUserInfo) bool { return m.Uid == userId })
					if userIndex >= 0 {
						team.UserList = append(team.UserList[:userIndex], team.UserList[userIndex+1:]...)
						removeUser = true
					}
				}
				if !removeUser {
					// 成员列表中未找到 尝试从邀请列表中移除
					team.InviteList = array.Delete(team.InviteList, func(m *pb.TeamInviteUserInfo) bool { return m.Uid == userId })
				}
			}
			break
		}
	}
	// TODO 通知
	return
}

// 队伍邀请玩家
func (this *CustomRoom) TeamInviteUser(teamUid, userId, nickname, headicon string) (err string) {
	if this.GetUserNum() >= this.UserLimit {
		return ecode.CUSTOM_ROOM_FULL.String()
	}
	this.teamLock.Lock()
	defer this.teamLock.Unlock()
	for _, team := range this.TeamList {
		if team.Uid == teamUid {
			team.InviteList = append(team.InviteList, &pb.TeamInviteUserInfo{
				Uid:      userId,
				Nickname: nickname,
				HeadIcon: headicon,
			})
			break
		}
	}
	return
}

// 队伍邀请玩家回复
func (this *CustomRoom) TeamInviteUserResponse(teamUid, userId string, agree bool) (err string) {
	this.teamLock.Lock()
	defer this.teamLock.Unlock()
	for _, team := range this.TeamList {
		if team.Uid == teamUid {
			// 从邀请列表移除
			index := array.FindIndex(team.InviteList, func(m *pb.TeamInviteUserInfo) bool { return m.Uid == userId })
			inviteInfo := team.InviteList[index]
			team.InviteList = append(team.InviteList[:index], team.InviteList[index+1:]...)
			if agree {
				// 添加到成员列表
				team.UserList = append(team.UserList, &pb.TeamUserInfo{
					Nickname: inviteInfo.Nickname,
					HeadIcon: inviteInfo.HeadIcon,
				})
			}
			break
		}
	}
	return
}

// 获取房间人数
func (this *CustomRoom) GetUserNum() int32 {
	this.teamLock.RLock()
	defer this.teamLock.RUnlock()
	var total int32
	for _, v := range this.TeamList {
		total += getCustomTeamUserNum(v)
	}
	return total
}

// 获取队伍人数
func getCustomTeamUserNum(team *pb.TeamInfo) int32 {
	var total int32
	if team.UserList != nil {
		total += int32(len(team.UserList))
	}
	if team.InviteList != nil {
		total += int32(len(team.InviteList))
	}
	return total
}

func (this *CustomRoom) ToPb(isDetail bool) *pb.CustomRoomInfo {
	rst := &pb.CustomRoomInfo{
		Name:       this.Name,
		Creator:    this.Creator,
		WinCond:    this.Wincond,
		CreateTime: this.CreateTime,
		OpenTime:   this.OpenTime,
		UserLimit:  this.UserLimit,
		Sid:        this.Sid,
		TeamList:   []*pb.TeamInfo{},
	}
	this.teamLock.RLock()
	for _, v := range this.TeamList {
		rst.UserNum += int32(len(v.UserList))
		rst.UserNum += int32(len(v.InviteList))
		if isDetail {
			rst.TeamList = append(rst.TeamList, v)
		}
	}
	this.teamLock.RUnlock()
	return rst
}

// 设置是否已开服
func (this *CustomRoom) SetOpened(opened bool) {
	this.Opened = opened
	saveCustomRoom(this.Sid)
}

// 标记保存自定义房间
func saveCustomRoom(sid int32) {
	customRoomDbChan <- sid
}

// 定时保存自定义房间db
func RunSaveCustomRoomsDbTick() {
	go func() {
		tiker := time.NewTicker(time.Minute * 1)
		for isRunning {
			<-tiker.C
			SaveCunstomRoomsDb()
		}
	}()
}

// 保存自定义房间db
func SaveCunstomRoomsDb() {
	if len(customRoomDbChan) == 0 {
		return
	}
	sidMap := map[int32]bool{}
	for len(customRoomDbChan) > 0 {
		sid := <-customRoomDbChan
		if !sidMap[sid] {
			sidMap[sid] = true
		}
	}

	for sid := range sidMap {
		info := CustinRooms.Get(sid)
		if info == nil {
			continue
		}
		customRoomDb.UpdateCustomRoom(info)
		if info.Opened {
			// 已开启且已保存 从内存中移除
			CustinRooms.Del(sid)
		}
	}
}
