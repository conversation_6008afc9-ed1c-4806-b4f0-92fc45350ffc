package g

import (
	"strings"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/enums/ctype"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// 临时的宝箱期望资源
var treasureResExpectationMap = ut.NewMapLock[int32, map[int32]float64]()

// 宝箱信息
type TreasureInfo struct {
	UID     string     `json:"uid" bson:"uid"`
	ID      int32      `json:"id" bson:"id"`
	Rewards []*TypeObj `json:"rewards" bson:"rewards"`
}

func NewTreasure(id int32) *TreasureInfo {
	return &TreasureInfo{
		UID:     ut.ID(),
		ID:      id,
		Rewards: []*TypeObj{},
	}
}

func NewTreasureByJson(data map[string]interface{}) *TreasureInfo {
	rewards := []*TypeObj{}
	if items, ok := data["rewards"].(primitive.A); ok {
		for _, val := range items {
			rewards = append(rewards, NewTypeObjByJson(val.(map[string]interface{})))
		}
	}
	return &TreasureInfo{
		UID:     ut.String(data["uid"]),
		ID:      ut.Int32(data["id"]),
		Rewards: rewards,
	}
}

func (this *TreasureInfo) ToJson() map[string]interface{} {
	return map[string]interface{}{
		"uid":     this.UID,
		"id":      this.ID,
		"rewards": array.Map(this.Rewards, func(m *TypeObj, _ int) map[string]interface{} { return m.ToJson() }),
	}
}

func (this *TreasureInfo) ToPb() *pb.TreasureInfo {
	return &pb.TreasureInfo{
		Uid:     this.UID,
		Id:      int32(this.ID),
		Rewards: array.Map(this.Rewards, func(m *TypeObj, _ int) *pb.TypeObj { return m.ToPb() }),
	}
}

func (this *TreasureInfo) ToRewardsPb() []*pb.TypeObj {
	return array.Map(this.Rewards, func(m *TypeObj, _ int) *pb.TypeObj { return m.ToPb() })
}

// 随机奖励
func (this *TreasureInfo) RandomRewards(mul float64) *TreasureInfo {
	json := config.GetJsonData("treasure", this.ID)
	if json == nil {
		return this
	}
	// 先获取数量
	arr := ut.StringToInt32s(ut.String(json["count"]), ",")
	count := ut.RandomInt32(arr[0], arr[1])
	// 先看奖励列表是否有
	if rewardsStr := ut.String(json["rewards"]); rewardsStr != "" {
		items := strings.Split(rewardsStr, "|")
		weights := ut.StringToInts(ut.String(json["weight"]), ",")
		this.Rewards = RandomRewardsByWeight(count, items, weights, mul)
	} else if fixedRewardsStr := ut.String(json["fixed_rewards"]); fixedRewardsStr != "" {
		// 固定奖励
		fixedRewards := strings.Split(fixedRewardsStr, "|")
		this.Rewards = RandomRewardsByFixed(count, fixedRewards, mul)
	}
	// 是否有特殊奖励
	if specialsStr := ut.String(json["one_special"]); specialsStr != "" {
		specials := strings.Split(specialsStr, "|")
		this.Rewards = append(this.Rewards, RandomRewardsByFixed(1, specials, mul)...)
	}
	// fmt.Println(this.Rewards)
	if len(this.Rewards) == 0 {
		this.Rewards = append(this.Rewards, NewTypeObj(ctype.CEREAL, 0, 50))
	}
	return this
}

// 获取宝箱物品获取概率
func calculateProbAtLeastOne(idx int, weights []int, drawsLeft int, probSoFar float64, seen bool) float64 {
	if drawsLeft <= 0 {
		return ut.If(seen, probSoFar, 0)
	}
	totalWeight := 0
	for _, m := range weights {
		totalWeight += m
	}
	probIfNotSeen := 0.0
	if !seen {
		weight := float64(weights[idx]) / float64(totalWeight)
		newWeights := array.Map(weights, func(m int, _ int) int { return m })
		newWeights[idx] = 0
		probIfNotSeen = calculateProbAtLeastOne(idx, newWeights, drawsLeft-1, probSoFar*weight, true)
	}
	probIfNot := 0.0
	for i, m := range weights {
		if i != idx && m > 0 {
			weight := float64(m) / float64(totalWeight)
			newWeights := array.Map(weights, func(m int, _ int) int { return m })
			newWeights[i] = 0
			probIfNot += calculateProbAtLeastOne(idx, newWeights, drawsLeft-1, probSoFar*weight, seen)
		}
	}
	return probIfNotSeen + probIfNot
}

func GetTreasureExpectationResMap(id int32) map[int32]float64 {
	resMap := treasureResExpectationMap.Get(id)
	if resMap != nil {
		return resMap
	}
	resMap = map[int32]float64{}
	json := config.GetJsonData("treasure", id)
	if json == nil {
		return resMap
	}
	// 先获取数量
	arr := ut.StringToInts(ut.String(json["count"]), ",")
	count := (float64(arr[0]) + float64(arr[1])) / 2 // 数量取区间平均数
	// 先看奖励列表是否有
	if rewardsStr := ut.String(json["rewards"]); rewardsStr != "" {
		minCnt, maxCnt := arr[0], arr[1]
		items := strings.Split(rewardsStr, "|")
		weights := ut.StringToInts(ut.String(json["weight"]), ",")
		for idx := range weights {
			odds := 0.0
			for i := minCnt; i <= maxCnt; i++ {
				odds += calculateProbAtLeastOne(idx, weights, i, 1.0, false)
			}
			odds /= float64(ut.Max(1, maxCnt-minCnt+1))
			vArr := ut.StringToInts(items[idx], ",")
			resType := ut.Int32(vArr[0])
			resCount := (float64(vArr[2]) + float64(vArr[3])) / 2.0 * odds
			resMap[resType] += resCount
		}
	} else if fixedRewardsStr := ut.String(json["fixed_rewards"]); fixedRewardsStr != "" {
		// 固定奖励
		fixedRewards := strings.Split(fixedRewardsStr, "|")
		totalWeight := 0
		for _, v := range fixedRewards {
			vArr := ut.StringToInts(v, ",")
			if len(vArr) < 3 {
				continue
			}
			totalWeight += ut.Int(vArr[2])
		}
		for _, v := range fixedRewards {
			vArr := ut.StringToInts(v, ",")
			if len(vArr) < 3 {
				continue
			}
			weight := ut.Float64(vArr[2]) / ut.Float64(totalWeight)
			resType := ut.Int32(vArr[0])
			resCount := count * weight
			resMap[resType] += resCount
		}
	}
	// 是否有特殊奖励
	if specialsStr := ut.String(json["one_special"]); specialsStr != "" {
		fixedRewards := strings.Split(specialsStr, "|")
		totalWeight := 0
		for _, v := range fixedRewards {
			vArr := ut.StringToInts(v, ",")
			if len(vArr) < 3 {
				continue
			}
			totalWeight += ut.Int(vArr[2])
		}
		for _, v := range fixedRewards {
			vArr := ut.StringToInts(v, ",")
			if len(vArr) < 3 {
				continue
			}
			weight := ut.Float64(vArr[2]) / ut.Float64(totalWeight)
			resType := ut.Int32(vArr[0])
			resCount := count * weight
			resMap[resType] += resCount
		}
	}
	treasureResExpectationMap.Set(id, resMap)
	return resMap
}

// 获取奖励期望
func (this *TreasureInfo) GetResExpectation(mul float64) map[int32]float64 {
	resMap := GetTreasureExpectationResMap(this.ID)
	// 基础资源奖励加成
	ret := map[int32]float64{}
	for tp := range resMap {
		count := resMap[tp]
		if tp == ctype.CEREAL || tp == ctype.TIMBER || tp == ctype.STONE {
			count *= mul
		}
		ret[tp] = count
	}
	return ret
}

func RandomRewardsByWeight(count int32, items []string, weights []int, mul float64) []*TypeObj {
	rewards, itemCount := []*TypeObj{}, int32(len(items))
	if count == 0 || itemCount == 0 || itemCount != int32(len(weights)) {
		return rewards
	} else if itemCount == count {
		for _, m := range items {
			if treasure := StringToTypeObjRandCount(m, mul); treasure != nil {
				rewards = append(rewards, treasure)
			}
		}
		return rewards
	}
	totalWeight, cnt := 0, len(weights)
	for _, m := range weights {
		totalWeight += m
	}
	for i := int32(0); i < count; i++ {
		offset := ut.Random(0, totalWeight-1)
		for ii := 0; ii < cnt; ii++ {
			val := weights[ii]
			if val == 0 {
				continue
			} else if offset < val {
				totalWeight -= val
				weights[ii] = 0
				if treasure := StringToTypeObjRandCount(items[ii], mul); treasure != nil {
					rewards = append(rewards, treasure)
				}
				break
			} else {
				offset -= val
			}
		}
	}
	return rewards
}

// 随机固定奖励
func RandomRewardsByFixed(count int32, items []string, mul float64) []*TypeObj {
	rewards := []*TypeObj{}
	if count == 0 {
		return rewards
	} else if len(items) == 1 {
		arr := ut.StringToInt32s(items[0], ",")
		return append(rewards, NewTypeObjForMul(arr[0], arr[1], count, mul))
	}
	weights := []map[string]interface{}{}
	totalWeight := 0
	for _, m := range items {
		arr := ut.StringToInts(m, ",")
		weight := arr[2]
		weights = append(weights, map[string]interface{}{
			"type":   arr[0],
			"id":     arr[1],
			"weight": weight,
		})
		totalWeight += weight
	}
	for i := int32(0); i < count; i++ {
		data := weights[ut.RandomIndexByWeightHasTotal(weights, totalWeight)]
		obj := NewTypeObjForMul(ut.Int32(data["type"]), ut.Int32(data["id"]), 1, mul)
		rewards = MergeTypeObjsCount(rewards, obj)
	}
	return rewards
}

func NewTypeObjForMul(tp, id, count int32, mul float64) *TypeObj {
	if tp == ctype.CEREAL || tp == ctype.TIMBER || tp == ctype.STONE {
		return NewTypeObj(tp, id, int32(float64(count)*mul)) // 只对基础资源生效
	}
	return NewTypeObj(tp, id, count)
}

// 字符串转类型对象 随机个数
func StringToTypeObjRandCount(str string, mul float64) *TypeObj {
	arr := ut.StringToInt32s(str, ",")
	if len(arr) != 4 {
		return nil
	}
	return NewTypeObjForMul(arr[0], arr[1], ut.RandomInt32(arr[2], arr[3]), mul)
}
