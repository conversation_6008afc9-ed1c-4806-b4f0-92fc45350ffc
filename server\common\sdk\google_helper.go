package sdk

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	slg "slgsrv/server/common"
	ut "slgsrv/utils"

	translate "cloud.google.com/go/translate/apiv3"
	"cloud.google.com/go/translate/apiv3/translatepb"
	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	o2 "golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/androidpublisher/v3"
	"google.golang.org/api/oauth2/v2"
	"google.golang.org/api/option"
)

const (
	GOOGLE_APPID                   = "743842452100-u2q1ekrc9sf2su3gn9pfcgg2der2lsbs.apps.googleusercontent.com"
	GOOGLE_iOS_APPID               = "743842452100-5b06eef6mpabk9fdjtab2nhav362svms.apps.googleusercontent.com"
	GOOGLE_PACKAGENAME             = "twgame.global.acers"
	GOOGLE_ORDER_VERIFY_URL        = "https://androidpublisher.googleapis.com/androidpublisher/v3/applications/"
	GOOGLE_PAY_KEY_FILE_PATH       = "bin/conf/google_client_secret.json"
	GOOGLE_PROJECT_ID              = "ninety-thousand-acres"
	FIREBASE_MESSAGE_KEY_FILE_PATH = "bin/conf/google_client_secret.json"

	GOOGLE_TRANSLATE_SECRET_PATH  = "bin/conf/google_translate_secret.json"
	GOOGLE_TRANSLATE_PROJECT_ID   = "tm-trans-453008"
	GOOGLE_TRANSLATE_GLOSSARY_ID  = "jwm_google_glossary_id"
	GOOGLE_TRANSLATE_GLOSSARY_URI = "gs://ninety-thousand-acres-us/jwm_google_glossary.csv"
)

// ---------------------- 登录 -------------------------

// Google token验证
func GoogleLoginCheck(idToken string) (ret *oauth2.Tokeninfo, err error) {
	log.Info("GoogleLoginCheck tokidTokenenInfo: %v", idToken)
	oauth2Service, err := oauth2.NewService(context.Background(), option.WithHTTPClient(http.DefaultClient))
	if err != nil {
		log.Error("GoogleVerifyToken oauth2 newService err: %v", err)
		return
	}
	tokenInfoCall := oauth2Service.Tokeninfo()
	tokenInfoCall.IdToken(idToken)
	tokenInfo, err := tokenInfoCall.Do()
	if err != nil {
		log.Error("GoogleVerifyToken tokenInfoCall err: %v", err)
		return
	}
	log.Info("GoogleLoginCheck tokenInfo: %v, userId: %v", tokenInfo, tokenInfo.UserId)
	// token到期
	if tokenInfo.ExpiresIn >= ut.Now() {
		errStr := "GoogleVerifyToken idtoken timeout"
		log.Error(errStr)
		return nil, errors.New(errStr)
	}
	// 防止恶意程序访问同一用户
	if tokenInfo.Audience != GOOGLE_APPID && tokenInfo.Audience != GOOGLE_iOS_APPID {
		errStr := "GoogleVerifyToken idtoken appid err"
		log.Error(errStr)
		return nil, errors.New(errStr)
	}
	return tokenInfo, nil
}

//--------------------- 支付 -------------------------

type GoogleOrderVerifyRet struct {
	OrderId              string `json:"orderId"`              // Google订单号
	PurchaseState        int    `json:"purchaseState"`        // 订单的购买状态 0.已购买 1.已取消 2.待处理
	ConsumptionState     int    `json:"consumptionState"`     // 商品的消耗状态 0.尚未消耗 1.已使用
	AcknowledgementState int    `json:"acknowledgementState"` // 应商品的确认状态 0.待确认 1. 已确认
	Quantity             int    `json:"quantity"`             // 数量
}

// Google订单验证
func GoogleOrderVerify(productId, token string) (err error, quantity int32, orderId, externalStr string, purchaseTime int64) {
	log.Info("GoogleOrderVerify productId: %v, token: %v", productId, token)
	jsonKey, err := os.ReadFile(GOOGLE_PAY_KEY_FILE_PATH)
	if err != nil {
		log.Error("GoogleOrderVerify jsonKey err: %v", err)
		return
	}
	client := &http.Client{Timeout: 10 * time.Second}
	ctx := context.WithValue(context.Background(), o2.HTTPClient, client)
	conf, err := google.JWTConfigFromJSON(jsonKey, androidpublisher.AndroidpublisherScope)
	val := conf.Client(ctx).Transport.(*o2.Transport)
	_, err = val.Source.Token()
	if err != nil {
		log.Error("GoogleOrderVerify token Transport err: %v", err)
		return
	}
	service, err := androidpublisher.NewService(ctx, option.WithHTTPClient(conf.Client(ctx)))
	if err != nil {
		log.Error("GoogleOrderVerify service err: %v", err)
		return
	}
	ps := androidpublisher.NewPurchasesProductsService(service)
	result, err := ps.Get(GOOGLE_PACKAGENAME, productId, token).Context(ctx).Do()
	if err != nil {
		log.Error("GoogleOrderVerify resp err: %v, result: %v", err, result)
		return
	}
	if result.PurchaseType != nil {
		// 0.测试（即从许可测试帐号中购买的服务）1.促销（即使用促销代码购买）2.激励广告
		if !slg.IS_SANDBOX && *result.PurchaseType == 0 {
			err = errors.New("Test Not In SandBox")
			log.Error("Test Not In SandBox PurchaseType: %v", *result.PurchaseType)
			return
		}
	}
	if result.PurchaseState != 0 {
		// PurchaseState应为0已购买
		err = errors.New("GoogleOrderVerify state err")
		log.Error("GoogleOrderVerify state err PurchaseState: %v, ConsumptionState: %v", result.PurchaseState)
		return
	}

	log.Info("GoogleOrderVerify result.Quantity: %v", result.Quantity)
	quantity = ut.MaxInt32(int32(result.Quantity), 1)
	orderId = result.OrderId
	externalStr = result.ObfuscatedExternalAccountId // 透传参数格式为 "cpOrderId_userId"
	purchaseTime = result.PurchaseTimeMillis
	// 未消费则消费订单
	if result.ConsumptionState != 1 {
		acErr := ps.Consume(GOOGLE_PACKAGENAME, productId, token).Context(ctx).Do()
		if acErr != nil {
			log.Info("GoogleOrderVerify acErr: %v", acErr)
		}
	}

	// log.Info("GoogleOrderVerify productId: %v, token: %v, checkConsume: %v", productId, token, checkConsume)
	// verifiUrl := GOOGLE_ORDER_VERIFY_URL + GOOGLE_PACKAGENAME + "/purchases/products/" + productId + "/tokens/" + token
	// resp, err := http.Get(verifiUrl)
	// defer func() {
	// 	_ = resp.Body.Close()
	// }()
	// if err != nil {
	// 	log.Error("GoogleOrderVerify resp productId: %v, token: %v, err: %v", productId, token, err)
	// 	return
	// }
	// if resp.StatusCode != http.StatusOK {
	// 	err = errors.New("GoogleOrderVerify http resp status err")
	// 	log.Error("GoogleOrderVerify http resp status err, status: %v", resp.Status)
	// 	return
	// }
	// body, _ := os.ReadAll(resp.Body)
	// err = json.Unmarshal(body, &ret)
	// if ret.PurchaseState != 0 || (!checkConsume && ret.ConsumptionState != 0) || (checkConsume && ret.ConsumptionState != 1) {
	// 	//checkConsume=false为订单验证 PurchaseState应为0已购买 ConsumptionState应为0未消耗
	// 	//checkConsume=true为领取验证 PurchaseState应为0已购买 ConsumptionState应为1已使用
	// 	err = errors.New("GoogleOrderVerify state err")
	// 	log.Error("GoogleOrderVerify state err PurchaseState: %v, ConsumptionState: %v", ret.PurchaseState, ret.ConsumptionState)
	// 	return
	// }
	return
}

// Google通知数据
type GoogleNotify struct {
	Message      *GoogleNotifyMessage `json:"message"` // 通知消息
	Subscription string               `json:"subscription"`
}

// Google通知消息
type GoogleNotifyMessage struct {
	Data      string `json:"data"` // base64编码的数据
	MessageId string `json:"messageId"`
}

// Google通知消息base64解码后
type DeveloperNotification struct {
	Version        string                      `json:"version"`
	PackageName    string                      `json:"packageName"`
	OneTime        *OneTimeProductNotification `json:"oneTimeProductNotification"`
	Subscription   *SubscriptionNotification   `json:"subscriptionNotification"`
	VoidedPurchase *VoidedPurchaseNotification `json:"voidedPurchaseNotification"`
	Test           *TestNotification           `json:"testNotification"`
}

// 订阅通知数据
type SubscriptionNotification struct {
	Version          string `json:"version"`
	NotificationType int    `json:"notificationType"` // 通知类型
	PurchaseToken    string `json:"purchaseToken"`    // 购买token
	SubscriptionId   string `json:"subscriptionId"`   // 订阅商品id
}

// 一次性购买
type OneTimeProductNotification struct {
	Version          string `json:"version"`
	NotificationType int    `json:"notificationType"` // 通知类型
	PurchaseToken    string `json:"purchaseToken"`    // 购买token
	Sku              string `json:"sku"`              // 商品id
}

// 退款
type VoidedPurchaseNotification struct {
	OrderId       string `json:"orderId"`       // 订单id
	ProductType   int    `json:"productType"`   // 购买类型 1订阅 2一次性
	RefundType    int    `json:"refundType"`    // 退款类型 1全部退款 2部分退款
	PurchaseToken string `json:"purchaseToken"` // 购买token
}

// 测试通知
type TestNotification struct {
	Version string `json:"version"`
}

// 订阅通知类型
const (
	GOOGLE_SUBSCRIPTION_RECOVERED              = 1  // 从帐号保留状态恢复了订阅
	GOOGLE_SUBSCRIPTION_RENEWED                = 2  // 续订了处于活动状态的订阅
	GOOGLE_SUBSCRIPTION_CANCELED               = 3  // 自愿或非自愿地取消了订阅
	GOOGLE_SUBSCRIPTION_PURCHASED              = 4  // 购买了新的订阅
	GOOGLE_SUBSCRIPTION_ON_HOLD                = 5  // 订阅已进入帐号保留状态（如果已启用）
	GOOGLE_SUBSCRIPTION_IN_GRACE_PERIOD        = 6  // 订阅已进入宽限期（如果已启用）
	GOOGLE_SUBSCRIPTION_RESTARTED              = 7  // 订阅已取消，但在用户恢复时尚未到期恢复订阅
	GOOGLE_SUBSCRIPTION_PRICE_CHANGE_CONFIRMED = 8  // 用户已成功确认订阅价格变动
	GOOGLE_SUBSCRIPTION_DEFERRED               = 9  // 订阅的续订时间点已延期
	GOOGLE_SUBSCRIPTION_PAUSED                 = 10 // 订阅已暂停
	GOOGLE_SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED = 11 // 订阅暂停计划已更改
	GOOGLE_SUBSCRIPTION_REVOKED                = 12 // 用户在到期时间之前已撤消订阅
	GOOGLE_SUBSCRIPTION_EXPIRED                = 13 // 订阅已到期
)

// Google货币微单位
const GOOGLE_PRICE_UNIT float32 = 1000000

// Google订阅验证
func GoogleSubVerify(token string) (err error, startTime, endTime int64, auto bool, currencyType, orderId string, price float32, offerType, productId, externalStr string, isApiErr bool) {
	isApiErr = true
	jsonKey, err := os.ReadFile(GOOGLE_PAY_KEY_FILE_PATH)
	if err != nil {
		log.Error("GoogleSubVerify jsonKey err: %v", err)
		return
	}
	client := &http.Client{Timeout: 10 * time.Second}
	ctx := context.WithValue(context.Background(), o2.HTTPClient, client)
	conf, err := google.JWTConfigFromJSON(jsonKey, androidpublisher.AndroidpublisherScope)
	val := conf.Client(ctx).Transport.(*o2.Transport)
	_, err = val.Source.Token()
	if err != nil {
		log.Error("GoogleSubVerify token Transport err: %v", err)
		return
	}
	service, err := androidpublisher.NewService(ctx, option.WithHTTPClient(conf.Client(ctx)))
	if err != nil {
		log.Error("GoogleSubVerify service err: %v", err)
		return
	}
	isApiErr = false

	// 使用V2接口查询
	psV2 := androidpublisher.NewPurchasesSubscriptionsv2Service(service)
	rst, err := psV2.Get(GOOGLE_PACKAGENAME, token).Context(ctx).Do()
	if err != nil || rst == nil {
		log.Error("GoogleSubVerify resp err: %v, result: %v", err, rst)
		return
	}
	if rst.SubscriptionState != "SUBSCRIPTION_STATE_ACTIVE" && rst.SubscriptionState != "SUBSCRIPTION_STATE_CANCELED" {
		// 订阅状态不是激活状态 或者已取消续订未过期
		err = errors.New("Not Active")
		log.Error("GoogleSubVerify Not Active SubscriptionState: %v", rst.SubscriptionState)
		return
	}
	if len(rst.LineItems) == 0 {
		// 没有订阅数据
		err = errors.New("No LineItems")
		log.Error("GoogleSubVerify No LineItems")
		return
	}
	startT, _ := time.Parse(time.RFC3339, rst.StartTime)
	startTime = startT.UnixMilli()
	// LineItems数组是时间升序 直接取最后一个
	subItem := rst.LineItems[len(rst.LineItems)-1]
	// latestSuccessfulOrderId的结构是 "订单号..期数序号"
	orderIdWithNo := subItem.LatestSuccessfulOrderId
	orderId = strings.Split(orderIdWithNo, "..")[0]

	productId = subItem.OfferDetails.BasePlanId
	endT, _ := time.Parse(time.RFC3339, subItem.ExpiryTime)
	endTime = endT.UnixMilli()
	auto = subItem.AutoRenewingPlan.AutoRenewEnabled
	currencyType = subItem.AutoRenewingPlan.RecurringPrice.CurrencyCode
	offerType = subItem.OfferDetails.OfferId
	price = float32(subItem.AutoRenewingPlan.RecurringPrice.Units)
	if rst.ExternalAccountIdentifiers != nil {
		externalStr = rst.ExternalAccountIdentifiers.ObfuscatedExternalAccountId
	}

	// result, err := ps.Get(GOOGLE_PACKAGENAME, productId, token).Context(ctx).Do()
	// if err != nil || result == nil {
	// 	log.Error("GoogleSubVerify resp err: %v, result: %v", err, result)
	// 	return
	// }
	// // result.PurchaseType 正常支付购买时为nil
	// if result.PurchaseType != nil {
	// 	// 0.测试（即从许可测试帐号中购买的服务）1.促销（即使用促销代码购买）2.激励广告
	// 	if !slg.IS_SANDBOX && *result.PurchaseType == 0 {
	// 		err = errors.New("Test Not In SandBox")
	// 		log.Error("GoogleSubVerify Test Not In SandBox PurchaseType: %v", *result.PurchaseType)
	// 		return
	// 	}
	// } else if result.PaymentState != nil && *result.PaymentState != 1 && *result.PaymentState != 2 {
	// 	// PaymentState 0未支付 1已支付 2免费试用 3待延迟升级/降级 对于已取消、已过期的订阅，不存在此字段
	// 	err = errors.New("GoogleSubVerify PaymentState err")
	// 	log.Error("GoogleSubVerify state err PaymentState: %v", *result.PaymentState)
	// 	return
	// }
	// startTime = int64(result.StartTimeMillis)
	// endTime = int64(result.ExpiryTimeMillis)
	// auto = result.AutoRenewing
	// currencyType = result.PriceCurrencyCode
	// price = ut.Float32Round(float32(result.PriceAmountMicros)/GOOGLE_PRICE_UNIT, 2)
	// // log.Info("GoogleSubVerify price: %v", price)
	// orderId = result.OrderId
	// if result.PaymentState != nil && *result.PaymentState == 2 {
	// 	// 免费试用
	// 	offerType = slg.TA_SUB_STATE_FREE
	// } else if result.IntroductoryPriceInfo != nil {
	// 	// 初次体验价信息
	// 	offerType = slg.TA_SUB_STATE_DISCOUNT
	// 	price = ut.Float32Round(float32(result.IntroductoryPriceInfo.IntroductoryPriceAmountMicros)/GOOGLE_PRICE_UNIT, 2)
	// }

	ps := androidpublisher.NewPurchasesSubscriptionsService(service)
	// 验证成功则确认订单
	acErr := ps.Acknowledge(GOOGLE_PACKAGENAME, productId, token, &androidpublisher.SubscriptionPurchasesAcknowledgeRequest{}).Context(ctx).Do()
	if acErr != nil {
		log.Info("GoogleSubVerify acErr: %v", acErr)
	}
	return
}

// 获取退款订单列表
func GoogleVoidedOrderCheck() (err error, list []*androidpublisher.VoidedPurchase) {
	if slg.IsDebug() || slg.IsChinaArea() {
		return // 国内不用google
	}
	log.Info("GoogleVoidedOrderCheck")
	jsonKey, err := os.ReadFile(GOOGLE_PAY_KEY_FILE_PATH)
	if err != nil {
		log.Error("GoogleVoidedOrderCheck jsonKey err: %v", err)
		return
	}
	client := &http.Client{Timeout: 10 * time.Second}
	ctx := context.WithValue(context.Background(), o2.HTTPClient, client)
	conf, err := google.JWTConfigFromJSON(jsonKey, androidpublisher.AndroidpublisherScope)
	val := conf.Client(ctx).Transport.(*o2.Transport)
	_, err = val.Source.Token()
	if err != nil {
		log.Error("GoogleVoidedOrderCheck token Transport err: %v", err)
		return
	}
	service, err := androidpublisher.NewService(ctx, option.WithHTTPClient(conf.Client(ctx)))
	if err != nil {
		log.Error("GoogleVoidedOrderCheck service err: %v", err)
		return
	}
	ps := androidpublisher.NewPurchasesVoidedpurchasesService(service)
	endTime := time.Now().UnixMilli()
	startTime := endTime - 10*60*1000 // 间隔10min
	result, err := ps.List(GOOGLE_PACKAGENAME).StartTime(startTime).EndTime(endTime).Type(1).Context(ctx).Do()
	if err != nil {
		log.Error("GoogleVoidedOrderCheck resp err: %v, result: %v", err, result)
		return
	}
	log.Info("GoogleVoidedOrderCheck result: %v", result)
	list = result.VoidedPurchases
	return
}

// 解析透传参数
func ParseGoogleExernalStr(externalStr string) (cpOid, userId string) {
	if externalStr != "" {
		exernalArr := strings.Split(externalStr, "_")
		if len(exernalArr) > 1 {
			cpOid = exernalArr[0]
			userId = exernalArr[1]
		}
	}
	return
}

// ------------------------- FCM消息通知 -----------------------
var (
	FCMApp           *firebase.App
	deleteFCMToken   deadlock.Map              // 失效删除的FCMToken
	messageQueue     = make(chan func(), 1000) // 任务队列
	fcmWorkerCount   = 10                      // 工作协程数量
	fcmClient        *messaging.Client
	fcmAndroidConfig *messaging.AndroidConfig
	fcmApnsConfig    *messaging.APNSConfig
)

// 初始化FCM app
func FirebaseCloudMessageInit() {
	opt := option.WithCredentialsFile(FIREBASE_MESSAGE_KEY_FILE_PATH)
	config := &firebase.Config{ProjectID: GOOGLE_PROJECT_ID}
	app, err := firebase.NewApp(context.Background(), config, opt)
	if err != nil {
		log.Error("FirebaseCloudMessageInit err: %v", err)
		return
	}
	FCMApp = app
	fcmClient, err = FCMApp.Messaging(context.Background())
	if err != nil {
		log.Error("FirebaseCloudMessageInit client err: %v", err)
		return
	}
	deleteFCMToken = deadlock.Map{}

	// 设置Android配置
	fcmAndroidConfig = &messaging.AndroidConfig{
		Notification: &messaging.AndroidNotification{
			DefaultSound: true,
		},
	}

	// 设置APNS配置
	fcmApnsConfig = &messaging.APNSConfig{
		Payload: &messaging.APNSPayload{
			Aps: &messaging.Aps{
				// Alert: &messaging.ApsAlert{
				// 	Title: title,
				// 	Body:  body,
				// },
				Sound: "default", // 设置为 "default" 使用设备的默认通知声音。
			},
		},
	}
	InitFirebaseWorkers()
}

// 初始化工作池
func InitFirebaseWorkers() {
	for i := 0; i < fcmWorkerCount; i++ {
		go worker() // 启动工作协程
	}
}

// 工作协程：持续处理消息队列
func worker() {
	for task := range messageQueue {
		task() // 执行发送任务
	}
}

// 离线消息通知
func SendOfflineMessge(registrationToken string, title, body string, data map[string]string) {
	if !slg.IsOpenOfflineMsg() {
		return
	}
	messageQueue <- func() {
		FirebaseSendMessage(registrationToken, title, body, data)
	}
}

// 发送FCM消息
func FirebaseSendMessage(registrationToken string, title, body string, data map[string]string) error {
	if _, ok := deleteFCMToken.Load(registrationToken); ok {
		// 检测令牌是否失效删除
		return errors.New("token invalid")
	}
	// 添加上下文超时
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 构建消息结构体
	message := &messaging.Message{
		Notification: &messaging.Notification{
			Title: title,
			Body:  body,
		},
		Android: fcmAndroidConfig,
		APNS:    fcmApnsConfig,
		Data:    data,
		Token:   registrationToken,
	}

	// 发送消息
	response, err := fcmClient.Send(ctx, message)
	if err != nil {
		if err.Error() == "Requested entity was not found." {
			// 令牌失效 删除令牌
			deleteFCMToken.Store(registrationToken, true)
		}
		log.Error("FirebaseSendMessage err: %v, response: %v", err, response)
		return err
	}
	return nil
}

// --------------------- 翻译 -------------------------

// 翻译api客户端
var translateClient *translate.TranslationClient

// 初始化翻译api客户端
func InitTranslateClient() error {
	ctx := context.Background()
	opt := option.WithCredentialsFile(GOOGLE_TRANSLATE_SECRET_PATH)

	client, err := translate.NewTranslationClient(ctx, opt)
	if err != nil {
		log.Error("InitTranslateClient err: %v", err)
		return err
	}
	translateClient = client
	return nil
}

// 翻译文本
func TranslateText(targetLanguage, text string) (string, error) {
	// lang, err := language.Parse(targetLanguage)
	// if err != nil {
	// 	log.Error("TranslateText lang parse err: %v")
	// 	return "", err
	// }
	if translateClient == nil {
		return text, nil
	}
	ctx := context.Background()

	req := &translatepb.TranslateTextRequest{
		Parent: fmt.Sprintf("projects/%s/locations/global", GOOGLE_TRANSLATE_PROJECT_ID),
		// SourceLanguageCode: sourceLang,
		TargetLanguageCode: targetLanguage,
		MimeType:           "text/plain", // Mime types: "text/plain", "text/html"
		Contents:           []string{text},
	}

	resp, err := translateClient.TranslateText(ctx, req)
	if err != nil {
		log.Error("TranslateText resp err: %v", err)
		return "", err
	}

	if len(resp.GetTranslations()) == 0 {
		log.Error("TranslateText resp nil lang: %v, text: %v", targetLanguage, text)
		return "", err
	}

	return resp.GetTranslations()[0].GetTranslatedText(), nil
}

// 使用术语表翻译
func TranslateTextWithGlossary(sourceLanguage, targetLang, text string) (string, error) {
	// projectID := "my-project-id"
	// location := "us-central1"
	// sourceLang := "en"
	// targetLang := "ja"
	// text := "Hello world"
	// glossaryID := "your-glossary-id"

	if translateClient == nil {
		return text, nil
	}
	ctx := context.Background()

	req := &translatepb.TranslateTextRequest{
		Parent:             fmt.Sprintf("projects/%s/locations/us-central1", GOOGLE_TRANSLATE_PROJECT_ID),
		SourceLanguageCode: sourceLanguage,
		TargetLanguageCode: targetLang,
		MimeType:           "text/plain", // Mime types: "text/plain", "text/html"
		Contents:           []string{text},
		GlossaryConfig: &translatepb.TranslateTextGlossaryConfig{
			Glossary: fmt.Sprintf("projects/%s/locations/us-central1/glossaries/%s", GOOGLE_TRANSLATE_PROJECT_ID, GOOGLE_TRANSLATE_GLOSSARY_ID),
		},
	}

	resp, err := translateClient.TranslateText(ctx, req)
	if err != nil {
		log.Error("TranslateText resp err: %v", err)
		return "", err
	}

	if len(resp.GetTranslations()) == 0 && len(resp.GetGlossaryTranslations()) == 0 {
		log.Error("TranslateText resp nil lang: %v, text: %v", targetLang, text)
		return "", err
	}

	// 匹配到术语表优先返回
	if len(resp.GlossaryTranslations) > 0 {
		return resp.GetGlossaryTranslations()[0].GetTranslatedText(), nil
	}

	return resp.GetTranslations()[0].GetTranslatedText(), nil
}

// 长时间创建处理对象
var createGlossaryOp *translate.CreateGlossaryOperation

// 创建术语表
func CreateGlossary(glossaryID, glossaryInputURI string) error {
	// projectID := "my-project-id"
	// location := "us-central1"
	// glossaryID := "my-glossary-display-name"
	// glossaryInputURI := "gs://cloud-samples-data/translation/glossary.csv"

	if createGlossaryOp != nil {
		return fmt.Errorf("正在创建中")
	}

	ctx := context.Background()
	opt := option.WithCredentialsFile(GOOGLE_TRANSLATE_SECRET_PATH)
	client, err := translate.NewTranslationClient(ctx, opt)
	if err != nil {
		log.Error("NewTranslationClient err: %v", err)
		return err
	}

	langArr := getTranslateLangArr()
	req := &translatepb.CreateGlossaryRequest{
		Parent: fmt.Sprintf("projects/%s/locations/us-central1", GOOGLE_TRANSLATE_PROJECT_ID),
		Glossary: &translatepb.Glossary{
			Name: fmt.Sprintf("projects/%s/locations/us-central1/glossaries/%s", GOOGLE_TRANSLATE_PROJECT_ID, glossaryID),
			Languages: &translatepb.Glossary_LanguageCodesSet_{
				LanguageCodesSet: &translatepb.Glossary_LanguageCodesSet{
					LanguageCodes: langArr,
				},
			},
			InputConfig: &translatepb.GlossaryInputConfig{
				Source: &translatepb.GlossaryInputConfig_GcsSource{
					GcsSource: &translatepb.GcsSource{
						InputUri: glossaryInputURI,
					},
				},
			},
		},
	}

	// The CreateGlossary operation is async.
	createGlossaryOp, err = client.CreateGlossary(ctx, req)
	if err != nil {
		log.Error("CreateGlossary err: %v", err)
		return err
	}
	log.Info("Processing operation name: %q\n", createGlossaryOp.Name())

	go func() {
		defer client.Close()
		resp, err := createGlossaryOp.Wait(ctx)
		if err != nil {
			log.Error("CreateGlossary Wait err: %v", err)
			return
		}
		log.Info("Created: %v\n", resp.GetName())
		log.Info("Input URI: %v\n", resp.InputConfig.GetGcsSource().GetInputUri())
		createGlossaryOp = nil
	}()

	return nil
}

func IsGlossaryCreating() bool {
	return createGlossaryOp != nil
}

// 查询术语表
func GetGlossary(id string) error {
	// projectID := "my-project-id"
	// location := "us-central1"
	// glossaryID := "glossary-id"

	ctx := context.Background()
	opt := option.WithCredentialsFile(GOOGLE_TRANSLATE_SECRET_PATH)
	client, err := translate.NewTranslationClient(ctx, opt)
	if err != nil {
		log.Error("NewTranslationClient: err: %v", err)
		return err
	}

	req := &translatepb.GetGlossaryRequest{
		Name: fmt.Sprintf("projects/%s/locations/us-central1/glossaries/%s", GOOGLE_TRANSLATE_PROJECT_ID, id),
	}

	resp, err := client.GetGlossary(ctx, req)
	if err != nil {
		return fmt.Errorf("GetGlossary: %w", err)
	}
	log.Info("Glossary name: %v", resp.GetName())
	log.Info("Entry count: %v", resp.GetEntryCount())
	log.Info("Input URI: %v", resp.GetInputConfig().GetGcsSource().GetInputUri())

	return nil
}

// 长时间创建处理对象
var delGlossaryOp *translate.DeleteGlossaryOperation

// 删除术语表
func DelGlossary(id string) error {
	if delGlossaryOp != nil {
		return fmt.Errorf("正在删除中")
	}

	ctx := context.Background()
	opt := option.WithCredentialsFile(GOOGLE_TRANSLATE_SECRET_PATH)
	client, err := translate.NewTranslationClient(ctx, opt)
	if err != nil {
		log.Error("NewTranslationClient: err: %v", err)
		return err
	}

	req := &translatepb.DeleteGlossaryRequest{
		Name: fmt.Sprintf("projects/%s/locations/us-central1/glossaries/%s", GOOGLE_TRANSLATE_PROJECT_ID, id),
	}
	delGlossaryOp, err = client.DeleteGlossary(context.TODO(), req)
	if err != nil {
		return fmt.Errorf("DelGlossary err: %w", err)
	}

	go func() {
		defer client.Close()
		resp, err := delGlossaryOp.Wait(ctx)
		if err != nil {
			log.Error("DelGlossary Wait err: %v", err)
			return
		}
		log.Info("DelGlossary finish name: %v", resp.GetName())
		delGlossaryOp = nil
	}()
	return nil
}

func IsGlossaryDeleting() bool {
	return delGlossaryOp != nil
}

// 获取需要翻译的所有语言
func getTranslateLangArr() []string {
	langMap := map[string]bool{}
	for _, v := range slg.TRANS_LANG_MAP {
		langMap[v] = true
	}
	arr := []string{}
	for lang := range langMap {
		arr = append(arr, lang)
	}
	return arr
}

// Google订阅productId转换和Apple一致
func GoogleSubProductIdTransfer(productId string) string {
	switch productId {
	case slg.SUB_PRODUCTID_AWARD_GOOGLE_MONTH:
		return slg.SUB_PRODUCTID_AWARD_MOUNTH
	case slg.SUB_PRODUCTID_AWARD_GOOGLE_QUARTER:
		return slg.SUB_PRODUCTID_AWARD_QUARTER
	case slg.SUB_PRODUCTID_GOOGLE_WEEK:
		return slg.SUB_PRODUCTID_WEEK
	case slg.SUB_PRODUCTID_GOOGLE_MONTH:
		return slg.SUB_PRODUCTID_MONTH
	case slg.SUB_PRODUCTID_GOOGLE_QUARTER:
		return slg.SUB_PRODUCTID_QUARTER
	}
	return productId
}
