package game

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/world"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

func (this *Game) InitHDBuild() {
	this.GetServer().RegisterGO("HD_MoveAreaBuild", this.moveAreaBuild) // 移动建筑
	this.GetServer().RegisterGO("HD_UpAreaBuild", this.upAreaBuild)     // 升级建筑
	this.GetServer().RegisterGO("HD_AddAreaBuild", this.addAreaBuild)   // 修建建筑
	this.GetServer().RegisterGO("HD_CancelBT", this.cancelBt)           // 取消修建
	this.GetServer().RegisterGO("HD_InDoneBt", this.inDoneBt)           // 立即完成修建
}

// 移动建筑
func (this *Game) moveAreaBuild(session gate.Session, msg *pb.GAME_HD_MOVEAREABUILD_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, uid, point := msg.GetIndex(), msg.GetUid(), &ut.Vec2{X: msg.GetPoint().X, Y: msg.GetPoint().Y}
	area := wld.GetArea(index)
	if area == nil || area.Owner != plr.Uid {
		return nil, ecode.BUILD_NOT_EXIST.String()
	} /* else if area.IsBattle() {
		return nil, ecode.BATTLEING.String() //当前战斗中
	} */
	build := area.GetBuildByUID(uid)
	if build == nil {
		return nil, ecode.BUILD_NOT_EXIST.String()
	} else if !area.CheckCanPlace(build, point) {
		return nil, ecode.POINT_OCCUPIED.String() // 位置被占用
	}
	build.Point.Set(point) // 直接改变位置
	wld.TagUpdateDBByIndex(index)
	// 通知
	wld.NotifyAreaUpdateInfo(index, constant.NQ_MOVE_BUILD, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_7: &pb.AreaBuildPointInfo{
		Uid:   build.Uid,
		Point: pb.NewVec2(point),
	}}, plr.Uid)
	return
}

// 升级建筑
func (this *Game) upAreaBuild(session gate.Session, msg *pb.GAME_HD_UPAREABUILD_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, uid := msg.GetIndex(), msg.GetUid()
	area := wld.GetArea(index)
	if area == nil || area.Owner != plr.Uid {
		return nil, ecode.UNKNOWN.String()
	} else if area.IsBattle() {
		return nil, ecode.BATTLEING.String() // 当前战斗中
	} else if plr.State == constant.PS_CAPTURE {
		return nil, ecode.UNKNOWN.String() // 已沦陷无法修建
	}
	build := area.GetBuildByUID(uid)
	if build == nil {
		return nil, ecode.BUILD_NOT_EXIST.String()
	} else if plr.GetBTInfoByBUid(build.Uid) != nil { // 修建中
		return nil, ecode.YET_IN_BTQUEUE.String()
	} else if !plr.CheckCTypes(build.GetPrepCond()) { // 条件不足
		return nil, ecode.COND_NOT_ENOUGH.String()
	} else if plr.IsBTQueueFull() { // 队列是否满了
		return nil, ecode.BTQUEUE_FULL.String()
	} else if build.IsMaxLv() { // 是否已经满级
		return nil, ecode.YET_MAXLV.String()
	} else if !plr.AntiCheatScoreCheck() { // 防作弊检测
		return nil, ecode.ANTI_CHEAT.String()
	}
	// 不是主城的话要检测是否超过主城等级
	if build.Id != constant.MAIN_BUILD_ID {
		if mb := area.GetMainBuild(); mb == nil || build.Lv >= mb.Lv {
			return nil, ecode.NEED_MAIN_LV.String()
		}
	}
	// 扣除资源
	if !plr.CheckAndDeductCostByTypeObjs(build.UpCosts) {
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 添加到修建队列
	plr.PutBTQueue(index, build.Uid, build.Id, build.Lv+1, build.GetBTNeedTime())
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_UPAREABUILD_S2C{
		Output: plr.ToOutputInfoPb(),
		Queues: plr.ToBTQueuesPb(),
	})
}

// 创建建筑
func (this *Game) addAreaBuild(session gate.Session, msg *pb.GAME_HD_ADDAREABUILD_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, id := msg.GetIndex(), msg.GetId()
	json := config.GetJsonData("buildBase", id)
	if json == nil {
		return nil, ecode.BUILD_NOT_EXIST.String()
	}
	btCount := ut.Int(json["bt_count"])
	if btCount == 0 {
		return nil, ecode.BUILD_NOT_EXIST.String()
	}
	area := wld.GetArea(index)
	if area == nil || area.Owner != plr.Uid || index != plr.MainCityIndex { // 目前暂时只能在主城修建建筑
		return nil, ecode.ONLY_IN_SLEF_MAIN_BUILD.String()
	} else if area.IsBattle() {
		return nil, ecode.BATTLEING.String() // 当前战斗中
	} else if plr.IsBTQueueFull() { // 队列是否满了
		return nil, ecode.BTQUEUE_FULL.String()
	} else if !plr.CheckCTypes(g.StringToTypeObjs(json["prep_cond"])) {
		return nil, ecode.COND_NOT_ENOUGH.String()
	} else if types := ut.StringToInts(ut.String(json["server"]), ","); len(types) > 0 && !array.Has(types, int(room.GetType())) {
		return nil, ecode.COND_NOT_ENOUGH.String() // 只能修建对于服务器类型的建筑
	} else if plr.State == constant.PS_CAPTURE {
		return nil, ecode.UNKNOWN.String() // 已沦陷无法修建
	} else if btCount == -1 {
		if len(area.GetAreaBuildsById(id)) > 0 {
			return nil, ecode.YET_IN_BTQUEUE.String() // 只能创建一个
		}
	} else if btCount == -3 {
		if !area.IsBuildsMaxLv(id) {
			return nil, ecode.HAS_ONE_BUILD_NOT_MAXLV.String() // 同建筑全部满级才可创建
		}
	}
	// 创建一个0级的
	build := world.NewAreaBuild(index, ut.ID(), ut.NewVec2(0, 0), id, 0)
	// 获取位置
	point, ok := area.GetBuildCanPlacePos(build)
	if !ok {
		return nil, ecode.NOT_CAN_PLACE_POS.String() // 没有可摆放的位置了
	} else if !plr.CheckAndDeductCostByTypeObjs(build.UpCosts) {
		return nil, ecode.RES_NOT_ENOUGH.String() // 扣除资源
	}
	// 设置位置
	build.Point.Set(point)
	// 添加到场景里面
	area.AddBuild(build)
	// 添加到修建队列
	plr.PutBTQueue(index, build.Uid, build.Id, 1, build.GetBTNeedTime())
	// 标记一下
	wld.TagUpdateDBByIndex(index)
	// 通知
	wld.NotifyAreaUpdateInfo(index, constant.NQ_ADD_BUILD, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_8: build.ToPb()}, plr.Uid)
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_ADDAREABUILD_S2C{
		Build:  build.ToPb(),
		Output: plr.ToOutputInfoPb(),
		Queues: plr.ToBTQueuesPb(),
	})
}

// 取消修建
func (this *Game) cancelBt(session gate.Session, msg *pb.GAME_HD_CANCELBT_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, uid := msg.GetIndex(), msg.GetUid()
	area := wld.GetArea(index)
	if area == nil || area.Owner != plr.Uid {
		return nil, ecode.UNKNOWN.String()
	}
	build := area.GetBuildByUID(uid)
	if build == nil {
		return nil, ecode.UNKNOWN.String()
	}
	// 取消队列
	plr.CancelBT(uid)
	s2c := &pb.GAME_HD_CANCELBT_S2C{Queues: plr.ToBTQueuesPb()}
	// 返回升到当前等级的资源
	if build.Lv > 0 {
		attrId := build.Id*1000 + (build.Lv - 1)
		json := config.GetJsonData("buildAttr", attrId)
		if json == nil {
			log.Error("not buildAttr, id=" + ut.Itoa(attrId))
		} else if tos := g.StringToTypeObjs(json["up_cost"]); len(tos) > 0 {
			plr.ChangeCostByTypeObjs(tos, 1) // 退还资源
			s2c.Output = plr.ToOutputInfoPb()
		}
	} else { // 如果是0级就不返回资源 并删除掉
		area.RemoveBuild(uid)
		wld.TagUpdateDBByIndex(index)
		wld.NotifyAreaUpdateInfo(index, constant.NQ_REMOVE_BUILD, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_9: uid})
	}
	return pb.ProtoMarshal(s2c)
}

// 立即完成
func (this *Game) inDoneBt(session gate.Session, msg *pb.GAME_HD_INDONEBT_C2S) (ret []byte, err string) {
	e, _, plr, _ := checkError(session)
	s2c := &pb.GAME_HD_INDONEBT_S2C{}
	if e != "" {
		return nil, e
	} else if plr.GetBTQueueCount() == 0 {
		s2c.Queues = plr.ToBTQueuesPb()
		return pb.ProtoMarshal(s2c)
	}
	gold := this.ChangePlayerGold(plr, -constant.IN_DONE_BT_GOLD, constant.GOLD_CHANGE_IN_DONE_BT)
	if gold == -1 {
		return nil, ecode.GOLD_NOT_ENOUGH.String() // 金币不足
	}
	// 完成所有
	plr.CompleteAllBt()
	// 返回
	s2c.Queues = plr.ToBTQueuesPb()
	s2c.Gold = gold
	return pb.ProtoMarshal(s2c)
}
