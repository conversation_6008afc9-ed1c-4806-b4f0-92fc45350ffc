package main

// import (
// 	"context"
// 	slg "slgsrv/server/common"
// 	"slgsrv/server/lobby"
// 	ut "slgsrv/utils"
// 	"time"

// 	"github.com/huyangv/vmqant/log"
// 	"go.mongodb.org/mongo-driver/bson"
// 	"go.mongodb.org/mongo-driver/mongo"
// 	"go.mongodb.org/mongo-driver/mongo/options"
// )

// // 处理兑换码数据库转换

// const (
// 	lobbyDbUri  = "mongodb://localhost:27017" //大厅服数据库地址
// 	lobbyDbName = "slgsrv"                    //大厅服数据库名
// )

// var lobbyDb *mongo.Database

// func main() {
// 	log.Info("start script")
// 	// 初始化
// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()
// 	// 连接大厅服数据库
// 	clientLobby, err := mongo.NewClient(options.Client().ApplyURI(lobbyDbUri))
// 	if err != nil {
// 		log.Error("parse lobbyDbUri err: %v", err)
// 	}
// 	err = clientLobby.Connect(ctx)
// 	if err != nil {
// 		log.Error("connect lobbyDb err: %v", err)
// 	}
// 	defer clientLobby.Disconnect(ctx)
// 	lobbyDb = clientLobby.Database(lobbyDbName)
// 	// 兑换码转换
// 	CdkTransfer()
// 	// 邮件兼容
// 	MailTransfer()
// }

// // 兑换码转换
// func CdkTransfer() {
// 	log.Info("start cdk transfer")
// 	cdkCol := lobbyDb.Collection(slg.DB_COLLECTION_NAME_GIFT)
// 	claimCol := lobbyDb.Collection(slg.DB_COLLECTION_NAME_GIFT_CLAIM)
// 	cursor, e := cdkCol.Find(context.TODO(), bson.D{})
// 	defer func() {
// 		_ = cursor.Close(context.TODO())
// 	}()
// 	count := 0
// 	if e == nil {
// 		var list []lobby.CodeGift
// 		cursor.All(context.TODO(), &list)
// 		for _, v := range list {
// 			cdk := &v
// 			// 兼容字段
// 			if cdk.IsClaim == nil || len(cdk.IsClaim) == 0 {
// 				continue
// 			}
// 			claimList := []interface{}{}
// 			for _, claimInfo := range cdk.IsClaim {
// 				newClaimInfo := &lobby.CodeClaim{
// 					UID:     claimInfo.UID + "_" + v.Code,
// 					CodeUid: v.UID,
// 				}
// 				claimList = append(claimList, newClaimInfo)
// 			}
// 			start := ut.Now()
// 			// 领取数据插入到新表
// 			claimCol.InsertMany(context.TODO(), claimList)
// 			log.Info("insertMany cost time: %vms, code: %v", ut.Now()-start, v.Code)
// 			count++
// 		}
// 	}
// 	log.Info("cdk transfer finish count: %v", count)
// }

// // 邮件转换
// func MailTransfer() {
// 	// log.Info("mail transfer")

// 	// mailCol := lobbyDb.Collection(slg.DB_COLLECTION_NAME_MAIL)
// 	// // 添加邮件表索引
// 	// mailIndexModels := make([]mongo.IndexModel, 0)
// 	// mailIndexModels = append(mailIndexModels, mongo.IndexModel{Keys: bson.M{"uid": 1}})
// 	// mailIndexModels = append(mailIndexModels, mongo.IndexModel{Keys: bson.M{"create_time": -1}})
// 	// mailIndexModels = append(mailIndexModels, mongo.IndexModel{Keys: bson.M{"receiver": 1}})
// 	// mailIndexModels = append(mailIndexModels, mongo.IndexModel{Keys: bson.M{"receiver_map": 1}})
// 	// mailIndexModels = append(mailIndexModels, mongo.IndexModel{Keys: bson.M{"sid": 1}})
// 	// _, err := mailCol.Indexes().CreateMany(context.TODO(), mailIndexModels)
// 	// if err != nil {
// 	// 	log.Error("mail add indexs err: %v", err)
// 	// }

// 	// userMailCol := lobbyDb.Collection(slg.DB_COLLECTION_NAME_USER_MAIL)
// 	// // 添加玩家邮件表索引
// 	// userIndexModels := make([]mongo.IndexModel, 0)
// 	// userIndexModels = append(userIndexModels, mongo.IndexModel{Keys: bson.M{"uid": 1}, Options: options.Index().SetUnique(true)})
// 	// userIndexModels = append(userIndexModels, mongo.IndexModel{Keys: bson.M{"user_id": -1}})
// 	// userIndexModels = append(userIndexModels, mongo.IndexModel{Keys: bson.M{"mail_uid": -1}})
// 	// _, err = userMailCol.Indexes().CreateMany(context.TODO(), userIndexModels)
// 	// if err != nil {
// 	// 	log.Error("user mail add indexs err: %v", err)
// 	// }

// 	// // 遍历邮件表
// 	// cursor, e := mailCol.Find(context.TODO(), bson.D{})
// 	// defer func() {
// 	// 	_ = cursor.Close(context.TODO())
// 	// }()
// 	// count := 0
// 	// if e == nil {
// 	// 	var list []mail.Mail
// 	// 	cursor.All(context.TODO(), &list)
// 	// 	for _, v := range list {
// 	// 		mail := &v
// 	// 		mailBase := &newMail.MailBaseInfo{
// 	// 			UID:        mail.UID,
// 	// 			SID:        mail.SID,
// 	// 			Receiver:   mail.Receiver,
// 	// 			Sender:     mail.Sender,
// 	// 			Title:      mail.Title,
// 	// 			ContentId:  mail.ContentId,
// 	// 			Content:    mail.Content,
// 	// 			CreateTime: mail.CreateTime,
// 	// 		}
// 	// 		// 兼容字段
// 	// 		if arr := strings.Split(mail.Receiver, "|"); len(arr) > 1 {
// 	// 			mailBase.ReceiverMap = map[string]bool{}
// 	// 			for _, userId := range arr {
// 	// 				mailBase.ReceiverMap[userId] = true
// 	// 			}
// 	// 		}
// 	// 		if arr := strings.Split(mail.NoReceiver, "|"); len(arr) > 0 && arr[0] != "" {
// 	// 			mailBase.NoReceiverMap = map[string]bool{}
// 	// 			for _, userId := range arr {
// 	// 				mailBase.NoReceiverMap[userId] = true
// 	// 			}
// 	// 		}

// 	// 		// 兼容领取信息
// 	// 		claimMap := map[string]bool{}
// 	// 		if mail.ClaimList != nil && len(mail.ClaimList) > 0 {
// 	// 			userMailList := []interface{}{}
// 	// 			for _, userId := range mail.ClaimList {
// 	// 				if claimMap[userId] {
// 	// 					continue
// 	// 				}
// 	// 				userMailList = append(userMailList, &newMail.UserMailInfo{
// 	// 					UID:     mailBase.UID + "_" + userId,
// 	// 					MailUid: mailBase.UID,
// 	// 					UserId:  userId,
// 	// 					IsRead:  true,
// 	// 					IsClaim: true,
// 	// 				})
// 	// 				if len(userMailList) >= 1000 {
// 	// 					_, err := userMailCol.InsertMany(context.TODO(), userMailList)
// 	// 					if err != nil {
// 	// 						log.Error("insert user claims err: %v, datas: %v", err, userMailList)
// 	// 					}
// 	// 					userMailList = []interface{}{}
// 	// 				}
// 	// 			}
// 	// 			if len(userMailList) > 0 {
// 	// 				_, err := userMailCol.InsertMany(context.TODO(), userMailList)
// 	// 				if err != nil {
// 	// 					log.Error("insert user claims err: %v, datas: %v", err, userMailList)
// 	// 				}
// 	// 			}
// 	// 		}

// 	// 		// 兼容单个领取信息
// 	// 		if mail.OneClaimMap != nil && len(mail.OneClaimMap) > 0 {
// 	// 			userMailList := []interface{}{}
// 	// 			for userId, claimList := range mail.OneClaimMap {
// 	// 				if claimMap[userId] {
// 	// 					continue
// 	// 				}
// 	// 				userMailList = append(userMailList, &newMail.UserMailInfo{
// 	// 					UID:       mailBase.UID + "_" + userId,
// 	// 					MailUid:   mailBase.UID,
// 	// 					UserId:    userId,
// 	// 					IsRead:    true,
// 	// 					IsClaim:   true,
// 	// 					ClaimList: claimList,
// 	// 				})
// 	// 				if len(userMailList) >= 1000 {
// 	// 					_, err := userMailCol.InsertMany(context.TODO(), userMailList)
// 	// 					if err != nil {
// 	// 						log.Error("insert user one claims err: %v, datas: %v", err, userMailList)
// 	// 					}
// 	// 					userMailList = []interface{}{}
// 	// 				}
// 	// 			}
// 	// 			if len(userMailList) > 0 {
// 	// 				_, err := userMailCol.InsertMany(context.TODO(), userMailList)
// 	// 				if err != nil {
// 	// 					log.Error("insert user one claims err: %v, datas: %v", err, userMailList)
// 	// 				}
// 	// 			}
// 	// 		}

// 	// 		// 更新邮件数据
// 	// 		mailCol.UpdateOne(context.TODO(), bson.M{"uid": mail.UID}, bson.M{"$set": mailBase})
// 	// 		count++
// 	// 		if count%1000 == 0 {
// 	// 			log.Info("mail transfer cur count: %v", count)
// 	// 		}
// 	// 	}
// 	// }
// 	// log.Info("mail transfer finish count: %v", count)
// }
