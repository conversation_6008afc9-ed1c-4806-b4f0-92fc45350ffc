package lobby

import (
	"strconv"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

func (this *Lobby) InitHDFriend() {
	this.GetServer().RegisterGO("HD_SearchUser", this.searchUser)                     // 查询玩家
	this.GetServer().RegisterGO("HD_ApplyFriend", this.applyFriend)                   // 好友申请
	this.GetServer().RegisterGO("HD_ApplyFriendResponse", this.applyFriendResponse)   // 好友申请回复
	this.GetServer().RegisterGO("HD_DelFriend", this.delFriend)                       // 删除好友
	this.GetServer().RegisterGO("HD_GetFriendChats", this.getFriendChats)             // 获取好友聊天消息
	this.GetServer().RegisterGO("HD_FriendChat", this.friendChat)                     // 好友聊天
	this.GetServer().RegisterGO("HD_SetFriendNoteName", this.setFriendNoteName)       // 设置好友备注名
	this.GetServer().RegisterGO("HD_FriendChatRead", this.friendChatRead)             // 好友聊天已读
	this.GetServer().RegisterGO("HD_Blacklist", this.blacklist)                       // 黑名单
	this.GetServer().RegisterGO("HD_SpectateFriend", this.spectateFriend)             // 观战好友
	this.GetServer().RegisterGO("HD_SendSkinGift", this.sendSkinGift)                 // 赠送皮肤
	this.GetServer().RegisterGO("HD_ReceiveFriendGift", this.receiveFriendGift)       // 领取好友礼物
	this.GetServer().RegisterGO("HD_GetFriendGiftRecords", this.getFriendGiftRecords) // 礼物赠送记录查询
}

// 查询玩家
func (this *Lobby) searchUser(session gate.Session, msg *pb.LOBBY_HD_SEARCHUSER_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	uid, name := msg.GetUid(), msg.GetNickname()
	var lastLoginTime int64
	if uid != "" {
		// 根据uid查询
		findUser := GetUserByDBNotAdd(uid)
		if findUser == nil {
			return nil, ecode.PLAYER_NOT_EXIST.String()
		}
		if !findUser.IsOnline() {
			lastLoginTime = findUser.LastLoginTime
		}
		return pb.ProtoMarshal(&pb.LOBBY_HD_SEARCHUSER_S2C{Uid: uid, Nickname: findUser.Nickname, HeadIcon: findUser.HeadIcon, LastLoginTime: int64(lastLoginTime)})
	} else if name != "" {
		// 根据昵称查询
		findUser, e := db.FindUserByName(name)
		if e != "" {
			return nil, ecode.PLAYER_NOT_EXIST.String()
		} else if GetUserByOnline(uid) == nil {
			lastLoginTime = findUser.LastLoginTime
		}
		return pb.ProtoMarshal(&pb.LOBBY_HD_SEARCHUSER_S2C{Uid: uid, Nickname: findUser.Nickname, HeadIcon: findUser.HeadIcon, LastLoginTime: int64(lastLoginTime)})
	} else {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
}

// 好友申请
func (this *Lobby) applyFriend(session gate.Session, msg *pb.LOBBY_HD_APPLYFRIEND_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.IsBannedChat() {
		return nil, ecode.BAN_OPT.String() // 被禁言不可操作
	}
	// if user.MaxLandCount < slg.FRIENDS_MIN_LAND_COUNT {
	// 	// 最大地块数不足
	// 	return nil, ecode.FRIEND_APPLY_LAND_LIMIT.String()
	// }
	if user.IsGuest() {
		// 游客不能添加好友
		return nil, ecode.FRIEND_NOT_GUEST.String()
	}
	uid := msg.GetUid()
	if user.UID == uid {
		// 不能添加自己为好友
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	var friendUserTmp *User
	if _, err := strconv.Atoi(uid); err == nil {
		// uid为数字则通过uid查询
		friendUserTmp = GetUserByDBNotAdd(uid)
	}
	if friendUserTmp == nil && uid != user.Nickname { // 找不到用昵称找
		friendUserTmp = GetUserByNicknmaeOrDB(uid)
	}
	if friendUserTmp == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	if friendUserTmp.UID == user.UID {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	// 判断好友数量上限
	if user.GetFriendCount() >= slg.FRIENDS_MAX_NUM {
		return nil, ecode.FRIENDS_MAX_NUM.String()
	}
	// 判断是否添加添加黑名单
	if user.HasBlackList(friendUserTmp.UID) {
		return nil, ecode.BLACKLIST_BLOCK_FRIEND.String()
	}
	applied := user.HasApplyFriend(friendUserTmp.UID)
	var rst map[string]interface{}
	rst, err = ut.RpcInterfaceMap(this.InvokeUserFunc(friendUserTmp.UID, slg.RPC_USER_APPLY_FRIEND_BY_OTHER, user.UID, user.Nickname, user.HeadIcon, user.GetPlaySid(), applied))
	if err != "" {
		return
	} else if applied {
		user.AddFriend(uid, ut.String(rst["nickName"]), ut.String(rst["headIcon"]), ut.Int32(rst["playSid"]), ut.Int64(rst["lastOfflineTime"]))
		user.DelApplyFriend(friendUserTmp.UID)
		// 通知添加好友并从申请列表删除
		user.NotifyUser(slg.LOBBY_FRIEND_APPLY, &pb.LOBBY_ONFRIENDAPPLY_NOTIFY{Uid: friendUserTmp.UID})
	}
	return
}

// 好友申请回复
func (this *Lobby) applyFriendResponse(session gate.Session, msg *pb.LOBBY_HD_APPLYFRIENDRESPONSE_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	uid, agree, ban := msg.GetUid(), msg.GetAgree(), msg.GetBan()
	// 被禁言不可操作
	if agree && user.IsBannedChat() {
		return nil, ecode.BAN_OPT.String()
	}
	if agree {
		// 判断对方是否申请添加好友
		if !user.HasApplyFriend(uid) {
			return nil, ecode.NOT_APPLY_FRIEND.String()
		}
		// 判断好友数量上限
		if len(user.FriendsList) >= slg.FRIENDS_MAX_NUM {
			return nil, ecode.FRIENDS_MAX_NUM.String()
		}
		// 判断是否已是好友
		if user.HasFriend(uid) {
			user.DelApplyFriend(uid)
			user.FlagUpdateDB()
			return nil, ecode.ALREADY_FRIENDS.String()
		}
		// 判断是否添加黑名单
		if user.HasBlackList(uid) {
			return nil, ecode.BLACKLIST_BLOCK_FRIEND.String()
		}
		// 获取对方玩家 在其对应的大厅服处理申请
		var rst map[string]interface{}
		rst, err = ut.RpcInterfaceMap(this.InvokeUserFunc(uid, slg.RPC_USER_APPLY_RESPONSE_BY_OTHER, user.UID, user.Nickname, user.HeadIcon, user.GetPlaySid(), agree))
		if err != "" {
			if err == ecode.PLAYER_NOT_EXIST.String() {
				user.DelApplyFriend(uid)
				user.FlagUpdateDB()
			}
			return
		}
		// 添加到好友列表
		user.AddFriend(uid, ut.String(rst["nickname"]), ut.String(rst["headIcon"]), ut.Int32(rst["playSid"]), ut.Int64(rst["lastOfflineTime"]))
	} else if ban && len(user.Blacklists) < slg.BLACK_LIST_MAX {
		// 拒绝且不再接受申请则直接加入黑名单
		friendUser := GetUserByDBNotAdd(uid)
		if friendUser == nil {
			user.DelApplyFriend(uid)
			user.FlagUpdateDB()
			return nil, ecode.PLAYER_NOT_EXIST.String()
		}
		user.AddBlackList(uid, friendUser.Nickname, friendUser.HeadIcon)
	}
	// 从申请列表删除
	user.DelApplyFriend(uid)
	user.FlagUpdateDB()
	return pb.ProtoMarshal(&pb.LOBBY_HD_APPLYFRIENDRESPONSE_S2C{
		FriendsList:   user.FriendsListToPb(),
		FriendsApplys: user.FriendsApplyListToPb(),
		Blacklists:    user.ToBlacklistsPb(),
	})
}

// 删除好友
func (this *Lobby) delFriend(session gate.Session, msg *pb.LOBBY_HD_DELFRIEND_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	uid := msg.GetUid()
	// 判断是否为好友
	if user.HasFriend(uid) {
		// 删除好友
		user.DelFriend(uid)
	}
	// 从对方里面也删除
	this.InvokeUserFuncNR(uid, slg.RPC_USER_DEL_FRIEND_BY_OTHER, user.UID)
	return pb.ProtoMarshal(&pb.LOBBY_HD_DELFRIEND_S2C{})
}

// 获取好友聊天消息
func (this *Lobby) getFriendChats(session gate.Session, msg *pb.LOBBY_HD_GETFRIENDCHATS_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	uid := msg.GetUid()
	friendInfo := user.GetFriend(uid)
	if friendInfo == nil {
		// 好友不存在
		return nil, ecode.NOT_FRIENDS.String()
	}
	channel := GetFriendChannel(user.UID, friendInfo.Uid)
	// 从聊天服获取好友聊天数据
	ret, err = ut.RpcBytes(this.InvokeChatRpc(slg.RPC_GET_FRIEND_CHAT, uid, channel))
	if err != "" {
		log.Error("getFriendChats channel: %v, err: %v", channel, err)
		return nil, ecode.DB_ERROR.String()
	}
	return
}

// 好友聊天
func (this *Lobby) friendChat(session gate.Session, msg *pb.LOBBY_HD_FRIENDCHAT_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	uid, friendUid, content, emoji := msg.GetUid(), msg.GetFriendUid(), msg.GetContent(), msg.GetEmoji()
	friendInfo := user.GetFriend(friendUid)
	if friendInfo == nil {
		// 好友不存在
		return nil, ecode.NOT_FRIENDS.String()
	}
	// 检测是否拥有表情
	if !user.CheckHasEmoji(emoji) {
		err = ecode.CHAT_EMOJI_NOT_EXIST.String()
		return
	}
	now := ut.Now()
	chatInfo := &pb.ChatInfo{
		Uid:     uid,
		Sender:  user.UID,
		Content: content,
		Emoji:   emoji,
		Time:    int64(now),
	}
	channel := GetFriendChannel(user.UID, friendUid)
	// 发送到聊天服
	data, err := pb.ProtoMarshal(chatInfo)
	_, err = this.InvokeChatRpc(slg.RPC_SEND_FRIEND_CHAT, channel, data)
	if err != "" {
		log.Warning("friendChat userId: %v, friendUid: %v, rpc err: %v", user.UID, friendUid, err)
		return
	}
	chatNotify := FriendChatToNotifyPb(chatInfo, user.UID)
	notifyData, err := pb.ProtoMarshal(chatNotify)
	if err != "" {
		log.Warning("friendChat userId: %v, friendUid: %v, notify err: %v", user.UID, friendUid, err)
		return
	}
	// 通知对方
	this.InvokeUserFunc(friendUid, slg.RPC_USER_RECEIVE_FRIEND_MSG, user.Nickname, notifyData)
	if err != "" {
		log.Warning("friendChat userId: %v, friendUid: %v, err: %v", user.UID, friendUid, err)
		return
	}
	user.FlagUpdateDB()
	chatNotify.Uid = friendUid
	user.NotifyUser(slg.LOBBY_FRIEND_CHAT, chatNotify)
	return pb.ProtoMarshal(&pb.LOBBY_HD_FRIENDCHAT_S2C{})
}

// 设置好友备注名
func (this *Lobby) setFriendNoteName(session gate.Session, msg *pb.LOBBY_HD_SETFRIENDNOTENAME_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	uid, noteName := msg.GetUid(), msg.GetNoteName()
	if ut.GetStringLen(noteName) > 8 {
		// 备注名长度限制
		return nil, ecode.TEXT_LEN_LIMIT.String()
	}
	user.FriendsLock.RLock()
	friendInfo := array.Find(user.FriendsList, func(v *Friend) bool { return v.Uid == uid })
	user.FriendsLock.RUnlock()
	if friendInfo == nil {
		// 好友不存在
		return nil, ecode.NOT_FRIENDS.String()
	}
	friendInfo.NoteName = noteName
	user.FlagUpdateDB()
	return pb.ProtoMarshal(&pb.LOBBY_HD_SETFRIENDNOTENAME_S2C{})
}

// 好友聊天已读
func (this *Lobby) friendChatRead(session gate.Session, msg *pb.LOBBY_HD_FRIENDCHATREAD_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	uid := msg.GetUid()
	user.FriendsLock.RLock()
	friendInfo := array.Find(user.FriendsList, func(v *Friend) bool { return v.Uid == uid })
	user.FriendsLock.RUnlock()
	if friendInfo == nil {
		// 好友不存在
		return nil, ecode.NOT_FRIENDS.String()
	}
	friendInfo.NotReadCount = 0
	user.FlagUpdateDB()
	return pb.ProtoMarshal(&pb.LOBBY_HD_FRIENDCHATREAD_S2C{})
}

// 黑名单
func (this *Lobby) blacklist(session gate.Session, msg *pb.LOBBY_HD_BLACKLIST_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	uid, state := msg.GetUid(), msg.GetState()
	i := array.FindIndex(user.Blacklists, func(m *BlacklistInfo) bool { return m.UID == uid })
	var pChannel string // 私聊频道
	if state {          // 取消黑名单
		if i != -1 {
			user.DelBlackList(i)
		}
	} else if i == -1 { // 添加
		if len(user.Blacklists) >= slg.BLACK_LIST_MAX {
			return nil, ecode.BLACKLIST_MAX_NUM.String()
		}
		rst, e := ut.RpcInterfaceMap(this.InvokeUserFunc(uid, slg.RPC_GET_USER_BASE_INFO))
		if e != "" {
			return nil, e
		}
		user.AddBlackList(uid, ut.String(rst["nickname"]), ut.String(rst["headIcon"]))
		// 如果是好友则删除
		if user.HasFriend(uid) {
			user.DelFriend(uid)
			user.NotifyUser(slg.LOBBY_FRIEND_DEL, &pb.LOBBY_ONFRIENDDEL_NOTIFY{Uid: uid})
			// 从对方里面也删除
			this.InvokeUserFuncNR(uid, slg.RPC_USER_DEL_FRIEND_BY_OTHER, user.UID)
		}
		if user.GetPlaySid() > 0 { // 在游戏服则删除私聊频道
			data, e := ut.RpcInterfaceMap(this.InvokeGameRpc(user.GetPlaySid(), slg.RPC_ADD_BLACK_LIST, user.UID, uid))
			if e == "" {
				pChannel = ut.String(data["channel"])
			}
		}
	}
	user.FlagUpdateDB()
	return pb.ProtoMarshal(&pb.LOBBY_HD_BLACKLIST_S2C{Blacklists: user.ToBlacklistsPb(), Channel: pChannel})
}

// 观战好友
func (this *Lobby) spectateFriend(session gate.Session, msg *pb.LOBBY_HD_SPECTATEFRIEND_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	uid := msg.GetUid()
	user.FriendsLock.RLock()
	friendInfo := array.Find(user.FriendsList, func(v *Friend) bool { return v.Uid == uid })
	user.FriendsLock.RUnlock()
	if friendInfo == nil {
		// 好友不存在
		return nil, ecode.NOT_FRIENDS.String()
	}
	info, e := ut.RpcInterfaceMap(this.InvokeUserFunc(uid, slg.RPC_GET_USER_INFO))
	if e == "" && info != nil {
		playSid := ut.Int32(info["playSid"])
		room := GetRoomById(playSid)
		if room == nil || room.IsClose || IsGameOver(room) {
			return nil, ecode.ROOM_OVER.String()
		} else if user.GetPlaySid() == playSid {
			return nil, ecode.UNKNOWN.String()
		} else if user.IsGiveupGame(playSid) {
			return nil, ecode.YET_GIVE_GAME.String()
		} else if playSid > 0 {
			session.Set("sid", ut.Itoa(playSid)) // 在这里设置一下服务器id
		}
		return pb.ProtoMarshal(&pb.LOBBY_HD_SPECTATEFRIEND_S2C{Sid: playSid})
	}
	return nil, ecode.NOT_FRIENDS.String()
}

// 赠送皮肤
func (this *Lobby) sendSkinGift(session gate.Session, msg *pb.LOBBY_HD_SENDSKINGIFT_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	giftId, boxId, friendUid := msg.GetId(), msg.GetBoxId(), msg.GetFriendUid()
	// 检测是否是好友
	user.FriendsLock.RLock()
	friendInfo := array.Find(user.FriendsList, func(v *Friend) bool { return v.Uid == friendUid })
	user.FriendsLock.RUnlock()
	if friendInfo == nil {
		// 好友不存在
		return nil, ecode.NOT_FRIENDS.String()
	}
	var boxPrice int32
	if IsRareSkin(giftId) {
		boxPrice = slg.RARE_SKIN_GIFT_BOX_PRICE
	} else {
		boxPrice = slg.NORMAL_SKIN_GIFT_BOX_PRICE
	}
	if user.Ingot < boxPrice {
		// 元宝不足
		return nil, ecode.INGOT_NOT_ENOUGH.String()
	}

	// 检测并扣除皮肤物品
	var sendSkinItem *SkinItem
	if sendSkinItem = user.ReduceSkinItemByGift(giftId); sendSkinItem == nil {
		// 物品不足
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 扣除元宝
	ingotRst := user.ChangeIngot(-boxPrice, constant.GOLD_CHANGE_FRIEND_GIFT)
	if ingotRst == -1 {
		// 元宝不足
		return nil, ecode.INGOT_NOT_ENOUGH.String()
	}
	_, err = this.InvokeUserFunc(friendUid, slg.RPC_FRIEND_SEND_GIFT, user.UID, sendSkinItem.UID, giftId, boxId, int32(ctype.SKIN_ITEM))
	if err != "" {
		log.Warning("sendSkinGift err uid: %v, friendUid: %v, giftId: %v, boxId: %v", user.UID, friendUid, giftId, boxId)
		// 赠送失败 返还皮肤物品和元宝
		user.SkinItemListLock.Lock()
		user.SkinItemList = append(user.SkinItemList, sendSkinItem)
		user.SkinItemListLock.Unlock()
		user.ChangeIngot(boxPrice, constant.GOLD_CHANGE_FRIEND_GIFT)
		return
	} else if user.GetPlaySid() != 0 && !user.CheckHasSkin(giftId) {
		// 正在对局中 检测是否还有该皮肤物品 没有则在游戏服卸下使用中的该皮肤
		this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_CLEAR_PWANS_SKIN, user.UID, giftId)
	}
	user.FlagUpdateDB()
	// 数数上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_sendSkinGift", map[string]interface{}{
		"ingot_cost":   boxPrice,
		"pawn_skin_id": giftId,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_SENDSKINGIFT_S2C{Ingot: user.Ingot, Uid: sendSkinItem.UID})
}

// 领取好友礼物
func (this *Lobby) receiveFriendGift(session gate.Session, msg *pb.LOBBY_HD_RECEIVEFRIENDGIFT_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	giftUid, friendUid := msg.GetUid(), msg.GetFriendUid()
	// 获取好友
	user.FriendsLock.RLock()
	friendInfo := array.Find(user.FriendsList, func(v *Friend) bool { return v.Uid == friendUid })
	user.FriendsLock.RUnlock()
	isBan := false
	itemTrack, e1 := skinItemTrackDb.FindSkinItemTrack(giftUid)
	// 领取礼物前先获取初始拥有者是否被封号
	if e1 == nil && itemTrack.Owner != "" {
		rpcRst, e := ut.RpcInterfaceMap(this.InvokeUserFunc(itemTrack.Owner, slg.RPC_USER_BAN_ACCCOUNT_CHECK))
		if e == "" && rpcRst != nil {
			isBan = ut.Bool(rpcRst["rst"])
		}
	}
	friendGift, err := user.ReceiveFriendGift(friendInfo, giftUid, isBan)
	if err != "" {
		return nil, err
	}
	s2c := &pb.LOBBY_HD_RECEIVEFRIENDGIFT_S2C{Rewards: &pb.UpdateOutPut{}}
	s2c.Rewards.SkinItems = user.ToSkinImtemsPb()
	s2c.Rewards.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_SkinItemEnum))
	user.FlagUpdateDB()
	// 更新礼物记录
	friendGiftRecordDb.UpdateFriendGiftRecord(giftUid)
	// 数数上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_receiveFriendGift", map[string]interface{}{
		"pawn_skin_id": friendGift.Id,
	})
	return pb.ProtoMarshal(s2c)
}

// 礼物赠送记录查询
func (this *Lobby) getFriendGiftRecords(session gate.Session, msg *pb.LOBBY_HD_GETFRIENDGIFTRECORDS_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	tp, page := ut.Int32(msg.GetPage()), ut.Int32(msg.GetPage())
	datas, err := friendGiftRecordDb.FindFriendGiftRecords(user.UID, tp, page)
	if err != "" {
		return nil, ecode.DB_ERROR.String()
	}
	s2c := &pb.LOBBY_HD_GETFRIENDGIFTRECORDS_S2C{List: array.Map(datas, func(m FriendGiftRecord, _ int) *pb.FriendGiftRecordInfo { return m.ToPb() })}
	if page == 1 {
		var totalPage int32
		// 第一页则返回总页数
		if len(datas) < int(slg.FRIEND_GIFT_RECORD_PAGE_NUM) {
			totalPage = 1
		} else {
			totalCount, err := friendGiftRecordDb.FindFriendGiftRecordsCount(user.UID, tp)
			if err != "" {
				return nil, ecode.DB_ERROR.String()
			}
			totalPage = totalCount / slg.FRIEND_GIFT_RECORD_PAGE_NUM
			if totalCount%slg.FRIEND_GIFT_RECORD_PAGE_NUM > 0 {
				totalPage++
			}
		}
		s2c.TotalPage = int32(totalPage)
	}
	return pb.ProtoMarshal(s2c)
}
