[{"id": 1001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 1002, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 1003, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 1004, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 1005, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 1006, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 1007, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 1008, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 1009, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 1010, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 2001, "value": 0.55, "round": 5, "round_add": "", "times": -1}, {"id": 2002, "value": 0.6, "round": 5, "round_add": "", "times": -1}, {"id": 2003, "value": 0.65, "round": 5, "round_add": "", "times": -1}, {"id": 2004, "value": 0.7, "round": 5, "round_add": "", "times": -1}, {"id": 2005, "value": 0.75, "round": 5, "round_add": "", "times": -1}, {"id": 2006, "value": 0.8, "round": 5, "round_add": "", "times": -1}, {"id": 3001, "value": 30, "round": 3, "round_add": "", "times": -1}, {"id": 3002, "value": 40, "round": 3, "round_add": "", "times": -1}, {"id": 3003, "value": 50, "round": 3, "round_add": "", "times": -1}, {"id": 3004, "value": 60, "round": 3, "round_add": "", "times": -1}, {"id": 3005, "value": 70, "round": 3, "round_add": "", "times": -1}, {"id": 3006, "value": 80, "round": 3, "round_add": "", "times": -1}, {"id": 4001, "value": 0.3, "round": 3, "round_add": "", "times": -1}, {"id": 4002, "value": 0.35, "round": 3, "round_add": "", "times": -1}, {"id": 4003, "value": 0.4, "round": 3, "round_add": "", "times": -1}, {"id": 4004, "value": 0.45, "round": 3, "round_add": "", "times": -1}, {"id": 4005, "value": 0.5, "round": 3, "round_add": "", "times": -1}, {"id": 4006, "value": 0.55, "round": 3, "round_add": "", "times": -1}, {"id": 4007, "value": 0.6, "round": 3, "round_add": "", "times": -1}, {"id": 4008, "value": 0.65, "round": 3, "round_add": "", "times": -1}, {"id": 4009, "value": 0.7, "round": 3, "round_add": "", "times": -1}, {"id": 4010, "value": 0.75, "round": 3, "round_add": "", "times": -1}, {"id": 5001, "value": 0, "round": 1, "round_add": "", "times": -1}, {"id": 5002, "value": 0, "round": 2, "round_add": "", "times": -1}, {"id": 5003, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 6001, "value": 0.3, "round": 1, "round_add": "", "times": -1}, {"id": 7001, "value": 0.3, "round": 3, "round_add": "", "times": -1}, {"id": 7002, "value": 0.4, "round": 3, "round_add": "", "times": -1}, {"id": 7003, "value": 0.5, "round": 3, "round_add": "", "times": -1}, {"id": 7004, "value": 0.6, "round": 3, "round_add": "", "times": -1}, {"id": 7005, "value": 0.7, "round": 3, "round_add": "", "times": -1}, {"id": 7006, "value": 0.8, "round": 3, "round_add": "", "times": -1}, {"id": 8001, "value": 0.01, "round": 3, "round_add": "", "times": -1}, {"id": 8002, "value": 0.015, "round": 3, "round_add": "", "times": -1}, {"id": 8003, "value": 0.02, "round": 3, "round_add": "", "times": -1}, {"id": 8004, "value": 0.025, "round": 3, "round_add": "", "times": -1}, {"id": 8005, "value": 0.03, "round": 3, "round_add": "", "times": -1}, {"id": 8006, "value": 0.035, "round": 3, "round_add": "", "times": -1}, {"id": 8101, "value": 0.02, "round": 2, "round_add": "", "times": -1}, {"id": 8102, "value": 0.03, "round": 2, "round_add": "", "times": -1}, {"id": 8103, "value": 0.04, "round": 2, "round_add": "", "times": -1}, {"id": 8104, "value": 0.05, "round": 2, "round_add": "", "times": -1}, {"id": 8105, "value": 0.06, "round": 2, "round_add": "", "times": -1}, {"id": 8106, "value": 0.07, "round": 2, "round_add": "", "times": -1}, {"id": 9001, "value": 0, "round": 1, "round_add": "", "times": -1}, {"id": 10001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 11001, "value": 0, "round": -1, "round_add": "", "times": 1}, {"id": 11002, "value": 0, "round": -1, "round_add": "", "times": 2}, {"id": 11003, "value": 0, "round": -1, "round_add": "", "times": 3}, {"id": 11004, "value": 0, "round": -1, "round_add": "", "times": 4}, {"id": 11001001, "value": 0, "round": -1, "round_add": "", "times": 1}, {"id": 11001002, "value": 0, "round": -1, "round_add": "", "times": 2}, {"id": 11001003, "value": 0, "round": -1, "round_add": "", "times": 3}, {"id": 11001004, "value": 0, "round": -1, "round_add": "", "times": 4}, {"id": 11102001, "value": 0, "round": -1, "round_add": "", "times": 1}, {"id": 11102002, "value": 0, "round": -1, "round_add": "", "times": 2}, {"id": 11102003, "value": 0, "round": -1, "round_add": "", "times": 3}, {"id": 11102004, "value": 0, "round": -1, "round_add": "", "times": 4}, {"id": 12001, "value": 2, "round": 3, "round_add": "", "times": -1}, {"id": 12002, "value": 3, "round": 3, "round_add": "", "times": -1}, {"id": 12003, "value": 4, "round": 3, "round_add": "", "times": -1}, {"id": 12004, "value": 5, "round": 3, "round_add": "", "times": -1}, {"id": 12005, "value": 7, "round": 3, "round_add": "", "times": -1}, {"id": 12006, "value": 9, "round": 3, "round_add": "", "times": -1}, {"id": 12001001, "value": 2, "round": 3, "round_add": "", "times": -1}, {"id": 12001002, "value": 3, "round": 3, "round_add": "", "times": -1}, {"id": 12001003, "value": 4, "round": 3, "round_add": "", "times": -1}, {"id": 12001004, "value": 5, "round": 3, "round_add": "", "times": -1}, {"id": 12001005, "value": 7, "round": 3, "round_add": "", "times": -1}, {"id": 12001006, "value": 9, "round": 3, "round_add": "", "times": -1}, {"id": 13001, "value": 0.5, "round": -1, "round_add": "", "times": -1}, {"id": 14001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 15001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 16001, "value": 0, "round": -1, "round_add": "", "times": 1}, {"id": 17001, "value": 0.15, "round": 4, "round_add": "", "times": -1}, {"id": 18001, "value": 0, "round": 2, "round_add": "", "times": -1}, {"id": 19001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 20001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 21001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 22001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 23001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 24001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 25001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 26001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 26001001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 26102001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 27001, "value": 0, "round": -1, "round_add": "", "times": 1}, {"id": 28001, "value": 0.2, "round": -1, "round_add": "", "times": -1}, {"id": 29001, "value": 0.2, "round": -1, "round_add": "", "times": -1}, {"id": 30001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 31001, "value": 0, "round": 10, "round_add": "", "times": -1}, {"id": 32001, "value": 0.5, "round": -1, "round_add": "", "times": -1}, {"id": 33001, "value": 0.05, "round": -1, "round_add": "", "times": -1}, {"id": 33002, "value": 0.1, "round": -1, "round_add": "", "times": -1}, {"id": 33003, "value": 0.15, "round": -1, "round_add": "", "times": -1}, {"id": 33004, "value": 0.2, "round": -1, "round_add": "", "times": -1}, {"id": 33005, "value": 0.25, "round": -1, "round_add": "", "times": -1}, {"id": 33006, "value": 0.3, "round": -1, "round_add": "", "times": -1}, {"id": 34001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 35001, "value": 0.03, "round": 5, "round_add": "", "times": -1}, {"id": 36001, "value": 0, "round": -1, "round_add": "", "times": 1}, {"id": 37001, "value": 35, "round": 1, "round_add": "", "times": -1}, {"id": 37002, "value": 35, "round": 2, "round_add": "", "times": -1}, {"id": 37003, "value": 35, "round": 3, "round_add": "", "times": -1}, {"id": 38001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 39001, "value": 0, "round": 1, "round_add": "", "times": -1}, {"id": 40001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 41001, "value": 30, "round": 3, "round_add": "", "times": -1}, {"id": 41002, "value": 40, "round": 3, "round_add": "", "times": -1}, {"id": 41003, "value": 50, "round": 3, "round_add": "", "times": -1}, {"id": 41004, "value": 60, "round": 3, "round_add": "", "times": -1}, {"id": 41005, "value": 70, "round": 3, "round_add": "", "times": -1}, {"id": 41006, "value": 80, "round": 3, "round_add": "", "times": -1}, {"id": 42001, "value": 0, "round": 5, "round_add": "", "times": -1}, {"id": 43001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 44001, "value": 0, "round": -1, "round_add": "", "times": 1}, {"id": 45001, "value": 0, "round": 10, "round_add": "", "times": 1}, {"id": 46001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 47001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 48001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 49001, "value": 0.1, "round": -1, "round_add": "", "times": -1}, {"id": 50001, "value": 0, "round": -1, "round_add": "", "times": 3}, {"id": 51001, "value": 50, "round": 1, "round_add": "", "times": -1}, {"id": 52001, "value": 0, "round": 2, "round_add": "", "times": -1}, {"id": 53001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 54001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 55001, "value": 0, "round": 1, "round_add": "", "times": -1}, {"id": 56001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 57001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 58001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 59001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 60001, "value": 0, "round": -1, "round_add": "", "times": 1}, {"id": 61001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 62001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 63001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 64001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 65001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 66001, "value": 0.3, "round": -1, "round_add": "", "times": -1}, {"id": 67001, "value": 100, "round": -1, "round_add": "", "times": -1}, {"id": 68001, "value": 0, "round": -1, "round_add": "", "times": 2}, {"id": 69001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 70001, "value": 0, "round": 1, "round_add": "", "times": -1}, {"id": 71001, "value": 0, "round": -1, "round_add": "", "times": 1}, {"id": 72001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 73001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 74001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 75001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 76001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 77001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 78001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 79001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 80001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 81001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 81002, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 81003, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 81004, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 81005, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 81006, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 81007, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 81008, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 81009, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 81010, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 81011, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 82001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 83001, "value": 0, "round": 5, "round_add": "", "times": -1}, {"id": 84001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 85001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 86001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 87001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 88001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 89001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 90001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 91001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 92001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 93001, "value": 0, "round": -1, "round_add": "", "times": 1}, {"id": 94001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 95001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 96001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 97001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 98001, "value": 0, "round": 2, "round_add": 1, "times": -1}, {"id": 99001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 100001, "value": 2, "round": 3, "round_add": "", "times": -1}, {"id": 101001, "value": 0, "round": 5, "round_add": "", "times": -1}, {"id": 102001, "value": 50, "round": -1, "round_add": "", "times": -1}, {"id": 103001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 104001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 105001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 106001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 107001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 108001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 109001, "value": 0, "round": -1, "round_add": "", "times": 2}, {"id": 110001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 111001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 112001, "value": 0, "round": 1, "round_add": "", "times": -1}, {"id": 113001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 114001, "value": 20, "round": -1, "round_add": "", "times": -1}, {"id": 115001, "value": 0, "round": 1, "round_add": "", "times": -1}, {"id": 116001, "value": 0, "round": 3, "round_add": "", "times": -1}, {"id": 1001001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 1003001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 1026001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 1027001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 1028001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 1029001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 1030001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 1042001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 1043001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 1044001, "value": 0, "round": -1, "round_add": "", "times": -1}, {"id": 2001001, "value": 5, "round": -1, "round_add": "", "times": -1}]