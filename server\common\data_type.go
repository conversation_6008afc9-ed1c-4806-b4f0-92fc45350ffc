package slg

// 游戏结束信息
type GameOverInfo struct {
	Winners     [][]string            `bson:"winners"`          // 胜利的玩家列表 [[uid, nickname]] 已弃用
	WinnerList  []*GameOverWinnerInfo `bson:"winner_list"`      // 获胜者列表
	WinAlliInfo *GameOverWinAlliInfo  `bson:"winner_alli_info"` // 获胜联盟信息

	AncientInfo  []int32 `bson:"ancient_info"` // 遗迹信息[id,lv]
	WinUID       string  `bson:"win_uid"`
	WinName      string  `bson:"win_name"`
	EndTime      int64   `bson:"end_time"`       // 结束时间
	WinType      int32   `bson:"win_type"`       // 1.玩家 2.联盟
	WinCellCount int32   `bson:"win_cell_count"` // 胜利时的地块数量
}

// 游戏结束获胜者信息
type GameOverWinnerInfo struct {
	UID         string `bson:"uid"`
	Name        string `bson:"name"`         // 名字
	Headicon    string `bson:"headicon"`     // 头像
	OccupyCount int32  `bson:"occupy_count"` // 攻陷次数
	Score       int32  `bson:"score"`        // 积分
	Job         int32  `bson:"job"`          // 职位
	KillPawn    int32  `bson:"kill_pawn"`    // 击杀士兵数量
	DeadPawn    int32  `bson:"dead_pawn"`    // 阵亡士兵数量
}

// 游戏结束获胜联盟信息
type GameOverWinAlliInfo struct {
	UID      string `bson:"uid"`
	Name     string `bson:"name"`     // 名字
	Headicon int32  `bson:"headicon"` // 头像
}
