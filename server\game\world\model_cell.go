package world

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	"slgsrv/server/game/player"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

func (this *Model) ToCellsPb() map[int32]*pb.CellInfo {
	this.Cells.RLock()
	defer this.Cells.RUnlock()
	ret := map[int32]*pb.CellInfo{}
	for index, data := range this.Cells.Map {
		ret[pb.Int32(index)] = data.ToPb()
	}
	return ret
}

func (this *Model) ToCellPb(cell *Cell) *pb.CellInfo {
	msg := cell.ToPb()
	if cell.CityId != constant.MAIN_CITY_ID {
	} else if plr := this.GetTempPlayer(cell.Owner); plr != nil { // 主城携带玩家信息
		msg.Player = this.ToTempPlayerPb(plr, this.GetAlliNameMap()[plr.AllianceUid])
	}
	return msg
}

// 获取城市皮肤列表
func (this *Model) ToCitySkinsPb() map[int32]int32 {
	data := map[int32]int32{}
	this.CitySkinMap.ForEach(func(v, k int32) bool {
		data[k] = v
		return true
	})
	return data
}

// 设置城市皮肤
func (this *Model) UpdateCitySkin(index, id int32) {
	if this.CitySkinMap.Get(index) == id {
		return
	} else if id == 0 {
		this.CitySkinMap.Del(index)
	} else {
		this.CitySkinMap.Set(index, id)
	}
	// 通知
	this.PutNotifyQueue(constant.NQ_CHANGE_CITY_SKIN, &pb.OnUpdateWorldInfoNotify{
		Data_75: &pb.CitySkinInfo{
			Index: index,
			Id:    id,
		},
	})
}

// 转换成实际的位置
func (this *Model) AmendIndex(index int32) int32 {
	cell := this.GetCell(index)
	if cell != nil && cell.DependIndex >= 0 {
		return cell.DependIndex
	}
	return index
}

func (this *Model) GetCell(index int32) *Cell {
	this.Cells.RLock()
	defer this.Cells.RUnlock()
	return this.Cells.Map[index]
}

func (this *Model) GetCellNotNULL(index int32) *Cell {
	cell := this.GetCell(index)
	if cell == nil {
		cell = NewCell(index, "", 0)
	}
	return cell
}

// 添加单元格 记住在外部要加锁
func (this *Model) AddCell(cell *Cell) {
	index := cell.index
	this.Cells.Lock()
	this.Cells.Map[index] = cell
	this.Cells.Unlock()
	this.AddPlayerOwnCell(cell.Owner, index)
	this.PutNotifyQueue(constant.NQ_ADD_CELL, &pb.OnUpdateWorldInfoNotify{Data_3: this.ToCellPb(cell)}) // 通知
	this.TagUpdateDBByIndex(index)
	this.WorldPbAddCell(cell, false)
}

// 删除地块
func (this *Model) RemoveCells(indexs []int32) {
	idxs := []int32{}
	removeCells := []*Cell{}
	this.Cells.Lock()
	for _, index := range indexs {
		if cell := this.Cells.Map[index]; cell != nil {
			delete(this.Cells.Map, index)
			idxs = append(idxs, index)
			this.TagUpdateDBByIndex(index)
			removeCells = append(removeCells, cell)
		}
	}
	this.Cells.Unlock()
	// 删除pb数据
	for _, cell := range removeCells {
		this.WorldPbRemoveCell(cell.Owner, cell.index, cell.IsAncient())
	}
	// 通知
	this.PutNotifyQueue(constant.NQ_REMOVE_CELLS, &pb.OnUpdateWorldInfoNotify{Data_4: array.Clone(indexs)}) // 通知
}

// 刷新地块依赖
func (this *Model) UpdateCellDepend(index int32) {
	cell := this.GetCell(index)
	if cell == nil || cell.GetAcreage() <= 1 {
		return // 面积大于1才设置
	}
	indexs := cell.GetOwnIndexs(this.room.GetMapSize())
	for _, idx := range indexs {
		if idx == index {
			continue
		} else if c := this.GetCell(idx); c != nil {
			c.DependIndex = index
		}
	}
}

// 获取产量
func (this *Model) GetPlayerCellOutput(uid string) (cereal, timber, stone float64) {
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		return
	}
	indexs := plr.OwnCells
	for _, index := range indexs {
		cell := this.GetCell(index)
		if cell == nil || !cell.IsHasRes() || cell.DependIndex >= 0 {
			continue
		}
		landId := this.Lands[index]
		var json map[string]interface{} = config.GetJsonData("land", landId)
		if cell.CityId > 0 && json != nil { // 从城市资源获取
			id := ut.Int32(json["type"])*100 + ut.Int32(json["lv"])
			json = config.GetJsonData("cityResAttr", cell.CityId*1000+id)
		} else if cell.CityId != 0 {
			continue
		}
		if json != nil {
			cereal += ut.Float64(json["cereal"])
			timber += ut.Float64(json["timber"])
			stone += ut.Float64(json["stone"])
		}
	}
	// 加上基础的
	if len(indexs) > 0 {
		cereal += constant.INIT_RES_OUTPUT
		timber += constant.INIT_RES_OUTPUT
		stone += constant.INIT_RES_OUTPUT
	}
	return
}

// 创建地块 主城
func (this *Model) CreateCellByMainCity(index int32, plr *player.Model, lang string, skinId int32) {
	data := plr.ToDB(this.GetPlayerDataToDB(plr.Uid))
	size := this.room.GetMapSize()
	area := this.GetArea(index)
	// 开始添加主城其他三个地块
	this.AddCellAndUpdateAreaDepend(index+1, plr.Uid, -constant.MAIN_CITY_ID)        // 右
	this.AddCellAndUpdateAreaDepend(index+size.X, plr.Uid, -constant.MAIN_CITY_ID)   // 上
	this.AddCellAndUpdateAreaDepend(index+size.Y+1, plr.Uid, -constant.MAIN_CITY_ID) // 右上
	// 第一个位置设置为 主城
	this.AddCell(NewCell(index, plr.Uid, constant.MAIN_CITY_ID))
	// 刷新依赖
	this.UpdateCellDepend(index)
	// 刷新战场信息
	this.InitAreaMainCity(area, data, lang)
	// 检测是否有皮肤
	this.UpdateCitySkin(index, skinId)
}

// 添加地块 并刷新区域依赖
func (this *Model) AddCellAndUpdateAreaDepend(index int32, owner string, cityId int32) *Cell {
	cell := NewCell(index, owner, cityId)
	this.AddCell(cell)
	// 刷新区域依赖
	if area := this.GetArea(index); area != nil {
		area.Owner = owner
		area.CityId = cityId
		area.Init(true)
	}
	return cell
}

// 占领地块
func (this *Model) OccupyCell(index int32, uids []string, battlePlayerUids []string) (uid string, needCheckIndexs []int32) {
	// log.Info("OccupyCell", index, uid)
	if len(uids) == 0 {
		return "", []int32{}
	}
	uid = uids[0]
	plr, _ := this.room.GetOnlinePlayerOrDB(uid).(*player.Model)
	if plr == nil { // 不在线从数据库获取
		return "", []int32{}
	}
	// 判断攻占地块的玩家是否沦陷 已沦陷则由队友攻占
	if plr.IsCapture() {
		return this.OccupyCell(index, uids[1:], battlePlayerUids)
	}
	cell := this.GetCellNotNULL(index)
	owner := cell.Owner
	landId := this.Lands[index]
	// 离线通知
	this.OfflineNotify(uid, constant.OFFLINE_MSG_TYPE_OCCUPY, owner, ut.String(landId), ut.String(cell.CityId))
	// 如果是主城
	if cell.CityId == constant.MAIN_CITY_ID {
		needCheckIndexs = array.Clone(this.GetPlayerOwnCells(owner))
		this.occupyMain(cell, plr, battlePlayerUids)
		return
	}
	isAncient := cell.IsAncient()
	// 如果在修建中 直接取消
	if this.CheckCellBTCitying(cell.index) {
		this.RemoveBTCityQueue(index)
	}
	// 如果是古城
	if isAncient {
		indexs := cell.GetOwnIndexs(this.room.GetMapSize())
		needCheckIndexs = append(needCheckIndexs, indexs...)
		// 如果占领的是玩家的 那么删除 并记录攻击次数
		this.RemovePlayerOwnCells(cell.Owner, needCheckIndexs)
		// 一个个占领
		for _, i := range indexs {
			this.AddOccupyCell(this.GetCellNotNULL(i), plr.Uid)
		}
		// 添加每日占领土地数量 只能是野地
		if owner == "" {
			plr.TodayOccupyCellCount += int32(len(indexs))
			// 首次攻占奖励 给当前玩家的所有盟友发放
			taskId := 40000000 + ut.AbsInt32(cell.CityId)
			plr.AddOtherTaskAndComplete(taskId)
			// 盟友
			if alli := this.GetAlliance(plr.AllianceUid); alli != nil {
				uids := alli.GetMemberUids()
				for _, m := range uids {
					if m == plr.Uid {
						continue
					} else if p, _ := this.room.GetOnlinePlayerOrDB(m).(*player.Model); p != nil {
						p.AddOtherTaskAndComplete(taskId)
						if !p.IsOnline() {
							this.room.UpdatePlayerDB(p)
						}
					}
				}
			}
		}
	} else {
		needCheckIndexs = append(needCheckIndexs, cell.index)
		// 如果占领的是玩家的 那么删除 并记录攻击次数
		this.RemovePlayerOwnCells(cell.Owner, needCheckIndexs)
		this.AddOccupyCell(cell, plr.Uid)
		// 添加每日占领土地数量 只能是野地
		if owner == "" {
			plr.TodayOccupyCellCount += 1
		}
	}
	// 检测行军
	// this.CheckAllMarchTargetCanAddArmy()
	// 如果在线
	if plr.IsOnline() {
		plr.UpdateOpSec(true) // 刷新产量
	} else {
		this.room.UpdatePlayerDB(plr) // 如果没在线 就保存到数据库
	}
	if owner == "" {
		// 添加防作弊检测积分
		plr.AddAntiCheatScore(constant.ANTI_CHEAT_OCCUPY_WILD_SCORE)
	}
	// 更新最大地块数
	this.NotifyLobbyMaxLandCount(plr)
	// 通知添加战令积分
	this.room.InvokeLobbyRpcNR(plr.GetLid(), slg.RPC_ADD_BATTLE_PASS_SCORE, plr.Uid, slg.BATTLE_PASS_BUY_SCORE_OCCUPY)
	return
}

// 占领主城
func (this *Model) occupyMain(cell *Cell, plr *player.Model, battlePlayerUids []string) {
	// 被占领的玩家
	oPlr, _ := this.room.GetOnlinePlayerOrDB(cell.Owner).(*player.Model)
	if oPlr == nil {
		return
	}
	oPlr.CaptureMutex.Lock()
	defer oPlr.CaptureMutex.Unlock()
	// 删除被占领玩家的所有士兵和行军
	oPlr.State = constant.PS_CAPTURE
	this.SetPlayerCapture(oPlr.Uid, true)
	this.RemovePlayerAllArmy(oPlr.Uid, false)
	this.RemovePlayerAllMarch(oPlr.Uid)
	this.RemovePlayerAllTransit(oPlr.Uid)
	this.RemoveDrillPawn(cell.index)         // 删除这个区域的训练士兵
	this.RemovePawnLvingArea(cell.index)     // 删除这个区域的士兵练级
	this.RemovePawnCuringArea(cell.index)    // 删除这个区域治疗的士兵
	this.RemovePlayerAllCellTonden(oPlr.Uid) // 删除屯田
	this.RepatriateTransitByTargetIndex(cell.index, cell.Owner)
	// 主城的4个地块
	indexs := cell.GetOwnIndexs(this.room.GetMapSize())
	var ancientAreas []*Area
	// 被占领玩家的所有地块
	ownCells := this.GetPlayerOwnCells(oPlr.Uid)
	for _, index := range ownCells {
		c := this.GetCellNotNULL(index)
		// 如果在修建中 直接删除
		if this.CheckCellBTCitying(index) {
			this.RemoveBTCityQueue(index)
		}
		// 一个个占领
		this.AddOccupyCell(c, plr.Uid)
		if ancient := this.AncientCityMap.Get(index); ancient != nil {
			if ancientAreas == nil {
				ancientAreas = []*Area{}
			}
			ancientAreas = append(ancientAreas, this.GetArea(index))
		}
	}

	// 检测攻占者政策
	if robPercent := this.GetPlayerPolicyEffectFloatByUid(plr.Uid, effect.OCCUPY_ROBBERY); robPercent > 0 {
		// 掠夺被攻占者资源
		robParam := robPercent * 0.01
		items := []*g.TypeObj{}
		if val := ut.Round(float64(oPlr.Cereal.Value) * robParam); val > 0 {
			items = append(items, &g.TypeObj{Type: ctype.CEREAL, Count: int32(val)})
		}
		if val := ut.Round(float64(oPlr.Timber.Value) * robParam); val > 0 {
			items = append(items, &g.TypeObj{Type: ctype.TIMBER, Count: int32(val)})
		}
		if val := ut.Round(float64(oPlr.Stone.Value) * robParam); val > 0 {
			items = append(items, &g.TypeObj{Type: ctype.STONE, Count: int32(val)})
		}
		if val := ut.Round(float64(oPlr.ExpBook) * robParam); val > 0 {
			items = append(items, &g.TypeObj{Type: ctype.EXP_BOOK, Count: int32(val)})
		}
		if val := ut.Round(float64(oPlr.Iron) * robParam); val > 0 {
			items = append(items, &g.TypeObj{Type: ctype.IRON, Count: int32(val)})
		}
		if val := ut.Round(float64(oPlr.UpScroll) * robParam); val > 0 {
			items = append(items, &g.TypeObj{Type: ctype.UP_SCROLL, Count: int32(val)})
		}
		if val := ut.Round(float64(oPlr.Fixator) * robParam); val > 0 {
			items = append(items, &g.TypeObj{Type: ctype.FIXATOR, Count: int32(val)})
		}
		// 发送邮件
		this.room.SendMailItemOne(slg.MAIL_OCCUPY_ROBBERY_ID, "policyText.name_1032", oPlr.Nickname, "-1", plr.Uid, items)
	}

	// 检测行军
	// this.CheckAllMarchTargetCanAddArmy()

	// 通知玩家被攻陷
	this.PutNotifyQueue(constant.NQ_CAPTURE, &pb.OnUpdateWorldInfoNotify{
		Data_29: &pb.CaptureInfo{
			Uid:      oPlr.Uid,
			Attacker: plr.Uid,
			Time:     int64(time.Now().UnixMilli()),
		},
	})
	// 迁移多出来的军队 因为占领主城之后地方会变小
	this.MigrationArmyByOccupy(indexs)
	// 是否有联盟
	if alli := this.GetAlliance(oPlr.AllianceUid); alli != nil {
		if alli.Creater == oPlr.Uid {
			// 血战到底则出局结算
			this.ConquerAlliSettle(alli, true, false)
			this.RemoveAlliance(alli, 100010) // 如果是盟主 删除联盟
			this.CheckGameOverByConquer()     // 检测血战到底是否结束
		} else {
			this.UpdateAllianceMemberEmbassyLv(alli, oPlr.Uid, 0) // 刷新大使馆等级
		}
	}
	// 删除联盟申请
	this.CleanPlayerAlliApply(oPlr.Uid, true)
	// 标记沦陷
	oPlr.Capture(plr.Uid)
	this.CleanTempPlayerInfo(oPlr.Uid)
	// 记录攻陷
	this.KillPlayerMain(plr.Uid, battlePlayerUids)
	this.AddCaptureNum(1)
	// 发送邮件
	this.room.SendMailOne(100003, "", plr.Nickname, "-1", oPlr.Uid)
	// 删除在市场里面卖的东西
	this.room.GetBazaar().CleanPlayerBazaarRes(oPlr.Uid)
	// 是否在线
	if plr.IsOnline() {
		plr.UpdateOpSec(true)                  // 刷新产量
		this.NotifyPlayerArmyDistInfo(plr.Uid) // 通知军队分布信息
	} else {
		this.room.UpdatePlayerDB(plr) // 如果没在线 就保存到数据库
	}
	// 是否在线
	if oPlr.IsOnline() {
		oPlr.UpdateOpSec(true)
		this.NotifyPlayerArmyDistInfo(oPlr.Uid) // 通知军队分布信息
	} else {
		this.room.UpdatePlayerDB(oPlr) // 如果没在线 就保存到数据库
	}
	// 重置古城信息
	for _, ancientArea := range ancientAreas {
		this.AncientOccupyReset(ancientArea, oPlr.Uid)
	}
	// 删除进攻方的主城免战
	this.RemoveAvoidWarArea(plr.MainCityIndex)
	// 清空皮肤
	this.UpdateCitySkin(cell.index, 0)
	// 更新最大地块数
	this.NotifyLobbyMaxLandCount(plr)
}

// 直接占领领地
func (this *Model) DirectOccupyCell(index int32, uid string, isAddScore bool) {
	if index <= 0 {
		return
	}
	index = this.AmendIndex(index)
	if area := this.GetArea(index); area == nil || area.Owner == uid {
		return
	} else {
		oldOwner := area.Owner
		uid, needCheckIndexs := this.OccupyCell(index, []string{uid}, []string{})
		if len(needCheckIndexs) > 0 {
			this.CheckAreaArmyNotBelongHereByIndexs(needCheckIndexs)
			this.CheckGameOverByLandCount(uid)
			if area.IsAncient() {
				this.AncientOccupyReset(area, oldOwner)
			}
		}
		// 添加积分
		if isAddScore {
			landLv := this.GetLandLv(index)
			index := this.GetPlayerMainIndex(uid)
			level := config.GetLandAttrLvByDis(this.GetToMapCellDis(index, this.GetPlayerMainIndex(uid)), landLv)
			if json := config.GetJsonData("landAttr", landLv*1000+level); json != nil {
				this.PlayerAddLandScore(uid, landLv, ut.Int32(json["uiLv"]))
			}
		}
	}
}

func (this *Model) AddOccupyCell(cell *Cell, owner string) {
	preOwner := cell.Owner
	if !cell.IsAncient() {
		// 重置城市 古城不重置
		cell.SetCity(0)
		cell.DependIndex = -1 // 取消关联 因为不是城市那么就没有关联
	}
	// 设置地块信息
	cell.Owner = owner
	// 刷新战场信息
	this.UpdateAreaOwnerInfo(cell.index, owner, cell.CityId)
	// worldPb从原owner中移除cell
	this.WorldPbRemoveCell(preOwner, cell.index, cell.IsAncient())
	// 添加地块
	this.AddCell(cell)
}

// 调整士兵位置 直接移动 不触发战斗
func (this *Model) MigrationArmyByOccupy(indexs []int32) {
	area := this.GetArea(indexs[0]) // 默认第一个是主城
	if area == nil || len(area.Armys.List) == 0 {
		return
	}
	armys := []*AreaArmy{}
	for i := int32(len(area.Armys.List)) - 1; i >= 0; i-- {
		army := area.Armys.List[i]
		if this.GetAreaFullArmyCount(area, army.Owner) > 0 {
			armys = append(armys, army)
			area.RemoveArmyByIndex(army.Uid, i)
			continue
		}
	}
	mainIndex := area.index
	size := this.room.GetMapSize()
	// 把多出来的军队安排到其他区域
	if len(armys) > 0 {
		for i, l := 1, len(indexs); i < l; i++ {
			area = this.GetArea(indexs[i])
			if area == nil {
				continue
			}
			for ii := len(armys) - 1; ii >= 0; ii-- {
				if army := armys[ii]; !this.IsAreaFullArmy(area, army.Owner) {
					armys = append(armys[:ii], armys[ii+1:]...)
					this.AddAreaArmyNoBattle(area, army, helper.GetDirByIndex(mainIndex, area.index, size))
				} else {
					break
				}
			}
			if len(armys) == 0 {
				break
			}
		}
	}
}

// 遣返以这个为目标的商队
func (this *Model) RepatriateTransitByTargetIndex(index int32, uid string) {
	now := time.Now().UnixMilli()
	this.Transits.RLock()
	defer this.Transits.RUnlock()
	for _, m := range this.Transits.List {
		if m.TargetIndex == index && m.Owner != uid {
			m.Type = constant.BAZAAR_RECORD_TYPE_NONE
			m.BackGoodsType = m.GoodsType
			m.BackGoodsCount = m.GoodsCount
			this.Transits.RbTree.RemoveElement(int(m.StartTime+int64(m.NeedTime)), m)
			this.TransitBack(m, now-ut.MaxInt64(m.StartTime+int64(m.NeedTime)-now, 0))
		}
	}
}

// WorldPb添加单元格
func (this *Model) WorldPbAddCell(cell *Cell, init bool) {
	if cell.Owner != "" || cell.CityId != 0 {
		// 添加数据到playerCellMap 玩家的数据添加到对应key=owner 野地上的城市数据添加到key=""中
		this.playerCellMap.Lock()
		playerCellInfo := this.playerCellMap.Map[cell.Owner]
		if playerCellInfo == nil {
			playerCellInfo = []int32{}
			this.playerCellMap.Map[cell.Owner] = playerCellInfo
		}
		exist := false
		for _, index := range playerCellInfo {
			if index == cell.index {
				exist = true
				break
			}
		}
		if !exist {
			this.playerCellMap.Map[cell.Owner] = append(this.playerCellMap.Map[cell.Owner], cell.index)
			this.playerCellMap.Unlock()
			if !init {
				this.PlayerCellSplit(cell.Owner)
			}
		} else {
			this.playerCellMap.Unlock()
		}
	}
}

// WorldPb移除单元格
func (this *Model) WorldPbRemoveCell(owner string, index int32, isAcient bool) {
	if owner == "" && !isAcient {
		return
	}
	this.playerCellMap.Lock()
	playerCellInfo := this.playerCellMap.Map[owner]
	if playerCellInfo != nil {
		cIndex := -1
		for i, cellIndex := range playerCellInfo {
			if cellIndex == index {
				cIndex = i
			}
		}
		if cIndex >= 0 {
			playerCellInfo = append(playerCellInfo[0:cIndex], playerCellInfo[cIndex+1:]...)
			if len(playerCellInfo) == 0 {
				delete(this.playerCellMap.Map, owner)
				this.playerCellMap.Unlock()
				this.DelWorldPbCellByUserId(owner)
			} else {
				this.playerCellMap.Map[owner] = playerCellInfo
				this.playerCellMap.Unlock()
				this.PlayerCellSplit(owner)
			}
		} else {
			this.playerCellMap.Unlock()
		}
	} else {
		this.playerCellMap.Unlock()
	}
}

// WorldPb单元格城市id变更
func (this *Model) WorldPbChangeCellCityId(owner string, index int32, cityByteId byte) {
	playerCellInfo := this.GetWorldPbCellsByUserId(owner)
	if playerCellInfo != nil {
		this.PlayerCellCityToBytes(owner)
	}
}

// 玩家地块矩形划分
func (this *Model) PlayerCellSplit(uid string) {
	this.playerCellMap.RLock()
	cells, ok := this.playerCellMap.Map[uid]
	this.playerCellMap.RUnlock()
	if !ok {
		return
	} else if len(cells) == 0 {
		this.DelWorldPbCellByUserId(uid)
		return
	}
	arr := []uint32{}            // 矩形地块数组
	singleArr := []uint32{}      // 单地块数组
	cityArr := []uint32{}        // 城市id数组
	splitMap := map[int32]bool{} // 已分配map
	this.Cells.RLock()
	for _, v := range cells {
		if c, ok := this.Cells.Map[v]; ok {
			if c.CityId > 0 {
				// 找出城市
				cityArr = append(cityArr, uint32(v))
				cityArr = append(cityArr, uint32(c.CityByteId))
			}
		}
		// 划分矩形
		if _, ok := splitMap[v]; ok {
			// 已分配到矩形则跳过
			continue
		}
		splitMap[v] = true
		start := v
		end := v
		// 向左查找
		start = this.PlayerCellSplitFindX(uid, v, -1, splitMap)
		// 向右查找
		end = this.PlayerCellSplitFindX(uid, v, 1, splitMap)
		length := end - start
		left := start
		// 向下查找
		start = this.PlayerCellSplitFindY(uid, left, -1, length, start, splitMap)
		// 向上查找
		end = this.PlayerCellSplitFindY(uid, left, 1, length, end, splitMap)
		if start == end {
			singleArr = append(singleArr, uint32(start))
		} else {
			arr = append(arr, uint32(start))
			arr = append(arr, uint32(end))
		}
	}
	this.Cells.RUnlock()
	matrixBytes := ut.IntCompressToBytes(arr, constant.PLAYER_CELL_DATA_BIT_COUNT, constant.PLAYER_CELL_DATA_BIT_COUNT)
	singleBytes := ut.IntCompressToBytes(singleArr, constant.PLAYER_CELL_DATA_BIT_COUNT)
	cityBytes := ut.IntCompressToBytes(cityArr, constant.PLAYER_CELL_DATA_BIT_COUNT, constant.PLAYER_CELL_CITY_BIT_COUNT)
	playerCellInfo := this.GetWorldPbCellsByUserId(uid)
	if playerCellInfo == nil {
		playerCellInfo = &pb.PlayerCellBytesInfo{}
		this.SetWorldPbCell(uid, playerCellInfo)
	}
	playerCellInfo.Indexs1 = matrixBytes
	playerCellInfo.Indexs2 = singleBytes
	playerCellInfo.Cities = cityBytes
}

// 玩家地块城市id转为字节数组
func (this *Model) PlayerCellCityToBytes(uid string) {
	this.playerCellMap.RLock()
	cells, ok := this.playerCellMap.Map[uid]
	this.playerCellMap.RUnlock()
	if !ok {
		return
	}
	if len(cells) == 0 {
		this.DelWorldPbCellByUserId(uid)
		return
	}
	cityArr := []uint32{} // 城市id数组
	this.Cells.RLock()
	for _, v := range cells {
		if c, ok := this.Cells.Map[v]; ok {
			if c.CityId != 0 {
				// 找出城市
				cityArr = append(cityArr, uint32(v))
				cityArr = append(cityArr, uint32(c.CityByteId))
			}
		}
	}
	this.Cells.RUnlock()
	cityBytes := ut.IntCompressToBytes(cityArr, constant.PLAYER_CELL_DATA_BIT_COUNT, constant.PLAYER_CELL_CITY_BIT_COUNT)
	playerCellInfo := this.GetWorldPbCellsByUserId(uid)
	if playerCellInfo == nil {
		playerCellInfo := &pb.PlayerCellBytesInfo{}
		this.SetWorldPbCell(uid, playerCellInfo)
	}
	playerCellInfo.Cities = cityBytes
}

func (this *Model) PlayerCellSplitFindX(uid string, v, c int32, splitMap map[int32]bool) (ret int32) {
	mapSize := this.room.GetMapSize()
	ret = v
	for i := int32(1); i < mapSize.X; i++ {
		nIndex := v + i*c
		if _, ok := splitMap[nIndex]; ok {
			return
		}
		if c, ok := this.Cells.Map[nIndex]; ok {
			if c.Owner != uid {
				return
			}
			splitMap[nIndex] = true
			ret = nIndex
		} else {
			return
		}
	}
	return
}

func (this *Model) PlayerCellSplitFindY(uid string, v int32, c, length, rst int32, splitMap map[int32]bool) (ret int32) {
	mapSize := this.room.GetMapSize()
	ret = rst
	for i := int32(1); i < mapSize.Y; i++ {
		y := mapSize.Y * i
		for j := int32(0); j <= length; j++ {
			nIndex := v + j + y*c
			if _, ok := splitMap[nIndex]; ok {
				return
			}
			if c, ok := this.Cells.Map[nIndex]; ok {
				if c.Owner != uid {
					return
				}
			} else {
				return
			}
		}
		for j := int32(0); j <= length; j++ {
			nIndex := v + j + y*c
			splitMap[nIndex] = true
		}
		ret += mapSize.Y * c
	}
	return
}

// 刷新玩家城市产出
func (this *Model) UpdatePlayerCityOutput(uid string, count int32, init bool) {
	plr := this.GetTempPlayer(uid)
	if plr == nil || count <= 0 || len(plr.OwnCells) == 0 || plr.IsGiveupGame {
		return
	}
	o1 := this.GetPlayerPolicyEffectInt(plr, effect.FARM_OUTPUT)
	o2 := this.GetPlayerPolicyEffectInt(plr, effect.MILL_OUTPUT)
	o3 := this.GetPlayerPolicyEffectInt(plr, effect.QUARRY_OUTPUT)
	if o1 == 0 && o2 == 0 && o3 == 0 {
		return
	}
	oddsMap := map[int32][]int32{
		constant.FARM_BUILD_ID:   {o1, ctype.EXP_BOOK, ctype.IRON},
		constant.TIMBER_BUILD_ID: {o2, ctype.EXP_BOOK},
		constant.QUARRY_BUILD_ID: {o3, ctype.IRON},
	}
	isNotify := false
	for _, index := range plr.OwnCells {
		cell := this.GetCell(index)
		if cell == nil || cell.CityId == 0 || !cell.IsHasRes() {
			continue
		}
		arr := oddsMap[cell.CityId]
		if len(arr) == 0 {
			continue
		}
		odds, types := arr[0], arr[1:]
		if odds == 0 {
			continue
		}
		outputs := plr.CityOutputMap.Get(index)
		if outputs == nil {
			outputs = []*g.TypeObj{}
		}
		ok := false
		for i := int32(0); i < count; i++ {
			if ut.ChanceInt32(odds) { // 是否有概率获得
				tp := types[ut.Random(0, len(types)-1)]
				if output := array.Find(outputs, func(m *g.TypeObj) bool { return m.Type == tp }); output != nil {
					output.Count += 1
				} else {
					outputs = append(outputs, g.NewTypeObj(tp, 0, 1))
				}
				ok = true
			}
		}
		if ok {
			plr.CityOutputMap.Set(index, outputs)
			isNotify = true
		}
	}
	// 通知
	if !init && isNotify {
		this.PutNotifyQueue(constant.NQ_PLAYER_CITY_OUTPUT, &pb.OnUpdateWorldInfoNotify{Data_31: &pb.PlayerCityOutputInfo{
			Uid:       plr.Uid,
			OutputMap: plr.ToCityOutputPb(),
		}})
	}
}

// 同步大厅服玩家最大地块数
func (this *Model) NotifyLobbyMaxLandCount(plr *player.Model) {
	maxLandCount := this.GetPlayerMaxLandCount(plr.Uid)
	userId := this.WorldEventMap.Get(tctype.WORLD_EVENT_TYPE_10K_LAND_FIRST)
	if maxLandCount >= 10000 && userId == "" {
		userId = plr.Uid
		this.WorldEventMap.Set(tctype.WORLD_EVENT_TYPE_10K_LAND_FIRST, userId)
		// 通知
		this.PutNotifyQueue(constant.NO_WORLD_EVENT, &pb.OnUpdateWorldInfoNotify{Data_89: this.WorldEventMap.Clone()})
	}
	active10kLand := plr.Uid == userId // 是否触发万亩任务
	this.room.InvokeLobbyRpcNR(plr.GetLid(), slg.RPC_MAX_LAND_COUNT_CHANGE, plr.Uid, maxLandCount, plr.SumOnlineTime/ut.TIME_HOUR, active10kLand)
}
