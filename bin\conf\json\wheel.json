[{"id": 1, "sort": 1, "factor": 0, "award": "5,0,5", "mul": 1, "weight": 130, "need_sid": "", "desc": ""}, {"id": 2, "sort": 2, "factor": 5, "award": "", "mul": 1, "weight": 700, "need_sid": 1, "desc": ""}, {"id": 3, "sort": 5, "factor": 0, "award": "23,0,5", "mul": 1, "weight": 130, "need_sid": "", "desc": ""}, {"id": 4, "sort": 7, "factor": 0, "award": "1,0,350|2,0,350|3,0,350|5,0,10", "mul": 1, "weight": 50, "need_sid": "", "desc": ""}, {"id": 5, "sort": 4, "factor": -1, "award": "", "mul": 1, "weight": 220, "need_sid": 1, "desc": ""}, {"id": 6, "sort": 8, "factor": 0, "award": "", "mul": 3, "weight": 80, "need_sid": "", "desc": "ui.next_double_award"}, {"id": 7, "sort": 6, "factor": 10, "award": "", "mul": 1, "weight": 560, "need_sid": 1, "desc": ""}, {"id": 8, "sort": 3, "factor": 0, "award": "1,0,350|2,0,350|3,0,350|23,0,10", "mul": 1, "weight": 50, "need_sid": "", "desc": ""}]