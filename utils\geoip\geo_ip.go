package geoip

import (
	"sync"

	"github.com/huyangv/vmqant/log"
	"github.com/ip2location/ip2location-go"
)

var (
	initLock sync.Once
	ipDb     *ip2location.DB
)

// 初始化数据库
func initGeoIpDb() {
	var err error
	ipDb, err = ip2location.OpenDB("bin/conf/IP2LOCATION-LITE-DB1.BIN")
	if err != nil {
		log.Error("open ip db fail err: %v", err)
	}
}

// 获取IP的地理位置
func GetGeoInfo(ipStr string) (country string) {
	initLock.Do(initGeoIpDb)
	if ipStr == "" {
		return
	}
	record, err := ipDb.Get_all(ipStr)
	if err != nil {
		log.Error("getGeoInfo err: %v")
		return
	}
	country = record.Country_long
	return
}
