package array

import (
	"math/rand"
)

func RemoveByIndex[T any](items []T, index int) []T {
	if index < 0 || index >= len(items) {
		return items
	}
	return append(items[:index], items[index+1:]...)
}

func Remove[T comparable](items []T, item T) []T {
	if len(items) == 0 {
		return items
	}
	for i, l := 0, len(items); i < l; i++ {
		if items[i] == item {
			return RemoveByIndex(items, i)
		}
	}
	return items
}

func CheckRemove[T comparable](items []T, item T) ([]T, bool) {
	if len(items) == 0 {
		return items, false
	}
	for i, l := 0, len(items); i < l; i++ {
		if items[i] == item {
			return RemoveByIndex(items, i), true
		}
	}
	return items, false
}

func RemoveMap[K string | int, V comparable](items []map[K]V, key K, item V) []map[K]V {
	if len(items) == 0 {
		return items
	}
	for i, l := 0, len(items); i < l; i++ {
		if items[i][key] == item {
			return append(items[:i], items[i+1:]...)
		}
	}
	return items
}

func RemoveItem[T any](items []T, cb func(m T) bool) []T {
	if len(items) == 0 {
		return items
	}
	for i, l := 0, len(items); i < l; i++ {
		if cb(items[i]) {
			return append(items[:i], items[i+1:]...)
		}
	}
	return items
}

func Delete[T any](items []T, cb func(m T) bool) []T {
	if len(items) == 0 {
		return items
	}
	for i := len(items) - 1; i >= 0; i-- {
		if cb(items[i]) {
			items = append(items[:i], items[i+1:]...)
		}
	}
	return items
}

func Find[T any](items []T, cb func(m T) bool) T {
	var t T
	if len(items) == 0 {
		return t
	}
	for _, it := range items {
		if cb(it) {
			return it
		}
	}
	return t
}

func FindIndex[T any](items []T, cb func(m T) bool) int {
	if len(items) == 0 {
		return -1
	}
	for i, it := range items {
		if cb(it) {
			return i
		}
	}
	return -1
}

func Some[T any](items []T, cb func(m T) bool) bool {
	if len(items) == 0 {
		return false
	}
	for _, it := range items {
		if cb(it) {
			return true
		}
	}
	return false
}

func Has[T comparable](items []T, item T) bool {
	if len(items) == 0 {
		return false
	}
	for _, it := range items {
		if it == item {
			return true
		}
	}
	return false
}

func Filter[T any](items []T, cb func(m T, i int) bool) []T {
	if len(items) == 0 {
		return items
	}
	arr := []T{}
	for i, it := range items {
		if cb(it, i) {
			arr = append(arr, it)
		}
	}
	return arr
}

func Map[T any, R any](items []T, cb func(m T, i int) R) []R {
	arr := []R{}
	for i, it := range items {
		arr = append(arr, cb(it, i))
	}
	return arr
}

func Reverse[T any](items []T) []T {
	arr := []T{}
	for i := len(items) - 1; i >= 0; i-- {
		arr = append(arr, items[i])
	}
	return arr
}

func Clone[T any](items []T) []T {
	arr := []T{}
	for _, m := range items {
		arr = append(arr, m)
	}
	return arr
}

// 随机单个
func RandomOneItem[T any](items []T) (t T) {
	if len(items) == 0 {
		return
	}
	return items[rand.Intn(len(items))]
}

// 头部追加
func AppendToHead[T any](items []T, item T) []T {
	return append([]T{item}, items...)
}
