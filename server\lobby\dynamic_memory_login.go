package lobby

import (
	"go/constant"
	"go/token"
	"reflect"
)

//go:generate yaegi extract ./

var Symbols = map[string]map[string]reflect.Value{}

func init() {
	Symbols["slgsrv/server/lobby/lobby"] = map[string]reflect.Value{
		// function, constant and variable definitions
		"AddGoldRecord":                  reflect.ValueOf(AddGoldRecord),
		"AddIngotRecord":                 reflect.ValueOf(AddIngotRecord),
		"AddItemRecord":                  reflect.ValueOf(AddItemRecord),
		"AddUserRankScore":               reflect.ValueOf(AddUserRankScore),
		"AddWorkshopItem":                reflect.ValueOf(AddWorkshopItem),
		"ApplyDelUser":                   reflect.ValueOf(ApplyDelUser),
		"BATTLE_PASS_PAY_STATE_BUY":      reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"BATTLE_PASS_PAY_STATE_REFUND":   reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"BATTLE_PASS_PAY_STATE_REWARD":   reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"BLIND_BOX_COND_ID_START":        reflect.ValueOf(&BLIND_BOX_COND_ID_START).Elem(),
		"BLIND_BOX_NORMAL_SKIN_TYPE":     reflect.ValueOf(&BLIND_BOX_NORMAL_SKIN_TYPE).Elem(),
		"BLIND_BOX_RARE_ODDS":            reflect.ValueOf(&BLIND_BOX_RARE_ODDS).Elem(),
		"BLIND_BOX_RARE_SKIN_TYPE":       reflect.ValueOf(&BLIND_BOX_RARE_SKIN_TYPE).Elem(),
		"BlindBoxModel":                  reflect.ValueOf(&BlindBoxModel).Elem(),
		"CheckUpdateUserDB":              reflect.ValueOf(CheckUpdateUserDB),
		"CreateGameRecord":               reflect.ValueOf(CreateGameRecord),
		"CreateTeam":                     reflect.ValueOf(CreateTeam),
		"CreateUser":                     reflect.ValueOf(CreateUser),
		"DelTeam":                        reflect.ValueOf(DelTeam),
		"DelUserAndDB":                   reflect.ValueOf(DelUserAndDB),
		"DelUserCheck":                   reflect.ValueOf(DelUserCheck),
		"DelUserInit":                    reflect.ValueOf(DelUserInit),
		"ExecuteUpdateUserDB":            reflect.ValueOf(ExecuteUpdateUserDB),
		"FriendChatToNotifyPb":           reflect.ValueOf(FriendChatToNotifyPb),
		"GenUid":                         reflect.ValueOf(GenUid),
		"GetCanPlayRookieRoom":           reflect.ValueOf(GetCanPlayRookieRoom),
		"GetCreatePlayerInfo":            reflect.ValueOf(GetCreatePlayerInfo),
		"GetCreatePlayerInfoByTableData": reflect.ValueOf(GetCreatePlayerInfoByTableData),
		"GetFriendChannel":               reflect.ValueOf(GetFriendChannel),
		"GetGameToken":                   reflect.ValueOf(GetGameToken),
		"GetItemRecordList":              reflect.ValueOf(GetItemRecordList),
		"GetOnlineSortedUsers":           reflect.ValueOf(GetOnlineSortedUsers),
		"GetOnlineSum":                   reflect.ValueOf(GetOnlineSum),
		"GetOsAndVer":                    reflect.ValueOf(GetOsAndVer),
		"GetRecordCol":                   reflect.ValueOf(GetRecordCol),
		"GetRoomById":                    reflect.ValueOf(GetRoomById),
		"GetRoomState":                   reflect.ValueOf(GetRoomState),
		"GetRoomRunDay":                  reflect.ValueOf(GetRoomRunDay),
		"GetScoreRanks":                  reflect.ValueOf(GetScoreRanks),
		"GetTranslateTexts":              reflect.ValueOf(GetTranslateTexts),
		"GetUser":                        reflect.ValueOf(GetUser),
		"GetUserByDB":                    reflect.ValueOf(GetUserByDB),
		"GetUserByDBNotAdd":              reflect.ValueOf(GetUserByDBNotAdd),
		"GetUserByNickname":              reflect.ValueOf(GetUserByNickname),
		"GetUserByNicknmaeOrDB":          reflect.ValueOf(GetUserByNicknmaeOrDB),
		"GetUserByOnline":                reflect.ValueOf(GetUserByOnline),
		"GetUserByRpc":                   reflect.ValueOf(GetUserByRpc),
		"HandleSubCheck":                 reflect.ValueOf(HandleSubCheck),
		"InitAchieveTaskByDB":            reflect.ValueOf(InitAchieveTaskByDB),
		"InitApplyInfo":                  reflect.ValueOf(InitApplyInfo),
		"InitBlindBoxModel":              reflect.ValueOf(InitBlindBoxModel),
		"InitGeneralTaskByDB":            reflect.ValueOf(InitGeneralTaskByDB),
		"InitPortrayalsFormDB":           reflect.ValueOf(InitPortrayalsFormDB),
		"InitRecordDB":                   reflect.ValueOf(InitRecordDB),
		"IsCloseApply":                   reflect.ValueOf(IsCloseApply),
		"IsGameOver":                     reflect.ValueOf(IsGameOver),
		"IsRareSkin":                     reflect.ValueOf(IsRareSkin),
		"IsRoomClose":                    reflect.ValueOf(IsRoomClose),
		"LINE_URL":                       reflect.ValueOf(constant.MakeFromLiteral("\"https://api.line.me/v2/profile\"", token.STRING, 0)),
		"LineLoginCheck":                 reflect.ValueOf(LineLoginCheck),
		"LobbyRdsLoadCheck":              reflect.ValueOf(LobbyRdsLoadCheck),
		"Module":                         reflect.ValueOf(&Module).Elem(),
		"NQ_NONE":                        reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"NQ_SYS_MSG":                     reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"NQ_SYS_NOTICE":                  reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"NQ_USER_TRUMPET":                reflect.ValueOf(constant.MakeFromLiteral("3", token.INT, 0)),
		"NewBattlePass":                  reflect.ValueOf(NewBattlePass),
		"NewTableData":                   reflect.ValueOf(NewTableData),
		"NewTeam":                        reflect.ValueOf(NewTeam),
		"NewTeamInviteInfo":              reflect.ValueOf(NewTeamInviteInfo),
		"NewTeamInviteUser":              reflect.ValueOf(NewTeamInviteUser),
		"NewTeamUser":                    reflect.ValueOf(NewTeamUser),
		"NewTitleInfo":                   reflect.ValueOf(NewTitleInfo),
		"NewWheelInfo":                   reflect.ValueOf(NewWheelInfo),
		"PublishUserNotify":              reflect.ValueOf(PublishUserNotify),
		"PutPreferenceData":              reflect.ValueOf(PutPreferenceData),
		"RANK_SEASON":                    reflect.ValueOf(&RANK_SEASON).Elem(),
		"RecordClient":                   reflect.ValueOf(&RecordClient).Elem(),
		"RecordDatabase":                 reflect.ValueOf(&RecordDatabase).Elem(),
		"RunLocalRpcFunc":                reflect.ValueOf(RunLocalRpcFunc),
		"RunTeamDbTick":                  reflect.ValueOf(RunTeamDbTick),
		"RunTick":                        reflect.ValueOf(RunTick),
		"SaveAllUser":                    reflect.ValueOf(SaveAllUser),
		"SaveUserDb":                     reflect.ValueOf(SaveUserDb),
		"SetUserOffline":                 reflect.ValueOf(SetUserOffline),
		"SettleUserRankScore":            reflect.ValueOf(SettleUserRankScore),
		"SettleUserWarToken":             reflect.ValueOf(SettleUserWarToken),
		"StopTick":                       reflect.ValueOf(StopTick),
		"Symbols":                        reflect.ValueOf(&Symbols).Elem(),
		"TranslateApiLoop":               reflect.ValueOf(TranslateApiLoop),
		"UpdatePreferenceData":           reflect.ValueOf(UpdatePreferenceData),
		"UpdateTeamDb":                   reflect.ValueOf(UpdateTeamDb),
		"UserLeaveTrack":                 reflect.ValueOf(UserLeaveTrack),
		"UserOffline":                    reflect.ValueOf(UserOffline),
		"VERSION":                        reflect.ValueOf(constant.MakeFromLiteral("12", token.INT, 0)),
		"WHEEL_EVERYDAY_FREE_COUNT":      reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),
		"WHEEL_EVERYDAY_MAX_COUNT":       reflect.ValueOf(constant.MakeFromLiteral("10", token.INT, 0)),
		"WHEEL_INTERVAL_MAX_TIME":        reflect.ValueOf(constant.MakeFromLiteral("300000", token.INT, 0)),
		"WORKSHOP_ITEM_STATE_AUDIT":      reflect.ValueOf(constant.MakeFromLiteral("0", token.INT, 0)),
		"WORKSHOP_ITEM_STATE_AUDIT_FAIL": reflect.ValueOf(constant.MakeFromLiteral("2", token.INT, 0)),
		"WORKSHOP_ITEM_STATE_AUDIT_OK":   reflect.ValueOf(constant.MakeFromLiteral("1", token.INT, 0)),

		// type definitions
		"AchieveTaskList":      reflect.ValueOf((*AchieveTaskList)(nil)),
		"ApplyFriend":          reflect.ValueOf((*ApplyFriend)(nil)),
		"ApplyInfo":            reflect.ValueOf((*ApplyInfo)(nil)),
		"BanRecord":            reflect.ValueOf((*BanRecord)(nil)),
		"BanRecordDb":          reflect.ValueOf((*BanRecordDb)(nil)),
		"BaseTeamUserInfo":     reflect.ValueOf((*BaseTeamUserInfo)(nil)),
		"BattlePass":           reflect.ValueOf((*BattlePass)(nil)),
		"BattlePassPayRecord":  reflect.ValueOf((*BattlePassPayRecord)(nil)),
		"BlacklistInfo":        reflect.ValueOf((*BlacklistInfo)(nil)),
		"BlindSkinBoxGroup":    reflect.ValueOf((*BlindSkinBoxGroup)(nil)),
		"BlindSkinBoxModel":    reflect.ValueOf((*BlindSkinBoxModel)(nil)),
		"BotanyInfo":           reflect.ValueOf((*BotanyInfo)(nil)),
		"CodeClaim":            reflect.ValueOf((*CodeClaim)(nil)),
		"CodeGift":             reflect.ValueOf((*CodeGift)(nil)),
		"DcCodeInfo":           reflect.ValueOf((*DcCodeInfo)(nil)),
		"DelUserInfo":          reflect.ValueOf((*DelUserInfo)(nil)),
		"Friend":               reflect.ValueOf((*Friend)(nil)),
		"FriendChatDb":         reflect.ValueOf((*FriendChatDb)(nil)),
		"FriendChatInfo":       reflect.ValueOf((*FriendChatInfo)(nil)),
		"FriendChatList":       reflect.ValueOf((*FriendChatList)(nil)),
		"FriendGift":           reflect.ValueOf((*FriendGift)(nil)),
		"FriendGiftList":       reflect.ValueOf((*FriendGiftList)(nil)),
		"FriendGiftRecord":     reflect.ValueOf((*FriendGiftRecord)(nil)),
		"GameRecordDB":         reflect.ValueOf((*GameRecordDB)(nil)),
		"GameRecordData":       reflect.ValueOf((*GameRecordData)(nil)),
		"GeneralTaskList":      reflect.ValueOf((*GeneralTaskList)(nil)),
		"GoldRecord":           reflect.ValueOf((*GoldRecord)(nil)),
		"GoldRecordTableData":  reflect.ValueOf((*GoldRecordTableData)(nil)),
		"GuideInfo":            reflect.ValueOf((*GuideInfo)(nil)),
		"IBaseTeamUserInfo":    reflect.ValueOf((*IBaseTeamUserInfo)(nil)),
		"IngotRecord":          reflect.ValueOf((*IngotRecord)(nil)),
		"InviteFriendInfo":     reflect.ValueOf((*InviteFriendInfo)(nil)),
		"ItemRecord":           reflect.ValueOf((*ItemRecord)(nil)),
		"LineGetProfileRet":    reflect.ValueOf((*LineGetProfileRet)(nil)),
		"Lobby":                reflect.ValueOf((*Lobby)(nil)),
		"Mongodb":              reflect.ValueOf((*Mongodb)(nil)),
		"NotFinishOrderInfo":   reflect.ValueOf((*NotFinishOrderInfo)(nil)),
		"NotifyData":           reflect.ValueOf((*NotifyData)(nil)),
		"OldCodeClaim":         reflect.ValueOf((*OldCodeClaim)(nil)),
		"PlantInfo":            reflect.ValueOf((*PlantInfo)(nil)),
		"PopularityRecord":     reflect.ValueOf((*PopularityRecord)(nil)),
		"PreferenceInfo":       reflect.ValueOf((*PreferenceInfo)(nil)),
		"RoomApplyInfo":        reflect.ValueOf((*RoomApplyInfo)(nil)),
		"ScoreRankManager":     reflect.ValueOf((*ScoreRankManager)(nil)),
		"SkinItem":             reflect.ValueOf((*SkinItem)(nil)),
		"SkinItemTrack":        reflect.ValueOf((*SkinItemTrack)(nil)),
		"TableData":            reflect.ValueOf((*TableData)(nil)),
		"TeamInfo":             reflect.ValueOf((*TeamInfo)(nil)),
		"TeamInviteInfo":       reflect.ValueOf((*TeamInviteInfo)(nil)),
		"TeamInviteUserInfo":   reflect.ValueOf((*TeamInviteUserInfo)(nil)),
		"TeamUserInfo":         reflect.ValueOf((*TeamUserInfo)(nil)),
		"TempFrinedInfo":       reflect.ValueOf((*TempFrinedInfo)(nil)),
		"TempRankInfo":         reflect.ValueOf((*TempRankInfo)(nil)),
		"TitleInfo":            reflect.ValueOf((*TitleInfo)(nil)),
		"TransInfo":            reflect.ValueOf((*TransInfo)(nil)),
		"User":                 reflect.ValueOf((*User)(nil)),
		"UserMap":              reflect.ValueOf((*UserMap)(nil)),
		"UserRankScoreInfo":    reflect.ValueOf((*UserRankScoreInfo)(nil)),
		"UserSubInfo":          reflect.ValueOf((*UserSubInfo)(nil)),
		"WheelDataInfo":        reflect.ValueOf((*WheelDataInfo)(nil)),
		"WheelRandomAwardInfo": reflect.ValueOf((*WheelRandomAwardInfo)(nil)),
		"WheelRecord":          reflect.ValueOf((*WheelRecord)(nil)),
		"WorkshopItem":         reflect.ValueOf((*WorkshopItem)(nil)),

		// interface wrapper definitions
		"_IBaseTeamUserInfo": reflect.ValueOf((*_lobby_IBaseTeamUserInfo)(nil)),
	}
}

// _lobby_IBaseTeamUserInfo is an interface wrapper for IBaseTeamUserInfo type
type _lobby_IBaseTeamUserInfo struct {
	IValue       interface{}
	WGetHeadIcon func() string
	WGetNickname func() string
	WGetUID      func() string
	WSetHeadIcon func(val string)
	WSetNickname func(val string)
}

func (W _lobby_IBaseTeamUserInfo) GetHeadIcon() string {
	return W.WGetHeadIcon()
}

func (W _lobby_IBaseTeamUserInfo) GetNickname() string {
	return W.WGetNickname()
}

func (W _lobby_IBaseTeamUserInfo) GetUID() string {
	return W.WGetUID()
}

func (W _lobby_IBaseTeamUserInfo) SetHeadIcon(val string) {
	W.WSetHeadIcon(val)
}

func (W _lobby_IBaseTeamUserInfo) SetNickname(val string) {
	W.WSetNickname(val)
}
