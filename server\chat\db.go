package chat

import (
	"context"

	slg "slgsrv/server/common"
	"slgsrv/server/lobby"
	mgo "slgsrv/utils/mgodb"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Mongodb struct {
	table string
}

func (this *Mongodb) getCollection() *mongo.Collection {
	return mgo.GetCollection(this.table)
}

// 玩家数据库
var db = &Mongodb{slg.DB_COLLECTION_NAME_USER}

// 根据不同登录方式的openid查询账号
func (this *Mongodb) FindByLoginTypeOpenid(openidField string, openid string) (data lobby.TableData, err string) {
	if openidField == "" {
		err = "FindByLoginTypeOpenid loginType err"
	} else if e := this.getCollection().FindOne(context.TODO(), bson.M{openidField: openid}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 插入单个
func (this *Mongodb) InsertOne(data lobby.TableData) (err string) {
	if _, e := this.getCollection().InsertOne(context.TODO(), data); e != nil {
		err = e.Error()
		log.Error("InsertOne", err)
	}
	return
}

// 是否有相同昵称
func (this *Mongodb) HasNickname(nickname string) bool {
	if _, e := this.getCollection().FindOne(context.TODO(), bson.M{"nickname": nickname}).DecodeBytes(); e != nil {
		return false
	}
	return true
}

// 是否有相同的uid
func (this *Mongodb) HasUid(uid string) bool {
	if _, e := this.getCollection().FindOne(context.TODO(), bson.M{"uid": uid}).DecodeBytes(); e != nil {
		return false
	}
	return true
}

// 更新一条
func (this *Mongodb) UpdateOne(uid string, key string, value interface{}) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{key: value}}); e != nil {
		err = e.Error()
		log.Error("user UpdateOne", err)
	}
	return
}

// 图鉴信息表
var galleryDb = &Mongodb{slg.DB_COLLECTION_NAME_GALLERY}

// 获取所有图鉴信息
func (this *Mongodb) FindAllGalleryInfos() (arr []*GalleryInfo, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.D{})
	if err != nil {
		return []*GalleryInfo{}, err
	} else if err = cur.Err(); err != nil {
		return []*GalleryInfo{}, err
	}
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	arr = []*GalleryInfo{}
	for cur.Next(context.TODO()) {
		var elem GalleryInfo
		if err = cur.Decode(&elem); err == nil {
			arr = append(arr, &elem)
		}
	}
	return
}

// 更新图鉴信息
func (this *Mongodb) UpdateGalleryInfos(datas []*GalleryInfo) error {
	defer func() {
		if err := recover(); err != nil {
			log.Error("UpdateGalleryInfos catch error: %v", err)
		}
	}()
	models := []mongo.WriteModel{}
	for _, m := range datas {
		models = append(models, mongo.NewUpdateOneModel().SetFilter(bson.M{"gid": m.Gid}).SetUpdate(bson.M{"$set": m}).SetUpsert(true))
	}
	if len(models) == 0 {
		return nil
	} else if _, e := this.getCollection().BulkWrite(context.TODO(), models, options.BulkWrite().SetOrdered(false)); e != nil {
		log.Error("UpdateGalleryInfos error.", e.Error())
		return e
	}
	return nil
}

// 删除数据
func (this *Mongodb) DelGallery(gid int32) (err error) {
	_, err = this.getCollection().DeleteOne(context.TODO(), bson.M{"gid": gid})
	return
}

// 图鉴评论表
var galleryCommentDb = &Mongodb{slg.DB_COLLECTION_NAME_GALLERY_COMMENT}

// 获取所有图鉴评论
func (this *Mongodb) FindAllGalleryComments(typeId, id int32) (arr []*GalleryComment, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.M{"type": typeId, "id": id})
	if err != nil {
		return []*GalleryComment{}, err
	} else if err = cur.Err(); err != nil {
		return []*GalleryComment{}, err
	}
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	arr = []*GalleryComment{}
	for cur.Next(context.TODO()) {
		var elem GalleryComment
		if err = cur.Decode(&elem); err == nil {
			arr = append(arr, &elem)
		}
	}
	return
}

// 查询指定玩家该图鉴的的评论
func (this *Mongodb) FindUserGalleryComment(typeId, id int32, userId string) (data GalleryComment, err error) {
	err = this.getCollection().FindOne(context.TODO(), bson.M{"type": typeId, "id": id, "user_id": userId}).Decode(&data)
	return
}

// 更新图鉴评论
func (this *Mongodb) UpdateGalleryComments(datas []*GalleryComment) error {
	defer func() {
		if err := recover(); err != nil {
			log.Error("UpdateGalleryComments catch error: %v", err)
		}
	}()
	models := []mongo.WriteModel{}
	for _, m := range datas {
		models = append(models, mongo.NewUpdateOneModel().SetFilter(bson.M{"uid": m.Uid}).SetUpdate(bson.M{"$set": m}).SetUpsert(true))
	}
	if len(models) == 0 {
		return nil
	} else if _, e := this.getCollection().BulkWrite(context.TODO(), models, options.BulkWrite().SetOrdered(false)); e != nil {
		log.Error("UpdateGalleryComments error.", e.Error())
		return e
	}
	return nil
}

// 好友聊天表
var friendChatDb = &Mongodb{slg.DB_COLLECTION_NAME_FRIEND_CHAT}

// 查询聊天
func (this *Mongodb) FindChat(channel string) (data *FriendChatList, err error) {
	err = this.getCollection().FindOne(context.TODO(), bson.M{"channel": channel}).Decode(&data)
	return
}

// 更新好友聊天列表
func (this *Mongodb) UpdateFriendChats(channel string, data []*FriendChatInfo) error {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"channel": channel}, bson.M{
		"$set": bson.M{
			"list": data,
		},
	}, options.Update().SetUpsert(true)); e != nil {
		log.Error("InsertChats data: %v, err: %v", data, e)
		return e
	}
	return nil
}

// 全局数据
var globalDb = &Mongodb{slg.DB_COLLECTION_NAME_GLOBAL}

func (this *Mongodb) FindGlobal() (data *GlobalData, err error) {
	err = this.getCollection().FindOne(context.TODO(), bson.M{}).Decode(&data)
	return
}

func (this *Mongodb) UpdateGlobal() {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{}, bson.M{"$set": global}, options.Update().SetUpsert(true)); e != nil {
		log.Error("UpdateGlobal", e.Error())
	}
}
