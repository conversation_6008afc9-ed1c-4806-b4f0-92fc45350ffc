package sdk

import (
	slg "slgsrv/server/common"

	"github.com/pemistahl/lingua-go"
	"github.com/siongui/gojianfan"
)

// 语言检测
// func DetectTextLang(text string) (lang string, err error) {
// 	detector := langdetdef.NewWithDefaultLanguages()
// 	lang = detector.GetClosestLanguage(text)
// 	return
// }

// 语言检测
// func DetectTextLang(text string) (lang string, err error) {
// 	info := whatlanggo.Detect(text)
// 	lang = info.Lang.Iso6391()
// 	if la := slg.TRANS_LANG_MAP[lang]; la != "" {
// 		lang = la
// 	}
// 	return
// }

// 语言检测
func DetectTextLang(text string) (lang string, err error) {
	languages := []lingua.Language{
		lingua.English,
		lingua.Chinese,
		lingua.Japanese,
		lingua.Korean,
		lingua.Indonesian,
		lingua.Thai,
		lingua.Vietnamese,
		lingua.Tagalog,
	}

	detector := lingua.NewLanguageDetectorBuilder().
		FromLanguages(languages...).
		Build()

	if language, exists := detector.DetectLanguageOf(text); exists {
		if la := slg.TRANS_LANG_MAP[language.IsoCode639_1().String()]; la != "" {
			lang = la
		} else {
			lang = language.IsoCode639_1().String()
		}
	}
	return
}

func ChineseS2T(text string) string {
	return gojianfan.S2T(text)
}

func ChineseT2S(text string) string {
	return gojianfan.T2S(text)
}
