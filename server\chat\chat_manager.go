package chat

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

const (
	CHANNEL_TYPE_WORLD = "0" // 世界频道
	CHANNEL_TYPE_TEAM  = "1" // 队伍频道
	CHANNEL_TYPE_LANG  = "2" // 语言频道

	CHAT_MAX_COUNT = 50 // 聊天信息最多存储数量（内存）
)

var (
	chatChannelMap = ut.NewMapLock[string, *ChatChanelInfo]() // k=>channel v=>channelInfo
	dbChan         = make(chan string, 10000)
)

// 聊天的玩家信息
type ChatUser struct {
	Uid     string
	Session gate.Session
}

// 聊天频道信息
type ChatChanelInfo struct {
	deadlock.RWMutex
	Channel  string
	ChatList []*pb.LobbyChatInfo
	UserMap  *ut.MapLock[string, *ChatUser]
}

// 广播新消息
func (this *ChatChanelInfo) NotifyChat(chat *pb.LobbyChatInfo) {
	notify := &pb.CHAT_ONLOBBYCHAT_NOTIFY{
		Data: chat,
	}
	byteData, err := pb.ProtoMarshal(notify)
	if err != "" {
		log.Error("NotifyChat err: %v", err)
		return
	}
	this.UserMap.ForEach(func(v *ChatUser, k string) bool {
		v.Session.SendNR(slg.CHAT_LOBBY_CHAT, byteData)
		return true
	})
}

func NewChatChannelInfo(channel string) *ChatChanelInfo {
	channelInfo := &ChatChanelInfo{
		Channel:  channel,
		ChatList: []*pb.LobbyChatInfo{},
		UserMap:  ut.NewMapLock[string, *ChatUser](),
	}
	channelInfo.FromDb()
	return channelInfo
}

// 初始化聊天数据
func InitChatChannel() {
	// 初始化世界聊天频道
	worldChan := NewChatChannelInfo(CHANNEL_TYPE_WORLD)
	chatChannelMap.Set(CHANNEL_TYPE_WORLD, worldChan)
}

// 从redis获取
func (this *ChatChanelInfo) FromDb() {
	rdsData, err := rds.RdsGet(rds.RDS_LOBBY_CHAT_KEY + this.Channel)
	if err != nil {
		return
	} else if rdsData != "" {
		data := []byte(rdsData)
		rdsChatList := &pb.REDIS_LOBBY_CHAT_LIST{}
		err = pb.ProtoUnMarshal(data, rdsChatList)
		if err == nil {
			this.ChatList = rdsChatList.List
		}
	}
}

// 获取聊天信息
func GetChatsOrCreate(channel, uid string, session gate.Session) []*pb.LobbyChatInfo {
	channelInfo := chatChannelMap.Get(channel)
	if channelInfo == nil {
		// 不存在则新建channel
		channelInfo = NewChatChannelInfo(channel)
		chatChannelMap.Set(channel, channelInfo)
	}
	// 每次获取设置该玩家session
	channelInfo.UserMap.Set(uid, &ChatUser{
		Uid:     uid,
		Session: session,
	})
	return channelInfo.ChatList
}

// 玩家离线
func UserOffline(uid, teamUid, lang string) {
	// 退出世界频道
	LeaveChannel(uid, CHANNEL_TYPE_WORLD)
	// 退出队伍频道
	teamChan := CHANNEL_TYPE_TEAM + "_" + teamUid
	LeaveChannel(uid, teamChan)
	// 退出语言频道
	langChan := CHANNEL_TYPE_LANG + "_" + lang
	LeaveChannel(uid, langChan)
}

// 离开频道
func LeaveChannel(uid, channel string) {
	if channelInfo := chatChannelMap.Get(channel); channelInfo != nil {
		channelInfo.UserMap.Del(uid)
	}
}

// 删除频道
func DelChannel(channel string) {
	channelInfo := chatChannelMap.Get(channel)
	if channelInfo == nil {
		return
	}
	channelInfo.Lock()
	channelInfo.ChatList = []*pb.LobbyChatInfo{}
	channelInfo.Unlock()
	chatChannelMap.Del(channel)
	// 从redsi删除
	channelKey := rds.RDS_LOBBY_CHAT_KEY + channel
	rds.RdsDelKeys(channelKey)
}

// 发送聊天
func SendChat(chatInfo *pb.LobbyChatInfo, replyUid string) {
	channel := chatInfo.Channel
	channelInfo := chatChannelMap.Get(channel)
	if channelInfo == nil {
		if chatInfo.Sender == "-1" {
			// 系统消息 不存在则新建channel
			channelInfo = NewChatChannelInfo(channel)
			chatChannelMap.Set(channel, channelInfo)
		} else {
			return
		}
	}
	// 回复消息
	if replyUid != "" {
		channelInfo.RLock()
		if chat := array.Find(channelInfo.ChatList, func(m *pb.LobbyChatInfo) bool { return m.Uid == replyUid }); chat != nil && chat.Content != "" {
			senderNickname := chat.SenderNickname
			chatInfo.ReplyInfo = &pb.ReplyChatInfo{Uid: chat.Uid, Text: senderNickname + ": " + chat.Content}
		}
		channelInfo.RUnlock()
	}
	channelInfo.Lock()
	channelInfo.ChatList = append(channelInfo.ChatList, chatInfo)
	if len(channelInfo.ChatList) > CHAT_MAX_COUNT {
		// 超过内存聊天数量上限
		channelInfo.ChatList = channelInfo.ChatList[1 : CHAT_MAX_COUNT+1]
	}
	channelInfo.Unlock()
	dbChan <- channelInfo.Channel
	// // 保存到redis
	// saveChat(channel, chatInfo)
	channelInfo.NotifyChat(chatInfo)
}

// 保存聊天信息到redsi
func saveChat(channel string, chatInfo *pb.LobbyChatInfo) {
	chatData, _ := pb.ProtoMarshal(chatInfo)
	channelKey := rds.RDS_LOBBY_CHAT_KEY + channel
	rds.RdsEvalHashByCmd(rds.RDS_SCRIPT_CMD_CHAT_STORE, []string{channelKey}, chatData)
}

func UpdateChatDb(close bool) {
	sum := len(dbChan)
	if sum == 0 {
		return
	}
	channelMap := map[string]bool{}
	for len(dbChan) > 0 {
		channel := <-dbChan
		if channelMap[channel] {
			continue
		}
		channelMap[channel] = true
		if len(channelMap) >= 1000 {
			log.Warning("UpdateChatDb len over 1000")
			break
		}
	}
	for channel := range channelMap {
		channelInfo := chatChannelMap.Get(channel)
		if channelInfo != nil {
			channelInfo.RLock()
			rdsData := &pb.REDIS_LOBBY_CHAT_LIST{List: channelInfo.ChatList}
			byteData, err := pb.ProtoMarshal(rdsData)
			channelInfo.RUnlock()
			if err == "" {
				key := rds.RDS_LOBBY_CHAT_KEY + channel
				rds.RdsSet(key, byteData)
			}
		}
	}
	surplus := len(dbChan)
	if close && surplus > 0 {
		UpdateChatDb(close)
	}
}

// 聊天db tick
func (this *Chat) RunTeamDbTick() {
	go func() {
		tiker := time.NewTicker(time.Second * 10)
		for isRunning {
			<-tiker.C
			UpdateChatDb(false)
		}
	}()
}

// 查询大厅聊天
func GetLobbyChatsByChannel(channel string, start, size, startTime, endTime int, filterUid string) []map[string]interface{} {
	arr := []map[string]interface{}{}
	channelInfo := chatChannelMap.Get(channel)
	if channelInfo == nil {
		return arr
	}
	channelInfo.RLock()
	for _, v := range channelInfo.ChatList {
		if v.Sender == "-1" {
			continue
		}
		if filterUid != "" && filterUid != v.Sender {
			continue
		}
		if startTime > 0 && v.Time < pb.Int64(startTime) {
			continue
		}
		if endTime > 0 && v.Time > pb.Int64(endTime) {
			break
		}
		arr = append(arr, ToWeb(v))
	}
	channelInfo.RUnlock()
	tail := ut.Min(len(arr), start+size)
	arr = arr[start:tail]
	return arr
}

// 查询队伍聊天列表
func GetChatTeamList(filterUid string) []map[string]interface{} {
	arr := []map[string]interface{}{}
	channel := CHANNEL_TYPE_TEAM + "_" + filterUid
	channelInfo := chatChannelMap.Get(channel)
	if channelInfo != nil {
		data := map[string]interface{}{}
		data["uid"] = filterUid
		if channelInfo.ChatList != nil {
			channelInfo.RLock()
			lastChat := channelInfo.ChatList[len(channelInfo.ChatList)-1]
			channelInfo.RUnlock()
			data["lastChat"] = map[string]interface{}{
				"name": lastChat.SenderNickname,
				"msg":  lastChat.Content,
				"time": lastChat.Time,
			}
		}
		arr = append(arr, data)
	}
	return arr
}

// 删除指定聊天
func DelLobbyChatByUid(channel, uid string) bool {
	channelInfo := chatChannelMap.Get(channel)
	if channelInfo == nil {
		return false
	}
	rst := false
	channelInfo.Lock()
	for i := len(channelInfo.ChatList) - 1; i >= 0; i-- {
		chat := channelInfo.ChatList[i]
		if chat.Uid == uid {
			channelInfo.ChatList = append(channelInfo.ChatList[:i], channelInfo.ChatList[i+1:]...)
			dbChan <- channelInfo.Channel
			rst = true
			break
		}
	}
	channelInfo.Unlock()
	return rst
}

func ToWeb(chatInfo *pb.LobbyChatInfo) map[string]interface{} {
	return map[string]interface{}{
		"uid":        chatInfo.Uid,
		"channel":    chatInfo.Channel,
		"senderName": chatInfo.SenderNickname,
		"senderHead": chatInfo.SenderHeadicon,
		"content":    chatInfo.Content,
		"emoji":      chatInfo.Emoji,
		"time":       chatInfo.Time,
	}
}
