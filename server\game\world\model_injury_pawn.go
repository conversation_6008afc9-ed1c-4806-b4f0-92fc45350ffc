package world

import (
	"maps"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// 医馆受伤士兵列表
type InjuryPawnList struct {
	deadlock.RWMutex
	List           []*pb.InjuryPawnInfo
	DeadPawnLvMap  map[int32]int32
	AbandomAcc     int32 // 医馆满员后累计放弃的士兵数量
	FullNoticeFlag int32 // 通知标记 正数为剩余位置通知 负数为满员累计放弃通知
	NoticeClose    bool  // 是否关闭通知
}

// 医馆添加受伤士兵
func (this *Model) AddInjuryPawn(uid, userId string, id, lv, skinId, cureCount int32) {
	plr := this.GetTempPlayer(userId)
	if plr == nil {
		return
	}
	area := this.GetArea(plr.MainCityIndex)
	if area == nil {
		return
	}
	// 根据等级获取添加到医馆的概率
	odds := constant.INJURY_PAWN_INIT_ODDS_MAP[lv]
	if odds <= 0 {
		return
	}
	minOdds := constant.INJURY_PAWN_MIN_ODDS_MAP[lv]
	plr.InjuryPawns.Lock()
	defer plr.InjuryPawns.Unlock()
	// 获取该等级士兵阵亡次数
	deadCount := plr.InjuryPawns.DeadPawnLvMap[lv]
	// 对应等级的士兵没阵亡一次后 减少回到医馆的概率
	odds = ut.MaxInt32(minOdds, odds-deadCount*constant.CURE_TIME_REDUCE_ODDS)
	plr.InjuryPawns.DeadPawnLvMap[lv]++
	add := ut.ChanceInt32(odds)
	if !add {
		return
	}
	// 先检查是否有相同uid的士兵
	for _, v := range plr.InjuryPawns.List {
		if v.Uid == uid {
			log.Warning("AddInjuryPawn repeat uid: %v, id: %v, lv: %v, userId %v", uid, id, lv, userId)
			return
		}
	}
	if len(plr.InjuryPawns.List) >= constant.INJURY_PAWN_MAX_COUNT {
		// 医馆人数上限
		plr.InjuryPawns.AbandomAcc++
		if !plr.InjuryPawns.NoticeClose && -plr.InjuryPawns.AbandomAcc < plr.InjuryPawns.FullNoticeFlag && constant.HOSPITAL_FULL_WARN_MAP[plr.InjuryPawns.AbandomAcc] {
			// 达到邮件通知阈值
			plr.InjuryPawns.FullNoticeFlag = -plr.InjuryPawns.AbandomAcc
			this.room.SendMailOne(slg.MAIL_HOSPITAL_FULL_ID, "", ut.String(plr.InjuryPawns.AbandomAcc), "-1", userId)
		}
		plr.IsNeedUpdateDB = true
		return
	}
	injuryInfo := &pb.InjuryPawnInfo{
		Uid:       uid,
		Id:        id,
		Lv:        lv,
		CureCount: cureCount,
		DeadTime:  time.Now().UnixMilli(),
	}
	plr.InjuryPawns.List = append(plr.InjuryPawns.List, injuryInfo)
	// 通知
	this.room.PutPlayerNotifyQueue(constant.NQ_PAWN_INJURY_ADD, area.Owner, &pb.OnUpdatePlayerInfoNotify{
		Data_83: injuryInfo,
	})
	leftNum := int32(constant.INJURY_PAWN_MAX_COUNT - len(plr.InjuryPawns.List))
	if !plr.InjuryPawns.NoticeClose && leftNum < plr.InjuryPawns.FullNoticeFlag && constant.HOSPITAL_LEFT_WARN_MAP[leftNum] {
		// 剩余容量达到邮件通知阈值
		plr.InjuryPawns.FullNoticeFlag = leftNum
		this.room.SendMailOne(slg.MAIL_HOSPITAL_ALMOST_FULL_ID, "", ut.String(leftNum), "-1", userId)
	}
	plr.IsNeedUpdateDB = true
}

// 获取受伤士兵
func (this *Model) GetInjuryPawn(uid, userId string) *pb.InjuryPawnInfo {
	plr := this.GetTempPlayer(userId)
	if plr == nil {
		return nil
	}
	plr.InjuryPawns.RLock()
	defer plr.InjuryPawns.RUnlock()
	for _, v := range plr.InjuryPawns.List {
		if v.Uid == uid {
			return v
		}
	}
	return nil
}

// 移除受伤的士兵
func (this *Model) RemoveInjuryPawn(uid, userId string) *pb.InjuryPawnInfo {
	plr := this.GetTempPlayer(userId)
	if plr == nil {
		return nil
	}
	plr.InjuryPawns.Lock()
	defer plr.InjuryPawns.Unlock()
	for i := len(plr.InjuryPawns.List) - 1; i >= 0; i-- {
		info := plr.InjuryPawns.List[i]
		if info.Uid == uid {
			plr.InjuryPawns.List = append(plr.InjuryPawns.List[:i], plr.InjuryPawns.List[i+1:]...)
			plr.InjuryPawns.AbandomAcc = 0
			plr.IsNeedUpdateDB = true
			// 通知
			this.room.PutPlayerNotifyQueue(constant.NQ_PAWN_INJURY_REMOVE, userId, &pb.OnUpdatePlayerInfoNotify{
				Data_84: uid,
			})
			return info
		}
	}
	return nil
}

// 更新受伤士兵的治疗状态
func (this *Model) ChangeInjuryPawnState(uid, userId string, curing bool) bool {
	plr := this.GetTempPlayer(userId)
	if plr == nil {
		return false
	}
	plr.InjuryPawns.Lock()
	defer plr.InjuryPawns.Unlock()
	for i := len(plr.InjuryPawns.List) - 1; i >= 0; i-- {
		info := plr.InjuryPawns.List[i]
		if info.Uid == uid {
			if info.Curing == curing {
				// 状态相同
				return false
			}
			info.Curing = curing
			plr.IsNeedUpdateDB = true
			return true
		}
	}
	return false
}

func (this *Model) InjuryPawnListClone(userId string) []*pb.InjuryPawnInfo {
	var list []*pb.InjuryPawnInfo
	if plr := this.GetTempPlayer(userId); plr != nil {
		plr.InjuryPawns.RLock()
		list = array.Clone(plr.InjuryPawns.List)
		plr.InjuryPawns.RUnlock()
	}
	return list
}

func (this *Model) GetDeadPawnLvMap(userId string) map[int32]int32 {
	ret := map[int32]int32{}
	if plr := this.GetTempPlayer(userId); plr != nil {
		plr.InjuryPawns.RLock()
		defer plr.InjuryPawns.RUnlock()
		maps.Copy(ret, plr.InjuryPawns.DeadPawnLvMap)
	}
	return ret
}

// 获取医馆满员后累计放弃的士兵数量
func (this *Model) GetHospitalAbandomAcc(userId string) int32 {
	if plr := this.GetTempPlayer(userId); plr != nil {
		plr.InjuryPawns.RLock()
		defer plr.InjuryPawns.RUnlock()
		return plr.InjuryPawns.AbandomAcc
	}
	return 0
}

// 获取医馆通知标记
func (this *Model) GetHospitalFullNoticeFlag(userId string) int32 {
	if plr := this.GetTempPlayer(userId); plr != nil {
		plr.InjuryPawns.RLock()
		defer plr.InjuryPawns.RUnlock()
		return plr.InjuryPawns.FullNoticeFlag
	}
	return 0
}

// 设置医馆通知标记
func (this *Model) SetHospitalFullNoticeFlag(userId string, flag int32) {
	if plr := this.GetTempPlayer(userId); plr != nil {
		plr.InjuryPawns.Lock()
		defer plr.InjuryPawns.Unlock()
		plr.InjuryPawns.FullNoticeFlag = flag
	}
}

// 获取医馆通知开关
func (this *Model) GetHospitalNoticeClose(userId string) bool {
	if plr := this.GetTempPlayer(userId); plr != nil {
		plr.InjuryPawns.RLock()
		defer plr.InjuryPawns.RUnlock()
		return plr.InjuryPawns.NoticeClose
	}
	return false
}

// 设置医馆通知开关
func (this *Model) SetHospitalNoticeClose(userId string, isClose bool) {
	if plr := this.GetTempPlayer(userId); plr != nil {
		plr.InjuryPawns.Lock()
		defer plr.InjuryPawns.Unlock()
		plr.InjuryPawns.NoticeClose = isClose
	}
}
