package g

import (
	"slgsrv/server/game/common/config"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 韬略
type StrategyObj struct {
	Fighters []IFighter // 携带者

	Type        int32
	initValue   int32
	initParams  int32
	magxOverlay int32 // 最大可叠加数量

	TargetType  int32 // 适用目标
	TargetValue int32 // 适用目标

	Value  int32 // 当前值
	Params int32
}

func NewStrategyObj(id int32) *StrategyObj {
	return new(StrategyObj).Init(id)
}

func (this *StrategyObj) Init(id int32) *StrategyObj {
	this.Type = id
	json := config.GetJsonData("strategy", id)
	if json != nil {
		this.initValue = ut.Int32(json["value"])
		this.initParams = ut.Int32(json["params"])
		targets := ut.StringToInt32s(ut.String(json["target_id"]), ",")
		this.TargetType = targets[0]
		this.TargetValue = targets[1]
		this.magxOverlay = ut.Int32(json["overlay"])
	}
	return this
}

// 创建一个新的
func (this *StrategyObj) Clone(targetType, targetValue int32, f IFighter) *StrategyObj {
	obj := NewStrategyObj(this.Type)
	obj.TargetType = targetType
	obj.TargetValue = targetValue
	obj.AddFighter(f)
	return obj
}

func (this *StrategyObj) GetValue() float64  { return float64(this.Value) }
func (this *StrategyObj) GetParams() float64 { return float64(this.Params) }

func (this *StrategyObj) GetFighters() []IFighter {
	return this.Fighters
}

func (this *StrategyObj) AddFighter(f IFighter) {
	if !array.Some(this.Fighters, func(m IFighter) bool { return m.GetUID() == f.GetUID() }) {
		this.Fighters = append(this.Fighters, f)
		this.UpdateCurValue()
	}
}

func (this *StrategyObj) RemoveFighter(uid string) bool {
	for i, l := 0, len(this.Fighters); i < l; i++ {
		if this.Fighters[i].GetUID() == uid {
			this.Fighters = append(this.Fighters[:i], this.Fighters[i+1:]...)
			this.UpdateCurValue()
			return true
		}
	}
	return false
}

// 刷新当前值
func (this *StrategyObj) UpdateCurValue() {
	lv := ut.MinInt32(int32(len(this.Fighters)), this.magxOverlay)
	this.Value = this.initValue * lv
	this.Params = this.initParams * lv
}
