package game

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/world"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDAncient() {
	this.GetServer().RegisterGO("HD_GetAncientInfo", this.getAncientInfo)           // 获取古城信息
	this.GetServer().RegisterGO("HD_AncientContribute", this.ancientContribute)     // 古城捐献资源
	this.GetServer().RegisterGO("HD_GetAncientLogs", this.getAncientLogs)           // 获取古城捐献记录
	this.GetServer().RegisterGO("HD_GetAncientDonateAcc", this.getAncientDonateAcc) // 获取古城捐献统计
}

// 获取古城信息
func (this *Game) getAncientInfo(session gate.Session, msg *pb.GAME_HD_GETANCIENTINFO_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	s2c := &pb.GAME_HD_GETANCIENTINFO_S2C{List: []*pb.AncientCityInfo{}}
	wld.AncientCityMap.ForEach(func(v *world.AncientInfo, k int32) bool {
		area := wld.GetArea(v.Index)
		if area == nil {
			return true
		}
		build := area.GetAncientBuild()
		if build == nil {
			return true
		}
		s2c.List = append(s2c.List, v.ToPb(build.Lv, plr.Uid, true))
		return true
	})
	return pb.ProtoMarshal(s2c)
}

// 古城捐献资源
func (this *Game) ancientContribute(session gate.Session, msg *pb.GAME_HD_ANCIENTCONTRIBUTE_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, ctbType, resType, count := msg.GetIndex(), int8(msg.GetType()), msg.GetResType(), msg.GetCount()
	area := wld.GetArea(index)
	if area == nil {
		return nil, ecode.UNKNOWN.String()
	}
	if !wld.CheckIsOneAlliance(area.Owner, plr.Uid) {
		return nil, ecode.AREA_NOT_ANCIETN.String()
	}
	build := area.GetAncientBuild()
	if !area.IsAncient() || build == nil {
		return nil, ecode.AREA_NOT_ANCIETN.String()
	}
	s2c := &pb.GAME_HD_ANCIENTCONTRIBUTE_S2C{}
	if s2c.Cost, err = wld.AncientResContribute(area, build, ctbType, resType, count, plr, s2c); err != "" {
		return
	}
	resInfo := wld.AncientCityMap.Get(area.GetIndex())
	if resInfo != nil {
		s2c.CurContribute = pb.Int32(resInfo.CurLogs.Get(plr.Uid))
		s2c.CtbSurplusCount = pb.Int32(resInfo.GetCurContributeCount())
	}
	return pb.ProtoMarshal(s2c)
}

// 获取古城捐献记录
func (this *Game) getAncientLogs(session gate.Session, msg *pb.GAME_HD_GETANCIENTLOGS_C2S) (ret []byte, err string) {
	e, _, _, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index := msg.GetIndex()
	resInfo := wld.AncientCityMap.Get(index)
	if resInfo == nil {
		return nil, ecode.AREA_NOT_ANCIETN.String()
	}
	// 历史捐献记录
	list := []*pb.AncientContributeLog{}
	resInfo.Logs.RLock()
	for _, v := range resInfo.Logs.List {
		list = append(list, &pb.AncientContributeLog{
			Uid:     v.Uid,
			Type:    int32(v.Type),
			ResType: v.ResType,
			Count:   v.Count,
			Time:    v.Time,
		})
	}
	resInfo.Logs.RUnlock()
	return pb.ProtoMarshal(&pb.GAME_HD_GETANCIENTLOGS_S2C{
		List: list,
	})
}

// 获取古城捐献统计
func (this *Game) getAncientDonateAcc(session gate.Session, msg *pb.GAME_HD_GETANCIENTDONATEACC_C2S) (ret []byte, err string) {
	e, _, _, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index := msg.GetIndex()
	resInfo := wld.AncientCityMap.Get(index)
	if resInfo == nil {
		return nil, ecode.AREA_NOT_ANCIETN.String()
	}
	list := []*pb.AncientContributeAccInfo{}
	resInfo.MemLogs.ForEach(func(v []*g.TypeObj, k string) bool {
		list = append(list, &pb.AncientContributeAccInfo{
			Uid:   k,
			Items: array.Map(v, func(m *g.TypeObj, _ int) *pb.TypeObj { return m.ToPb() }),
		})
		return true
	})
	return pb.ProtoMarshal(&pb.GAME_HD_GETANCIENTDONATEACC_S2C{
		List: list,
	})
}
