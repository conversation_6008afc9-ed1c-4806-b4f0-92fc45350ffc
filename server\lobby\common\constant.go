package lc

const (
	POINTSETS_ONE_COST       = 10  // 点将一次费用
	POINTSETS_ONE_GOLD_COST  = 598 // 点将五次 金币费用
	POINTSETS_PORTRAYAL_ODDS = 3   // 有多少几率直接获得画像

	PORTRAYAL_COMP_NEED_COUNT        = 3   // 残卷合成画像 需要数量
	RESTORE_PORTRAYAL_WAR_TOKEN_COST = 50  // 保存画像费用 兵符
	RESTORE_PORTRAYAL_GOLD_COST      = 598 // 保存画像费用 金币
	BUY_OPT_HERO_COST                = 999 // 自选英雄费用
	PORTRAYAL_SAVE_SLOT_MAX          = 2   // 画像保存槽位上限
	PORTRAYAL_SAVE_SLOT_PRICE        = 200 // 购买保存槽位价格

	TODAY_FREE_GOLD = 3 // 每日免费金币

	PLANT_FERTILIZE_COST = 20  // 种植释放费用
	SIGN_DAYS_MAX        = 100 // 签到天数上限
)

// 英雄自选礼包
var HERO_OPT_GIFT = map[int32][]int32{
	1: {310101, 320101, 340101},
	2: {310101, 310401, 320101, 320401, 330301, 340101, 340601},
	// 3：全自选
	4: {310101, 310201, 310401, 310601, 320101, 320201, 320301, 320401, 330202, 330301, 330501, 340101, 340401, 340501, 340601},
}

var (
	// 点将台残卷概率和对应数量 [权重, 数量]
	PORTRAYAL_POINT_SET_WEIGHTS      = [][]int32{{900, 1}, {95, 3}, {5, 27}}
	PORTRAYAL_POINT_SET_TOTAL_WEIGHT = 1000
)

var (
	INIT_PORTRAYAL_HERO_LIST = []int32{320201, 330102, 340401} // 首次点将台英雄列表 曹仁 韩当 许褚
)
