package fsp

import (
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"

	"github.com/sasha-s/go-deadlock"
)

type MainDoor struct {
	Fighter
	area  Area // 当前场景
	point *ut.Vec2
}

func (this *MainDoor) Init(area Area, camp int32, point *ut.Vec2) *MainDoor {
	this.area = area
	this.camp = camp
	this.point = point.Clone()
	this.blackboardMutex = new(deadlock.RWMutex)
	return this
}

func (this *MainDoor) CheckBattleBeginTrigger() {
}

func (this *MainDoor) GetEnterDir() int32                                           { return -1 }
func (this *MainDoor) GetSearchDir(attackTarget g.IFighter) int32                   { return -1 }
func (this *MainDoor) GetID() int32                                                 { return 0 }
func (this *MainDoor) GetUID() string                                               { return ut.Itoa(this.area.GetIndex()) + "_" + this.point.ID() }
func (this *MainDoor) GetArmyUID() string                                           { return "" }
func (this *MainDoor) GetLV() int32                                                 { return 1 }
func (this *MainDoor) GetAttack() int32                                             { return 0 }
func (this *MainDoor) GetInstabilityMaxAttack() int32                               { return 0 }
func (this *MainDoor) GetAttackSpeed() int32                                        { return 0 } // 出手速度
func (this *MainDoor) GetAttackIndex() int32                                        { return 0 } // 出手顺序
func (this *MainDoor) GetTempAttackIndex() int32                                    { return 0 } // 出手顺序
func (this *MainDoor) GetPoint() *ut.Vec2                                           { return this.point }
func (this *MainDoor) GetPoints() []*ut.Vec2                                        { return []*ut.Vec2{this.point} }
func (this *MainDoor) GetPawnType() int32                                           { return constant.PAWN_TYPE_BUILD }
func (this *MainDoor) GetSkillByType(tp int32) g.PawnSkill                          { return nil }
func (this *MainDoor) GetEquipEffects() []*g.EquipEffectObj                         { return []*g.EquipEffectObj{} } // 获取装备效果列表
func (this *MainDoor) GetEquipEffectByType(tp int32) *g.EquipEffectObj              { return nil }                   // 获取装备效果列表
func (this *MainDoor) GetCanByReqelPoint(point *ut.Vec2, rang int32) *ut.Vec2       { return nil }
func (this *MainDoor) CheckInAttackRange(target g.IFighter, attackRange int32) bool { return false }
func (this *MainDoor) CheckInMyAttackRange(target g.IFighter) bool {
	return this.CheckInAttackRange(target, this.GetAttackRange())
}
func (this *MainDoor) GetMinDis(target g.IFighter) int32 { return 0 }

func (this *MainDoor) GetOwner() string { return this.area.GetOwner() }
func (this *MainDoor) GetMaxHp() int32  { return this.area.GetMaxHP() }

func (this *MainDoor) IsPawn() bool      { return false }
func (this *MainDoor) IsBuild() bool     { return true }
func (this *MainDoor) IsTower() bool     { return false }
func (this *MainDoor) IsNoncombat() bool { return false }
func (this *MainDoor) IsFlag() bool      { return false }
func (this *MainDoor) IsHero() bool      { return false }
func (this *MainDoor) IsBoss() bool      { return false }

// 获取攻击动画时间
func (this *MainDoor) GetAttackAnimTimes() [][]float64 {
	return nil
}

func (this *MainDoor) AmendMaxHp(ignoreBuffType int32) int32 {
	return this.GetMaxHp()
}

func (this *MainDoor) InitSkillAttackAnimTime() {
}

func (this *MainDoor) AmendAttack(attack int32) int32 {
	return 0
}

func (this *MainDoor) GetActAttack() int32 {
	return 0
}

func (this *MainDoor) GetIgnoreBuffAttack(tp int32) int32 {
	return 0
}

func (this *MainDoor) GetHPRatio() float64 {
	return float64(this.area.GetCurHP()) / float64(this.area.GetMaxHP())
}

func (this *MainDoor) GetPortrayalSkill() *g.PortrayalSkill {
	return nil
}

func (this *MainDoor) IsDie() bool {
	return this.area.GetCurHP() <= 0
}

// 是否满血
func (this *MainDoor) IsFullHP() bool {
	return this.area.GetCurHP() >= this.area.GetMaxHP()
}

// 受击前处理
func (this *MainDoor) HitPrepDamageHandle(damage, trueDamage int32) (int32, int32) {
	return damage, trueDamage
}

// 受击
func (this *MainDoor) OnHit(damage, baseDamage int32) (int32, int32, int32) {
	hp := this.area.GetCurHP()
	if damage > hp {
		damage = hp
	}
	this.area.SetCurHP(hp - damage)
	this.area.RecordLastAttackMainPlayer()
	return damage, 0, 0
}

// 回血
func (this *MainDoor) OnHeal(val int32, suckbloodShield bool) int32 {
	curHp := this.area.GetCurHP()
	maxHp := this.area.GetMaxHP()
	if curHp+val > maxHp {
		val = maxHp - curHp
	}
	this.area.SetCurHP(curHp + val)
	return val
}

func (this *MainDoor) IsHasAnger() bool { return false }

func (this *MainDoor) GetAngerRatio() float64 { return 0 }

// 增加怒气
func (this *MainDoor) AddAnger(val int32) {
}

// 设置怒气
func (this *MainDoor) setAnger(val int32) {
}

// 是否可以释放技能
func (this *MainDoor) IsCanUseSkill() bool {
	return false
}

func (this *MainDoor) GetBuffs() []*g.BuffObj {
	return []*g.BuffObj{}
}

func (this *MainDoor) GetBuff(tp int32) *g.BuffObj {
	return nil
}

// 添加buff
func (this *MainDoor) AddBuff(tp int32, provider string, lv int32) *g.BuffObj {
	return nil
}

func (this *MainDoor) AddBuffValue(tp int32, provider string, value float64) *g.BuffObj {
	return nil
}

func (this *MainDoor) CheckTriggerBuff(tp int32) *g.BuffObj {
	return nil
}

func (this *MainDoor) CheckTriggerBuffOr(types ...int32) *g.BuffObj {
	return nil
}

func (this *MainDoor) IsHasBuff(tp int32) bool {
	return false
}

// 删除一个buff
func (this *MainDoor) RemoveBuff(tp int32) bool {
	return false
}

// 删除多个buff
func (this *MainDoor) RemoveMultiBuff(types ...int32) {
}

// 删除指定某人施加的buff
func (this *MainDoor) RemoveBuffByProvider(tp int32, provider string) bool {
	return false
}

// 删除所有护盾buff
func (this *MainDoor) RemoveShieldBuffs() {
}

// 获取韬略数值
func (this *MainDoor) GetStrategyValue(tp int32) int32 {
	return 0
}

// 获取韬略数值
func (this *MainDoor) GetStrategyParams(tp int32) int32 {
	return 0
}

// 是否有某个韬略
func (this *MainDoor) IsHasStrategys(tps ...int32) bool {
	return false
}
