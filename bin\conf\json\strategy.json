[{"id": 10001, "value": 10, "v_suffix": "%", "params": 10, "p_suffix": "%", "target_id": "4,0", "overlay": 2}, {"id": 10002, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "4,0", "overlay": 2}, {"id": 10003, "value": 10, "v_suffix": "%", "params": 10, "p_suffix": "%", "target_id": "3,0", "overlay": 1}, {"id": 10004, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "3,0", "overlay": 3}, {"id": 20001, "value": 2, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "10,0", "overlay": 3}, {"id": 20002, "value": 20, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "10,0", "overlay": 3}, {"id": 20003, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "10,0", "overlay": 3}, {"id": 20004, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "10,0", "overlay": 3}, {"id": 20005, "value": 5, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "10,0", "overlay": 3}, {"id": 20006, "value": 5, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "10,0", "overlay": 3}, {"id": 30101, "value": 40, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3101", "overlay": 1}, {"id": 30102, "value": 5, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,1", "overlay": 2}, {"id": 30201, "value": 20, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3102", "overlay": 2}, {"id": 30301, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "0,0", "overlay": 3}, {"id": 30401, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,1", "overlay": 3}, {"id": 33101, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3105", "overlay": 1}, {"id": 30501, "value": 5, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3106", "overlay": 2}, {"id": 30502, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3106", "overlay": 1}, {"id": 30601, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "0,0", "overlay": 3}, {"id": 30602, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3201", "overlay": 1}, {"id": 30701, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3202", "overlay": 1}, {"id": 30702, "value": 20, "v_suffix": "%", "params": 30, "p_suffix": "%", "target_id": "1,3202", "overlay": 1}, {"id": 30801, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3203", "overlay": 3}, {"id": 30802, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3203", "overlay": 2}, {"id": 30901, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3204", "overlay": 1}, {"id": 30902, "value": 50, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3204", "overlay": 1}, {"id": 34001, "value": 1, "v_suffix": "", "params": 10, "p_suffix": "%", "target_id": "1,3206", "overlay": 1}, {"id": 31001, "value": 50, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3301", "overlay": 2}, {"id": 31101, "value": 50, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3302", "overlay": 2}, {"id": 31102, "value": 3, "v_suffix": "%", "params": 5, "p_suffix": "", "target_id": "1,3302", "overlay": 1}, {"id": 31201, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "0,0", "overlay": 3}, {"id": 31202, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3303", "overlay": 1}, {"id": 31301, "value": 30, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3304", "overlay": 1}, {"id": 31302, "value": 3, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3304", "overlay": 1}, {"id": 31401, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3305", "overlay": 1}, {"id": 31402, "value": 2, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3305", "overlay": 1}, {"id": 33001, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3306", "overlay": 1}, {"id": 33002, "value": 5, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3306", "overlay": 2}, {"id": 31501, "value": 20, "v_suffix": "%", "params": 30, "p_suffix": "%", "target_id": "1,3401", "overlay": 1}, {"id": 31502, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3401", "overlay": 1}, {"id": 31601, "value": 30, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3402", "overlay": 1}, {"id": 31602, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3402", "overlay": 2}, {"id": 31701, "value": 15, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3403", "overlay": 2}, {"id": 31801, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "0,0", "overlay": 3}, {"id": 31802, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3404", "overlay": 1}, {"id": 31901, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,4", "overlay": 1}, {"id": 32001, "value": 50, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3406", "overlay": 2}, {"id": 32002, "value": 50, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3406", "overlay": 2}, {"id": 40101, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,1", "overlay": 1}, {"id": 40102, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,1", "overlay": 1}, {"id": 40103, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,1", "overlay": 1}, {"id": 40104, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,1", "overlay": 1}, {"id": 40105, "value": 5, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,1", "overlay": 3}, {"id": 40106, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,1", "overlay": 3}, {"id": 40201, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,2", "overlay": 1}, {"id": 40202, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,2", "overlay": 1}, {"id": 40203, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,2", "overlay": 2}, {"id": 40204, "value": 2, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,2", "overlay": 2}, {"id": 40205, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,2", "overlay": 3}, {"id": 40206, "value": 2, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,2", "overlay": 3}, {"id": 40301, "value": 2, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,3", "overlay": 2}, {"id": 40302, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,3", "overlay": 1}, {"id": 40303, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,3", "overlay": 1}, {"id": 40304, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,3", "overlay": 1}, {"id": 40305, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,3", "overlay": 1}, {"id": 40306, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,3", "overlay": 3}, {"id": 40401, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,4", "overlay": 1}, {"id": 40402, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,4", "overlay": 1}, {"id": 40403, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "2,4", "overlay": 1}, {"id": 40404, "value": 15, "v_suffix": "%", "params": 50, "p_suffix": "%", "target_id": "2,4", "overlay": 1}, {"id": 40405, "value": 15, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,4", "overlay": 2}, {"id": 40406, "value": 2, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,4", "overlay": 2}, {"id": 50001, "value": 20, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3101", "overlay": 1}, {"id": 50002, "value": 5, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,1", "overlay": 1}, {"id": 50003, "value": 1, "v_suffix": "", "params": 20, "p_suffix": "", "target_id": "0,0", "overlay": 1}, {"id": 50004, "value": 50, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3104", "overlay": 1}, {"id": 50005, "value": 5, "v_suffix": "%", "params": 10, "p_suffix": "", "target_id": "0,0", "overlay": 1}, {"id": 50006, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3201", "overlay": 1}, {"id": 50007, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "0,0", "overlay": 1}, {"id": 50008, "value": 10, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,2", "overlay": 1}, {"id": 50009, "value": 2, "v_suffix": "", "params": 20, "p_suffix": "", "target_id": "2,2", "overlay": 1}, {"id": 50010, "value": 2, "v_suffix": "", "params": 20, "p_suffix": "", "target_id": "0,0", "overlay": 1}, {"id": 50011, "value": 20, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,3", "overlay": 1}, {"id": 50012, "value": 30, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "0,0", "overlay": 1}, {"id": 50013, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3302", "overlay": 1}, {"id": 50014, "value": "", "v_suffix": "", "params": "", "p_suffix": "", "target_id": "0,0", "overlay": 1}, {"id": 50015, "value": "", "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3305", "overlay": 1}, {"id": 50016, "value": 20, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,4", "overlay": 1}, {"id": 50017, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3401", "overlay": 1}, {"id": 50018, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3106", "overlay": 1}, {"id": 50019, "value": 20, "v_suffix": "%", "params": 30, "p_suffix": "", "target_id": "0,0", "overlay": 1}, {"id": 50020, "value": 20, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,2", "overlay": 1}, {"id": 50021, "value": 20, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,1", "overlay": 1}, {"id": 50022, "value": 2, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3102", "overlay": 1}, {"id": 50023, "value": 20, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,2", "overlay": 1}, {"id": 50024, "value": 20, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "0,0", "overlay": 1}, {"id": 50025, "value": 20, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "2,4", "overlay": 1}, {"id": 50026, "value": 20, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3203", "overlay": 1}, {"id": 50027, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3302", "overlay": 1}, {"id": 50028, "value": 5, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "0,0", "overlay": 1}, {"id": 50029, "value": 15, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "0,0", "overlay": 1}, {"id": 50030, "value": 2, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "1,3101", "overlay": 1}, {"id": 50031, "value": 1, "v_suffix": "", "params": "", "p_suffix": "", "target_id": "1,3304", "overlay": 1}, {"id": 50032, "value": 20, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "0,0", "overlay": 1}, {"id": 50033, "value": 15, "v_suffix": "%", "params": "", "p_suffix": "", "target_id": "0,0", "overlay": 1}]