package lobby

import (
	"regexp"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sdk"
	ut "slgsrv/utils"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/log"
)

// // 翻译缓存
// type TranslateCache struct {
// 	cache *ut.LFUCache
// }

// var transCache = &TranslateCache{cache: ut.NewLFUCache(1000)}

type TransInfo struct {
	Uid        string // 聊天唯一id
	UserId     string // 用户uid
	Lang       string // 目标语言
	Text       string // 文本
	SourceLang string // 源文本语言
}

var transChan = make(chan *TransInfo, 500)

var list []string

// 获取翻译后的文本
func GetTranslateTexts(lang, text, uid string, user *User) {
	if text == "" || lang == "" {
		return
	}
	language, ok := slg.TRANS_LANG_MAP[lang]
	if !ok {
		log.Error("GetTranslateTexts lang err: %v", lang)
		return
	}

	sourceLang, err := sdk.DetectTextLang(text)
	if sourceLang == "ZH" {
		// 检测语言为中文时特殊处理
		if language == "zh-CN" {
			text = sdk.ChineseT2S(text)
			language = sourceLang
		} else if language == "zh-HK" || language == "zh-TW" {
			text = sdk.ChineseS2T(text)
			language = sourceLang
		} else {
			// 目标语言不为中文时 统一转为繁体到谷歌术语表翻译
			text = sdk.ChineseS2T(text)
			sourceLang = "zh-TW"
		}
	}

	if sourceLang == language {
		// 语言相同 直接返回
		user.NotifyUser(slg.LOBBY_TRANSLATE_TEXT, &pb.LOBBY_ONTRANSLATETEXT_NOTIFY{
			Uid:  uid,
			Text: text,
		})
		return
	}

	digitPattern := `^[0-9.,;:?! ]+$`
	matched, _ := regexp.MatchString(digitPattern, text)
	if matched {
		// 纯数字和特殊符号不翻译
		user.NotifyUser(slg.LOBBY_TRANSLATE_TEXT, &pb.LOBBY_ONTRANSLATETEXT_NOTIFY{
			Uid:  uid,
			Text: text,
		})
		return
	}

	key := getKey(lang, text)
	data, err := rds.TsltRdsGet(key)
	if err == nil {
		// 直接通知翻译完成
		user.NotifyUser(slg.LOBBY_TRANSLATE_TEXT, &pb.LOBBY_ONTRANSLATETEXT_NOTIFY{
			Uid:  uid,
			Text: ut.String(data),
		})
	} else {
		// 没有则放入通道调用API翻译
		transInfo := &TransInfo{
			Uid:        uid,
			UserId:     user.UID,
			Lang:       language,
			Text:       text,
			SourceLang: sourceLang,
		}
		transChan <- transInfo
	}
}

// 调用API执行翻译
func TranslateApiLoop() {
	go func() {
		for isRunning {
			transInfo, ok := <-transChan
			if !isRunning || !ok {
				break
			}
			user := GetUserByOnline(transInfo.UserId)
			if user == nil {
				continue
			}
			// 翻译前再取一次缓存
			key := getKey(transInfo.Lang, transInfo.Text)
			data, err := rds.TsltRdsGet(key)
			if err == nil {
				// 直接通知翻译完成
				user.NotifyUser(slg.LOBBY_TRANSLATE_TEXT, &pb.LOBBY_ONTRANSLATETEXT_NOTIFY{
					Uid:  transInfo.Uid,
					Text: ut.String(data),
				})
				continue
			}
			if transInfo.SourceLang != "" && slg.GLOSSARY_LANG_MAP[transInfo.SourceLang] {
				// 检测到源文本语言 使用术语表翻译
				data, err = sdk.TranslateTextWithGlossary(transInfo.SourceLang, transInfo.Lang, transInfo.Text)
				if err != nil {
					// 使用术语表报错 使用普通翻译
					data, err = sdk.TranslateText(transInfo.Lang, transInfo.Text)
				}
			} else {
				// 不使用术语表翻译
				data, err = sdk.TranslateText(transInfo.Lang, transInfo.Text)
			}
			if err != nil {
				data = transInfo.Text
			}
			// 通知用户翻译完成
			if user := GetUserByOnline(transInfo.UserId); user != nil {
				user.NotifyUser(slg.LOBBY_TRANSLATE_TEXT, &pb.LOBBY_ONTRANSLATETEXT_NOTIFY{
					Uid:  transInfo.Uid,
					Text: ut.String(data),
				})
			}
			// 更新缓存
			rds.TsltRdsSet(key, data)
		}
	}()
}

// // 获取缓存容量
// func GetTransCacheCapacity() int {
// 	return transCache.cache.Capacity
// }

// // 更新缓存容量
// func UpdateTransCacheCapacity(capacity int) {
// 	transCache.cache.UpdateCapacity(capacity)
// }

func getKey(lang, text string) string {
	return lang + "_" + text
}
