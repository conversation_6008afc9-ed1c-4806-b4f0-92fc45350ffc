package mail

import (
	"context"

	slg "slgsrv/server/common"
	mgo "slgsrv/utils/mgodb"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Mongodb struct {
	table string
}

func (this *Mongodb) getCollection() *mongo.Collection {
	return mgo.GetCollection(this.table)
}

// 邮件信息表
var mailDb = &Mongodb{slg.DB_COLLECTION_NAME_MAIL}

// 查询指定邮件信息
func (this *Mongodb) FindOneMailBase(uid string) (data MailBaseInfo, err error) {
	err = this.getCollection().FindOne(context.TODO(), bson.M{"uid": uid}).Decode(&data)
	return
}

// 查询指定玩家的基础邮件信息
func (this *Mongodb) FindMailsByUserId(userId string, sid int32) (datas []*MailBaseInfo, err error) {
	sidBson := bson.M{"sid": 0}
	if sid != 0 {
		sidBson = bson.M{"$or": []bson.M{{"sid": 0}, {"sid": sid}}}
	}
	filter := bson.M{
		"$and": []bson.M{sidBson, {"receiver": userId}},
	}
	cur, err := this.getCollection().Find(context.TODO(), filter)
	defer func() {
		cur.Close(context.TODO())
	}()
	if err = cur.All(context.TODO(), &datas); err != nil {
		log.Error("FindMailsByUserId error! err: %v", err)
	}
	return
}

// 查询指定区服的邮件
func (this *Mongodb) FindMailsBySid(sid int32) (datas []*MailBaseInfo, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.M{"sid": sid})
	defer func() {
		cur.Close(context.TODO())
	}()
	if err = cur.All(context.TODO(), &datas); err != nil {
		log.Error("FindMailsBySid error err: %v, sid: %v", err, sid)
	}
	return
}

// 查询邮件是否存在
func (this *Mongodb) FindMailExist(uid string) bool {
	filter := bson.M{"uid": uid}
	opts := options.FindOne().SetProjection(bson.M{"_id": 1}) // 只返回 _id 字段
	result := this.getCollection().FindOne(context.TODO(), filter, opts)
	return result != nil && result.Err() == nil
}

// 获取所有群发邮件
func (this *Mongodb) FindAllMassMail() (datas []*MailBaseInfo, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.M{
		"$or": []bson.M{
			{"receiver_map": bson.M{"$ne": nil}}, // 群发
			{"receiver": "0"},                    // 所有人
		},
	})
	defer func() {
		cur.Close(context.TODO())
	}()
	err = cur.All(context.TODO(), &datas)
	return
}

// 插入单个
func (this *Mongodb) InsertOneMail(data *MailBaseInfo) (err string) {
	if _, e := this.getCollection().InsertOne(context.TODO(), data); e != nil {
		err = e.Error()
		log.Error("InsertOne", err)
	}
	return
}

// 更新一条
func (this *Mongodb) UpdateOne(uid string, key string, value interface{}) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{key: value}}); e != nil {
		err = e.Error()
		log.Error("user UpdateOne", err)
	}
	return
}

// 删除一条
func (this *Mongodb) DeleteOneMail(uid string) (err string) {
	if _, e := this.getCollection().DeleteOne(context.TODO(), bson.M{"uid": uid}); e != nil {
		err = e.Error()
		log.Error("user DeleteOneMail", err)
	}
	return
}

// 删除指定区服邮件
func (this *Mongodb) DeleteMailsBySid(sid int) (err string) {
	if _, e := this.getCollection().DeleteMany(context.TODO(), bson.M{"sid": sid}); e != nil {
		err = e.Error()
		log.Error("user DeleteMailsBySid", err)
	}
	return
}

// 获取数据总数
func (this *Mongodb) FindTimeoutMails(now int64) (arr []MailBaseInfo, err string) {
	cur, e := this.getCollection().Find(context.TODO(), bson.M{
		"create_time": bson.M{"$lt": now - slg.NORMAL_MAIL_EXPIR_TIME},
	})
	if e != nil {
		return []MailBaseInfo{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []MailBaseInfo{}, e.Error()
	}
	if e = cur.All(context.TODO(), &arr); e != nil {
		err = e.Error()
	}
	_ = cur.Close(context.TODO())
	return
}

// GetMail web接口获取邮件
func (this *Mongodb) GetMail(sid int, sender, receiver string, size, skip int64) (arr []MailBaseInfo, err error) {
	filter := bson.M{}
	if sid != -1 {
		filter["sid"] = sid
	}
	// 如果sender 和 receiver不同 则两个条件分开
	if sender != receiver {
		if sender != "" {
			filter["sender"] = sender
		}
		if receiver != "" {
			arr := []bson.M{
				{"receiver": "0"},
			}
			if "receiver" != "0" {
				arr = append(arr, bson.M{
					"receiver_map." + receiver: bson.M{
						"$exists": true,
					},
				})
			}
			filter["$or"] = arr
		}
	} else {
		// 否则就是查询某个固定id相关的所有邮件,不论收发
		arr := []bson.M{
			{"sender": sender},
			{"receiver": "0"},
		}
		if receiver != "0" {
			arr = append(arr, bson.M{
				"receiver_map." + receiver: bson.M{
					"$exists": true,
				},
			})
		}
		filter["$or"] = arr
	}
	opt := &options.FindOptions{Sort: bson.M{"create_time": -1}, Skip: &skip, Limit: &size}
	cur, err := this.getCollection().Find(context.TODO(), filter, opt)
	if err != nil {
		return nil, err
	}

	if err = cur.All(context.TODO(), &arr); err != nil {
		return nil, err
	}
	cur.Close(context.TODO())
	return arr, nil
}

// 玩家邮件数据表
var userMailDb = &Mongodb{slg.DB_COLLECTION_NAME_USER_MAIL}

// 获取指定玩家所有邮件数据
func (this *Mongodb) FindUserMailInfo(userId string) (datas []*UserMailInfo, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.M{"user_id": userId})
	defer func() {
		cur.Close(context.TODO())
	}()
	err = cur.All(context.TODO(), &datas)
	return
}

// 批量更新玩家邮件数据
func (this *Mongodb) UpdateUserMailList(datas []*UserMailInfo) {
	defer func() {
		if err := recover(); err != nil {
			log.Error("UpdateUserMailList catch error: %v", err)
		}
	}()
	models := []mongo.WriteModel{}
	for _, m := range datas {
		models = append(models, mongo.NewUpdateOneModel().SetFilter(bson.M{"uid": m.UID}).SetUpdate(bson.M{"$set": m}).SetUpsert(true))
	}
	if len(models) == 0 {
		return
	} else if _, e := this.getCollection().BulkWrite(context.TODO(), models, options.BulkWrite().SetOrdered(false)); e != nil {
		log.Error("UpdateUserMailList error.", e.Error())
	}
}

// 批量删除玩家邮件数据
func (this *Mongodb) DelUserMailList(delList []string) {
	defer func() {
		if err := recover(); err != nil {
			log.Error("DelUserMailList catch error: %v", err)
		}
	}()
	models := []mongo.WriteModel{}
	for _, m := range delList {
		models = append(models, mongo.NewDeleteOneModel().SetFilter(bson.M{"uid": m}))
	}
	if len(models) == 0 {
		return
	} else if _, e := this.getCollection().BulkWrite(context.TODO(), models, options.BulkWrite().SetOrdered(false)); e != nil {
		log.Error("DelUserMailList error.", e.Error())
	}
}

// 删除指定邮件id的玩家邮件数据
func (this *Mongodb) DelUserMailByMailUid(mailUid string) (err error) {
	_, err = this.getCollection().DeleteMany(context.TODO(), bson.M{"mail_uid": mailUid})
	return
}
