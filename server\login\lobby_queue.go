package login

import (
	"github.com/sasha-s/go-deadlock"
)

var lobbyQueue = &LobbyQueue{}

// 大厅服排队数据
type LobbyQueue struct {
	curNum  int // 当前排队序号
	tailNum int // 队列尾序号
	deadlock.RWMutex
}

type LobbyQueueUnit struct {
	SessionID string `json:"session_id"` // 用户连接服sessionId
	ServerID  string `json:"server_id"`  // 用户session所在的连接服id
}

// // 添加到大厅服排队处理Tick
// func (this *Login) runLobbyQueueTick() {
// 	go func() {
// 		tiker := time.NewTicker(time.Second * 1)
// 		for isRunning {
// 			<-tiker.C
// 			if lobbyQueue.tailNum == 0 {
// 				continue
// 			}

// 		}
// 	}()
// }
