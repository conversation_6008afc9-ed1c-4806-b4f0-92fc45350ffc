package game

import (
	"strings"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sensitive"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDChat() {
	this.GetServer().RegisterGO("HD_GetChats", this.getChats)       // 获取聊天信息
	this.GetServer().RegisterGO("HD_AddPChat", this.addPChat)       // 添加私聊 这里只是检测玩家是否存在 实际不添加
	this.GetServer().RegisterGO("HD_RemovePChat", this.removePChat) // 删除私聊
	this.GetServer().RegisterGO("HD_SendChat", this.sendChat)       // 发送聊天
}

// 获取聊天列表
func (this *Game) getChats(session gate.Session, msg *pb.GAME_HD_GETCHATS_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	list, canCloseTimeMap := wld.GetChatsPbByAlliUID(plr.Uid, plr.AllianceUid, wld.GetPlayerLanguage(plr.Uid))
	return pb.ProtoMarshal(&pb.GAME_HD_GETCHATS_S2C{
		List:            list,
		CanCloseTimeMap: canCloseTimeMap,
	})
}

// 添加私聊
func (this *Game) addPChat(session gate.Session, msg *pb.GAME_HD_ADDPCHAT_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	tplr := wld.GetTempPlayerByNameOrUID(msg.GetName())
	if tplr == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	if tplr.Uid == plr.Uid {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	// 检测是否是黑名单
	data, e := ut.RpcInterfaceMap(this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_GET_BLACK_LIST, plr.Uid))
	if e != "" {
		return nil, e
	} else if array.Has(ut.StringArray(data["blacklists"]), tplr.Uid) {
		return nil, ecode.BLACKLIST_BLOCK_FRIEND.String() // 在黑名单中
	}
	// 检测对方是否把你拉黑
	otherData, oErr := ut.RpcInterfaceMap(this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_GET_BLACK_LIST, tplr.Uid))
	if oErr != "" {
		return nil, oErr
	} else if array.Has(ut.StringArray(otherData["blacklists"]), plr.Uid) {
		return nil, ecode.BLACKLIST_BLOCK_FRIEND.String() // 对方把你拉黑了
	}
	return pb.ProtoMarshal(&pb.GAME_HD_ADDPCHAT_S2C{
		Uid: tplr.Uid,
	})
}

// 删除私聊
func (this *Game) removePChat(session gate.Session, msg *pb.GAME_HD_REMOVEPCHAT_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	channel := msg.GetChannel()
	chats := wld.GetChatsByChannel(channel)
	if len(chats) == 0 {
		return nil, ecode.PCHAT_NOT_EXIST.String()
	}
	uid := chats[len(chats)-1].UID
	plr.SetHidePChatChannel(channel, uid)
	return pb.ProtoMarshal(&pb.GAME_HD_REMOVEPCHAT_S2C{
		Uid: uid,
	})
}

// 发送聊天
func (this *Game) sendChat(session gate.Session, msg *pb.GAME_HD_SENDCHAT_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	now := time.Now().UnixMilli()
	plr.LastSendChatTime = now
	uid, channel, content, emoji, portrayalId, equipUid := msg.GetUid(), msg.GetChannel(), msg.GetContent(), msg.GetEmoji(), msg.GetPortrayalId(), msg.GetEquipUid()
	if channel == "" || channel == "3" {
		return nil, ecode.UNKNOWN.String() // 不可向系统发送消息
	}
	bannedChatEndTime, blacklists := int64(0), []string{}
	var portrayalInfo *pb.PortrayalInfo = nil
	data, err := ut.RpcInterfaceMap(this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_GET_USER_CHECK_CHAT_INFO, plr.Uid, emoji, portrayalId))
	if err != "" {
		return nil, err
	} else if data != nil {
		bannedChatEndTime = ut.Int64(data["bannedChatEndTime"])
		blacklists = ut.StringArray(data["blacklists"])
		portrayalInfo = g.NewPortrayalPbByJson(ut.MapInterface(data["portrayalInfo"]))
	}
	pHideChannel := ""
	if strings.Index(channel, "2_") == 0 { // 私聊
		if plr.IsSpectator() {
			return nil, ecode.PCHAT_NOT_EXIST.String() // 观战无法私聊
		}
		pHideChannel = plr.GetHidePChatChannelChatUID(channel)
		arr := strings.Split(channel, "_")[1:]
		otherUid := array.Find(arr, func(m string) bool { return m != plr.Uid })
		if otherUid == "" {
			return nil, ecode.PCHAT_NOT_EXIST.String() // 私聊对象不存在
		}
		if array.Has(blacklists, otherUid) {
			return nil, ecode.PCHAT_NOT_EXIST.String() // 黑名单了 不可发私聊
		}
		otherData, oErr := ut.RpcInterfaceMap(this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_GET_BLACK_LIST, otherUid))
		if oErr != "" {
			return nil, oErr
		} else if array.Has(ut.StringArray(otherData["blacklists"]), plr.Uid) {
			return nil, ecode.PCHAT_NOT_EXIST.String() // 对方把你拉黑了 不可发私聊
		}
	}
	var equipInfo *pb.EquipInfo = nil
	if bannedChatEndTime == 0 && equipUid != "" { // 检测装备
		if equip := plr.GetEquipByUID(equipUid); equip != nil {
			equipInfo = equip.ToBasePb()
		} else {
			return nil, ecode.EQUIP_NOT_EXIST.String()
		}
	}

	channelArr := strings.Split(channel, "_")
	tp := channelArr[0]
	if tp == "4" && len(channelArr) == 3 {
		// 联盟副频 检查该成员是否在频道中
		if alli := wld.GetAlliance(channelArr[1]); alli != nil {
			alliChannelInfo := alli.GetChatChannel(channelArr[2])
			if alliChannelInfo == nil {
				// 频道不存在
				return nil, ecode.ALLI_CHAT_NOT_EXIST.String()
			}
			if alliChannelInfo.MemberFilter && !array.Has(alliChannelInfo.MemberUids, plr.Uid) {
				// 成员不在该频道中
				return nil, ecode.ALLI_CHAT_NOT_EXIST.String()
			}
		} else {
			// 联盟不存在
			return nil, ecode.ALLIANCE_NOT_EXIST.String()
		}
	}

	bannedSurplusTime := ut.MaxInt64(0, bannedChatEndTime-now)
	var senderNickname, senderHeadicon string
	var senderTitle int32
	if plr.IsSpectator() {
		tempPly := wld.GetTempPlayer(plr.Uid)
		senderNickname = plr.Nickname
		senderHeadicon = plr.HeadIcon
		senderTitle = tempPly.Title
	}
	chat := wld.SendChat(uid, sensitive.Replace(content), emoji, portrayalInfo, equipInfo, channel, plr.Uid, bannedSurplusTime, pHideChannel, senderNickname, senderHeadicon, senderTitle, msg.GetBattleInfo(), msg.GetReplyUid())
	// 通知
	room.NotifyChat(chat)
	return pb.ProtoMarshal(&pb.GAME_HD_SENDCHAT_S2C{
		BannedSurplusTime: bannedSurplusTime,
	})
}
