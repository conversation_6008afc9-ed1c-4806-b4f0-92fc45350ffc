package eeffect

// 装备效果类型
const (
	NONE                  = iota
	BLOOD_RETURN          // 回血 1
	REDUCTION_INJURY      // 减伤 2
	CRIT                  // 暴击伤害 3
	BEGIN_BLOOD           // 回合开始回血 4
	SUCK_BLOOD            // 吸血 5
	THE_INJURY            // 反伤 6
	INFALLIBLE            // 必中 7
	DODGE                 // 闪避 8
	EXECUTE               // 处决 9
	FIXED_DAMAGE          // 固定伤害 10
	NOT_DODGE             // 攻击范围+1，无法被闪避 11
	REDUCTION_RANGED      // 减少远程伤害 12
	CONTINUE_ACTION       // 继续行动 13
	CUR_HP_DAMAGE         // 造成当前生命值额外伤害 14
	RECOVER_ANGER         // 攻击恢复怒气 15
	LOW_HP_SHIELD         // 血量低于50%获得护盾 16
	MAX_HP_TRUE_DMG       // 百分比真实伤害 17
	SAME_CORPS_REDUCE_DMG // 受到同兵种的伤害递减 18
	ATTACK_GET_SHIELD     // 攻击获得护盾 19
	HIT_GET_SUCK_BLOOD    // 受到获得吸血 20
	_ADD_BASE_ATTACK      // 基础攻击力提高 21
	_ADD_BASE_HP          // 基础生命提高 22
	_ADD_SHIELD_EFFECT    // 护盾效果提高 23
	_ADD_RUSH_DAMAGE      // 冲锋伤害提高 24
	_ADD_DASH_DAMAGE      // 冲撞伤害提高 25
	REDUCTION_MELEE       // 受到的近战伤害减少 26
	GEN_FLAG_BY_DIE       // 阵亡时掉落军旗 27
	ADD_MOVE_RANGE        // 移动范围+1 28
	_THE_INJURY_RANGED    // 反伤远程伤害 29
	_THE_INJURY_MELEE     // 反伤近战伤害 30
	RANGE_ATTACK          // 范围攻击 31
	TODAY_ADD_HP          // 每天加生命 32
	TODAY_ADD_ATTACK      // 每天加攻击 33
	LOW_HP_ATTACk         // 战斧 34
	BATTLE_BEGIN_SHIELD   // 为友军加护盾 35
	DOUBLE_EDGED_SWORD    // 月牙刀 36
	FLAMING_ARMOR         // 焱阳铠 37
	THOUSAND_UMBRELLA     // 千机伞 38
	CENTERING_HELMET      // 定心盔 39
	DIZZY_HEART_DROP      // 眩心坠 40
	CRIMSONGOLD_SHIELD    // 赤金盾 41
	BURNING_HEART_RING    // 焚心戒 42
	BILLHOOK              // 钩镰 43
	BELT_BLOODRAGE        // 血怒腰带 44
	OBSIDIAN_ARMOR        // 黑曜铠 45
	BLACK_IRON_STAFF      // 玄铁杖 46
	SPIKY_BALL            // 尖刺球 47
	SILVER_SNAKE_WHIP     // 银蛇鞭 48
	LONGYUAN_SWORD        // 龙渊剑 49
	MINGGUANG_ARMOR       // 明光铠 50
	BAIBI_SWORD           // 百辟刀 51

	// ADD_BASE_ATTACK_1 = 2101
	// ADD_BASE_ATTACK_2 = 2102
	// ADD_BASE_ATTACK_3 = 2103
	// ADD_BASE_ATTACK_4 = 2104
	// ADD_BASE_HP_1     = 2201
	// ADD_BASE_HP_2     = 2202
	// ADD_BASE_HP_3     = 2203
	// ADD_BASE_HP_4     = 2204
)
