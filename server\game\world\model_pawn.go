package world

import (
	"context"
	"strings"

	slg "slgsrv/server/common"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/log"
)

// 添加士兵使用记录
func (this *Model) AddPawnUse(id, lv int32) {
	if this.room.IsGameOver() {
		return
	}
	// 根据等级获取实际添加的影响值
	if int(lv) >= len(slg.PAWN_COST_LV_LIST) || lv <= 0 {
		return
	}
	// 实际影响值 = 对应等级参数 * 统计倍率
	addVal := slg.PAWN_COST_LV_LIST[int(lv-1)] * int32(slg.PAWN_COST_CAL_MUL)
	this.PawnUseStasticMap.Lock()
	this.PawnUseStasticMap.Map[id] += addVal
	this.PawnUseStasticMap.Unlock()
}

func (this *Model) ToPawnUseDb() map[int32]int32 {
	return this.PawnUseStasticMap.Clone()
}

// 获取士兵对应等级的资源消耗
func (this *Model) GetPawnCost(id, lv int32) (int32, []*g.TypeObj) {
	arr := []*g.TypeObj{}
	var needTime int32
	if lv < 0 || int(lv) >= len(slg.PAWN_COST_LV_LIST) {
		return needTime, arr
	} else if lv == 0 { // 读取配置表
		if json := config.GetJsonData("pawnBase", id); json != nil {
			arr = append(arr, g.StringToTypeObjs(json["drill_cost"])...)
			needTime = ut.Int32(json["drill_time"])
		}
	} else { // 升级
		attrId := id*1000 + lv
		if json := config.GetJsonData("pawnAttr", attrId); json != nil {
			arr = append(arr, g.StringToTypeObjs(json["lv_cost"])...)
			needTime = ut.Int32(json["lv_time"])
		}
	}
	// 缓存中有资源消耗数据
	if baseCost := this.PawnCostMap[id]; baseCost > 0 {
		lvParam := slg.PAWN_COST_LV_LIST[lv]
		costVal := lvParam * baseCost
		baseNeedTime := slg.CompatiValueInt32(float64(slg.PAWN_COST_LIMIT_LIST[0]), float64(slg.PAWN_COST_LIMIT_LIST[1]), float64(slg.PAWN_COST_TIME_LIST[0]), float64(slg.PAWN_COST_TIME_LIST[1]), baseCost)
		needTime = baseNeedTime * lvParam
		// 替换配置表中获取到的粮食消耗
		for _, v := range arr {
			if v.Type == ctype.CEREAL {
				v.Count = costVal
				break
			}
		}
	}
	return needTime, arr
}

// 添加本局的士兵使用统计 对局结束时调用
func (this *Model) AddGamePawnUseStastics() {
	// 构建存储字符串
	stasticData := ut.String(this.room.GetSID()) + ":"
	isFirst := true
	this.PawnUseStasticMap.ForEach(func(v, k int32) bool {
		if !isFirst {
			stasticData += "|"
		}
		stasticData += ut.String(k) + "_" + ut.String(v)
		isFirst = false
		return true
	})

	// 添加统计数据到redis
	rst, err := rds.RdsEvalHashByCmd(rds.RDS_SCRIPT_CMD_ADD_ROOM_PAWN_USE, []string{rds.RDS_GAME_PAWN_USE_LIST_KEY}, stasticData)
	if err == nil && ut.Int32(rst) > 0 {
		// 添加成功 更新士兵资源消耗
		list, err := rds.RdsGetListRange(rds.RDS_GAME_PAWN_USE_LIST_KEY, 0, -1)
		if err == nil {
			// 获取历史采样的对局士兵使用记录列表 并计算出新的各兵种资源消耗
			pawnUseMap := map[int32]int64{}
			datas := config.GetJson("pawnBase").Datas
			for _, data := range datas {
				if ut.Int32(data["need_build_lv"]) > 0 { // 初始化所有玩家可用兵种
					pawnUseMap[ut.Int32(data["id"])] = 0
				}
			}
			var total int64 // 采样参数总计
			for _, data := range list {
				dataArr := strings.Split(data, ":")
				if len(dataArr) < 2 {
					continue
				}
				pawnUserStr := dataArr[1]
				pawnUserArr := strings.Split(pawnUserStr, "|")
				for _, useStr := range pawnUserArr {
					useArr := strings.Split(useStr, "_")
					if len(useArr) < 2 {
						continue
					}
					id := ut.Int32(useArr[0])
					val := ut.Int64(useArr[1])
					pawnUseMap[id] += val
					total += val
				}
			}

			pipe := rds.RdsPipeline()
			minCost, maxCost := slg.PAWN_COST_LIMIT_LIST[0], slg.PAWN_COST_LIMIT_LIST[1]
			avgCost := (minCost + maxCost) / 2
			totalVal := int64(avgCost) * int64(len(pawnUseMap)) // 兵种总资源消耗 = 平均消耗 * 兵种数量
			for id, val := range pawnUseMap {
				pawnCost := int32((float64(val) / float64(total)) * float64(totalVal)) // 该兵种资源消耗 = 兵种总资源消耗 * (该兵种采样参数 / 采样参数总计)
				// 取配置上下限
				pawnCost = ut.MaxInt32(pawnCost, minCost)
				pawnCost = ut.MinInt32(pawnCost, maxCost)
				pipe.HSet(context.TODO(), rds.RDS_GAME_PAWN_COST_MAP_KEY, id, pawnCost)
			}
			// 更新各兵种资源消耗到redis
			pipe.Exec(context.TODO())
		} else {
			log.Warning("get RDS_GAME_PAWN_USE_LIST_KEY err: %v", err)
		}
	} else {
		log.Warning("add RDS_SCRIPT_CMD_ADD_ROOM_PAWN_USE err: %v", err)
	}
}

// 初始化士兵资源消耗map 开服时调用
func (this *Model) InitPawnCostMap() {
	data, err := rds.RdsHGetAll(rds.RDS_GAME_PAWN_COST_MAP_KEY)
	if err == nil && data != nil {
		for k, v := range data {
			this.PawnCostMap[ut.Int32(k)] = ut.Int32(v)
		}
	} else {
		log.Warning("InitPawnCostMap err: %v", err)
	}
	// 保存到数据库
	this.db.UpdatePawnCosts(this.PawnCostMap)
}
