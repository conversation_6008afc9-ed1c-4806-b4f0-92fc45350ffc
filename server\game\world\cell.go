package world

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
)

// 一个单元格信息
type Cell struct {
	Owner string   // 拥有者
	Size  *ut.Vec2 // 大小

	index       int32 // 位置
	CityId      int32 // 城市ID
	DependIndex int32 // 关联位置
	CityByteId  byte
}

func NewCell(index int32, owner string, cityId int32) *Cell {
	cell := &Cell{
		index:       index,
		Owner:       owner,
		DependIndex: -1,
		Size:        ut.NewVec2(1, 1),
	}
	cell.SetCity(cityId)
	return cell
}

func NewCellByDB(index int32, data map[string]interface{}) *Cell {
	cell := NewCell(index, ut.String(data["owner"]), ut.Int32(data["cityId"]))
	return cell
}

func (this *Cell) ToDB() map[string]interface{} {
	msg := map[string]interface{}{
		"owner": this.Owner,
	}
	if this.CityId != 0 {
		msg["cityId"] = this.CityId
	}
	return msg
}

func (this *Cell) ToPb() *pb.CellInfo {
	ret := &pb.CellInfo{
		Index: pb.Int32(this.index),
		Owner: this.Owner,
	}
	if this.CityId > 0 {
		ret.CityId = pb.Int32(this.CityId)
	}
	return ret
}

func (this *Cell) GetIndex() int32 { return this.index }

// 是否有资源
func (this *Cell) IsHasRes() bool {
	id := ut.AbsInt32(this.CityId)
	return id != constant.MAIN_CITY_ID && id != constant.FORT_CITY_ID && !this.IsAncient()
}

// 是否遗迹
func (this *Cell) IsAncient() bool {
	id := ut.AbsInt32(this.CityId)
	return id >= constant.CHANGAN_CITY_ID && id <= constant.LUOYANG_CITY_ID
}

// 是否主城
func (this *Cell) IsMainCity() bool {
	return ut.AbsInt32(this.CityId) == constant.MAIN_CITY_ID
}

// 获取占地面积
func (this *Cell) GetAcreage() int32 {
	return this.Size.X * this.Size.Y
}

// 获取所有拥有的位置
func (this *Cell) GetOwnPoints(size *ut.Vec2) []*ut.Vec2 {
	return helper.GenPointsBySize(this.Size, helper.IndexToPoint(this.index, size))
}

// 获取所有拥有的位置
func (this *Cell) GetOwnIndexs(size *ut.Vec2) []int32 {
	points := this.GetOwnPoints(size)
	indexs := []int32{}
	for _, point := range points {
		indexs = append(indexs, helper.Vec2ToIndex(point, size))
	}
	return indexs
}

// 设置城市
func (this *Cell) SetCity(id int32) {
	this.CityId = id
	this.CityByteId = 0
	if id == 0 {
		this.Size.Init(1, 1)
	} else if data := config.GetJsonData("city", id); data != nil {
		this.Size.Set(ut.NewVec2ByString(data["cell_size"].(string), "x"))
		this.CityByteId = ut.Byte(data["byte_id"])
	} else {
		this.Size.Init(1, 1)
	}
}
