package player

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
)

// 个人标记信息 已弃用
type MapMarkInfo struct {
	Name  string   `bson:"name"`
	Point *ut.Vec2 `bson:"point"`
}

func (this *Model) ToMapMarksPb() map[int32]*pb.MapMarkInfo {
	ret := map[int32]*pb.MapMarkInfo{}
	this.MapMarskMap.ForEach(func(v *g.MapMarkInfo, k int32) bool {
		ret[k] = v.Topb()
		return true
	})
	return ret
}

func (this *Model) GetMapMarkCount() int {
	return this.MapMarskMap.Count()
}

func (this *Model) FindMapMark(index int32) *g.MapMarkInfo {
	return this.MapMarskMap.Get(index)
}

func (this *Model) AddMapMark(index int32, flag int32, desc string) {
	// 删除相同类型的标记
	this.MapMarskMap.DeleteEach(func(v *g.MapMarkInfo, k int32) bool {
		return v.Flag == flag
	})
	this.MapMarskMap.Set(index, g.NewMapMarkInfo(index, flag, desc))
}

func (this *Model) RemoveMapMark(index int32) {
	this.MapMarskMap.Del(index)
}
