package ut

import (
	"math"
	"strings"
)

const base62Chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"

// EncodeBase62 将数字转换为 Base62 字符串
func EncodeBase62(num int) string {
	if num == 0 {
		return string(base62Chars[0])
	}

	base := len(base62Chars)
	var encoded strings.Builder

	for num > 0 {
		remainder := num % base
		encoded.WriteByte(base62Chars[remainder])
		num /= base
	}

	// Base62 结果是倒序的，因此需要反转字符串
	result := encoded.String()
	return reverse(result)
}

// DecodeBase62 将 Base62 字符串转换回数字
func DecodeBase62(encoded string) int {
	base := len(base62Chars)
	num := 0

	for i, char := range encoded {
		index := strings.IndexRune(base62Chars, char)
		power := len(encoded) - (i + 1)
		num += index * int(math.Pow(float64(base), float64(power)))
	}

	return num
}

// reverse 反转字符串
func reverse(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}
