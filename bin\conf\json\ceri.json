[{"id": 1, "type": 1, "value": 1004, "need_lv": 2, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 2, "type": 1, "value": 1005, "need_lv": 2, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 3, "type": 1, "value": 1006, "need_lv": 2, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 4, "type": 1, "value": 1007, "need_lv": 2, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 5, "type": 1, "value": 1008, "need_lv": 11, "need_server_type": "0,1,2", "weight": 80, "cost": "1,0,70|2,0,50|3,0,50", "time": 400}, {"id": 6, "type": 1, "value": 1009, "need_lv": 5, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 7, "type": 1, "value": 1010, "need_lv": 5, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 8, "type": 1, "value": 1011, "need_lv": 18, "need_server_type": "0,1,2", "weight": 70, "cost": "1,0,80|2,0,60|3,0,60", "time": 450}, {"id": 9, "type": 1, "value": 1012, "need_lv": 11, "need_server_type": "0,1,2", "weight": 90, "cost": "1,0,60|2,0,40|3,0,40", "time": 350}, {"id": 10, "type": 1, "value": 1013, "need_lv": 2, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 11, "type": 1, "value": 1014, "need_lv": 2, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 12, "type": 1, "value": 1015, "need_lv": 8, "need_server_type": "0,1,2", "weight": 60, "cost": "1,0,90|2,0,70|3,0,70", "time": 500}, {"id": 13, "type": 1, "value": 1016, "need_lv": 14, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 14, "type": 1, "value": 1017, "need_lv": 14, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 15, "type": 1, "value": 1018, "need_lv": 14, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 16, "type": 1, "value": 1019, "need_lv": 5, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 17, "type": 2, "value": 3102, "need_lv": 4, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 18, "type": 2, "value": 3103, "need_lv": 1, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 20, "type": 2, "value": 3202, "need_lv": 4, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 21, "type": 2, "value": 3301, "need_lv": 1, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 22, "type": 2, "value": 3302, "need_lv": 1, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 23, "type": 2, "value": 3402, "need_lv": 7, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 24, "type": 2, "value": 3502, "need_lv": 10, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 25, "type": 3, "value": 6003, "need_lv": 3, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 26, "type": 3, "value": 6004, "need_lv": 3, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 27, "type": 3, "value": 6005, "need_lv": 3, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 28, "type": 3, "value": 6006, "need_lv": 6, "need_server_type": "0,1,2", "weight": 80, "cost": "1,0,70|2,0,50|3,0,50", "time": 400}, {"id": 29, "type": 3, "value": 6007, "need_lv": 3, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 30, "type": 3, "value": 6008, "need_lv": 3, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 31, "type": 3, "value": 6009, "need_lv": 6, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 32, "type": 3, "value": 6010, "need_lv": 9, "need_server_type": "0,1,2", "weight": 80, "cost": "1,0,70|2,0,50|3,0,50", "time": 400}, {"id": 33, "type": 3, "value": 6011, "need_lv": 12, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 34, "type": 3, "value": 6012, "need_lv": 12, "need_server_type": "0,1,2", "weight": 80, "cost": "1,0,70|2,0,50|3,0,50", "time": 400}, {"id": 35, "type": 3, "value": 6013, "need_lv": 3, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 36, "type": 3, "value": 6014, "need_lv": 6, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 37, "type": 4, "value": 6101, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 38, "type": 4, "value": 6102, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 39, "type": 4, "value": 6103, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 40, "type": 4, "value": 6104, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 41, "type": 4, "value": 6105, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 42, "type": 4, "value": 6106, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 43, "type": 4, "value": 6107, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 44, "type": 4, "value": 6108, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 45, "type": 4, "value": 6109, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 46, "type": 1, "value": 1020, "need_lv": 11, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 47, "type": 1, "value": 1021, "need_lv": 11, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 48, "type": 1, "value": 1022, "need_lv": 11, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 49, "type": 2, "value": 3203, "need_lv": 4, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 50, "type": 2, "value": 3303, "need_lv": 1, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 51, "type": 2, "value": 3304, "need_lv": 1, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 52, "type": 2, "value": 3403, "need_lv": 7, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 53, "type": 2, "value": 3404, "need_lv": 7, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 54, "type": 3, "value": 6015, "need_lv": 9, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 55, "type": 3, "value": 6016, "need_lv": 6, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 56, "type": 3, "value": 6017, "need_lv": 9, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 57, "type": 3, "value": 6018, "need_lv": 9, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 58, "type": 3, "value": 6019, "need_lv": 3, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 59, "type": 3, "value": 6020, "need_lv": 3, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 60, "type": 3, "value": 6021, "need_lv": 6, "need_server_type": "0,1,2", "weight": 80, "cost": "1,0,70|2,0,50|3,0,50", "time": 400}, {"id": 61, "type": 4, "value": 6110, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 62, "type": 4, "value": 6111, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 63, "type": 4, "value": 6112, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 64, "type": 4, "value": 6113, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 65, "type": 4, "value": 6114, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 66, "type": 3, "value": 6022, "need_lv": 3, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 67, "type": 4, "value": 6115, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 68, "type": 2, "value": 3104, "need_lv": 4, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 69, "type": 4, "value": 6116, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 70, "type": 3, "value": 6023, "need_lv": 6, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 71, "type": 2, "value": 3204, "need_lv": 4, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 72, "type": 2, "value": 3305, "need_lv": 1, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 73, "type": 4, "value": 6117, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 74, "type": 2, "value": 3105, "need_lv": 4, "need_server_type": "0,1,2", "weight": 90, "cost": "1,0,60|2,0,40|3,0,40", "time": 350}, {"id": 75, "type": 2, "value": 3405, "need_lv": 7, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 76, "type": 4, "value": 6118, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 77, "type": 4, "value": 6119, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 78, "type": 2, "value": 3205, "need_lv": 4, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 79, "type": 4, "value": 6120, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 80, "type": 3, "value": 6024, "need_lv": 6, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 81, "type": 3, "value": 6025, "need_lv": 19, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 82, "type": 3, "value": 6026, "need_lv": 9, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 83, "type": 3, "value": 6027, "need_lv": 9, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 84, "type": 3, "value": 6028, "need_lv": 6, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 85, "type": 1, "value": 1024, "need_lv": 5, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 86, "type": 1, "value": 1025, "need_lv": 5, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 87, "type": 2, "value": 3406, "need_lv": 7, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 88, "type": 4, "value": 6121, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 89, "type": 2, "value": 3106, "need_lv": 4, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 90, "type": 4, "value": 6122, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 91, "type": 3, "value": 6029, "need_lv": 6, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 92, "type": 3, "value": 6030, "need_lv": 9, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 93, "type": 3, "value": 6031, "need_lv": 9, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 94, "type": 3, "value": 6032, "need_lv": 6, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 95, "type": 4, "value": 6123, "need_lv": 17, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 96, "type": 2, "value": 3306, "need_lv": 1, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 97, "type": 3, "value": 6033, "need_lv": 9, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 98, "type": 3, "value": 6034, "need_lv": 6, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 99, "type": 3, "value": 6035, "need_lv": 6, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}, {"id": 100, "type": 3, "value": 6036, "need_lv": 3, "need_server_type": "0,1,2", "weight": 100, "cost": "1,0,50|2,0,30|3,0,30", "time": 300}]