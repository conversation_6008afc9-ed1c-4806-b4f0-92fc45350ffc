package main

import (
	"encoding/csv"
	"encoding/json"
	"os"
	"sort"
	"strings"

	slg "slgsrv/server/common"
	ut "slgsrv/utils"
)

const (
	filePath = "./bin/conf/json/"
	fileName = "cityText"
)

func main() {
	// 1. 读取JSON文件
	jsonFile, err := os.ReadFile(filePath + fileName + ".json")
	if err != nil {
		panic("读取文件失败: " + err.<PERSON>rror())
	}

	// 2. 解析JSON数据
	var data []map[string]interface{}
	if err := json.Unmarshal(jsonFile, &data); err != nil {
		panic("JSON解析失败: " + err.Error())
	}

	// 3. 动态生成表头（含嵌套字段）
	headerMap := make(map[string]bool)
	for _, item := range data {
		for key, val := range item {
			if nested, ok := val.(map[string]interface{}); ok {
				for subKey := range nested {
					headerMap[key+"."+subKey] = true
				}
			} else {
				headerMap[key] = true
			}
		}
	}

	// 转换为有序表头（按字母排序）
	headers := make([]string, 0, len(headerMap))
	showHeaders := make([]string, 0, len(headerMap))
	for k := range headerMap {
		headers = append(headers, k)
	}
	sort.Strings(headers)

	for _, k := range headers {
		// 语言处理
		if v := slg.TRANS_LANG_MAP[k]; v != "" {
			k = v
		}
		showHeaders = append(showHeaders, k)
	}

	// 4. 创建CSV文件（添加BOM头解决中文乱码）
	csvFile, _ := os.Create(fileName + ".csv")
	defer csvFile.Close()
	csvFile.WriteString("\xEF\xBB\xBF") // UTF-8 BOM‌:ml-citation{ref="1,2" data="citationList"}

	writer := csv.NewWriter(csvFile)
	defer writer.Flush()

	// 5. 写入表头
	writer.Write(showHeaders)

	// 6. 写入数据行
	for _, item := range data {
		record := make([]string, len(headers))
		for i, h := range headers {
			// 处理嵌套字段
			keys := strings.Split(h, ".")
			var value interface{} = item
			for _, k := range keys {
				if m, ok := value.(map[string]interface{}); ok {
					value = m[k]
				} else {
					value = nil
					break
				}
			}

			// 空值处理
			if value == nil {
				record[i] = ""
				continue
			}

			// 特殊类型转换（如数组转字符串）
			switch v := value.(type) {
			case string:
				record[i] = strings.ReplaceAll(v, "\"", "\"\"") // 转义双引号‌:ml-citation{ref="5" data="citationList"}
			case float64:
				record[i] = strings.TrimRight(strings.TrimRight(ut.String(v), "0"), ".") // 去除小数冗余零
			default:
				record[i] = strings.ReplaceAll(v.(string), "\"", "\"\"")
			}
		}
		writer.Write(record)
	}
}
