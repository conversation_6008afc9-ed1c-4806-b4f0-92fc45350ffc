package lobby

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/enums/ctype"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 种植信息
type PlantInfo struct {
	CompleteTime int64 `bson:"complete_time"` // 完成时间
	WateringTime int64 `bson:"watering_time"` // 浇水时间
	ID           int32 `bson:"id"`            // 植物id
}

func (this *PlantInfo) Clean() {
	this.ID = 0
	this.CompleteTime = 0
	this.WateringTime = 0
}

// 是否浇过水了
func (this *PlantInfo) IsWatering() bool {
	if slg.IsDebug() {
		return false
	}
	return ut.NowZeroTime() == ut.DateZeroTime(this.WateringTime)
}

// 植物信息
type BotanyInfo struct {
	ID    int32 `bson:"id"`
	Count int32 `bson:"count"`
}

func (this *User) ToPlantDataPb() *pb.PlantInfo {
	data := this.PlantData
	return &pb.PlantInfo{
		Id:         data.ID,
		RemainTime: ut.MaxInt64(0, data.CompleteTime-time.Now().UnixMilli()),
		IsWatering: data.IsWatering(),
	}
}

func (this *User) ToUnlockBotanysPb() []*pb.BotanyInfo {
	this.BotanyLock.RLock()
	defer this.BotanyLock.RUnlock()
	return array.Map(this.UnlockBotanys, func(m *BotanyInfo, _ int) *pb.BotanyInfo {
		return &pb.BotanyInfo{Id: m.ID, Count: m.Count}
	})
}

func (this *User) UnlockBotanysClone() []*BotanyInfo {
	this.BotanyLock.RLock()
	defer this.BotanyLock.RUnlock()
	return array.Map(this.UnlockBotanys, func(m *BotanyInfo, _ int) *BotanyInfo {
		return m
	})
}

func (this *User) HasBotany(id int32) bool {
	return array.Some(this.UnlockBotanys, func(m *BotanyInfo) bool { return m.ID == id && m.Count > 0 })
}

// 改变植物数量
func (this *User) ChangeBotany(id, count int32, sendUid string) {
	if config.GetJsonData("botany", id) == nil {
		return
	}
	var botany *BotanyInfo
	this.BotanyLock.Lock()
	if botany = array.Find(this.UnlockBotanys, func(m *BotanyInfo) bool { return m.ID == id }); botany != nil {
		botany.Count += count
		if botany.Count <= 0 {
			this.UnlockBotanys = array.RemoveItem(this.UnlockBotanys, func(m *BotanyInfo) bool { return m.ID == id })
		}
	} else if count > 0 {
		botany = &BotanyInfo{ID: id, Count: count}
		this.UnlockBotanys = append(this.UnlockBotanys, botany)
	}
	this.BotanyLock.Unlock()
	AddItemRecord(this.UID, ctype.BOTANY, id, count, botany.Count, 0, sendUid)
	this.FlagUpdateDB()
}

func (this *User) CheckUpdatePlant(now int64) {
	if this.PlantData == nil || this.PlantData.CompleteTime == 0 {
		return
	} else if now < this.PlantData.CompleteTime {
		return
	}
	// 表示成熟了
	this.PlantData.CompleteTime = 0
}
